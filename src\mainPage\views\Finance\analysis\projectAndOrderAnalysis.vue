<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      :row-style="
        ({ row, index }) => {
          return {
            'background-color': row.isUpdate == 1 ? 'var(--el-color-success-light-9)' : '',
          };
        }
      "
      :summary-method="getSummaries"
    >
      <template #menu-left="{}">
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">采购成本：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.purchasePrice }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">人工成本：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.labourCost }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">销售总额：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.contractTotalPrice }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">业务费用：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.businessFee }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">交付费用：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.feeCost }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">税金：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.taxCost }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">利润：</el-text>
        <el-text type="primary" size="large">￥{{ statisticData.profitPrice }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">利润率：</el-text>
        <el-text type="primary" size="large">
          {{
            statisticData.profitPrice && statisticData.contractTotalPrice
              ? ((statisticData.profitPrice / statisticData.contractTotalPrice) * 100).toFixed(2) +
                '%'
              : '---'
          }}
        </el-text>
      </template>
      <template #contractName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.contractName }}</el-link>
      </template>
      <template #businessName-search>
        <userSelect v-model="params.businessUser"></userSelect>
      </template>
      <template #accountingAmount="{ row }">
        <el-popover placement="right" :ref="`pop_${row.id}1`" :width="160" trigger="click">
          <template #reference>
            <span
              >{{ row.accountingAmount || '---' }}
              <el-button type="primary" size="small" icon="edit" text></el-button>
            </span>
          </template>
          <template #default>
            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
              <el-input v-model="row.newAccountingAmount" type="number" placeholder=""></el-input>
              <div style="display: flex; align-items: center; gap: 10px">
                <!-- <span style="font-size: 12px;">取消
                </span> -->
                <el-button
                  size="small"
                  style="border-radius: 3px"
                  @click="
                    updateFee(row, 'newAccountingAmount', 'accountingAmount', `pop_${row.id}1`)
                  "
                  type="primary"
                  >确认</el-button
                >
              </div>
            </div>
          </template>
        </el-popover>
      </template>
      <template #manageFee="{ row }">
        <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
          <template #reference>
            <span
              >{{ row.manageFee || '---'
              }}<el-button type="primary" size="small" icon="edit" text></el-button
            ></span>
          </template>
          <template #default>
            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
              <el-input v-model="row.newManageFee" type="number" placeholder=""></el-input>
              <div style="display: flex; align-items: center; gap: 10px">
                <!-- <span style="font-size: 12px;">取消
                </span> -->
                <el-button
                  size="small"
                  style="border-radius: 3px"
                  @click="updateFee(row, 'newManageFee', 'manageFee', `pop_${row.id}2`)"
                  type="primary"
                  >确认</el-button
                >
              </div>
            </div>
          </template>
        </el-popover>
      </template>
      <template #number="{ row }">
        <el-link type="primary" @click="viewProduct(row)">{{
          parseFloat(row.number || 0) || 1
        }}</el-link>
      </template>
      <template #feeCost="{ row }">
        <el-link type="primary" @click="viewCostDetail(row)"> {{ row.feeCost || '---' }}</el-link>
      </template>
     
      <template #purchaseCost="{ row }">
        <el-link type="primary" @click="viewOrderDetail(row)">
          {{ row.purchaseCost || '---' }}
          </el-link
        >
        <el-popover placement="right" :ref="`pop_${row.id}1`" :width="160" trigger="click">
          <template #reference>
            <span
              ><el-button type="primary" size="small" icon="edit" text></el-button
            ></span>
          </template>
          <template #default>
            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
              <el-input
                v-model="row.newPurchaseCost"
                type="number"
                placeholder="请输入采购成本"
              ></el-input>
              <div style="display: flex; align-items: center; gap: 10px">
                <!-- <span style="font-size: 12px;">取消
                </span> -->
                <el-button
                  size="small"
                  style="border-radius: 3px"
                  @click="updateFee(row, 'newPurchaseCost', 'purchaseCost', `pop_${row.id}1`)"
                  type="primary"
                  >确认</el-button
                >
              </div>
            </div>
          </template>
        </el-popover>
      </template>
      <template #labourCost="{ row }">
        <span type="info" >
              {{ row.labourCost || '---' }}
            </span>
        <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
         
          <template #reference>
            <el-button type="primary" size="small" icon="edit" text></el-button>
          </template>
          <template #default>
            <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
              <el-input
                v-model="row.newLabourCost"
                type="number"
                placeholder="请输入人工成本"
              ></el-input>
              <div style="display: flex; align-items: center; gap: 10px">
                <!-- <span style="font-size: 12px;">取消
                </span> -->
                <el-button
                  size="small"
                  style="border-radius: 3px"
                  @click="updateFee(row, 'newLabourCost', 'labourCost', `pop_${row.id}2`)"
                  type="primary"
                  >确认</el-button
                >
              </div>
            </div>
          </template>
        </el-popover>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      title="项目利润分析"
      v-model="dialogVisible"
      height="100%"
      :before-close="handleClose"
    >
      <el-card shadow="never" style="height: 400px" :body-style="{ height: '100%' }">
        <div ref="myChart" :style="{ width: '100%', height: '100%' }"></div>
      </el-card>
    </el-dialog>
    <el-drawer title="产品明细" size="80%" v-model="detailDrawer">
      <contractProduct :offer-id="currentId"></contractProduct>
    </el-drawer>
    <el-drawer title="费用明细" v-model="costDetailDrawer" size="80%">
      <costDetail :seal-contract-id="currentContractId"></costDetail>
    </el-drawer>
    <payRecord
      ref="payRecordRef"
      :projectName="currentProjectName"
    ></payRecord>
    <purchaseCostDetail ref="purchaseCostDetailRef" :project-id="currentProjectId" > </purchaseCostDetail>
  </basic-container>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import userSelect from '@/views/desk/components/userSelect.vue';
import * as echarts from 'echarts';
import moment from 'moment';
import contractProduct from './contractProduct.vue';
import { ElMessage } from 'element-plus';
import costDetail from './components/costDetail.vue';
import payRecord from '../../Project/detail/payRecord.vue';
import purchaseCostDetail from './components/purchaseCostDetail.vue';
let option = ref({
  height: 'auto',
  maxHeight: '650',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  size: 'small',
  searchLabelWidth: 120,
  menuWidth: 120,
  menu: false,
  border: true,
  searchIcon:true,
  searchIndex:4,
  showSummary: true,
  // sumColumnList: [
  //   { name: 'actualPaymentPrice', type: 'sum' },
  //   { name: 'actualPaymentPrice', type: 'sum' },
  // ],
  column: [
    {
      label: '客户名称',
      prop: 'customerName',
      search: true,
      component: 'wf-customer-drop',
      overHidden: true,
      width: 180,
    },
    {
      // type: 'tree',
      label: '业务板块',

      width: 100,
      span: 12,
      parent: false,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      display: true,
      filterable: true,
      // dataType:'string',
      // multiple:true,
      prop: 'businessTypeId',
      // hide:true,
      search: true,
      checkStrictly: true,
      formatter: row => {
        return row.businessTypeName;
      },
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },
    {
      label: '项目/订单名称',
      prop: 'contractName',
      width: 200,
      overHidden: true,
      search: true,
    },
    {
      label: '签订时间',
      prop: 'signDate',
      width: 90,
      search: true,
      searchSpan: 5,
      component: 'wf-daterange-search',
    },
    {
      label: '产品名称',
      prop: 'productNames',
      overHidden: true,
      width: 130,
    },
    {
      label: '数量',
      prop: 'number',
      width: 60,
      formatter: row => {
        return parseFloat(row.number || 0) || 1;
      },
    },
    {
      label: '单价',
      prop: 'purchasePrice',
      width: 80,
    },
    {
      label: '含税成本总额',
      prop: 'hsprice',
      width: 80,
      children: [
        {
          label: '采购成本',
          prop: 'purchaseCost',
        },
        {
          label: '人工成本',
          prop: 'labourCost',
        },
      ],
    },
    // {
    //   label: '含税核算成本',
    //   prop: 'accountingAmount',
    //   width: 100,
    // },
    // {
    //   label: '财务核算成本',
    //   prop: 'financeAccountingAmount',
    //   width: 100,
    // },
    {
      label: '销售单价',
      prop: 'sealPrice',
      width: 80,
    },
    {
      label: '销售总额/合同额',
      prop: 'contractTotalPrice',
      width: 100,
    },
    {
      label: '业务费用',
      prop: 'businessFee',
      width: 80,
    },
    {
      label: '交付费用',
      prop: 'feeCost',
      width: 80,
    },
    // {
    //   label: '管理费用',
    //   prop: 'manageFee',
    //   width: 80,
    // },
    // {
    //   label: '质保尾款',
    //   prop: 'balancePayment',
    //   width: 80,
    // },
    // {
    //   label: '逾期利息',
    //   prop: 'overdueInterest',
    //   width: 80,
    // },

    // {
    //   label: '年份',
    //   prop: 'year',
    //   type: 'year',
    //   format: 'YYYY',
    //   valueFormat: 'YYYY',
    //   search: true,
    //   hide: true,
    //   width: 100,
    // },
    // {
    //   label: '月份',
    //   prop: 'date',
    //   type: 'month',
    //   format: 'YYYY-MM',
    //   valueFormat: 'YYYY-MM',
    //   search: true,
    //   hide: true,
    //   width: 100,
    // },

    // {
    //   label: '采购成本',
    //   prop: 'purchaseCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.purchaseCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.purchaseCost).toFixed(2);
    //   },
    // },

    // {
    //   label: '人工成本',
    //   prop: 'labourCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.labourCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.labourCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '费用',
    //   prop: 'feeCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.feeCost).toFixed(2)) ? '' : parseFloat(row.feeCost).toFixed(2);
    //   },
    // },
    {
      label: '税金',
      prop: 'taxCost',
      width: 80,
      formatter: row => {
        return isNaN(parseFloat(row.taxCost).toFixed(2)) ? '' : parseFloat(row.taxCost).toFixed(2);
      },
    },
    {
      label: '利润',
      prop: 'grossProfit',
      width: 90,
      fixed: 'right',
      formatter: row => {
        return isNaN(parseFloat(row.grossProfit).toFixed(2))
          ? ''
          : parseFloat(row.grossProfit).toFixed(2);
      },
    },
    {
      label: '利润率',
      prop: 'grossProfitRate',
      width: 70,
      fixed: 'right',
    },
    {
      label: '业务员',
      prop: 'businessName',
      search: true,
      searchSpan: 3,
      component: 'wf-user-drop',
    },
     {
      label: '开票公司',
      type: 'select',
      prop: 'billingCompany',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },
      search:true,
      hide:true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label:'合同开票',
      type:'select',
      hide:true,
      prop:'isSealContractInvoice',
      search:true,
      dicData:[
        {label:'无需开票',value:0},
        {label:'需要开票',value:1},
      ]
    },
    
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/profitAnalysis/page';
let params = ref({
  // year: moment(new Date()).format('YYYY'),
  signDate: [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          newAccountingAmount: item.accountingAmount,
          newManageFee: item.manageFee,
        };
      });
      // tableData.value = [
      //   {
      //     contractName: '项目1',
      //     contractTotalPrice: 10000,
      //     customerName: '客户1',
      //     purchaseCost: 1000,
      //     labourCost: 500,
      //     otherCost: 1000,
      //     taxCost: 1000,
      //     feeCost: 1000,
      //     profit: 1000,
      //     profitRate: 1000,
      //     businessName: '业务员1',
      //   },
      // ];
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
  axios
    .get('/api/vt-admin/profitAnalysis/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      console.log(res.data.data);
      statisticData.value = res.data.data;
    });
}
let router = useRouter();
let myChart = ref(null);
let dialogVisible = ref(false);
function viewEcharts(row) {
  dialogVisible.value = true;
  proxy.$nextTick(() => {
    const chart = echarts.init(myChart.value);
    let option = {
      tooltip: {
        trigger: 'item',
        formatter: function (params) {
          var name = params.name;
          return (
            '<div style="display:flex; align-items:center;">' +
            '<span style="display:inline-block;width:10px;height:10px;background-color:' +
            params.color +
            ';margin-right:5px;"></span>' +
            name +
            ': ' +
            params.value +
            '</div>'
          );
        },
      },
      series: [
        {
          name: 'Access From',
          type: 'pie',

          avoidLabelOverlap: false,
          itemStyle: {
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          labelLine: {
            show: true,
          },
          data: [
            { value: row.purchaseCost, name: '采购成本' },
            { value: row.labourCost, name: '人工成本' },
            { value: row.feeCost, name: '其他成本' },
            { value: row.taxCost, name: '税金' },
            { value: row.profit, name: '利润' },
          ],
          label: {
            show: true,
            formatter: function (params) {
              console.log(params);
              var name = params.name;
              var value = params.value;
              return name + '\n' + value;
            },
          },
        },
      ],
    };
    chart.setOption(option);
  });
}

function searchChange(params, done) {
  onLoad();
  done();
}
onMounted(() => {
  
});
let statisticData = ref({});

function getSummaries({ columns, data }) {
  let arr = [];
  let valueArr = [
    'contractTotalPrice',
    'purchaseCost',
    'labourCost',
    'feeCost',
    'taxCost',
    'grossProfit',
    'businessFee',
    'feeCost',
  ];
  columns.forEach((item, index) => {
    if (index === 0) {
      arr.push(
        <div>
          本页小计：
          {/* <br />
          合计： */}
        </div>
      );
    } else {
      if (valueArr.includes(item.property)) {
        const value = data.reduce((sum, i) => {
          sum = sum + i[item.property] * 1;
          return sum;
        }, 0);
        arr.push(
          <div>
            {isNaN(value) ? '' : value.toFixed(2)}
            {/* <br />
            {
              statisticData.value[
                item.property == 'purchaseCost'
                  ? 'purchasePrice'
                  : item.property == 'profit'
                  ? 'profitPrice'
                  : item.property
              ]
            } */}
          </div>
        );
      } else {
        arr.push('');
      }
    }
  });
  return arr;
}
function updateFee(row, nKey, oKey, ref) {
  axios
    .post('/api/vt-admin/profitAnalysis/update', {
      id: row.id,
      [oKey]: row[nKey],
    })
    .then(res => {
      ElMessage.success(res.data.msg);
      console.log(proxy.$refs[ref]);

      proxy.$refs[ref].hide();
      onLoad();
    });
}
let detailDrawer = ref(false);
let currentId = ref(null);

let costDetailDrawer = ref(false);
let currentContractId = ref(null);
function viewCostDetail(row) {
  costDetailDrawer.value = true;
  currentContractId.value = row.sealContractId;
}
function viewDetail(row) {
  if(row.projectId){
    router.push({
    path: '/Project/detail/detail',
    query: {
      id:row.projectId,
      name:row.contractName
    },
  });
  }else{
    router.push({
    path: '/Contract/customer/allCustomerContract',
    query: {
      sealContractId: row.sealContractId,
    },
  });
  }
  

}
function viewProduct(row) {
  currentId.value = row.offerId;
  detailDrawer.value = true;
}
function reset() {
  params.value.businessUser = '';
  onLoad();
}
// let currentProjectId = ref(null);
// let purchaseCostDetailRef = ref(null);
// function viewOrderDetail(row) {
//   currentProjectId.value = row.projectId;
//   purchaseCostDetailRef.value.open()
// }


function viewOrderDetail(row) {
  router.push({
    path: '/SRM/procure/order',
    query: {
      offerName:row.contractName
    },
  });
}


let currentProjectName = ref('')
function viewLabourCostDetail(row) {
  currentProjectName.value = row.contractName;
  proxy.$refs.payRecordRef.open()
}

</script>

<style lang="scss" scoped>
:deep(.el-table--small .cell) {
  padding: 0;
}
</style>
