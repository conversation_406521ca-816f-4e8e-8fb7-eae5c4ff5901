<template>
  <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
    :table-loading="loading" ref="crud" @keyup.enter="onLoad" @row-del="rowDel" @search-reset="onLoad"
    @search-change="searchChange" @refresh-change="onLoad" @current-change="onLoad" @size-change="onLoad"
    v-model="form">
    <template #menu-left>
      <el-button type="primary" @click="handleAdd" icon="plus">新增</el-button>
      <el-text size="large" style="font-weight: bolder; margin-left: 10px">签证总额：</el-text>
      <el-text type="primary" size="large">￥{{ (allTotalPrice * 1).toLocaleString() }}</el-text>
    </template>
    <template #menu="{ row }">
      <el-button text type="primary" @click="handleComplete(row)" v-if="row.deliveryContact == 0"
        icon="CircleCheckFilled">完成</el-button>
      <el-button text type="primary" @click="edit(row)" icon="edit" v-if="row.changeStatus == 0">编辑</el-button>
      <el-button text type="primary" @click="complete(row)" icon="SuccessFilled"
        v-if="row.changeStatus == 0">完成</el-button>
      <el-button text type="primary" @click="handleView(row)" icon="view">详情</el-button>
      <el-button text type="primary" @click="returnBack(row)" icon="back" v-if="row.changeStatus == 1">撤回</el-button>
      <el-button text type="primary" @click="$refs.crud.rowDel(row)" icon="delete"
        v-if="row.changeStatus == 0 && $store.getters.userInfo.user_id == row.createUser">删除</el-button>
      <!-- <el-button type="primary" text icon="Printer" @click="printChange(row)">打印</el-button> -->
    </template>
    <template #files="{ row }">
      <file :fileList="row.attachList"></file>
      <el-button type="primary" circle @click="addFile(row)" icon="plus"></el-button>
    </template>
    <template #changeFiles="{ row }">
      <file :fileList="row.changeFileList"></file>
    </template>
  </avue-crud>

  <el-drawer v-model="drawer" destroy-on-close size="95%" title="新增签证">
    <el-row style="height: 100%" :gutter="8">
      <el-col style="height: 100%" :span="6">
        <el-card class="box-card" shadow="never" style="height: 100%; overflow: auto">
          <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
          </avue-form>
        </el-card>
      </el-col>
      <el-col style="height: 100%" :span="18">
        <el-card class="box-card" shadow="never" style="height: 100%; overflow: auto">
          <avue-crud :option="selectProductOption" @row-del="productRowDel" :summary-method="summaryMethod"
            ref="addCrudRef" :data="selectProductList">
            <template #menu-left>
              <el-button type="primary" icon="plus" v-if="!isDetail" @click="addProduct">选择产品</el-button>
              <el-button type="primary" icon="plus" v-if="!isDetail"
                @click="$refs.wfProductSelectRef.visible = true">添加产品</el-button>
              <el-button type="primary" plain icon="upload" v-if="!isDetail"
                @click="handleImportInquirySheet">导入产品</el-button>
            </template>
            <template #menu="{ row }">
              <el-button type="primary" text icon="delete" @click="$refs.addCrudRef?.rowDel(row)">删除</el-button>
              <el-button type="primary" text icon="link" @click="handleLink(row)">关联</el-button>
            </template>
            <template v-if="!isDetail" #number="{ row }">
              <div class="disable">
                <el-input-number :min="0" size="small" @blur="() => { setTotalPrice(row); setCostTotalPrice(row) }"
                  :controls="false" style="width: 100%" v-model="row.number"></el-input-number>
              </div>
            </template>
            <template v-if="!isDetail" #changeType="{ row, size }">
              <avue-select v-model="row.changeType" :size="size" class="disable"
                @change="() => { setTotalPrice(row); setCostTotalPrice(row) }" placeholder="请选择内容" :dic="[
                  { value: 0, label: '增加' },
                  { value: 2, label: '减少', disabled: !row.detailId },
                ]"></avue-select>
            </template>
            <template v-if="!isDetail" #sealPrice="{ row }">
              <el-input :min="0" size="small" @blur="setTotalPrice" class="disable" style="width: 100%"
                v-model="row.sealPrice"></el-input>
            </template>
            <template v-if="!isDetail" #costPrice="{ row }">
              <el-input :min="0" size="small" v-if="row.changeType == 0" @blur="setCostTotalPrice" class="disable"
                style="width: 100%" v-model="row.costPrice"></el-input>
            </template>
            <template #costTotalPrice="{ row }">
              <span v-if="row.changeType == 0">{{ (row.number * row.costPrice).toFixed(2) }}</span>

            </template>
            <template v-if="!isDetail" #remark="{ row }">
              <el-input size="small" class="disable" style="width: 100%" v-model="row.remark"
                type="textarea"></el-input>
            </template>
            <template #productName="{ row }">
              <el-tag type="danger" effect="plain" size="small" v-if="!row.detailId">新</el-tag>{{ row.productName }}
            </template>
            <!-- <template #classify="{ row }">
              <div v-if="row.id">
                <span>{{ row.classify }}</span>
              </div>
              <avue-input-tree
                default-expand-all
                v-else
                v-model="row.classify"
                :parent="false"
                :node-click="
                  node => {
                    classifyNodeClick(node, row);
                  }
                "
                placeholder="请选择内容"
                :dic="$refs.productSelectRef.treeData"
              ></avue-input-tree>
            </template> -->
          </avue-crud>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <div style="flex: auto">
        <!-- <el-button type="primary" v-if="!isDetail" @click="handlePrint">预览 打印</el-button> -->
        <el-button type="primary" v-if="!isDetail" @click="$refs.addFormRef.submit()">确 认</el-button>
      </div>
    </template>
    <el-drawer title="选择产品" v-model="innerDrawer" size="80%" append-to-body>
      <productSelect ref="productSelectRef" :type="1" :disableList="selectProductList" :projectId="props.projectId">
      </productSelect>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="confirmAddProduct">确 认</el-button>
        </div>
      </template>
    </el-drawer>
  </el-drawer>
  <wfProductSelect ref="wfProductSelectRef" @onConfirm="handleProductSelectConfirm"></wfProductSelect>
  <el-drawer v-model="printDrawer" :with-header="false" size="50%">
    <div id="print_box" style="height: calc(100% - 50px)">
      <h3 style="text-align: center; text-decoration: underline">签证签证单</h3>
      <div style="display: flex; justify-content: space-between">
        <div>编制人：{{ projectForm.technologyUserName }}</div>
        <div>项目编号：{{ projectForm.projectCode }}</div>
      </div>
      <table border="1" style="width: 100%">
        <colgroup>
          <col style="width: 20%" />
        </colgroup>
        <tbody>
          <tr>
            <td>项目名称：</td>
            <td style="font-weight: bold">{{ projectForm.projectName }}</td>
          </tr>
          <tr>
            <td>签证原因：</td>
            <td>{{ form.changeReason }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" v-if="printData.addList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">一、增项内容</th>
          </tr>
          <tr>
            <th>产品名称</th>
            <th>规格型号</th>
            <th>品牌</th>
            <th>数量</th>
            <th>单价</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.addList">
            <td>{{ item.productName }}</td>
            <td>{{ item.productSpecification }}</td>
            <td>{{ item.productBrand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ (item.sealPrice * 1).toFixed(2) }}</td>
            <td>{{ (item.number * item.sealPrice).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(A=)</td>
            <td style="text-align: right" colspan="5">￥{{ printData.addTotal.toFixed(2) }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" v-if="printData.reduceList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">
              {{ printData.addList.length > 0 ? '二' : '一' }}、减项内容
            </th>
          </tr>
          <tr>
            <th>产品名称</th>
            <th>规格型号</th>
            <th>品牌</th>
            <th>数量</th>
            <th>单价</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.reduceList">
            <td>{{ item.productName }}</td>
            <td>{{ item.productSpecification }}</td>
            <td>{{ item.productBrand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ (item.sealPrice * 1).toFixed(2) }}</td>
            <td>{{ (item.number * item.sealPrice).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(B=)</td>
            <td style="text-align: right" colspan="5">
              ￥ -{{ printData.reduceTotal.toFixed(2) }}
            </td>
          </tr>
        </tbody>
      </table>
      <!-- <table border="1" v-if="printData.otherList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 30%" />
          <col style="width: 30%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">
              {{
                printData.addList.length > 0 && printData.reduceList.length > 0
                  ? '三'
                  : printData.addList.length > 0 || printData.reduceList.length > 0
                  ? '二'
                  : '一'
              }}、其他费内容
            </th>
          </tr>
          <tr>
            <th>收费内容</th>
            <th>收费标准(%)</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.otherList">
            <td>{{ item.productName }}</td>
            <td>{{ item.chargePercentage }}</td>
            <td>{{ (item.totalPrice * 1).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(C=)</td>
            <td style="text-align: right" colspan="5">
              ￥{{ (printData.otherTotal * 1).toFixed(2) }}
            </td>
          </tr>
        </tbody>
      </table> -->
      <table border="1" style="width: 100%">
        <colgroup>
          <col style="width: 50%" />
          <col style="width: 50%" />
        </colgroup>
        <tbody>
          <tr>
            <td colspan="2" style="text-align: left">
              {{
                ['一', '二', '三', '四'][
                [
                  printData.addList?.length,
                  printData.reduceList?.length,
                  printData.otherList?.length,
                ].filter(item => item == true).length
                ]
              }}、合计：{{
                (
                  printData.addTotal * 1 -
                  printData.reduceTotal * 1 +
                  printData.otherTotal * 1
                ).toFixed(2)
              }}
              元，人民币大写：{{
                chieseNumMoney(
                  printData.addTotal * 1 - printData.reduceTotal * 1 + printData.otherTotal * 1 < 0 ? (printData.addTotal *
                    1 - printData.reduceTotal * 1 + printData.otherTotal) * -1 : printData.addTotal * 1 -
                    printData.reduceTotal * 1 + printData.otherTotal * 1) }} </td>
          </tr>
          <tr>
            <td style="text-align: left; padding-left: 5px">
              <br />施工单位意见：<br /><br />施工单位：{{ form.constructionRepresentative }}
              <br /><br />施工单位代表： <br /><br />提交日期：<br />
            </td>
            <td style="text-align: left; padding-left: 5px">
              <br />审核单位意见：<br /><br />审核单位：{{ projectForm.customerName }}
              <br /><br />审核单位代表： <br /><br />审核日期：<br />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <template #footer>
      <el-button @click="printDrawer = false">取消</el-button>
      <el-button type="primary" v-print="print">确定 打印</el-button>
    </template>
  </el-drawer>

  <el-dialog title="导入产品" v-model="dialogVisible" width="30%" class="avue-dialog avue-dialog--top">
    <el-upload class="upload-demo" drag ref="upload" :on-success="handleSuccess" :on-error="handleError"
      :on-exceed="handleExceed" :limit="1" accept=".xlsx,.xls"
      action="/api/vt-admin/businessInquiry/analyzeExcel?parseRowNumber=1&sheetNum=1" :headers="{
        [website.tokenHeader]: $store.getters.token,
        Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
      }" multiple :auto-upload="false">
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <div><el-link type="primary" slot="tip" @click.stop="download">下载模板</el-link></div>
    </el-upload>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleSubmit" type="primary">确 定</el-button>
    </div>
  </el-dialog>

  <el-drawer title="导入产品" v-model="isEditInquirySheet" size="70%">
    <avue-form :option="fileOption" ref="fileRef" v-model="fileForm"></avue-form>
    <avue-crud @row-dblclick="handleRowDBLClick" @row-del="handleRowDel" ref="fileCrudRef" @row-update="handleRowUpdate"
      :option="inquirySheetOption" @selection-change="handleChange" :data="inquirySheetData">
      <template #header>
        <el-alert type="success" size="small">双击可编辑</el-alert>
      </template>
      <template #menu-left>
        <el-button type="primary" @click="clearEmptyProduct">清除空产品</el-button>
        <!-- <el-dropdown style="margin-left: 10px" @command="handleCommand">
          <el-button type="primary">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="classifyName">编辑分类</el-dropdown-item>
              <el-dropdown-item command="productBrand">编辑品牌</el-dropdown-item>
              <el-dropdown-item command="customProductSpecification">编辑型号</el-dropdown-item>
              <el-dropdown-item command="costPrice">编辑单价</el-dropdown-item>
              <el-dropdown-item command="customUnit">编辑单位</el-dropdown-item>
              <el-dropdown-item command="number">编辑数量</el-dropdown-item>
              <el-dropdown-item command="deleteProduct"
                ><span style="color: var(--el-color-danger)">删除产品</span></el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
    </el-dropdown> -->
      </template>
      <template #totalPrice="{ row }">
        {{ row.number * row.sealPrice }}
      </template>
    </avue-crud>
    <template #footer>
      <div style="flex: auto">
        <el-button type="danger" @click="resetImport" plain>重新导入</el-button>
        <el-button type="primary" @click="handleAddProduct">添加产品</el-button>
      </div>
    </template>
  </el-drawer>
  <el-dialog v-model="importDialogVisible" title="导入询价单" style="width: 50%" class="avue-dialog avue-dialog--top">
    <el-form label-width="160" class="uploadForm">
      <el-form-item label="工作表所在位置:" required>
        <el-input style="margin-right: 5px" v-model="sheetNum" size="large" placeholder="请输入所在表"></el-input>
      </el-form-item>
      <el-form-item label="表头所在行:" required>
        <el-input v-model="headerRow" size="large" placeholder="请输入表头所在行"></el-input>
      </el-form-item>
      <el-form-item label="文件上传:" required>
        <el-upload :show-file-list="false" accept=".xlsx,.xls" style="width: 100%" :disabled="!headerRow || !sheetNum"
          :http-request="uploadFile" drag>
          <div style="width: 100%">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          </div>

          <!-- <el-button
              type=""
              size="large"
              :disabled="!headerRow && !sheetNum"
              :title="headerRow ? '导入询价单' : '请先选择表头所在行'"
              v-if="$route.query.type != 'detail'"
              @click="importSheet"
              style="width: 50px; margin-left: 5px; border-radius: 1px; margin-right: 5px"
              icon="Upload"
            ></el-button> -->
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- <div class="avue-dialog__footer">
      <el-button @click="importDialogVisible = false">取 消</el-button>
      <el-button type="primary">确 定</el-button>
    </div> -->
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { Base64 } from 'js-base64';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
import productSelect from './selectNew.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { randomLenNum, DX } from '@/utils/util';
import { ElNotification, ElPopconfirm } from 'element-plus';
import progress from '@/components/progress/index.vue';
import request from '@/axios';
import { ElMessageBox, ElSelect, ElOption } from 'element-plus';
import Sortable from 'sortablejs';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 280,
  border: true,
  column: [
    {
      label: '签证主题',
      prop: 'title',
      width: 250,
      overHidden: true,
    },

    {
      label: '签证金额',
      prop: 'totalPrice',
      overHidden: true,
      width: 120,
    },
    // {
    //   label: '签证人',
    //   prop: 'changeUser',
    //   type: 'input',
    // },
    {
      label: '签证日期',
      prop: 'changeDate',
      searchSpan: 6,
      searchRange: true,
      type: 'date',
      format: 'YYYY-MM-DD',
      width: 100,
      valueFormat: 'YYYY-MM-DD',
    },

    {
      label: '签证原因',
      prop: 'changeReason',
      overHidden: true,
    },
    {
      label: '签证附件',
      prop: 'changeFiles',
      type: 'upload',
      value: [],
      dataType: 'object',
      loadText: '签证签证上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    // {
    //   label: '签证类型',
    //   prop: 'remark',
    //   overHidden: true,
    // },

    // {
    //   label: '备注',
    //   prop: 'deliveryUser',
    // },

    // {
    //   label: '创建人',
    //   prop: 'deliveryAddress',
    //   type: 'select',
    // },
    // {
    //   label: '创建时间',
    //   prop: 'deliveryContact',
    //   searchSpan: 6,
    //   searchRange: true,
    //   type: 'date',
    // },
    {
      label: '完成附件',
      prop: 'files',
      type: 'upload',
      value: [],
      dataType: 'object',
      loadText: '完成附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '创建人',
      prop: 'createName',
      type: 'input',
      width: 100,
    },
    {
      label: '签证状态',
      prop: 'changeStatus',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '申请中',
        },
        {
          value: 1,
          label: '已完成',
        },
        {
          value: 2,
          label: '已取消',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  projectPrice: String,
  projectId: String,
});

const emit = defineEmits(['success']);
const addUrl = '';
const delUrl = '/api/vt-admin/projectChange/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectChange/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let allTotalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/projectChange/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      allTotalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}

function searchChange(params, done) {
  onLoad();
  done();
}

//   请购新增

let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '签证主题',
      prop: 'title',
      width: 250,
      span: 24,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入签证主题',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '签证原因',
      prop: 'changeReason',
      span: 24,
      type: 'textarea',
    },

    {
      label: '签证日期',
      prop: 'changeDate',
      searchSpan: 6,
      span: 24,
      searchRange: true,

      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '签证成本金额',
      prop: 'costTotalPrice',
      // readonly: true,
      overHidden: true,
      width: 120,
      span: 24,
    },
    {
      label: '签证金额',
      prop: 'totalPrice',
      // readonly: true,
      overHidden: true,
      width: 120,
      span: 24,
    },
    // {
    //   label: '签证类型',
    //   prop: 'remark',
    //   overHidden: true,
    // },

    // {
    //   label: '备注',
    //   prop: 'deliveryUser',
    // },

    // {
    //   label: '签证签证',
    //   prop: 'files',
    //   type: 'upload',
    //   value: [],
    //   dataType: 'object',
    //   loadText: '签证签证上传中，请稍等',
    //   span: 24,
    //   slot: true,
    //   // align: 'center',
    //   propsHttp: {
    //     res: 'data',
    //     url: 'id',
    //     name: 'originalName',
    //     // home: 'https://www.w3school.com.cn',
    //   },
    //   action: '/blade-resource/attach/upload',
    //   uploadPreview: (res, data, done, file) => {
    //     console.log(res, data, done, file);
    //   },
    // },
    // {
    //   label: '施工单位',
    //   prop: 'constructionRepresentative',
    //   type: 'select',
    //   props: {
    //     label: 'dictValue',
    //     value: 'dictValue',
    //   },
    //   span: 24,
    //   overHidden: true,
    //   cell: false,
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=billingCompany',
    // },
    // {
    //   label: '联系人',
    //   prop: 'changeUser',
    //   type: 'input',
    //   span: 24,
    // },
    // {
    //   label: '联系电话',
    //   prop: 'changeUserPhone',
    //   type: 'input',
    //   span: 24,
    // },
    {
      label: '产品是否含税',
      prop: 'isHasTax',
      type: 'switch',
      rules: [{ required: true, message: '请选择是否含税', trigger: 'change' }],

      span: 24,
      value: 0,
      dicData: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
      change: () => {
        setTimeout(() => {
          setTotalPrice();
        });
      },
      control: val => {
        return {
          taxRate: {
            display: val == 0,
          },
        };
      },
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      span: 24,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      value: '13',
      cell: false,
      prop: 'taxRate',
      // change:({value}) => {
      //   totalAmount(value)
      // },
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      change: ({ value }) => {
        setTimeout(() => {
          setTotalPrice();
        });
      },
    },
    {
      label: '签证附件',
      prop: 'changeFiles',
      type: 'upload',
      value: [],
      dataType: 'object',
      loadText: '签证签证上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});

let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  delBtn: false,
  addBtn: false,
  maxHeight: 'auto',
  border: true,
  align: 'center',
  selection: false,
  // calcHeight: 200,
  size: 'small',
  // sortable: true,
  menuWidth: 140,
  showSummary: true,

  column: [
    {
      label: '产品名称',
      prop: 'productName',
      width: 130,
      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      width: 150,
      cell: false,
      type: 'input',
    },
    {
      label: '品牌',
      prop: 'productBrand',
      width: 80,
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '产品描述',
      prop: 'description',
      width: 150,
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 80,
      span: 12,
    },
    // {
    //   label: '所属分类',
    //   prop: 'classify',
    // },
    // {
    //   label: '清单数量',
    //   prop: 'deepenNumber',
    //   type: 'number',
    //   align: 'center',
    //   span: 12,
    //   width: 120,
    //   cell: false,
    // },
    {
      label: '签证类型',
      prop: 'changeType',
      width: 80,
      type: 'select',
      dicData: [
        {
          label: '增加',
          value: 0,
        },
        {
          label: '减少',
          value: 2,
        },
      ],
    },
    {
      label: '原销售价',
      prop: 'oldSealPrice',
      width: 80,
    },
    {
      label: '签证数量',
      prop: 'number',
      width: 80,
    },
    // {
    //   label: '价格',
    //   prop: 'beforeTotalPrice	',
    //   width: 100,
    // },
    {
      label: '单价',
      prop: 'sealPrice',
      width: 85,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      formatter: row => {
        return (row.number * row.sealPrice).toFixed(2);
      },
      width: 100,
    },
    {
      label: '成本单价',
      prop: 'costPrice',
      width: 85,
    },
    {
      label: '成本金额',
      prop: 'costTotalPrice',
      formatter: row => {
        return (row.number * row.costPrice).toFixed(2);
      },
      width: 100,
    },
    {
      label: '备注',
      prop: 'remark',
      width: 130,
      type: 'textarea',
    },
  ],
});

let contractList = ref([]);
let currentContractId = ref('');
// 查询客户合同
function queryContract(id) {
  axios
    .get('/api/vt-admin/sealContract/pageForStatement', {
      params: {
        customerId: form.value.customerId,
        ...applyQuery.value,
        ...pageContract.value,
        selectType: 5,
      },
    })
    .then(res => {
      contractList.value = [...contractList.value, ...res.data.data.records];
    });
}

function handleAdd() {
  isDetail.value = false;
  formOption.value.detail = false;
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
  });
}
let selectType = ref(0); // 0  选择  1 关联
function addProduct() {
  selectType.value = 0;
  innerDrawer.value = true;
}
let productList = ref([]);
let selectLoading = ref(false);

let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  let data = proxy.$refs.productSelectRef.getSelectList();
  if (selectType.value == 0) {
    selectProductList.value.push(
      ...data.map(i => {
        return {
          ...i,
          productName: i.customProductName,
          productSpecification: i.customProductSpecification,
          unitName: i.customUnit || i.product.unitName,
          sealPrice: i.zhhsdj,
          oldSealPrice: i.zhhsdj,
          detailId: i.id,
          id: null,
          number: 1,
        };
      })
    );
    //   selectList.value = [];
  } else {
    if (data.length > 1) return proxy.$message.warning('请选择一条数据');
    const row = data[0];
    currentRow.value.detailId = row.id;
    currentRow.value.oldSealPrice = row.zhhsdj;
    currentRow.value.productName = row.customProductName;
    currentRow.value.productSpecification = row.customProductSpecification;
    currentRow.value.description = row.customProductDescription;
    currentRow.value.productBrand = row.productBrand;
    currentRow.value.unitName = row.customUnit;
  }
  // 清空选择
  proxy.$refs.productSelectRef.clearSelect();
  innerDrawer.value = false;
  setTotalPrice();
  setCostTotalPrice();
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      if (row.detailId) {
        selectProductList.value = selectProductList.value.filter(
          item => item.detailId !== row.detailId
        );
      } else if (row.id) {
        selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      } else {
        selectProductList.value = selectProductList.value.filter(item => item.uuid !== row.uuid);
      }
      setTotalPrice()
    })
    .catch(() => { });
}

function totalPrice() {
  return {
    addTotal: selectProductList.value
      .filter(item => item.changeType == 0)
      .reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
    reduceTotal: selectProductList.value
      .filter(item => item.changeType == 2)
      .reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
    // otherTotal: form.value.otherCost.reduce((pre, cur) => {
    //   return pre + cur.totalPrice * 1;
    // }, 0),
  };
}
function submit(form, done, loading) {
  console.log(form, selectProductList.value);
  const productList = selectProductList.value.map((item, index) => {
    return {
      ...item,
      type: 0,
      sort: index,
    };
  });
  // const otherList = form.otherCost.map(item => {
  //   return {
  //     ...item,
  //     type: 1,
  //   };
  // });
  const value = productList.reduce((prev, curr) => {
    if (curr.changeType == 2) {
      prev += curr.number * curr.sealPrice * -1;
    } else {
      prev += curr.number * curr.sealPrice;
    }

    return prev;
  }, 0);

  const taxPrice = form.isHasTax == 0 ? value * (form.taxRate / 100) : null;
  console.log(taxPrice, form.totalPrice, form.taxRate, '111111');

  const data = {
    ...form,
    changeFiles: form.changeFiles && form.changeFiles.map(item => item.value).join(','),
    detailDTOList: [...productList],
    taxPrice,
    projectId: route.query.id,
  };
  data.createTime = null;
  console.log(data);
  let url = form.id ? '/api/vt-admin/projectChange/update' : '/api/vt-admin/projectChange/save';
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        done();
        onLoad();
        drawer.value = false;
        clearAll();
        emit('success');
      }
    })
    .catch(() => {
      done();
    });
}
function clearAll() {
  proxy.$refs.addFormRef.resetFields();
  selectProductList.value = [];
  form.id = null;
  form.value = {};
}
let pageContract = ref({
  current: 1,
  size: 20,
});
function loadMore() {
  console.log(1111);
  pageContract.value.current += 1;
  queryContract();
}
function handleProductSelectConfirm(id) {
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      let data = res.data.data;
      data.number = 1;
      data.productId = res.data.data.id;
      data.id = null;
      data.uuid = randomLenNum(10);
      data.customProductName = data.productName;
      data.customProductSpecification = data.productSpecification;
      data.product = {
        unitName: data.unitName,
      };
      data.costPrice = 0;
      data.changeType = 0;
      selectProductList.value.push(data);
      data.sealPrice = '';
    });
}

function handleComplete(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'datetime',
          label: '实际收货时间',
          span: 24,
          value: dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDeliveryDate',
        },
        {
          label: '签证签证',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '签证签证上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectApplyPurchase/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        emit('success');
        res.close();
        onLoad();
      });
    },
  });
}
let isDetail = ref(false);
function handleView(row) {
  drawer.value = true;
  isDetail.value = true;
  formOption.value.detail = true;
  selectProductOption.value.menu = false;
  form.value = row;
  parseData(row.detailVOS);
  setTimeout(() => {
    form.value.totalPrice = row.totalPrice;
  }, 500);
  // selectProductList.value = row.detailVOS;
}
// function classifyNodeClick(node, row) {
//   row.moduleId = node.parentId;
// }
let printDrawer = ref(false);
const print = ref({
  id: 'print_box',
});
let printData = ref({});
function handlePrint() {
  const data = {
    addList: selectProductList.value.filter(item => item.changeType == 0),
    reduceList: selectProductList.value.filter(item => item.changeType == 2),
    // otherList: form.value.otherCost,
    addTotal: totalPrice().addTotal,
    reduceTotal: totalPrice().reduceTotal,
    // otherTotal: totalPrice().otherTotal,
  };
  printData.value = data;
  printDrawer.value = true;
}

function totalText() {
  const A = 'A';
  const B = '-B';
  const C = '+C';
  return A + B + C;
}
function chieseNumMoney(number) {
  return DX(number);
}

onMounted(() => {
  getProjectDetail();
});
let projectForm = ref({});
function getProjectDetail() {
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      projectForm.value = res.data.data;
    });
}
function parseData(data) {
  // form.value.otherCost = data.filter(item => item.type == 1);
  selectProductList.value = data
    .filter(item => item.type == 0)
    .map(item => {
      return {
        ...item,
        number: parseFloat(item.number),
      };
    });
}

// 编辑
function edit(row) {
  parseData(row.detailVOS);
  isDetail.value = false;
  formOption.value.detail = false;
  drawer.value = true;
  selectProductOption.value.menu = true;
  form.value = {
    ...form.value,
    ...row,
    changeFiles: row.changeFileList
      ? row.changeFileList.map(item => {
        return {
          label: item.originalName,
          value: item.id,
        };
      })
      : [],
  };
  console.log(form.value);
  nextTick(() => {
    setSort();
    setTimeout(() => {
      form.value.totalPrice = row.totalPrice;
      form.value.costTotalPrice = row.costTotalPrice;
    }, 700);
  });
}
// 打印
function printChange(row) {
  parseData(row.detailVOS);
  handlePrint();
  form.value = {
    ...row,
  };
}
function complete(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          label: '状态',
          prop: 'changeStatus',
          type: 'radio',
          span: 24,
          dicData: [
            {
              value: 1,
              label: '完成',
            },
            {
              value: 2,
              label: '取消',
            },
          ],
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectChange/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
const indexText = computed(() => value => { });
// function indexText(value) {
//    const arr = [printData.addList?.length > 0,printData.reduceList?.length > 0,printData.otherList?.length > 0].filter(item => item);
//   const arr1 = ['一','二','三','四']
//   if(value > arr.length){
//     return arr1[arr.length]
//   }else{
//     return arr[value]
//   }
// }

// 导入
let dialogVisible = ref(false);
function upload1() {
  console.log(111);
  dialogVisible.value = true;
}

function download() {
  let a = document.createElement('a'); // 创建a标签
  a.href = '/template/projectChangeTemplate.xlsx'; // 文件路径
  a.download = '产品导入模板.xlsx'; // 文件名称
  a.style.display = 'none'; // 隐藏a标签
  document.body.appendChild(a);
  // 定时器(可选)
  setTimeout(() => {
    a.click(); // 模拟点击(要加)
    document.removeChild(a); //删除元素(要加)
    setTimeout(() => {
      self.URL.revokeObjectURL(a.href); // 用来释放文件路径(可选)
    }, 200);
  }, 66);
}
function handleSuccess(res) {
  console.log(res);
  if (res.code == 200) {
    proxy.$message({
      message: res.msg.split(',').join('<br>'),
      dangerouslyUseHTMLString: true,
      type: 'success',
      duration: 2000,
    });
    let data = res.data;
    data = data.map(item => {
      return {
        productName: item['名称'],
        productSpecification: item['型号'],
        productBrand: item['品牌'],
        description: item['产品描述'],
        unitName: item['单位'],
        number: item['数量'] * 1,
        sealPrice: item['单价'],
        totalPrice: item['金额'],
        remark: item['备注'],
        uuid: randomLenNum(10),
        changeType: 0,
      };
    });
    selectProductList.value.push(...data);
  } else {
    proxy.$message({
      message: '数据有误',
      dangerouslyUseHTMLString: true,
      type: 'warning',
      duration: 2000,
    });
    // 获取文本内容
    var textContent = res.msg.replace(/,/g, '\n');

    // 创建 Blob 对象
    var blob = new Blob([textContent], { type: 'text/plain' });

    // 创建下载链接
    var downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);

    // 设置下载文件的名称
    downloadLink.download = '错误数据.txt';

    // 将下载链接添加到页面
    document.body.appendChild(downloadLink);

    // 模拟点击下载链接
    downloadLink.click();

    // 移除下载链接
    document.body.removeChild(downloadLink);
  }

  notice.value.close();
  proxy.$refs.upload.clearFiles();
  dialogVisible.value = false;
}
let notice = ref(null);
function handleError(res) {
  console.log(res);
  proxy.$message.error(res.data.msg);
  proxy.$refs.upload.clearFiles();
  notice.value.close();
}
function handleSubmit(params) {
  proxy.$nextTick(() => {
    proxy.$refs.upload.submit();
    notice.value = ElNotification({
      title: '导入中',
      position: 'bottom-right',
      duration: 0,
      message: h(progress, {
        // 事件要以onXxx的形式书写
        onFinish: status => {
          if (status.value == 'ok') {
            notice.close(); // 关闭ElNotification
          }
        },
      }),
    });
  });
}
function handleExceed() {
  proxy.$message.warning('只能上传一个文件');
}

// 询价单管理
let inquiryDrawer = ref(false);
let currentInquirySheet = ref('');
let headerRow = ref('');
let sheetNum = ref(1);
let importBrand = ref('');
let inquirySheetList = ref([]);
let fileOptionData = ref([]);
let isEditInquirySheet = ref(false);
let fileForm = ref({});
let categoryTreeData = ref([]);
let importDialogVisible = ref(false);
function handleOpenInquirySheet() {
  inquiryDrawer.value = true;
  isSelectFromSheet.value = false;
  categoryTreeData.value = proxy.$refs.sheet.getTree();
  if (inquirySheetData.value.length > 0) {
    isEditInquirySheet.value = true;
  }
}
function addInquirySheet() {
  proxy.$refs.inquirySheetSelectRef.open();
}
let inquirySheetPageData = ref([]);
let inquirySheetParams = ref({});
let currentSheetData = ref(null);
let sheetPage = ref({
  current: 1,
  size: 10,
});
function getInquirySheetPage() {
  axios
    .get('/api/vt-admin/businessInquiry/page', {
      params: {
        ...sheetPage.value,
        ...inquirySheetParams.value,
      },
    })
    .then(res => {
      // loading.value = false;
      inquirySheetPageData.value.push(...res.data.data.records);

      currentSheetData.value = currentSheetData.value || inquirySheetPageData.value[0];
      handleSheetChange(currentSheetData.value);
    })
    .catch(() => {
      // loading.value = false;
    });
}
async function handleSheetChange(value, done) {
  console.log(value);
  const { businessName, id } = value;
  const res = await axios.get('/api/vt-admin/businessInquiry/detail?id=' + id);
  inquirySheetList.value.push({
    businessName,
    id,
    tableData: res.data.data.detailVOS,
  });
  currentInquirySheet.value = inquirySheetList.value.length - 1;
  if (typeof done == 'function') {
    done();
  }
}
function handleClickCard(item) {
  currentSheetData.value = item;
  handleSheetChange(item);
}
function handleConfirmSheet(data) {
  proxy.$refs.sheet.setData(data);
}
function loadMoreSheet() {
  console.log(1111);
  sheetPage.value.current += 1;
  getInquirySheetPage();
}
const fileOption = computed(() => {
  return {
    submitBtn: false,
    emptyBtn: false,
    size: 'small',
    align: 'center',
    labelWidth: 100,
    column: [
      {
        label: '序号',
        prop: 'serialNumber',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品名称',
        prop: 'productName',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '品牌',
        prop: 'productBrand',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '规格型号',
        prop: 'productSpecification',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品描述',
        prop: 'description',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '单位',
        prop: 'unitName',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '数量',
        prop: 'number',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品单价',
        prop: 'sealPrice',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      // {
      //   label: '金额',
      //   prop: 'totalPrice',
      //   type: 'select',
      //   span: 4,
      //   dicData: fileOptionData.value,
      // },
      {
        label: '备注',
        prop: 'remark',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
    ],
  };
});
let allData = ref([]);
let fileName = '';
function uploadFile(params) {
  var { file } = params;
  fileName = file.name.substring(0, file.name.lastIndexOf('.'));

  // 调用上传接口
  var formData = new FormData();
  formData.append('file', file);
  request({
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    url: '/vt-admin/businessInquiry/analyzeExcel',
    method: 'post',
    params: {
      parseRowNumber: headerRow.value * 1,
      sheetNum: sheetNum.value * 1,
    },

    data: formData,
  }).then(res => {
    if (!res.data.data || res.data.data.length == 0) {
      proxy.$message.error('解析失败');
      return;
    }
    fileOptionData.value = Object.keys(res.data.data[0]).map(item => {
      return {
        label: item,
        value: item,
      };
    });
    allData.value = res.data.data.map(item => {
      return {
        ...item,
        uuid: randomLenNum(10),
        source: 2,
      };
    });
    initData();

    isEditInquirySheet.value = true;
    importDialogVisible.value = false;
  });
}
let inquirySheetData = ref([]);
let inquirySheetOption = ref({
  menuWidth: 100,
  editBtn: false,
  height: 'auto',
  align: 'center',
  addBtn: false,
  delBtn: true,
  size: 'small',
  // header: false,
  showSummary: true,
  // selection: true,
  sumColumnList: [
    {
      name: 'totalPrice',
      type: 'sum',
    },
  ],
  border: true,
  index: false,
  column: {
    serialNumber: {
      label: '序号',
      overHidden: true,
      width: 80,
    },
    productName: {
      label: '产品名称',
      overHidden: true,
      width: 180,
    },
    productBrand: {
      label: '品牌',
      overHidden: true,
      width: 100,
    },
    productSpecification: {
      label: '规格型号',
      overHidden: true,
      span: 24,
      width: 180,
    },
    description: {
      label: '产品描述',
      overHidden: true,
      span: 24,
      type: 'textarea',
    },
    unitName: {
      label: '单位',
      overHidden: true,
      label: '单位',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictValue',
        desc: 'desc',
      },

      dicUrl: '/blade-system/dict/dictionary?code=unit',
    },
    number: {
      label: '数量',
      overHidden: true,
      width: 80,
    },
    sealPrice: {
      label: '单价',
      overHidden: true,
      width: 100,
    },
    totalPrice: {
      label: '金额',
      overHidden: true,
      width: 100,
    },
    remark: {
      label: '备注',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },
    // laborCost: {
    //   label: '人工单价',
    //   overHidden: true,
    //   width: 100,
    // },
    // ybhsdj: {
    //   label: '延保单价',
    //   overHidden: true,
    //   width: 100,
    // },
    // qthsdj: {
    //   label: '其他单价',
    //   overHidden: true,
    //   width: 100,
    // },
    // // totalPrice: {
    // //   label: '金额',
    // //   overHidden: true,
    // //   editDisplay: false,
    // // },
    // costPrice: {
    //   label: '产品成本单价',
    //   width: 120,
    //   overHidden: true,
    // },
    // rgcbdj: {
    //   label: '人工成本单价',
    //   overHidden: true,
    //   width: 120,
    // },
    // ybcbdj: {
    //   label: '延保成本单价',
    //   overHidden: true,
    //   width: 120,
    // },
    // qtcbdj: {
    //   label: '其他成本单价',
    //   overHidden: true,
    //   width: 120,
    // },
    // classifyName: {
    //   label: '分类',
    //   span: 24,
    //   type: 'tree',
    //   parent: false,
    //   dicData: [],
    //   width: 200,
    //   props: {
    //     label: 'label',
    //     value: 'value',
    //     children: 'children',
    //   },
    // },
  },
});
watch(
  () => fileForm.value,
  () => {
    initData();
  },
  {
    deep: true,
    immediate: true,
  }
);
function initData() {
  inquirySheetData.value = allData.value.map(item => {
    let sealPrice = item[fileForm.value.sealPrice]?.replace(',', '');
    sealPrice = item[fileForm.value.sealPrice] === '' || isNaN(sealPrice) ? '' : sealPrice;
    let number =
      item[fileForm.value.number] === '' || isNaN(item[fileForm.value.number])
        ? ''
        : parseFloat(item[fileForm.value.number]);
    let serialNumber =
      item[fileForm.value.serialNumber] === '' || isNaN(item[fileForm.value.serialNumber])
        ? ''
        : parseFloat(item[fileForm.value.serialNumber]);

    return {
      productName: item[fileForm.value.productName],
      serialNumber: serialNumber,
      productBrand: item[fileForm.value.productBrand] || importBrand.value,
      productSpecification: item[fileForm.value.productSpecification],
      unitName: item[fileForm.value.unitName],
      description: item[fileForm.value.description],
      remark: item[fileForm.value.remark],

      sealPrice: sealPrice,
      uuid: item.uuid,
      changeType: number < 0 ? 2 : 0,
      number: Math.abs(number),
      totalPrice: number * sealPrice,
    };
  });
}
function handleRowDBLClick(row, event) {
  proxy.$refs.fileCrudRef.rowEdit(row, row.$index);
}
function handleRowUpdate(row, index, done, loading) {
  inquirySheetData.value[index] = row;
  done();
}
function handleRowDel(form, index, done) {
  console.log(form, index, done);

  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      inquirySheetData.value = inquirySheetData.value.filter((item, i) => index != i);
      done();
      proxy.$message({
        type: 'success',
        message: '删除成功!',
      });
    })
    .catch(() => { });
}
let selectValue = ref('productName');
function clearEmptyProduct() {
  ElMessageBox({
    title: '提示',
    type: 'warning',
    message: () => [
      h('div', null, [
        h('span', null, '此操作将删除所有'),
        h(
          ElSelect,
          {
            modelValue: selectValue.value,
            placeholder: '请选择',
            clearable: true,
            style: 'width: 120px',
            'onUpdate:modelValue': value => {
              console.log(value);
              selectValue.value = value;
            },
          },
          [
            h(ElOption, {
              label: '产品名称',
              value: 'productName',
            }),
            h(ElOption, {
              label: '品牌',
              value: 'productBrand',
            }),
            h(ElOption, {
              label: '规格型号',
              value: 'productSpecification',
            }),
            h(ElOption, {
              label: '单位',
              value: 'unitName',
            }),
            h(ElOption, {
              label: '数量',
              value: 'number',
            }),
          ]
        ),
        h('span', null, '为空的数据, 是否继续,请确保关联的表头正确?'),
      ]),
    ],
  }).then(r => {
    inquirySheetData.value = inquirySheetData.value.filter(item => {
      return !!item[selectValue.value];
    });
  });
  // proxy
  //   .$confirm('此操作将删除所有空数据, 是否继续,请确保产品名称关联的表头正确?', '提示', {
  //     confirmButtonText: '确定',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //   })
  //   .then(() => {
  //     inquirySheetData.value = inquirySheetData.value.filter(item => {
  //       return !!item.customProductName;
  //     });
  //     proxy.$message({
  //       type: 'success',
  //       message: '删除成功!',
  //     });
  //   })
  //   .catch(() => {});
}

function handleCommand(key) {
  let dicData = [];
  if (key == 'classifyName') {
    dicData = proxy.$refs.sheet.getTree();
  }
  const option = {
    productBrand: {
      label: '品牌',
      prop: 'productBrand',

      span: 24,
    },
    customProductSpecification: {
      label: '规格型号',
      prop: 'customProductSpecification',

      span: 24,
    },
    customUnit: {
      label: '单位',
      prop: 'customUnit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      span: 24,
      type: 'select',
    },
    number: {
      label: '数量',
      prop: 'number',
      span: 24,
    },
    costPrice: {
      label: '单价',
      prop: 'costPrice',
      span: 24,
    },
    classifyName: {
      label: '分类',
      prop: 'classifyName',
      span: 24,
      type: 'tree',
      parent: false,
      dicData: dicData,
      props: {
        label: 'label',
        value: 'value',
        children: 'children',
      },
    },
  };
  if (selectList.value.length > 0) {
    if (key == 'deleteProduct') {
      const uuids = selectList.value.map(item => item.uuid);
      inquirySheetData.value = inquirySheetData.value.filter(
        (item, index) => !uuids.includes(item.uuid)
      );
    } else {
      proxy.$refs.dialogForm.show({
        title: '编辑产品',

        option: {
          column: [option[key]],
        },
        callback(res) {
          selectList.value.forEach(item => {
            item[key] = res.data[key];
          });
          res.close();
          proxy.$refs.fileCrudRef.toggleSelection();
        },
      });
    }
  } else {
    proxy.$message({
      type: 'warning',
      message: '请选择需要编辑的数据!',
    });
  }
}
function resetImport() {
  clearData();
  isEditInquirySheet.value = false;
}
function clearData() {
  inquirySheetData.value = [];
  allData.value = [];
  importBrand.value = '';
  headerRow.value = '';
  fileOptionData.value = [];
  proxy.$refs.fileRef.resetForm();
  isSave.value = false;
}
let isSave = ref(false);
function handleImportInquirySheet() {
  if (inquirySheetData.value.length > 0) {
    isEditInquirySheet.value = true;
  } else {
    importDialogVisible.value = true;
  }
}
function handleAddProduct() {
  selectProductList.value.push(...inquirySheetData.value);
  inquirySheetData.value = [];
  isEditInquirySheet.value = false;
}

// 关联产品
let currentRow = ref({});
function handleLink(row) {
  currentRow.value = row;
  selectType.value = 1;
  innerDrawer.value = true;
}

onMounted(() => { });
function setSort() {
  const el = proxy.$refs.addCrudRef.$el.querySelector('.el-table__body tbody');

  new Sortable(el, {
    animation: 180,
    delay: 0,
    filter: '.disable',
    put: true,
    preventOnFilter: false,
    onEnd: evt => {
      console.log(evt);

      const targetRow = selectProductList.value.splice(evt.oldIndex, 1);
      selectProductList.value.splice(evt.newIndex, 0, targetRow[0]);
    },
  });
}
function setTotalPrice() {
  setTimeout(() => {
    const totalObj = totalPrice();
    form.value.totalPrice = (totalObj.addTotal - totalObj.reduceTotal).toFixed(2);
    if (form.value.isHasTax == 0) {
      const taxPrice = form.value.totalPrice * (form.value.taxRate / 100);

      form.value.totalPrice = (form.value.totalPrice * 1 + taxPrice).toFixed(2);
    }
  }, 100);
}
function setCostTotalPrice() {
  setTimeout(() => {
    form.value.costTotalPrice = selectProductList.value.filter(item => item.changeType == 0).reduce((pre, cur) => {
      return pre + cur.number * cur.costPrice;
    }, 0);
  }, 100);
}
function returnBack(row) {
  proxy
    .$confirm('是否确认撤回?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/projectChange/returnBack', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          emit('success');
          onLoad();
        });
    })
    .catch(() => { });
}

function summaryMethod({ columns, data }) {
  const sums = [];
  columns.forEach((item, index) => {
    if (index == 8) {
      sums[index] = '合计:';
      return;
    }
    if (index == 9) {
      const value = data.reduce((prev, curr) => {
        if (curr.changeType == 2) {
          prev += curr.number * curr.sealPrice * -1;
        } else {
          prev += curr.number * curr.sealPrice;
        }

        return prev;
      }, 0);
      let taxPrice = 0;
      if (form.value.isHasTax == 0) {
        taxPrice = (value * form.value.taxRate) / 100;
        sums[index] = (
          <div>
            税金：{taxPrice.toFixed(2)} <br />
            {(value + taxPrice).toFixed(2)}
          </div>
        );
      } else {
        sums[index] = <div>{(value + taxPrice).toFixed(2)}</div>;
      }
    } else if (index == 11) {
      const value = data.reduce((prev, curr) => {
        if (curr.changeType == 2) {
          prev = prev ;
        } else {
          prev += curr.number * curr.costPrice;
        }

        return prev;
      }, 0);
      sums[index] = <div>{(value).toFixed(2)}</div>;
    } else {
      sums[index] = '';
    }
  });
  return sums;
}
function addFile(row) {
  const fileList = row.attachList ? row.attachList.map(item => {
    return {
      label: item.originalName,
      value: item.id,
    };
  }) : []
  proxy.$refs.dialogForm.show({
    title: '上传完成文件',
    option: {
      column: [
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          value: fileList,
          dataType: 'object',
          loadText: '签证签证上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        }
      ]

    },
    callback(res) {
      const data = {
        id: row.id,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectChange/uploadFiles', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
</script>

<style lang="scss" scoped>
table td {
  text-align: center;
}
</style>
<style media="print">
@page {
  size: auto;
  margin: 3mm;
}

@media print {
  html {
    height: auto !important;
  }
}
</style>
