<template>
  <div style="height: 100%">
    <!-- <div style="height: 100px; margin-bottom: 20px">
        <el-card shadow="never">
          <avue-form :option="baseOption" ref="addForm" v-model="form">
            <template #baseInfo-header="column">
              <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
              <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
            </template>
          </avue-form>
        </el-card>
      </div> -->
    <div style="height: 100%" v-loading="loading" element-loading-text="刷新数据中">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="5">
          <el-card shadow="never" style="height: 100%">
            <avue-tree
              ref="tree"
              :option="treeOption"
              :data="treeData"
              @node-click="handleNodeClick"
            ></avue-tree>
          </el-card>
        </el-col>
        <el-col :span="19">
          <el-card shadow="never" style="height: calc(100vh - 160px); overflow-y: auto">
            <div :ref="`box_${item.moduleId}`" v-for="(item, index) in treeData" :key="item.id">
              <h3>{{ item.moduleName }}</h3>
              <el-table
                class="avue-crud"
                v-if="item.children && item.children.length > 0"
                :span-method="arraySpanMethod"
                :data="item.children"
                :row-class-name="rowClassName"
                border
                :tree-props="{
                  children: 'detailVOList',
                }"
                :ref="`table_${item.moduleId}`"
                @select="
                  (data1, data2) => {
                    selectTr1(data1, data2, `table_${item.moduleId}`);
                  }
                "
                default-expand-all
                @select-all="selectAll(item.children, `table_${item.moduleId}`)"
                :expand-row-keys="item.children && item.children.map(item => item.id)"
                row-key="id"
                align="center"
              >
                <el-table-column type="selection" :selectable="selectableTree" width="50" />
                <el-table-column
                  label="设备名称"
                  #default="{ row }"
                  show-overflow-tooltip
                  prop="customProductName"
                  width="130"
                >
                  <span v-if="!row.splitVOS">
                    <span :class="{ active: !!row.detailVOList }">{{
                      row.value || row.customProductName
                    }}</span>
                  </span>
                  <span v-else>
                    <el-tag
                      size="small"
                      type="danger"
                      v-if="row.splitVOS && row.splitVOS.length > 0"
                      effect="plain"
                      >拆</el-tag
                    >
                    <span>{{ row.customProductName }}</span>
                  </span>
                </el-table-column>
                <el-table-column
                  label="规格型号"
                  show-overflow-tooltip
                  #default="{ row }"
                  prop="customProductSpecification"
                  width="130"
                >
                  {{
                    row.customProductSpecification || row.product?.productSpecification
                  }}</el-table-column
                >

                <el-table-column
                  label="产品描述"
                  show-overflow-tooltip
                  width="200"
                  #default="{ row }"
                  prop="customProductDescription"
                >
                  {{ row.customProductDescription || row.product?.description }}
                </el-table-column>
                <el-table-column
                  label="品牌"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="productBrand"
                >
                  {{ row.productBrand || row.product?.productBrand }}
                </el-table-column>
                <el-table-column
                  label="单位"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="product.unitName"
                >
                  {{ row.customUnit || row.product?.unitName }}
                </el-table-column>
                 <el-table-column label="清单数量" align="center" prop="deepenNumber">
                </el-table-column>
                <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                           
                  <template #default="{ row }">
                    {{ row.purchaseNums && parseFloat(row.purchaseNums) }}   </template
                  >         
                </el-table-column>
                         
                <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                             <template #default="{ row }">
                    {{ row.noPurchaseNums && parseFloat(row.noPurchaseNums) }} </template
                  >         
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="深化备注"
                  #default="{ row }"
                  width="300"
                  prop="deepenRemark"
                >
                </el-table-column>
              </el-table>
              <div v-else>
                <el-table
                  class="avue-crud"
                  :data="item.detailVOList"
                  :row-class-name="tableRowClassName"
                  border
                  :ref="`table_${item.moduleId}`"
                  row-key="id"
                  align="center"
                  :tree-props="{
                    children: 'detailVOList',
                  }"
                >
                  <el-table-column type="selection" width="50" />

                  <el-table-column
                    label="设备名称"
                    #default="{ row }"
                    show-overflow-tooltip
                    prop="customProductName"
                    width="130"
                  >
                    <el-tag
                      size="small"
                      type="danger"
                      v-if="row.detailVOList && row.detailVOList.length > 0"
                      effect="plain"
                      >拆</el-tag
                    >
                    <span>{{ row.customProductName }}</span>
                  </el-table-column>
                  <el-table-column
                    label="规格型号"
                    show-overflow-tooltip
                    #default="{ row }"
                    prop="customProductSpecification"
                    width="130"
                  >
                    {{
                      row.customProductSpecification || row.product?.productSpecification
                    }}</el-table-column
                  >

                  <el-table-column
                    label="产品描述"
                    show-overflow-tooltip
                    width="200"
                    #default="{ row }"
                    prop="customProductDescription"
                  >
                    {{ row.customProductDescription || row.product?.description }}
                  </el-table-column>
                  <el-table-column
                    label="品牌"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="productBrand"
                  >
                    {{ row.productBrand || row.product?.productBrand }}
                  </el-table-column>
                  <el-table-column
                    label="单位"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="product.unitName"
                  >
                    {{ row.customUnit || row.product?.unitName }}
                  </el-table-column>
                   <el-table-column label="数量" align="center" prop="deepenNumber">
                  </el-table-column>
                           
                  <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                             
                    <template #default="{ row }">
                      {{ row.purchaseNums && parseFloat(row.purchaseNums) }}   </template
                    >         
                  </el-table-column>
                           
                  <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                               <template #default="{ row }">
                      {{ row.noPurchaseNums && parseFloat(row.noPurchaseNums) }} </template
                    >         
                  </el-table-column>

                  <el-table-column
                    show-overflow-tooltip
                    label="深化备注"
                    #default="{ row }"
                    :width="300"
                    prop="deepenRemark"
                  >
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <wfProductSelect
        @onConfirm="handleProductSelectConfirm"
        ref="productSelectRef"
      ></wfProductSelect>
    </div>
  </div>
</template>

<script setup>
import { watchEffect, getCurrentInstance, onMounted } from 'vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { useRoute } from 'vue-router';
import { randomLenNum } from '@/utils/util';
import axios from 'axios';

const route = useRoute();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  detail: {
    type: Boolean,
    default: false,
  },
  isView: {
    type: Boolean,
    default: false,
  },
  deepenStatus: {
    type: Number,
    default: 0,
  },
  height: Number,
  disableList: Array,
  type: Number, // 0 请购 1  变更
  projectId: String,
});
const { proxy } = getCurrentInstance();
let treeData = ref([]);
let allData = ref([]);
let tableData = ref([]);
let form = ref({});
let loading = ref(false);

onMounted(() => {
  getDetail();
});
function getDetail() {
  allData.value = [];
  tableData.value = [];
  axios.post('/api/vt-admin/project/projectProducts?id=' + route.query.id, {}).then(res => {
    form.value = res.data.data;
    form.value.businessOpportunityId = props.id;
    const data = res.data.data.moduleVOList.map(item => {
      return {
        ...item,
        moduleId: item.id,
      };
    });
    formatData(data);
    form.value.moduleVOList = null;
  });
}
let treeOption = ref({
  addBtn: false,
  defaultExpandAll: true,
  props: {
    value: 'value',
    label: 'label',
  },
});

function formatData(data) {
  treeData.value = data.map(item => {
    item.label = item.moduleName;
    const isHasClassify = item.detailVOList.every(item => item.classify);
    if (isHasClassify) {
      item.children = item.detailVOList
        .map(i => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: item.moduleId,
            id: i.moduleId + i.classify,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push({
              ...cur,
              // deepenNumber: cur.deepenNumber ? cur.deepenNumber * 1 : cur.number * 1,
              // leaderDeepenNumber: cur.leaderDeepenNumber
              //   ? cur.leaderDeepenNumber * 1
              //   : cur.deepenNumber * 1,
              detailVOList: item.detailVOList
                .filter(i => i.classify == cur.value)
                .map(item => {
                  return {
                    ...item,
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                    isLeaf: item.splitVOS && item.splitVOS.length > 0 ? false : true,
                    detailVOList:
                      item.splitVOS && item.splitVOS.length > 0
                        ? item.splitVOS.map(item1 => {
                            item1.number = 0;
                            return {
                              ...item1,
                              uuid: randomLenNum(10),
                              number: parseFloat(item1.number),
                              deepenNumber: item1.deepenNumber
                                ? item1.deepenNumber * 1
                                : item1.number * 1,
                              leaderDeepenNumber:
                                form.value.deepenType == 0
                                  ? null
                                  : item1.leaderDeepenNumber || item1.leaderDeepenNumber == 0
                                  ? item1.leaderDeepenNumber * 1
                                  : item1.deepenNumber * 1,
                              parentId: item.id,
                              isLeaf: true,
                              splitId: item1.id,
                            };
                          })
                        : [],
                  };
                }),
            });
          }
          return acc;
        }, []);
    } else {
      item.detailVOList = item.detailVOList.map(item => {
        const obj = {
          ...item,
          deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
          leaderDeepenNumber:
            form.value.deepenType == 0
              ? null
              : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
              ? item.leaderDeepenNumber * 1
              : item.deepenNumber * 1,

          detailVOList:
            item.splitVOS && item.splitVOS.length > 0
              ? item.splitVOS.map(item1 => {
                  item1.number = 0;
                  return {
                    ...item1,
                    uuid: randomLenNum(10),
                    number: parseFloat(item1.number),
                    deepenNumber: item1.deepenNumber ? item1.deepenNumber * 1 : item1.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item1.leaderDeepenNumber || item1.leaderDeepenNumber == 0
                        ? item1.leaderDeepenNumber * 1
                        : item1.deepenNumber * 1,
                    parentId: item.id,
                    isLeaf: true,
                    splitId: item1.id,
                  };
                })
              : [],
          isLeaf: true,
        };
        return obj;
      });
    }
    item.isHasClassify = isHasClassify;

    return item;
  });

  if (props.type == 0) {
    getChangeList();
  }
  allData.value = data;
}
let changeList = ref();
function getChangeList() {
  const uuid = randomLenNum(10);
  axios.post('/api/vt-admin/projectChange/completeListById?id=' + route.query.id).then(res => {
    changeList.value = res.data.data
      .filter(item => item.changeStatus == 1)
      .map(item => {
        return {
          ...item,
          moduleId: item.id,
          value: item.title,
          label: item.title,
          parentId: uuid,
          id: item.id + item.title,
          detailVOList: item.detailVOS
            .filter(item => item.changeType == 0)
            .map(item1 => {
              return {
                ...item1,
                isLeaf: true,
                deepenNumber: parseFloat(item1.number),

                parentId: item.id,
                customProductName: item1.productName,
                customProductDescription: item1.description,
                customProductSpecification: item1.productSpecification,
                customUnit: item1.unitName,
                customProductName: item1.productName,
                uuid: randomLenNum(10),
              };
            }),
        };
      });

    if (changeList.value.length > 0) {
      treeData.value.push({
        label: '变更项',
        moduleName: '变更项',
        id: uuid,
        moduleId: uuid,
        children: [...changeList.value.filter(item => item.detailVOList.length > 0)],
      });
    }
  });
}
function selectableTree(row) {
  if (props.type == 0) {
    return (
      !props.disableList.some(item => item.detailId == row.id) &&
      (row.noPurchaseNums > 0 || row.parentId)
    );
  } else {
    return !props.disableList.some(item => item.detailId == row.id);
  }
}
// function handleNodeClick(node) {
//   if (!node) {
//     allData.value.forEach(item => {
//       item.detailVOList.forEach(element => {
//         element.deepenNumber =
//           element.deepenNumber || element.deepenNumber == 0
//             ? element.deepenNumber * 1
//             : element.number * 1;
//         element.leaderDeepenNumber =
//           element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//             ? element.leaderDeepenNumber * 1
//             : element.deepenNumber * 1;
//         tableData.value.push(element);
//       });
//     });
//   }
//   if (!node.parentId) {
//     ;
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     node.detailVOList.forEach(element => {
//       element.deepenNumber =
//         element.deepenNumber || element.deepenNumber == 0
//           ? element.deepenNumber * 1
//           : element.number * 1;
//       element.leaderDeepenNumber =
//         element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//           ? element.leaderDeepenNumber * 1
//           : element.deepenNumber * 1;
//       tableData.value.push(element);
//     });
//   } else {
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     if (node.children && node.children.length > 0) return;
//     allData.value
//       .find(item => item.moduleId === node.parentId)
//       .detailVOList.forEach(element => {
//         if (element.classify === node.value) {
//           element.deepenNumber =
//             element.deepenNumber || element.deepenNumber == 0
//               ? element.deepenNumber * 1
//               : element.number * 1;
//           element.leaderDeepenNumber =
//             element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//               ? element.leaderDeepenNumber * 1
//               : element.deepenNumber * 1;
//           tableData.value.push(element);
//         }
//       });
//   }
// }

function handleNodeClick(node) {
  const id = 'box_' + node.moduleId;

  proxy.$nextTick(() => {
    if (node.parentId) {
      const ele = document.querySelector(`.row_${node.parentId}${node.value}`);

      ele.scrollIntoView({ behavior: 'smooth' });
    } else {
      proxy.$refs[id][0].scrollIntoView({ behavior: 'smooth' });
    }
  });
}
function rowClassName(val) {
  return 'row_' + val.row.parentId + val.row.value;
}
let currModuleId = ref(null);
let currClassfiy = ref(null);
function handleAddProduct(row) {
  currModuleId.value = row.parentId;
  currClassfiy.value = row.value;
  proxy.$refs.productSelectRef.visible = true;
}
function handleProductSelectConfirm(id) {
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      const {
        productName,
        productSpecification,
        description,
        productBrand,
        unitName,
        id,
        number = 0,
      } = res.data.data;
      const data = {
        customProductName: productName,
        customProductSpecification: productSpecification,
        customProductDescription: description,
        product: {
          productBrand: productBrand,
          unitName: unitName,
        },
        uuid: id,
        productId: id,
        number: number,
        deepenNumber: '',
        leaderDeepenNumber: 1,
        classify: currClassfiy.value,
      };
      // tableData.value.push(data);
      // proxy.$nextTick(() => {
      //   proxy.$refs.table.setScrollTop(10000);
      // });

      treeData.value
        .find(item => item.moduleId === currModuleId.value)
        .children.find(item => item.value == currClassfiy.value)
        .detailVOList.push(data);
    });
}
function getData() {
  const data = {
    ...form.value,
    moduleHistoryDTOList: treeData.value.map(item => {
      return {
        ...item,
        detailHistoryDTOS: item.isHasClassify
          ? item.children.reduce((pre, cur) => {
              return [...pre, ...cur.detailVOList];
            }, [])
          : item.detailVOList,
        detailVOList: null,
      };
    }),
  };
  console.log(data, 'getData');
  return data;
}
function setId(id) {
  form.value.id = id;
}

function tableRowClassName(row) {
  return row.number == 0 ? 'success-row' : '';
}
defineExpose({ getData, setId, getDetail, getSelectList, clearSelect });
// 删除产品
function deleteProduct(rowp, index) {
  rowp.detailVOList.splice(index, 1);
  // const index = treeData.value
  //   .find(i => i.moduleId === item.moduleId).children(item => item.value === row.classify)
  //   .detailVOList.findIndex(item => item.uuid === row.uuid);
  //   treeData.value.find(i => i.moduleId === item.moduleId).children.find(item => item.value == row.classify).detailVOList.splice(index, 1);
}
// function arraySpanMethod({  row,
//   column,
//   rowIndex,
//   columnIndex,}) {
//   if(column == 1){
//     return
//   }
// }
let isAllSelectObj = ref({});
// 全选/取消选操作
function selectAll(data, ref) {
  isAllSelectObj.value[ref] = !isAllSelectObj.value[ref];
  data.forEach(item => {
    proxy.$refs[ref][0].toggleRowSelection(item, isAllSelectObj.value[ref]);
    if (item.detailVOList && item.detailVOList.length > 0) {
      item.detailVOList.forEach(item => {
        proxy.$refs[ref][0].toggleRowSelection(item, isAllSelectObj.value[ref]);
        if (item.detailVOList && item.detailVOList.length > 0) {
          item.detailVOList.forEach(item => {
            proxy.$refs[ref][0].toggleRowSelection(item, isAllSelectObj.value[ref]);
          });
        }
      });
    }
  });
}
//选择某行
function selectTr1(data1, data2, ref) {
  if (data1.findIndex(item => item.id == data2.id) > -1) {
    // proxy.$refs[ref][0].toggleRowSelection(data2.detailVOList[0]);
    if (data2.detailVOList && data2.detailVOList.length > 0) {
      data2.detailVOList.forEach(item => {
        proxy.$refs[ref][0].toggleRowSelection(item, true);
        if (item.detailVOList && item.detailVOList.length > 0) {
          item.detailVOList.forEach(item => {
            proxy.$refs[ref][0].toggleRowSelection(item, true);
          });
        } else {
          if (!item.sealPrice || item.sealPrice == 0) {
            proxy
              .$confirm(`${item.customProductName}销售单价为0，确认要请购吗`, '提示', {
                type: 'warning',
              })
              .then(
                () => {},
                () => {
                  proxy.$refs[ref][0].toggleRowSelection(item, false);
                }
              );
          }
        }
      });
    } else {
      if (props.type == 0) {
        if (!data2.sealPrice || data2.sealPrice == 0) {
          proxy
            .$confirm('该产品销售单价为0，确认要请购吗', '提示', {
              type: 'warning',
            })
            .then(
              () => {},
              () => {
                proxy.$refs[ref][0].toggleRowSelection(data2, false);
              }
            );
        }
      }
    }
  } else {
    data2.detailVOList &&
      data2.detailVOList.forEach(item => {
        proxy.$refs[ref][0].toggleRowSelection(item, false);
        if (item.detailVOList && item.detailVOList.length > 0) {
          item.detailVOList.forEach(item => {
            proxy.$refs[ref][0].toggleRowSelection(item, false);
          });
        }
      });
  }
}
//递归子级
function toggleSelect(data, flag, type, ref) {
  if (type === 'all') {
    if (data.length > 0) {
      data.forEach(item => {
        toggleSelection(item, flag, ref);
        if (item.detailVOList && item.detailVOList.length > 0) {
          toggleSelect(item.detailVOList, flag, type, ref);
        }
      });
    }
  } else {
    if (data[0].detailVOList && data[0].detailVOList.length > 0) {
      data[0].detailVOList.forEach(item => {
        item.isChecked = !item.isChecked;
        proxy.$refs[ref][0].toggleRowSelection(item, flag);
      });
    }
  }
}
//改变选中
function toggleSelection(row, flag, ref) {
  row.isChecked = flag;
  proxy.$nextTick(() => {
    proxy.$refs[ref][0].toggleRowSelection(row, flag);
  });
}
function getSelectList() {
  let res = [];
  treeData.value.forEach(item => {
    const data = proxy.$refs[`table_${item.moduleId}`][0].getSelectionRows();
    res.push(
      ...data.filter(item => {
        return item.isLeaf == true;
      })
    );
  });
  return res;
}
function clearSelect() {
  treeData.value.forEach(item => {
    proxy.$refs[`table_${item.moduleId}`][0].clearSelection();
    //     data.forEach(item => {
    //     proxy.$refs[ref][0].toggleRowSelection(item, isAllSelectObj.value[ref]);
    //     if (item.detailVOList && item.detailVOList.length > 0) {
    //       item.detailVOList.forEach(item => {
    //         proxy.$refs[ref][0].toggleRowSelection(item, isAllSelectObj.value[ref]);
    //       });
    //     }
    //   });
  });
}
</script>

<style lang="scss" scoped>
:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 30px !important;
}
.active {
  font-size: 18px;
  font-weight: bolder;
  color: #000;
}
:deep(.el-table__placeholder) {
  width: 0;
}
</style>
