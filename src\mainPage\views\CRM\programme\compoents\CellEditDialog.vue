<template>
  <el-dialog
    v-model="visible"
    :title="dialogTitle"
    width="500px"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
    append-to-body
    draggable
  >
    <el-form :model="formData" @submit.native.prevent label-position="top" label-width="0" size="default">
      <!-- 产品名称 -->
      <el-form-item 
        v-if="fieldName === 'customProductName'" 
        label="产品名称"
        :rules="!props.readOnly ? [] : [{ required: true, message: '产品名称不能为空', trigger: 'blur' }]"
      >
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          placeholder="请输入产品名称"
          :maxlength="200"
          show-word-limit
          clearable
          ref="inputRef"
        />
       
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span> 
      </el-form-item>

      <!-- 品牌 -->
      <el-form-item v-else-if="fieldName === 'productBrand'" label="品牌">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          placeholder="请输入品牌"
          :maxlength="100"
          show-word-limit
          clearable
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 规格型号 -->
      <el-form-item v-else-if="fieldName === 'customProductSpecification'" label="规格型号">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          placeholder="请输入规格型号"
          :maxlength="200"
          show-word-limit
          clearable
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 产品描述 -->
      <el-form-item v-else-if="fieldName === 'customProductDescription'" label="产品描述">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          type="textarea"
          :rows="8"
          placeholder="请输入产品描述"
          :maxlength="5000"
          show-word-limit
          ref="inputRef"
        />
        <div v-else class="readonly-textarea">{{ formData.value || '-' }}</div>
      </el-form-item>

      <!-- 单位 -->
      <el-form-item v-else-if="fieldName === 'customUnit'" label="单位">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          placeholder="请输入单位"
          :maxlength="20"
          show-word-limit
          clearable
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 数量 -->
      <el-form-item 
        v-else-if="fieldName === 'number'" 
        label="数量"
        :rules="props.readOnly ? [] : [{ required: true, message: '数量不能为空', trigger: 'blur' }]"
      >
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="1"
          placeholder="请输入数量"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 设备单价 -->
      <el-form-item v-else-if="fieldName === 'sealPrice'" label="设备单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入设备单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 人工单价 -->
      <el-form-item v-else-if="fieldName === 'laborCost'" label="人工单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入人工单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 其他单价 -->
      <el-form-item v-else-if="fieldName === 'qthsdj'" label="其他单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入其他单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 延保单价 -->
      <el-form-item v-else-if="fieldName === 'ybhsdj'" label="延保单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入延保单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 设备成本单价 -->
      <el-form-item v-else-if="fieldName === 'costPrice'" label="设备成本单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入设备成本单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 人工成本单价 -->
      <el-form-item v-else-if="fieldName === 'rgcbdj'" label="人工成本单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入人工成本单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 其他成本单价 -->
      <el-form-item v-else-if="fieldName === 'qtcbdj'" label="其他成本单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入其他成本单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 延保成本单价 -->
      <el-form-item v-else-if="fieldName === 'ybcbdj'" label="延保成本单价">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入延保成本单价"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 专项成本 -->
      <el-form-item v-else-if="fieldName === 'specialCostPrice'" label="专项成本">
        <el-input-number
          v-if="!props.readOnly"
          v-model="formData.value"
          :min="0"
          :precision="2"
          :step="0.01"
          placeholder="请输入专项成本"
          style="width: 100%"
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>

      <!-- 备注 -->
      <el-form-item v-else-if="fieldName === 'remark'" label="备注">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          type="textarea"
         :rows="8"
          placeholder="请输入备注"
          :maxlength="500"
          show-word-limit
          ref="inputRef"
        />
        <div v-else class="readonly-textarea">{{ formData.value || '-' }}</div>
      </el-form-item>

      <!-- 通用字段 -->
      <el-form-item v-else :label="fieldLabel">
        <el-input
          v-if="!props.readOnly"
          v-model="formData.value"
          placeholder="请输入内容"
          clearable
          ref="inputRef"
        />
        <span v-else class="readonly-text">{{ formData.value || '-' }}</span>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ readOnly ? '关闭' : '取消' }}</el-button>
        <el-button v-if="!props.readOnly" type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  fieldName: {
    type: String,
    default: ''
  },
  fieldValue: {
    type: [String, Number],
    default: ''
  },
  product: {
    type: Object,
    default: () => ({})
  },
  readOnly: {
    type: Boolean,
    default: false
  },
})

const emit = defineEmits(['update:modelValue', 'confirm'])

const visible = ref(false)
const formData = ref({ value: '' })
const inputRef = ref(null)

// 字段标签映射
const fieldLabels = {
  customProductName: '产品名称',
  productBrand: '品牌',
  customProductSpecification: '规格型号',
  customProductDescription: '产品描述',
  customUnit: '单位',
  number: '数量',
  sealPrice: '设备单价',
  laborCost: '人工单价',
  qthsdj: '其他单价',
  ybhsdj: '延保单价',
  costPrice: '设备成本单价',
  rgcbdj: '人工成本单价',
  qtcbdj: '其他成本单价',
  ybcbdj: '延保成本单价',
  specialCostPrice: '专项成本',
  remark: '备注'
}

const fieldLabel = computed(() => {
  return fieldLabels[props.fieldName] || props.fieldName
})

const dialogTitle = computed(() => {
  return `${props.readOnly ? '查看' : '编辑'}${fieldLabel.value}`
})

// 监听 modelValue 变化
watch(() => props.modelValue, (newVal) => {
  visible.value = newVal
  if (newVal) {
    // 初始化表单数据
    formData.value.value = props.fieldValue || ''
    // 聚焦输入框
    nextTick(() => {
      if (inputRef.value) {
        if (inputRef.value.focus) {
          inputRef.value.focus()
        } else if (inputRef.value.$el && inputRef.value.$el.focus) {
          inputRef.value.$el.focus()
        }
      }
    })
  }
})

// 监听 visible 变化，同步到父组件
watch(visible, (newVal) => {
  emit('update:modelValue', newVal)
})

const handleClose = () => {
  visible.value = false
}

const handleConfirm = () => {
  emit('confirm', {
    field: props.fieldName,
    value: formData.value.value,
    product: props.product
  })
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  text-align: right;
}

:deep(.el-dialog__header) {
  background-color: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 15px 20px;
}

:deep(.el-dialog__title) {
  font-weight: 600;
  color: #303133;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.readonly-text {
  display: inline-block;
  min-height: 32px;
  line-height: 32px;
  padding: 0 11px;
  color: #606266;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  word-break: break-all;
}

.readonly-textarea {
  min-height: 200px;
  max-height: 400px;
  overflow-y: auto;
  padding: 8px 11px;
  color: #606266;
  background-color: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  width: 100%;
  box-sizing: border-box;
  white-space: pre-wrap;
  word-break: break-all;
  line-height: 1.5;
}
</style>
