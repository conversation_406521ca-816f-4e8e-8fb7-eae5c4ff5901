<template>
    <!-- <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex === item.value ? 'active' : ''"
        @click="handleTabClick(item,index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div> -->
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @current-change="onLoad"
        @refresh-change="onLoad"
        @size-change="onLoad"
        :cell-style="cellStyle"
        v-model="form"
      >
      <template #menu-left>
        <div style="display: flex">
          <!-- <el-button type="primary" @click="addContract" icon="plus">新增合同</el-button> -->
          <div style="display: flex; align-items: center;gap: 20px;">
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
            <span style="font-weight: bolder">未收款总额：</span>
            <el-text type="primary" size="large">￥{{ (noReceivedPrice * 1).toLocaleString() }}</el-text>
          </div>
       </div>
        
      </template>
        <template #contractCode="{ row }">
           <el-link type="primary" @click="toDetail(row)">
            <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> 
            <el-popover ref="popover1" placement="top-start" :disabled="!row.remark"  width="200" trigger="hover" :content="row.remark">
              <template  #reference>
                 <el-tag type="danger" v-if="row.contractStatus == 2" effect="dark" size="small"
            >中止</el-tag
          >
              </template>
          </el-popover>
          <el-tag
            v-if="row.isPreOrder == 1"
            type="danger"
            size="small"
            effect="plain"
            title="预订单"
            >预</el-tag
          >
          {{ row.contractCode }}
          <i
            title="该订单正在申请撤销"
            v-if="row.isApplyCancel == 1"
            class="element-icons el-icon-chehui1"
            style="color: var(--el-color-warning); font-size: 20px"
          ></i>
        </el-link>
        </template>
        <template #menu="{ row }">
          <el-button text type="primary" @click="toDetail(row)" icon="view">详情</el-button>
          <!-- <el-button text type="primary" @click="distribution(row)" icon="Aim" v-if="row.isDistribution == 0">分配</el-button> -->
        </template>
        <template #contractTotalPrice-search="{ row }">
          <div style="display: flex">
            <el-input placeholder="最小合同额" v-model.number="params.contractMinPrice"></el-input>-
            <el-input placeholder="最大合同额" v-model.number="params.contractMaxPrice"></el-input>
          </div>
        </template>
        <template #planCollectionPrice="{ row }">
        <div v-if="row.noReceivedPrice == 0" style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
        <div v-else>
          <span
            :style="{
              color:
                row.planCollectionDays > 7
                  ? 'var(--el-color-success)'
                  : row.planCollectionDays <= 7 && row.planCollectionDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
            }"
            class="planCollectionDays"
            >{{ row.planCollectionDays }}</span
          ><span>{{ row.planCollectionDays || row.planCollectionDays == 0 ? '天' : ''}}</span>
        </div>
      </template>
       <!-- <template #customerName="{row}">
        <el-link type="primary" @click="toCustomerDetail(row)">{{ row.customerName }}</el-link>
      </template> -->
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  console.log(window);
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 6,
    searchSpan: 6,
    searchIcon: true,
    searchIndex: 3,
    searchLabelWidth: 120,
    menuWidth: 150,
    border: true,
    column: [
      {
        label: '项目信息',
        children: [
        {
          label: '合同编号',
          prop: 'contractCode',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
        },
        {
          label: '对方订单编号',
          prop: 'customerOrderNumber',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
        },
        {
          label: '合同名称',
          prop: 'contractName',component: 'wf-customer-drop',
          width: 200,
          overHidden: true,
          search: true,
          slot: true,
        },
        {
          label: '客户名称',
          prop: 'customerName',
          //width: 150,
          search: true,
          width: 200,
        },
        {
          label: '关联商机',
          prop: 'businessOpportunityName',
          //width: 150,
          // search: true,
          overHidden:true,
          width: 200,
        },
          // {
          //   label: '商机名称',
          //   prop: 'businessOpportunityName',
          //   //width: 150,
          //   width: 200,
          //   search: true,
          // },
          {
            label: '签订日期',
            type: 'date',
            prop: 'signDate',
            component: 'wf-daterange-search',
        search:true,
            // hide: true,
            sortable: true,
            searchSpan: 6,
            width: 120,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
           
          },
  
          //   {
          //     label: "建设单位",
          //     prop: "ownerName",
          //     overHidden: true,
          //     search: true,
          //     //width: 150,
          //   },
        ],
      },
      {
      label: '财务信息',
      children: [
        {
          label: '合同总额',
          prop: 'contractTotalPrice',
          // search: true,
          // hide: true,
          searchSpan: 6,
          searchLabelWidth: 120,
          searchSlot: true,
          sortable: true,  width:100,
        },
        {
          label: '开票金额',
          prop: 'hasInvoice',
          width:100,
          sortable: true,
          formatter:(row) => {
            if(row.isNeedInvoice == 0){
              return '无需开票'
            }else {
               return row.hasInvoice
            }
          }
        },
        {
          label: '未开票金额',
          prop: 'noInvoice',
          width:100,
          sortable: true,
          formatter:(row) => {
            if(row.isNeedInvoice == 0){
              return '无需开票'
            }else {
               return row.noInvoice
            }
          }
        },  {
          label: '离回款时间',
          prop: 'planCollectionPrice',
          width:100,
          html:true,
          formatter:(row) => {
            return `<div ><span style="color: ${row.planCollectionDays > 7?'var(--el-color-success)': row.planCollectionDays <= 7 && row.planCollectionDays >= 0?'var(--el-color-warning)':'var(--el-color-danger)'}" class='planCollectionDays'>${row.planCollectionDays }</span><span>${row.planCollectionDays || row.planCollectionDays == 0 ? '天' : ''}</span></div>`
          }
        },
        {
          label: '收讫状态',
          prop: 'isReceived',
          width: 100,
          hide:true,
          searchSpan:4,
          search:true,
          type:'select',
          dicData:[{
            value:0,
            label:'已收讫'
          },
          {
            value:1,
            label:'未收讫'
          } ,{
              value: 2,
              label: '回款为空',
              desc:'没填写计划收款或收款未到100%'
            },
        ]
        },
        {
          label: '已收款',
          prop: 'receivedPrice',
          width:100,
          sortable: true,
        },
        {
          label: '未收款',
          prop: 'noReceivedPrice',
          width:100,
          sortable: true,
        },
        {
          label: '回款比例(%)',
          prop: 'receiveRate',
          formatter:(row, value, column, cell) => {
            return row.receiveRate + '%'
          }
        },
      ],
    },
      // {
      //   label: "回款逾期",
      //   prop: "overdue",
      // },
      {
        label: '业务员',
        prop: 'businessName',
        // search:true,
        component: 'wf-user-drop',
        formatter:(row, value, column, cell) =>{
          return row.businessUserName
        }
      },
      {
        label: '项目经理',
        prop: 'technologyUserName',
        // hide: true,
      },
      {
          label:'最终用户',
          prop:'finalCustomer',
          span:12,
          hide:true,
          search:true,
        }
      // {
      //   label: '状态',
      //   children: [
      //     // {
      //     //   label: "合同签订时间",
      //     //   prop: "contractSignDate",
      //     // },
      //     {
      //       label: '合同状态',
      //       prop: 'contractStatus',
      //       dicData: [
      //         {
      //           value: 0,
      //           label: '待审批',
      //         },
      //         {
      //           value: 1,
      //           label: '执行中',
      //         },
      //         {
      //           value: 2,
      //           label: '待执行',
      //         },
      //         {
      //           value: 3,
      //           label: '暂停',
      //         },
      //         {
      //           value: 4,
      //           label: '终止',
      //         },
      //         {
      //           value: 5,
      //           label: '诉讼',
      //         },
      //         {
      //           value: 6,
      //           label: '待签订',
      //         },
      //         {
      //           value: 7,
      //           label: '已签未回',
      //         },
      //       ],
      //       width: 80,
      //       type: 'select',
      //       dataType: 'string',
      //     },
      //   ],
      // },
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
//   let tabs = ref([
//   {
//     label: '全部合同',
//     value: null,
//   },
//   {
//     label: '订单合同',
//     value: 0,
//   },
//   {
//     label: '项目合同',
//     value: 1,
//   },
 
// ]);

// let tabIndex = ref(null);
// function handleTabClick(item,index) {
//   tabIndex.value = item.value;
//   onLoad();
// }
  const addUrl = '';
  const delUrl = '';
  const updateUrl = '';
  const tableUrl = '/api/vt-admin/sealContract/page';
  let params = ref({
    contractMinPrice:'',
    contractMaxPrice:'',
    signDate:[`${new Date().getFullYear()}-01-01`,`${new Date().getFullYear()}-12-31`]
  });
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
//   onMounted(() => {
//     onLoad();
//   });
  let loading = ref(false);
  let totalPrice = ref(0);
  let noReceivedPrice = ref(0)
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
          size,
          current,
          ...params.value,
          signStartDate: params.value.signDate && params.value.signDate[0],
          signEndDate: params.value.signDate && params.value.signDate[1],
          signDate: null,
          contractTotalPrice:null,
          selectType:3,
          
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      })
      .catch(err => {
        loading.value = false;
      });
         // 获取统计数据
  axios
    .get('/api/vt-admin/sealContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        contractTotalPrice: null,
        selectType: 3,
        
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noReceivedPrice.value = res.data.data.noReceivedPrice;
    });
  }
  let router = useRouter();
  
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  function addContract() {
    router.push({
      path: '/Contract/editContract',
    });
  }
  function toDetail(row) {
    router.push({
      path: '/Contract/customer/compoents/detail',
      query: {
        id: row.id,
         name:row.contractName
      },
    });
  }
  function reset() {
    params.value.contractMaxPrice = ''
    params.value.contractMinPrice = ''
    onLoad()
  }
  function distribution(row) {
  proxy.$refs.dialogForm.show({
    title: '分配',
    option: {
      column: [
        {
          label: '项目经理',
          type: 'input',
          value: row.technologyUser,
          component: 'wf-user-select',
          // tip:'项目的技术负责，而非深化设计的技术人员',
          params: {
            checkType: 'checkBox',
          },
          prop: 'technologyUser',
          span: 24,
        },
        {
          type: 'switch',
          label: '深化设计',
          span: 12,
          display: true,
          value:1,
          disabled:row.isHasOption == 0,
          prop: 'isNeedDeepen',
        //   beforeChange :(done) => {
            
        //   },
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
      ],
    },
    callback(res) {
   
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContract/distributionTechnique', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function cellStyle({row,column}) {
  if(row.hasInvoice == 0 && column.property == 'hasInvoice'&& row.isNeedInvoice == 1){
    return {
      color:'#fff',
      backgroundColor:'var(--el-color-danger-light-7)'
    }
  }
}
function toCustomerDetail(row) {
   router.push({
    path: '/CRM/customer/detail/detail',
    query: {
      customerId: row.customerId,
      name:row.customerName
    },
  });
}
  </script>
  

  <style lang="scss" scoped>
  .project-main {
    width: calc(100% - 12px);
    margin: 0 auto;
  
    .tabs {
      width: calc(100% - 12px);
     margin-bottom: -10px;
      height: 35px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
  
      .tab-item {
        width: 96px;
        height: 30px;
        line-height: 30px;
        font-size: 15px;
        text-align: center;
        background-color: #fff;
        margin-bottom: -4px;
        border-radius: 5px 5px 0px 0px;
        color: #303133;
        cursor: pointer;
        margin-right: 5px;
        &.active {
          color: #fff;
          background-color: var(--el-color-primary);
        }
      }
    }
    .tab-main {
      width: 100%;
      height: calc(100% - 35px);
    }
  }
  :deep(.planCollectionDays){
  
  font-size:25px;
  font-weight: bolder;
}
.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);

  color: var(--el-color-success);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}
  </style>
  
  