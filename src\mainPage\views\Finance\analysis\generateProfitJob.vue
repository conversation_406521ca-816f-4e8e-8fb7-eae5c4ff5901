<template>
  <basic-container v-loading="loading">
    <div style="display: flex; flex-direction: column; gap: 10px">
      <div style="display: flex; align-items: center; gap: 10px">
        <span style="font-size: 14px; color: #333">业务员：</span
        ><userSelect v-model="params.businessUser"></userSelect>
        <span style="font-size: 14px; color: #333">查询月份：</span>

        <el-date-picker
          v-model="params.date"
          type="month"
          format="YYYY-MM"
          value-format="YYYY-MM"
          placeholder="请选择日期"
        ></el-date-picker>
        <el-button type="primary" icon="search" @click="getList">查询</el-button>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">初纯利总额：</el-text>
        <el-text type="primary" size="large"
          ><el-text>IT产品供应：</el-text>{{ totalDetail?.itProductListTotal?.toFixed(2) }} +
          <el-text>IT集成：</el-text>{{ totalDetail?.itIntegrationListTotal?.toFixed(2) }} +
          <el-text>智能化集成：</el-text
          >{{ totalDetail?.weakCurrentIntegrationListTotal?.toFixed(2) }} +
          <el-text>报销费用：</el-text>-{{ totalDetail?.expenseVOListTotal?.toFixed(2) }} +
          <el-text>额外费用：</el-text
          >{{ totalDetail?.profitBusinessUserExtraVOListTotal?.toFixed(2) }} = ￥{{
            (detailForm.totalProfit && (detailForm.totalProfit * 1).toFixed(2)) || 0
          }}</el-text
        >
      </div>
      <div v-if="!(params.date && params.businessUser)">
        <el-empty description="请选择业务员和查询月份"></el-empty>
      </div>
      <div v-else>
        <h3 v-if="detailForm.itProductList && detailForm.itProductList.length > 0">IT产品供应</h3>
        <avue-crud
          v-if="detailForm.itProductList && detailForm.itProductList.length > 0"
          :cell-style="cellStyle"
          :summary-method="getSummaries"
          :option="option"
          :data="detailForm.itProductList"
        >
          <template #contractName="{ row }">
            <el-link type="primary" @click="viewDetail(row)">{{ row.contractName }}</el-link>
          </template>
          <template #accountingAmount="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}1`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.accountingAmount || '---' }}
                  <el-button type="primary" size="small" icon="edit" text></el-button>
                </span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newAccountingAmount"
                    type="number"
                    placeholder=""
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newAccountingAmount', 'accountingAmount', `pop_${row.id}1`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #manageFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.manageFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input v-model="row.newManageFee" type="number" placeholder=""></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newManageFee', 'manageFee', `pop_${row.id}2`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #purchasePrice="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}7`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.purchasePrice || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newPurchasePrice"
                    type="number"
                    placeholder="请输入采购单价"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newPurchasePrice', 'purchasePrice', `pop_${row.id}7`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #feeCost="{ row }">
            <el-link type="primary" @click="viewCostDetail(row)">
              {{ row.feeCost || '---' }}</el-link
            >
            <el-popover placement="right" :ref="`pop_${row.id}3`" :width="160" trigger="click">
              <template #reference>
                <span> <el-button type="primary" size="small" icon="edit" text></el-button></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newFeeCost"
                    type="number"
                    placeholder="请输入交付费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newFeeCost', 'feeCost', `pop_${row.id}3`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #balancePayment="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}4`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.balancePayment || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newNalancePayment"
                    type="number"
                    placeholder="请输入质保尾款"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newNalancePayment', 'balancePayment', `pop_${row.id}4`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #overdueInterest="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}5`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.overdueInterest || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newOverdueInterest"
                    type="number"
                    placeholder="请输入逾期利息"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newOverdueInterest', 'overdueInterest', `pop_${row.id}5`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #businessFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}6`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.businessFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newBusinessFee"
                    type="number"
                    placeholder="请输入业务费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newBusinessFee', 'businessFee', `pop_${row.id}6`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #number="{ row }">
            <el-link type="primary" @click="viewProduct(row)">{{
              parseFloat(row.number || 0) || 1
            }}</el-link>
          </template>
          <template #purchaseCost="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row)">
              {{ row.purchaseCost || '---' }}
            </el-link>
          </template>
        </avue-crud>
        <h3 v-if="detailForm.itIntegrationList && detailForm.itIntegrationList.length > 0">
          IT集成
        </h3>
        <avue-crud
          v-if="detailForm.itIntegrationList && detailForm.itIntegrationList.length > 0"
          :cell-style="cellStyle"
          :summary-method="getSummaries"
          :option="option"
          :data="detailForm.itIntegrationList"
        >
          <template #contractName="{ row }">
            <el-link type="primary" @click="viewDetail(row)">{{ row.contractName }}</el-link>
          </template>
          <template #accountingAmount="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}1`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.accountingAmount || '---' }}
                  <el-button type="primary" size="small" icon="edit" text></el-button>
                </span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newAccountingAmount"
                    type="number"
                    placeholder=""
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newAccountingAmount', 'accountingAmount', `pop_${row.id}1`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #purchasePrice="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.purchasePrice || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newPurchasePrice"
                    type="number"
                    placeholder="请输入采购单价"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newPurchasePrice', 'purchasePrice', `pop_${row.id}2`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #manageFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.manageFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input v-model="row.newManageFee" type="number" placeholder=""></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newManageFee', 'manageFee', `pop_${row.id}2`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #feeCost="{ row }">
            <el-link type="primary" @click="viewCostDetail(row)">
              {{ row.feeCost || '---' }}</el-link
            >
            <el-popover placement="right" :ref="`pop_${row.id}3`" :width="160" trigger="click">
              <template #reference>
                <span> <el-button type="primary" size="small" icon="edit" text></el-button></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newFeeCost"
                    type="number"
                    placeholder="请输入交付费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newFeeCost', 'feeCost', `pop_${row.id}3`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #balancePayment="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}4`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.balancePayment || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newNalancePayment"
                    type="number"
                    placeholder="请输入质保尾款"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newNalancePayment', 'balancePayment', `pop_${row.id}4`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #overdueInterest="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}5`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.overdueInterest || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newOverdueInterest"
                    type="number"
                    placeholder="请输入逾期利息"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newOverdueInterest', 'overdueInterest', `pop_${row.id}5`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #businessFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}6`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.businessFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newBusinessFee"
                    type="number"
                    placeholder="请输入业务费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newBusinessFee', 'businessFee', `pop_${row.id}6`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>

          <template #number="{ row }">
            <el-link type="primary" @click="viewProduct(row)">{{
              parseFloat(row.number || 0) || 1
            }}</el-link>
          </template>
          <template #purchaseCost="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row)">
              {{ row.purchaseCost || '---' }}
            </el-link>
          </template>
        </avue-crud>
        <h3
          v-if="
            detailForm.weakCurrentIntegrationList &&
            detailForm.weakCurrentIntegrationList.length > 0
          "
        >
          智能化集成
        </h3>
        <avue-crud
          v-if="
            detailForm.weakCurrentIntegrationList &&
            detailForm.weakCurrentIntegrationList.length > 0
          "
          :cell-style="cellStyle"
          :summary-method="getSummaries"
          :option="option2"
          :data="detailForm.weakCurrentIntegrationList"
        >
          <template #purchaseCost="{ row }">
            <el-link type="primary" @click="viewOrderDetail(row)">
              {{ row.purchaseCost || '---' }}
            </el-link>
          </template>
          <template #contractName="{ row }">
            <el-link type="primary" @click="viewDetail(row)">{{ row.contractName }}</el-link>
          </template>
          <template #accountingAmount="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}1`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.accountingAmount || '---' }}
                  <el-button type="primary" size="small" icon="edit" text></el-button>
                </span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newAccountingAmount"
                    type="number"
                    placeholder=""
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newAccountingAmount', 'accountingAmount', `pop_${row.id}1`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #manageFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}2`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.manageFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newManageFee"
                    type="number"
                    placeholder="请输入管理费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newManageFee', 'manageFee', `pop_${row.id}2`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #feeCost="{ row }">
            <el-link type="primary" @click="viewCostDetail(row)">
              {{ row.feeCost || '---' }}</el-link
            >
            <el-popover placement="right" :ref="`pop_${row.id}3`" :width="160" trigger="click">
              <template #reference>
                <span> <el-button type="primary" size="small" icon="edit" text></el-button></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newFeeCost"
                    type="number"
                    placeholder="请输入交付费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newFeeCost', 'feeCost', `pop_${row.id}3`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #balancePayment="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}4`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.balancePayment || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newNalancePayment"
                    type="number"
                    placeholder="请输入质保尾款"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newNalancePayment', 'balancePayment', `pop_${row.id}4`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #overdueInterest="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}5`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.overdueInterest || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newOverdueInterest"
                    type="number"
                    placeholder="请输入逾期利息"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="
                        updateFee(row, 'newOverdueInterest', 'overdueInterest', `pop_${row.id}5`)
                      "
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
          <template #businessFee="{ row }">
            <el-popover placement="right" :ref="`pop_${row.id}6`" :width="160" trigger="click">
              <template #reference>
                <span
                  >{{ row.businessFee || '---'
                  }}<el-button type="primary" size="small" icon="edit" text></el-button
                ></span>
              </template>
              <template #default>
                <div style="display: flex; flex-direction: column; align-items: flex-end; gap: 5px">
                  <el-input
                    v-model="row.newBusinessFee"
                    type="number"
                    placeholder="请输入业务费用"
                  ></el-input>
                  <div style="display: flex; align-items: center; gap: 10px">
                    <!-- <span style="font-size: 12px;">取消
                </span> -->
                    <el-button
                      size="small"
                      style="border-radius: 3px"
                      @click="updateFee(row, 'newBusinessFee', 'businessFee', `pop_${row.id}6`)"
                      type="primary"
                      >确认</el-button
                    >
                  </div>
                </div>
              </template>
            </el-popover>
          </template>
        </avue-crud>
        <h3 v-if="detailForm.expenseVOList && detailForm.expenseVOList.length > 0">相关费用</h3>
        <avue-crud
          v-if="detailForm.expenseVOList && detailForm.expenseVOList.length > 0"
          :option="option3"
          :summary-method="getSummaries"
          :data="detailForm.expenseVOList"
        >
          <template #reimbursementFiles="{ row }">
            <File :fileList="row.fileList"></File>
          </template>
          <template #completeFiles-form="{ row }">
            <File :fileList="form.completeFileList"></File>
          </template>

          <template #reimbursementStatus="{ row }">
            <div v-if="row.reimbursementStatus == 1">
              <el-tag effect="plain" type="info" v-if="row.auditType == 0">待主管审核</el-tag>
              <el-tag effect="plain" type="info" v-if="row.auditType == 1">待总经理审核</el-tag>
            </div>
            <div v-else>
              <el-tooltip :content="row.auditRemark" :disabled="!(row.reimbursementStatus == 3)">
                <el-tag
                  effect="plain"
                  :type="
                    row.reimbursementStatus == 0
                      ? 'info'
                      : row.reimbursementStatus == 2 || row.reimbursementStatus == 4
                      ? 'success'
                      : 'danger'
                  "
                  >{{ row.$reimbursementStatus }}</el-tag
                >
              </el-tooltip>
            </div>
          </template>
          <template #reimbursementCode="{ row }">
            <el-link type="primary" @click="crud.rowView(row)">{{ row.reimbursementCode }}</el-link>
          </template>
          <template #reimbursementFiles1-form="{ row }">
            <File :fileList="row.fileList"></File>
          </template>
        </avue-crud>
        <h3>额外费用</h3>
        <otherCost
          :date="params.date"
          @add-success="getList"
          :table-data="detailForm.profitBusinessUserExtraVOList"
          :businessUser="params.businessUser"
        ></otherCost>
      </div>
    </div>
    <el-drawer title="产品明细" size="80%" v-model="detailDrawer">
      <contractProduct :offer-id="currentId"></contractProduct>
    </el-drawer>
    <el-drawer title="费用明细" v-model="costDetailDrawer" size="80%">
      <costDetail :seal-contract-id="currentContractId"></costDetail>
    </el-drawer>
 
  </basic-container>
</template>
<script setup lang="jsx">
import userSelect from '@/views/desk/components/userSelect.vue';
import axios from 'axios';
import { watch, getCurrentInstance, computed } from 'vue';
import otherCost from './otherCost.vue';
import { useRoute, useRouter } from 'vue-router';
import contractProduct from './contractProduct.vue';
import moment from 'moment';
import { ElMessage } from 'element-plus';
import costDetail from './components/costDetail.vue';

let params = ref({
  date: moment(new Date()).format('YYYY-MM'),
});
let router = useRouter();
const { proxy } = getCurrentInstance();
let option = ref({
  header: false,
  size: 'small',
  menu: false,
  align: 'center',
  maxHeight: 600,
  border: true,
  showSummary: true,
  sumColumnList: [
    { name: 'purchaseCost', type: 'sum' },
    { name: 'financeAccountingAmount', type: 'sum' },
    { name: 'contractTotalPrice', type: 'sum' },
    { name: 'purchaseCost', type: 'sum' },
    { name: 'businessFee', type: 'sum' },
    { name: 'feeCost', type: 'sum' },
    { name: 'manageFee', type: 'sum' },
    { name: 'balancePayment', type: 'sum' },
    { name: 'overdueInterest', type: 'sum' },
    { name: 'profit', type: 'sum' },
  ],
  column: [
    {
      label: '客户名称',
      prop: 'customerName',

      component: 'wf-customer-drop',
      overHidden: true,
      width: 200,
    },

    {
      label: '订单名称',
      prop: 'contractName',
      width: 200,
      overHidden: true,
    },
    {
      label: '产品名称',
      prop: 'productNames',
      overHidden: true,
      width: 130,
    },
    {
      label: '数量',
      prop: 'number',
      width: 60,
    },
    {
      label: '单价',
      prop: 'purchasePrice',
      width: 80,
    },
    {
      label: '采购成本总额',
      prop: 'purchaseCost',
      width: 80,
    },
    {
      label: '含税核算成本',
      prop: 'accountingAmount',
      width: 100,
    },
    {
      label: '财务核算成本',
      prop: 'financeAccountingAmount',
      width: 100,
    },
    {
      label: '销售单价',
      prop: 'sealPrice',
      width: 80,
    },
    {
      label: '销售总额',
      prop: 'contractTotalPrice',
      width: 100,
    },
    {
      label: '业务费用',
      prop: 'businessFee',
      width: 80,
    },
    {
      label: '交付费用',
      prop: 'feeCost',
      width: 80,
    },
    {
      label: '管理费用',
      prop: 'manageFee',
      width: 80,
    },
    {
      label: '质保尾款',
      prop: 'balancePayment',
      width: 80,
    },
    {
      label: '逾期利息',
      prop: 'overdueInterest',
      width: 80,
    },

    {
      label: '签订时间',
      prop: 'signDate',
      width: 110,
    },

    // {
    //   label: '采购成本',
    //   prop: 'purchaseCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.purchaseCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.purchaseCost).toFixed(2);
    //   },
    // },

    // {
    //   label: '人工成本',
    //   prop: 'labourCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.labourCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.labourCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '费用',
    //   prop: 'feeCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.feeCost).toFixed(2)) ? '' : parseFloat(row.feeCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '税金',
    //   prop: 'taxCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.taxCost).toFixed(2)) ? '' : parseFloat(row.taxCost).toFixed(2);
    //   },
    // },
    {
      label: '初利润',
      prop: 'profit',
      width: 90,
      fixed: 'right',
      formatter: row => {
        return isNaN(parseFloat(row.profit).toFixed(2)) ? '' : parseFloat(row.profit).toFixed(2);
      },
    },
    {
      label: '初利润率',
      prop: 'profitRate',
      hide: true,
      width: 80,
      fixed: 'right',
    },
  ],
});
let option1 = ref({
  header: false,
  size: 'small',
  border: true,
  maxHeight: 600,
  align: 'center',
  menu: false,
  showSummary: true,
  sumColumnList: [
    { name: 'purchaseCost', type: 'sum' },
    { name: 'accountingAmount', type: 'sum' },
    { name: 'financeAccountingAmount', type: 'sum' },
    { name: 'contractTotalPrice', type: 'sum' },
    { name: 'businessFee', type: 'sum' },
    { name: 'feeCost', type: 'sum' },
    { name: 'manageFee', type: 'sum' },
    { name: 'balancePayment', type: 'sum' },
    { name: 'overdueInterest', type: 'sum' },
    { name: 'profit', type: 'sum' },
  ],
  column: [
    {
      label: '客户名称',
      prop: 'customerName',

      component: 'wf-customer-drop',
      overHidden: true,
    },

    {
      label: '项目名称',
      prop: 'contractName',

      overHidden: true,
    },

    {
      label: '设备采购成本',
      prop: 'purchaseCost',
      width: 80,
    },
    {
      label: '部门核算成本',
      prop: 'accountingAmount',
      width: 100,
    },
    {
      label: '财务核算成本',
      prop: 'financeAccountingAmount',
      width: 100,
    },

    {
      label: '合同金额',
      prop: 'contractTotalPrice',
      width: 100,
    },
    {
      label: '业务费用',
      prop: 'businessFee',
      width: 80,
    },
    {
      label: '交付费用',
      prop: 'feeCost',
      width: 80,
    },
    {
      label: '管理费用',
      prop: 'manageFee',
      width: 80,
    },
    {
      label: '质保尾款',
      prop: 'balancePayment',
      width: 80,
    },
    {
      label: '逾期利息',
      prop: 'overdueInterest',
      width: 80,
    },

    {
      label: '签订时间',
      prop: 'signDate',
      width: 110,
    },

    // {
    //   label: '采购成本',
    //   prop: 'purchaseCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.purchaseCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.purchaseCost).toFixed(2);
    //   },
    // },

    // {
    //   label: '人工成本',
    //   prop: 'labourCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.labourCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.labourCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '费用',
    //   prop: 'feeCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.feeCost).toFixed(2)) ? '' : parseFloat(row.feeCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '税金',
    //   prop: 'taxCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.taxCost).toFixed(2)) ? '' : parseFloat(row.taxCost).toFixed(2);
    //   },
    // },
    {
      label: '初利润',
      prop: 'profit',
      width: 90,
      fixed: 'right',
      formatter: row => {
        return isNaN(parseFloat(row.profit).toFixed(2)) ? '' : parseFloat(row.profit).toFixed(2);
      },
    },
    {
      label: '初利润率',
      prop: 'profitRate',
      width: 80,
      hide: true,
      fixed: 'right',
    },
  ],
});
let option2 = ref({
  header: false,
  menu: false,
  size: 'small',
  border: true,
  align: 'center',
  showSummary: true,
  sumColumnList: [
    { name: 'purchaseCost', type: 'sum' },
    { name: 'accountingAmount', type: 'sum' },
    { name: 'financeAccountingAmount', type: 'sum' },
    { name: 'contractTotalPrice', type: 'sum' },
    { name: 'businessFee', type: 'sum' },
    { name: 'feeCost', type: 'sum' },
    { name: 'manageFee', type: 'sum' },
    { name: 'balancePayment', type: 'sum' },
    { name: 'overdueInterest', type: 'sum' },
    { name: 'labourCost', type: 'sum' },
  ],
  column: [
    {
      label: '客户名称',
      prop: 'customerName',

      component: 'wf-customer-drop',
      overHidden: true,
    },

    {
      label: '项目名称',
      prop: 'contractName',

      overHidden: true,
    },

    {
      label: '设备采购成本',
      prop: 'purchaseCost',
      width: 80,
    },
    {
      label: '人工成本',
      prop: 'labourCost',
      width: 80,
    },
    {
      label: '部门核算成本',
      prop: 'accountingAmount',
      width: 100,
    },
    {
      label: '财务核算成本',
      prop: 'financeAccountingAmount',
      width: 100,
    },

    {
      label: '合同金额',
      prop: 'contractTotalPrice',
      width: 100,
    },
    {
      label: '业务费用',
      prop: 'businessFee',
      width: 80,
    },
    {
      label: '交付费用',
      prop: 'feeCost',
      width: 80,
    },
    {
      label: '管理费用',
      prop: 'manageFee',
      width: 80,
    },
    {
      label: '质保尾款',
      prop: 'balancePayment',
      width: 80,
    },
    {
      label: '逾期利息',
      prop: 'overdueInterest',
      width: 80,
    },

    {
      label: '签订时间',
      prop: 'signDate',
      width: 110,
    },

    // {
    //   label: '采购成本',
    //   prop: 'purchaseCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.purchaseCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.purchaseCost).toFixed(2);
    //   },
    // },

    // {
    //   label: '人工成本',
    //   prop: 'labourCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.labourCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.labourCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '费用',
    //   prop: 'feeCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.feeCost).toFixed(2)) ? '' : parseFloat(row.feeCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '税金',
    //   prop: 'taxCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.taxCost).toFixed(2)) ? '' : parseFloat(row.taxCost).toFixed(2);
    //   },
    // },
    {
      label: '初利润',
      prop: 'profit',
      width: 90,
      fixed: 'right',
      formatter: row => {
        return isNaN(parseFloat(row.profit).toFixed(2)) ? '' : parseFloat(row.profit).toFixed(2);
      },
    },
    {
      label: '初利润率',
      prop: 'profitRate',
      width: 80,
      hide: true,
      fixed: 'right',
    },
  ],
});
let option3 = ref({
  header: false,
  menu: false,
  size: 'small',
  border: true,
  align: 'center',
  showSummary: true,
  sumColumnList: [{ name: 'expensePrice', type: 'sum' }],
  column: {
    purpose: {
      label: '报销事宜',
    },
    expenseType: {
      type: 'tree',
      label: '报销类型',
      dicUrl: '/blade-system/dict-biz/dictionary-tree?code=expenseType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      parent: false,
    },
    customerName: {
      label: '关联客户',
      width: 200,
      overHidden: true,
    },
    // sealContractName: {
    //   label: '关联合同',
    // },
    expensePrice: {
      label: '报销金额',
      width: 130,
    },
    // expenseStatus: {
    //   label: '报销状态',
    //   dicData: [
    //     {
    //       value: 0,
    //       label: '未报销',
    //     },
    //     {
    //       value: 1,
    //       label: '已报销',
    //     },
    //     {
    //       value: 2,
    //       label: '报销中',
    //     },
    //   ],
    // },
    reimbursementUserName: {
      label: '报销人',
      width: 130,
    },
    expenseDate: {
      label: '费用产生日期',
      width: 130,
    },
  },
});
watch(
  () => params,
  () => {
    getList();
  },
  {
    deep: true,
  }
);
let detailForm = ref([]);
let loading = ref(false);
function getList() {
  const { businessUser, date } = params.value;
  if (businessUser && date) {
    loading.value = true;
    axios
      .get('/api/vt-admin/profitAnalysisUser/listForBusinessPerson', {
        params: params.value,
      })
      .then(res => {
        detailForm.value = res.data.data;
        loading.value = false;
      })
      .catch(() => {
        loading.value = false;
      });
  }
}
let detailDrawer = ref(false);
let currentId = ref(null);
function viewDetail(row) {
  if (row.projectId) {
    router.push({
      path: '/Project/detail/detail',
      query: {
        id: row.projectId,
        name: row.contractName,
      },
    });
  } else {
    router.push({
      path: '/Contract/customer/allCustomerContract',
      query: {
        sealContractId: row.sealContractId,
      },
    });
  }
}
function viewProduct(row) {
  currentId.value = row.offerId;
  detailDrawer.value = true;
}
function updateFee(row, nKey, oKey, ref) {
  const text = {
    overdueInterest: 'updateOverdueInterest',
    balancePayment: 'updateBalancePayment',
    purchasePrice: 'updateProductPrice',
    feeCost: 'updateFeeCost',
    manageFee: 'updateManageFee',
    businessFee: 'updateBusinessFee',
    accountingAmount: 'updateAccountingAmount',
  };
  axios
    .post(`/api/vt-admin/profitAnalysisUser/${text[oKey]}`, {
      id: row.id,
      [oKey]: row[nKey],
    })
    .then(res => {
      ElMessage.success(res.data.msg);
      proxy.$refs[ref].hide();
      getList();
    });
}
function rowStyle({ row, index }) {
  return {
    'background-color': row.isUpdate == 1 ? 'var(--el-color-success-light-9)' : '',
  };
}
function cellStyle({ column, row }) {
  const obj = {
    purchasePrice: 'isUpdatePurchasePrice',
    accountingAmount: 'isUpdate',
    feeCost: 'isUpdateFeeCost',
    manageFee: 'isUpdateManageFee',
    businessFee: 'isUpdateBusinessFee',
    balancePayment: 'isUpdateBalancePayment',
    overdueInterest: 'isUpdateOverdueInterest',
  };
  if (obj[column.property]) {
    console.log(row[obj[column.property]], obj[column.property]);

    return {
      'background-color': row[obj[column.property]] == 1 ? 'var(--el-color-success-light-9)' : '',
    };
  }
}
function getSummaries({ columns, data }) {
  let arr = [];
  let valueArr = [
    'accountingAmount',
    'purchaseCost',
    'financeAccountingAmount',
    'contractTotalPrice',
    'businessFee',
    'feeCost',
    'manageFee',
    'balancePayment',
    'overdueInterest',
    'profit',
    'labourCost',
    'expensePrice',
  ];
  columns.forEach((item, index) => {
    if (index === 0) {
      arr.push(
        <div>
          本页小计：
          {/* <br />
          合计： */}
        </div>
      );
    } else {
      if (valueArr.includes(item.property)) {
        const value = data.reduce((sum, i) => {
          sum = sum + i[item.property] * 1;
          return sum;
        }, 0);

        arr.push(
          <div>
            {item.property == 'expensePrice' ? '-' : ''}
            {isNaN(value) ? '' : value.toFixed(2)}
            {/* <br />
            {
              statisticData.value[
                item.property == 'purchaseCost'
                  ? 'purchasePrice'
                  : item.property == 'profit'
                  ? 'profitPrice'
                  : item.property
              ]
            } */}
          </div>
        );
      } else {
        arr.push('');
      }
    }
  });
  return arr;
}
let costDetailDrawer = ref(false);
let currentContractId = ref(null);
function viewCostDetail(row) {
  costDetailDrawer.value = true;
  currentContractId.value = row.sealContractId;
}
const totalDetail = computed(() => {
  console.log(111);

  return {
    itProductListTotal: detailForm.value.itProductList.reduce((pre, cur) => {
      pre += cur.profit * 1;
      return pre;
    }, 0),
    itIntegrationListTotal: detailForm.value.itIntegrationList.reduce((pre, cur) => {
      pre += cur.profit * 1;
      return pre;
    }, 0),
    weakCurrentIntegrationListTotal: detailForm.value.weakCurrentIntegrationList.reduce(
      (pre, cur) => {
        pre += cur.profit * 1;
        return pre;
      },
      0
    ),
    expenseVOListTotal: detailForm.value.expenseVOList.reduce((pre, cur) => {
      pre += cur.expensePrice * 1;
      return pre;
    }, 0),
    profitBusinessUserExtraVOListTotal: detailForm.value.profitBusinessUserExtraVOList.reduce(
      (pre, cur) => {
        pre += cur.amount * 1;
        return pre;
      },
      0
    ),
  };
});
function viewOrderDetail(row) {
  router.push({
    path: '/SRM/procure/order',
    query: {
      offerName: row.contractName,
    },
  });
}

</script>

<style lang="scss" scoped>
:deep(.el-table--small .cell) {
  padding: 0;
}
</style>
