<template>
  <el-dialog
    ref="nf-dialog"
    class="nf-dialog"
    v-model="visible"
    title="供应商选择"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="isInit && visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model:page="page"
      v-model="form"
      v-model:search="query"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template v-if="checkType == 'radio'" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #menu-left>
        <el-button type="primary" @click="handleAdd" size="default" icon="plus">新 增</el-button>
      </template>
    </avue-crud>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="default">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="default">确 定</el-button>
      </span>
    </template>
    <el-drawer title="新增供应商" append-to-body size="70%" v-model="drawer">
      <addSupplier :isNew="true" @success="handleSuccess"></addSupplier>
    </el-drawer>
  </el-dialog>
</template>
<script>
import { getUser } from '@/api/system/user';
import axios from 'axios';
import addSupplier from '@/views/SRM/supplier/compoents/update.vue';
export default {
  props: {
    defaultChecked: String,
    userUrl: {
      type: String,
      default: () => {
        return '/vt-admin/supplier/page';
      },
    },
    customOption: Object,
    checkType: {
      type: String,
      default: () => {
        return 'radio';
      },
    },
    params: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {
    addSupplier,
  },
  watch: {
    checkType: {
      handler(val) {
        if (val == 'radio') {
          this.option.selection = false;
          this.findObject(this.option.column, 'radio').hide = false;
        } else {
          this.option.selection = true;
          this.findObject(this.option.column, 'radio').hide = true;
        }
      },
      immediate: true,
    },
    params: {
      handler(val) {
        if (val) {
          this.query = {
            ...this.query,
            ...val,
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach(ele => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(',');
    },
    names() {
      let names = new Set();
      this.selectionList.forEach(ele => {
        names.add(ele.supplierName);
      });
      return Array.from(names).join(',');
    },
  },
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      props: {
        id: 'id',
        name: 'realName',
        records: 'data.data.records',
        total: 'data.data.total',
      },
      option: {
        size: 'default',
        searchSize: 'default',
        align: 'center',
        menu: false,
        addBtn: false,
        header: true,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: '',
            prop: 'radio',
            type: 'radio',
            width: 55,
            hide: true,
          },
          {
            label: '供应商名称',
            searchLabelWidth: 120,
            prop: 'supplierName',
            width: 250,
            overHidden: true,
            search: true,
          },
          {
            type: 'input',
            label: '主要供应品牌',
            search: true,
            searchLabelWidth: 120,
            span: 12,

            display: true,
            prop: 'brand',
          },
          {
            label: '主要供应产品',
            prop: 'mainProduct',
          },
          {
            type: 'select',
            label: '供应商分类',
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择供应商分类',
              },
            ],
            display: true,
            prop: 'supplierClassify',
            dicUrl: '/blade-system/dict-biz/dictionary?code=supplierClassify',
            props: {
              label: 'dictValue',
              value: 'id',
            },
          },
          {
            type: 'input',
            label: '供应商特色',
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择供应商级别',
              },
            ],
            display: true,
            prop: 'supplierFeature',
          },
        ],
      },
      drawer: false,
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, userProps } = this.customOption;
          if (column) this.option.column = column;
          if (userProps) this.props = userProps;
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$emit('onConfirm', this.ids, this.names);
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == 'function') done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (this.checkType == 'radio') {
        this.selectionList = [row];
        this.form.radio = row.id;
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.defaultChecked) return;
      let defaultChecked = this.defaultChecked;

      if (this.checkType == 'checkbox') {
        // this.selectionClear()
        const checks = defaultChecked.split(',');
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async c => {
              let row = this.data.find(d => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find(d => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await this.getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud) this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find(d => d.id == defaultChecked);
        if (!row) {
          let res = await this.getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.form.radio = defaultChecked;
        } else {
          this.selectionList = [];
          this.form.radio = '';
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const param = {
        current: page.currentPage,
        size: page.pageSize,
        ...Object.assign(params, this.query),
      };
      this.$axios.get(this.userUrl, { params: param }).then(res => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;

        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = '') {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split('.').forEach(ele => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = '';
        }
      });
      return result;
    },
    getDetail(c) {
      return axios.get('/api/vt-admin/supplier/detail', {
        params: {
          id: c,
        },
      });
    },
    handleAdd() {
      this.drawer = true;
    },
    handleSuccess() {
      this.drawer = false;
      this.onLoad(this.page);
    },
  },
};
</script>
<style lang="scss">
.nf-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>
