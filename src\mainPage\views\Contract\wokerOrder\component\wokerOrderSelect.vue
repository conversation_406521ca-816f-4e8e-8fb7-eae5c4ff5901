<template>
  <el-dialog class="avue-dialog" title="选择工单" v-model="dialogVisible" width="1000">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @selection-change="handleChange"
      @size-change="onLoad"
      v-model="form"
      @row-click="rowClick"
    >
      <template #radio="{ row }">
        <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
      </template>
    </avue-crud>
    <span class="avue-dialog__footer">
      <el-button @click="dialogVisible = false" icon="close">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" icon="check">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script setup>

import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps(['sealContractId']);
let option = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 4,
  menuWidth: 180,
  labelWidth: 120,
  menu:false,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  column: [
    {
        label:'',
        prop:'radio',
        width:50,
    },
   
    {
      label: '工单名称',
      prop: 'objectName',
      search: true,
      display: false,
      searchSpan:6,
    },
    // {
    //   label: '工单类型',
    //   prop: 'lables',
    //   placeholder: '请输入工单类型',
    //   type: 'tree',
    //   display: false,
    //   props: {
    //     value: 'id',
    //     label: 'dictValue',
    //   },
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
    // },
    // 管理合同
    {
      label: '合同名称',
      prop: 'contractName',
      search: true,
      display: false,
      searchSpan:6,
    },
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '服务时间',
      prop: 'planTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '工单价格',
      prop: 'orderPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,
      width: 110,
      labelWidth: 120,
      display: false,
    },
    {
      label: '工程师',
      prop: 'handleUserName',
      width: 110,
      display: false,
    //   search: true,
    },
    {
      label: '工单状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      searchSpan:6,
      width: 110,
      dicData: [
        {
          label: '待处理',
          value: 0,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        },{
          label:'待接单',
          value:3
        },
          {
          label:'已关单',
          value:3
        }
      ],
    },
  ],
});
let form = ref({});
let dialogVisible = ref(false);
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let selectRow = ref({});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        selectType: 0,
        sealContractId: props.sealContractId,
        size,
        objectType:0,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleIsHasCollectionCodeChange(item) {
  axios
    .post('/api/blade-system/user/update-info', {
      isHasCollectionCode: item.isHasCollectionCode,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
let selectList = ref([]);
const emits = defineEmits(['onConfirm']);
function handleChange(list) {
  selectList.value = list.length ? [list.pop()] : [];
}
function rowClick(row) {
  //   proxy.$refs.crud.toggleSelection([row]);
  //   selectList.value = [row];
  selectRow.value = row.id;
}
function handleConfirm() {
  const data = tableData.value.find(item => item.id === selectRow.value);
  ;
  emits('onConfirm', [data]);
  dialogVisible.value = false;
}
defineExpose({
  dialogVisible,
});
</script>

<style lang="scss" scoped></style>
