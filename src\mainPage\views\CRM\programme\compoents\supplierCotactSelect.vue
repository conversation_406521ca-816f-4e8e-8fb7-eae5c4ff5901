<template>
  <el-dialog
    title="选择联系人"
    v-model="dialogVisible"
    style="width: 60%"
    draggable
    class="avue-dialog avue-dialog--top"
  >
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      @row-click="rowClick"
      v-model="form"
    >
      <template #radio="{ row }">
        <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
      </template>
      <template #menu="{ row }">
        <el-button type="text" icon="view" size="small" @click="handleView(row)">查看</el-button>
      </template>
      <template #templateCategory="{ row }">
        <div v-if="row.templateCategory" style="display: flex; flex-wrap: wrap">
          <el-tag effect='plain'
            style="margin: 2px"
            size="small"
            v-for="item in row.$templateCategory?.split('|') || []"
            :key="item"
            >{{ item }}</el-tag
          >
        </div>
      </template>
      <!-- <template #templateCategory-search>
          <templateTagSelect v-model="params.templateCategory"></templateTagSelect>
        </template> -->
    </avue-crud>

    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,

  searchMenuSpan: 6,
  searchSpan: 10,
  menuWidth: 100,
  header: false,
  menu: true,
  border: true,
  column: [
  {
      label: '',
      prop: 'radio',
      width: 60,
      hide: false,
    },
    {
      type: 'select',
      label: '联系人标签',

      span: 12,
      dataType: 'string',
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '请选择联系人标签',
        },
      ],
      width: 300,
      prop: 'concatType',

      dicUrl: '/blade-system/dict-biz/dictionary?code=supplier_concat_type',
      remote: false,
    },
    {
      label: '姓名',
      prop: 'concatName',
    },
    {
      label: '手机',
      prop: 'concatPhone',
      type: 'number',
      controls: false,
    },

    {
      label: '微信',
      prop: 'wxCode',
    },
    {
      label: '性别',
      prop: 'sex',
      type: 'select',
      dicData: [
        {
          value: 1,
          label: '男',
        },
        {
          value: 2,
          label: '女',
        },
      ],
    },
    {
      label: '部门',
      prop: 'dept',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=dept',
    },
    // {
    //   label: '职务',
    //   prop: 'post',
    //   type: 'select',
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=position',

    // },

    {
      label: '备注',
      prop: 'remark',
      type: 'input',
      span: 24,
      rows: 2,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const tableUrl = '/api/vt-admin/supplier/detail';

let tableData = ref([{}]);
let treeData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let params = ref({
  // businessType: route.query.businessType,
});
let loading = ref(false);
const props = defineProps({
  supplierId: {
    type: String,
    default: '',
  },
});
watch(() => {
  // params.value.businessType = props.businessType;
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        id: props.supplierId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.supplierConcatVOList;
    })
    .catch(() => {
      loading.value = false;
    });
}

let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
let selectRow = ref(null);
function rowClick(row) {
  selectRow.value = row.id;
}
function open() {
  onLoad();
  dialogVisible.value = true;
}
function handleConfirm() {
  proxy.$emit('change', tableData.value.find(item => item.id === selectRow.value), () => {
    dialogVisible.value = false;
  });
}
let drawer = ref(false);
let detailForm = ref({});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
