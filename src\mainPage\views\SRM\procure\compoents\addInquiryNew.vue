<template>
  <div style="padding: 0 15px">
    <el-divider content-position="left"
      ><span style="color: var(--el-color-primary)">类型</span></el-divider
    >
    <el-radio-group v-model="form.inquiryType" @change="handleTypeChange">
      <el-radio :label="0">生成询价单</el-radio>
      <!-- <el-radio :label="1">合作方采购</el-radio> -->
    </el-radio-group>
    <el-divider content-position="left"
      ><span style="color: var(--el-color-primary)">{{form.inquiryType == 0 ? '询价' : '采购'}}的产品</span></el-divider
    >
    <avue-crud :option="option" :data="tableData"></avue-crud>
    <el-divider v-if="form.inquiryType == 0" content-position="left"
      ><span style="color: var(--el-color-primary)">关联的供应商</span></el-divider
    >
    <el-row  v-if="form.inquiryType == 0" :gutter="10">
      <el-col :span="12" v-for="item in supplierData" :key="item.id">
        <el-card
          shadow="hover"
          body-style="padding:15px"
          :style="{ border: item.isChecked ? '1px solid var(--el-color-primary)!important' : '' }"
          @click="item.isChecked = !item.isChecked"
        >
          <div style="display: flex; align-items: center; gap: 20px">
            <el-checkbox
              @click.prevent
              v-model="item.isChecked"
              label=""
              :indeterminate="false"
            ></el-checkbox>
            <div style="display: flex; gap: 5px; flex-direction: column">
              <div>
                <el-text size="large">{{ item.supplierName }}</el-text>
              </div>
              <div>
                <el-select
                  size="small"
                  style="width: 250px"
                  v-model="item.supplierContactId"
                  placeholder="请选择联系人"
                >
                  <el-option
                    v-for="i in item.supplierConcatVOList"
                    :key="i.value"
                    :label="i.concatName"
                    :value="i.id"
                  >
                    <span style="float: left">{{ i.concatName }}</span>
                    <span
                      style="float: right; color: var(--el-text-color-secondary); font-size: 13px"
                    >
                      {{ i.mail }}
                    </span>
                  </el-option>
                </el-select>
                <el-text
                  v-if="
                    item.supplierContactId &&
                    !item.supplierConcatVOList.find(ii => ii.id == item.supplierContactId)?.mail
                  "
                  type="danger"
                  size="small"
                  >未有邮箱</el-text
                ><el-text
                  v-if="
                    item.supplierContactId &&
                    !item.supplierConcatVOList.find(ii => ii.id == item.supplierContactId)?.mail
                  "
                  style="cursor: pointer"
                  type="primary"
                  size="small"
                  @click.stop="addEmail(item.supplierContactId, item.supplierConcatVOList)"
                  >完善</el-text
                >
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card
          shadow="hover"
          body-style="padding:0px"
          style="border: 1px dashed var(--el-color-primary) !important"
        >
          <div style="display: flex; align-items: center; gap: 20px; height: 80px">
            <el-button
              style="width: 100%; height: 100%; border: none; font-size: 40px"
              @click="addSupplier"
              plain
              type="primary"
              icon="plus"
            ></el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <el-divider v-if="form.inquiryType == 0" content-position="left"
      ><span style="color: var(--el-color-primary)">询价单有效期</span>
      <el-input
        type="text"
        size="small"
        style="width: 100px; margin-right: 5px"
        v-model="form.overDays"
        suffix="天"
        placeholder="请输入询价单有效期"
      >
        <template #append>天</template>
      </el-input>

      <span style="color: var(--el-color-primary)">是否发送询价邮件</span>
      <el-switch v-model="form.isMail" :active-value="1" :inactive-value="0" @change="">
      </el-switch>
    </el-divider>
    
    <!-- 合作方采购信息 -->
    <div v-if="form.inquiryType === 1">
      <el-divider content-position="left"
        ><span style="color: var(--el-color-primary)">备注</span></el-divider
      >
      <el-form :model="partnerForm" label-width="120px">
        <el-row :gutter="20">
          <!-- <el-col :span="12">
            <el-form-item label="收货人" required>
              <el-input v-model="partnerForm.receiver" placeholder="请输入收货人"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系方式" required>
              <el-input v-model="partnerForm.contactPhone" placeholder="请输入联系方式"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="收货地址" required>
              <el-input v-model="partnerForm.address" placeholder="请输入收货地址"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期收货时间" required>
              <el-date-picker
                v-model="partnerForm.expectedDeliveryTime"
                type="date"
                placeholder="选择预期收货时间"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col> -->
          <!-- 备注 -->
          <el-col :span="24">
            <el-form-item label="备注">
              <el-input type="textarea" v-model="partnerForm.remark" placeholder="请输入备注"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
  <div class="avue-dialog__footer">
    <el-button @click="$emit('cancel')">取 消</el-button>
    <el-button @click="submit" type="primary">{{ form.inquiryType === 0 ? '生成询价单' : '提交采购' }}</el-button>
  </div>
  <wfSupplierSelect ref="wfSupplierSelectRef" @onConfirm="handleConfirm"></wfSupplierSelect>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import {useRoute} from 'vue-router';
import { watch, getCurrentInstance } from 'vue';
import wfSupplierSelect from '@/components/Y-UI/wf-supplier-select.vue';
const props = defineProps(['tableData']);
const emit = defineEmits(['cancel']);
const { proxy } = getCurrentInstance();
const route = useRoute();
let option = ref({
  header: false,
  menu: false,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,

      span: 6,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,

      span: 18,
      type: 'input',
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
      span: 6,
    },

    {
      label: '数量',
      prop: 'number',
      overHidden: true,

      span: 18,
      type: 'input',
    },
  ],
});
watch(
  () => props.tableData,
  val => {
    console.log(val);

    if (val) {
      getSupplierList();
    }
  },
  {
    immediate: true,
  }
);
let supplierData = ref([{ isChecked: true }]);
function getSupplierList() {
  axios
    .get('/api/vt-admin/supplier/pageForProduct', {
      params: {
        productIds: props.tableData.map(item => item.productId).join(','),
      },
    })
    .then(res => {
      supplierData.value = res.data.data.records.map(item => {
        return {
          ...item,
          isChecked: true,
          supplierContactId:
            item.supplierConcatVOList &&
            item.supplierConcatVOList.find(item => item.concatTypeName == '销售对接人')?.id,
        };
      });
    });
}

let form = ref({
  overDays: 1,
  isMail: 1,
  inquiryType: 0, // 默认为生成询价单
});

let partnerForm = ref({
  receiver: '',
  contactPhone: '',
  address: '',
  expectedDeliveryTime: '',
  remark: '',
});

function handleTypeChange(val) {
  console.log('询价单类型变更:', val);
}
function submit() {
  if(form.value.inquiryType === 0){
    const supplierList = supplierData.value
    .filter(item => item.isChecked)
    .map(item => {
      return {
        supplierId: item.id,
        supplierContactId: item.supplierContactId,
      };
    });
  if (supplierList.length === 0) {
    return ElMessage.error('请选择供应商');
  }

  const data = {
    ...form.value,
    //   contactPerson: userInfo.value.user_id,
    //   contactPhone: res.data.data.phone,
    inquirySource: 0,
    purchaseId: props.purchaseId,
    inquiryStatus: 1,
    detailDTOList: props.tableData.map(item => {
      return {
        ...item,
        id: null,
        productId: item.productId,
        orderDetailId: item.id,
      };
    }),
    supplierList,
  };
  
 
  axios
    .post(`/api/vt-admin/purchaseInquiry/${data.id ? 'update' : 'save'}`, data)
    .then(res => {
      if (res.data.code == 200) {
        ElMessage.success(res.data.msg);
        emit('cancel', true);
      }
    })
    .catch(err => {});

  }

     // 如果是合作方采购，添加收货信息
  if (form.value.inquiryType === 1) {
    // // 验证收货信息是否完整
    // if (!partnerForm.value.receiver) {
    //   return ElMessage.error('请输入收货人');
    // }
    // if (!partnerForm.value.contactPhone) {
    //   return ElMessage.error('请输入联系方式');
    // }
    // if (!partnerForm.value.address) {
    //   return ElMessage.error('请输入收货地址');
    // }
    // if (!partnerForm.value.expectedDeliveryTime) {
    //   return ElMessage.error('请选择预期收货时间');
    // }
    
    const data = {
      id:route.query.id,
      detailIdList:props.tableData.map(item => item.id),
      remark: partnerForm.value.remark,
    }
    // // 添加合作方采购相关信息
    // data.receiver = partnerForm.value.receiver;
    // data.contactPhone = partnerForm.value.contactPhone;
    // data.address = partnerForm.value.address;
    // data.expectedDeliveryTime = partnerForm.value.expectedDeliveryTime;
    axios.post('/api/vt-admin/purchaseOrder/toCooperationPurchase'  ,data).then(res => {
      if (res.data.code == 200) {
        ElMessage.success(res.data.msg);
        emit('cancel', true);
      }
    })
  }
}
let wfSupplierSelectRef = ref();
function addSupplier() {
  wfSupplierSelectRef.value.visible = true;
}
function handleConfirm(id) {
  axios
    .get('/api/vt-admin/supplier/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      supplierData.value.push({
        ...res.data.data,
        isChecked: true,
        supplierContactId:
          res.data.data.supplierConcatVOList &&
          res.data.data.supplierConcatVOList.find(item => item.concatTypeName == '销售对接人')?.id,
      });
    });
}
function addEmail(id, list) {
  ;
  proxy.$refs.dialogForm.show({
    title: '添加邮箱',
    option: {
      column: [
        {
          label: '邮箱',
          prop: 'mail',
          rules: [
            {
              required: true,
              message: '请输入邮箱',
              trigger: 'blur',
              type:'email'
            },
          ],
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/supplierConcat/update', {
          id: id,
          mail: res.data.mail,
        })
        .then(r => {
          ;
          proxy.$message.success(r.data.msg);
          res.close();
          list.find(item => item.id == id).mail = res.data.mail;
          console.log(list,supplierData);
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>
