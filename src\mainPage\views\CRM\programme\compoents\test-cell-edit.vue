<template>
  <div style="padding: 20px;">
    <h2>单元格编辑功能测试</h2>
    
    <el-card style="margin-bottom: 20px;">
      <h3>测试说明</h3>
      <p>双击下面表格中的单元格，应该会弹出编辑窗口</p>
    </el-card>

    <el-table :data="testData" border style="width: 100%">
      <el-table-column prop="index" label="序号" width="80" />
      <el-table-column label="产品名称" width="200">
        <template #default="{ row }">
          <div 
            @dblclick="handleCellDoubleClick({ field: 'customProductName', value: row.customProductName, product: row })"
            style="cursor: pointer; padding: 8px;"
          >
            {{ row.customProductName }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="品牌" width="150">
        <template #default="{ row }">
          <div 
            @dblclick="handleCellDoubleClick({ field: 'productBrand', value: row.productBrand, product: row })"
            style="cursor: pointer; padding: 8px;"
          >
            {{ row.productBrand }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="数量" width="100">
        <template #default="{ row }">
          <div 
            @dblclick="handleCellDoubleClick({ field: 'number', value: row.number, product: row })"
            style="cursor: pointer; padding: 8px; text-align: right;"
          >
            {{ row.number }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="设备单价" width="120">
        <template #default="{ row }">
          <div 
            @dblclick="handleCellDoubleClick({ field: 'sealPrice', value: row.sealPrice, product: row })"
            style="cursor: pointer; padding: 8px; text-align: right;"
          >
            {{ row.sealPrice }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="备注">
        <template #default="{ row }">
          <div 
            @dblclick="handleCellDoubleClick({ field: 'remark', value: row.remark, product: row })"
            style="cursor: pointer; padding: 8px;"
          >
            {{ row.remark }}
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 单元格编辑弹窗 -->
    <CellEditDialog
      v-model="cellEditDialog.visible"
      :field-name="cellEditDialog.fieldName"
      :field-value="cellEditDialog.fieldValue"
      :product="cellEditDialog.product"
      @confirm="handleCellEditConfirm"
    />

    <el-card style="margin-top: 20px;">
      <h3>当前数据</h3>
      <pre>{{ JSON.stringify(testData, null, 2) }}</pre>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import CellEditDialog from './CellEditDialog.vue'

// 测试数据
const testData = ref([
  {
    uuid: '1',
    customProductName: '测试产品1',
    productBrand: '品牌A',
    number: 10,
    sealPrice: 100.50,
    remark: '这是备注1'
  },
  {
    uuid: '2',
    customProductName: '测试产品2',
    productBrand: '品牌B',
    number: 5,
    sealPrice: 200.00,
    remark: '这是备注2'
  },
  {
    uuid: '3',
    customProductName: '测试产品3',
    productBrand: '品牌C',
    number: 8,
    sealPrice: 150.75,
    remark: '这是备注3'
  }
])

// 单元格编辑弹窗状态
const cellEditDialog = ref({
  visible: false,
  fieldName: '',
  fieldValue: '',
  product: null
})

// 处理单元格双击事件
function handleCellDoubleClick({ field, value, product }) {
  console.log('双击单元格:', { field, value, product })
  
  // 设置弹窗数据
  cellEditDialog.value = {
    visible: true,
    fieldName: field,
    fieldValue: value,
    product: product
  }
}

// 处理单元格编辑确认
function handleCellEditConfirm({ field, value, product }) {
  console.log('确认编辑:', { field, value, product })
  
  // 更新产品数据
  if (product && field) {
    // 对于数字字段，确保转换为数字类型
    if (['number', 'sealPrice'].includes(field)) {
      product[field] = Number(value) || 0
    } else {
      product[field] = value
    }
    
    console.log('更新后的产品:', product)
  }
}
</script>

<style scoped>
.el-table td {
  padding: 0 !important;
}

.el-table td > div {
  transition: background-color 0.2s ease;
}

.el-table td > div:hover {
  background-color: #f5f7fa;
}
</style>
