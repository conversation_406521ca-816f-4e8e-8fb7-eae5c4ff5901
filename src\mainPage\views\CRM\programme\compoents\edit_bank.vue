<template>
  <div class="wrap">
    <div
      :style="{ height: isFold ? '50px' : 'auto' }"
      style="overflow: hidden; transition: all 0.3s"
    >
      <el-row :gutter="10" v-if="!isFold">
        <el-col :span="5">
          <el-card
            :body-style="{ padding: 0 }"
            shadow="never"
            style="
              height: 100%;
              overflow-y: auto;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
            "
          >
            <div style="font-size: 14px; font-weight: bolder">所有汇总:</div>
            <div style="display: flex; gap: 20px">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  成本金额：
                  <span style="color: #000">{{ allModuleTotal.costTotal.toFixed(2) }}</span>
                </div>
                <div style="color: #666">
                  总金额：
                  <span style="color: #000">{{ (allModuleTotal.total * 1).toFixed(2) }}</span>
                </div>

                <div style="color: #666">
                  客户报价：
                  <span>{{
                    (allModuleTotal.total - ((form.discountsPrice * 1).toFixed(2) || 0))?.toFixed(2)
                  }}</span>
                </div>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  毛利润 ：<span>{{
                    (allModuleTotal.total - allModuleTotal.costTotal).toFixed(2)
                  }}</span>
                </div>
                <div style="color: #666">
                  折减：
                  <span style="color: var(--el-color-danger)">
                    {{ (form.discountsPrice * 1).toFixed(2) || `0.00` }}
                  </span>
                  <el-button
                    @click="editDiscountsPrice"
                    type="primary"
                    icon="edit"
                    v-if="$route.query.type != 'detail' && !props.dialogView"
                    text
                  ></el-button>
                </div>
                <div style="color: #666">
                  毛利润率：
                  <span>{{
                    (
                      (allModuleTotal.total -
                        ((form.discountsPrice * 1).toFixed(2) || 0) -
                        allModuleTotal.costTotal) /
                      (allModuleTotal.total - ((form.discountsPrice * 1).toFixed(2) || 0))
                    ).toFixed(2)
                  }}</span>
                </div>
              </div>
            </div>
          </el-card></el-col
        >
        <el-col :span="6">
          <el-card
            :body-style="{ padding: 0 }"
            shadow="never"
            style="
              height: 100%;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
              overflow-y: auto;
            "
          >
            <div v-if="form.currentModule > 0">
              <div style="font-size: 14px; font-weight: bolder">
                当前模块汇总:<el-text type="primary">{{
                  moduleDTOList.find((item, index) => index == form.currentModule).moduleName
                }}</el-text>
                <el-icon
                  v-if="$route.query.type != 'detail' && !props.dialogView"
                  @click="addModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer"
                  title="编辑"
                  ><edit
                /></el-icon>
                <el-icon
                  v-if="$route.query.type != 'detail' && !props.dialogView"
                  @click="copyModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer"
                  title="复制模块"
                  ><CopyDocument
                /></el-icon>
                <el-icon
                  @click="delModule"
                  title="删除模块"
                  v-if="$route.query.type != 'detail' && !props.dialogView"
                  style="margin-left: 5px; cursor: pointer; color: var(--el-color-danger)"
                  ><delete
                /></el-icon>
                <el-icon
                  @click="saveModuleTemplate"
                  title="保存模板"
                  style="margin-left: 5px; cursor: pointer; color: var(--el-color-primary)"
                  ><Document
                /></el-icon>
              </div>
              <div style="display: flex; gap: 20px">
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                  "
                >
                  <div style="color: #666">
                    设备金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalProductPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    人工金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalLaborCost.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    其他金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalQtPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    延保金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalYbPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666" v-if="!form.isHasTax">
                    税金：
                    <span style="color: #000">{{ currentModuleTotal.taxPrice.toFixed(2) }}</span>
                  </div>
                  <div style="color: #666">
                    报价金额：
                    <span style="color: #000">{{ currentModuleTotal.total.toFixed(2) }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润 ：<span style="color: black">{{
                      (currentModuleTotal.total - currentModuleTotal.costTotal).toFixed(2)
                    }}</span>
                  </div>
                </div>
                <div style="display: flex; flex-direction: column; justify-content: space-between">
                  <div style="color: #666">
                    设备成本金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalCostPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    人工成本金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalRgcbPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666" v-if="!form.isHasTax">
                    成本税金：
                    <span style="color: #000">{{
                      currentModuleTotal.costTaxPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    其他成本金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalQtcbPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    延保成本金额：
                    <span style="color: #000">{{
                      currentModuleTotal.totalYbcbPrice.toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    报价成本金额：
                    <span style="color: #000">{{ currentModuleTotal.costTotal.toFixed(2) }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润率：
                    <span style="color: black">{{
                      isNaN(
                        (
                          (currentModuleTotal.total - currentModuleTotal.costTotal) /
                          currentModuleTotal.total
                        ).toFixed(2)
                      )
                        ? '--'
                        : (
                            (currentModuleTotal.total - currentModuleTotal.costTotal) /
                            currentModuleTotal.total
                          ).toFixed(2)
                    }}</span>
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 10px;
                  "
                ></div>
              </div>
            </div> </el-card
        ></el-col>
        <el-col :span="5">
          <el-card
            :body-style="{ padding: 0 }"
            shadow="never"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box; font-size: 14px"
          >
            <div style="font-size: 14px; font-weight: bolder">
              产品参考信息:
              <el-text type="primary" style="overflow: hidden; white-space: nowrap">{{
                referInfo.productName
              }}</el-text>
            </div>
            <div style="display: flex; gap: 10px">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  最低销售价： <span style="color: #000">{{ referInfo.minSealPrice }}</span>
                </div>
                <div style="color: #666">
                  最近设备销售价： <span style="color: #000">{{ referInfo.preSealPrice }}</span>
                </div>
                <!-- <div style="color: #666">
                最近人工销售价： <span style="color: #000">{{ `--` }}</span>
              </div>
              <div style="color: #666">
                最近其他销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->

                <div style="color: #666">
                  参考销售价： <span style="color: #000">{{ referInfo.referSealPrice }}</span>
                </div>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <!-- <div style="color: #666">
                最近延保销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->
              </div>
            </div>
          </el-card></el-col
        >
        <el-col :span="4">
          <el-card
            shadow="never"
            :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
          >
            <div
              style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              快捷配置:
              <el-switch
                v-model="form.isLock"
                size="small"
                v-if="$route.query.type != 'detail' && !props.dialogView"
                active-text="锁定"
                inactive-text="解锁"
              />
              <el-button type="primary" text size="small" @click="colmunDrawer = !colmunDrawer"
                >显示设置</el-button
              >
            </div>
            <el-row>
              <el-form>
                <el-form-item style="margin: 0" label="设备利润比:">
                  <el-col :span="12">
                    <el-popconfirm
                      hide-icon
                      @confirm="setPrice(form.productTax)"
                      title="修改将会重新计算所有产品设备单价，是否确认?"
                    >
                      <template #reference>
                        <el-input
                          size="small"
                          :disabled="
                            form.isLock || $route.query.type == 'detail' || props.dialogView
                          "
                          v-model="form.productTax"
                          placeholder="请输入"
                        ></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
                <el-form-item style="margin: 0" label="人工利润比:">
                  <el-col :span="12">
                    <el-popconfirm
                      hide-icon
                      @confirm="setLaborPrice(form.laborTax)"
                      title="修改将会重新计算所有产品人工单价，是否确认?"
                    >
                      <template #reference>
                        <el-input
                          size="small"
                          :disabled="
                            form.isLock || $route.query.type == 'detail' || props.dialogView
                          "
                          v-model="form.laborTax"
                          placeholder="请输入"
                        ></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
              </el-form>
            </el-row> </el-card
        ></el-col>
        <el-col :span="4">
          <el-card
            shadow="never"
            :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
          >
            <div
              style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              导出配置:
              <el-switch
                style="margin-right: 10px"
                v-model="form.isHasTax"
                size="small"
                v-if="$route.query.type != 'detail' && !props.dialogView"
                active-text="含税"
                inactive-text="未税"
                :active-value="1"
                :inactive-value="0"
              />
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              设备税率：
              <el-input-number
                size="small"
                style="width: 60%"
                step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.productRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              人工税率：
              <el-input-number
                size="small"
                style="width: 60%"
                step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.labourRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              延保税率：
              <el-input-number
                size="small"
                step="0.01"
                style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.warrantyRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              其他税率：
              <el-input-number
                size="small"
                step="0.01"
                style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.otherRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row style="height: 50px">
        <el-col :span="24">
          <el-card
            shadow="never"
            :body-style="{ padding: '5px', width: '100%', overflowX: 'auto' }"
            :key="randomKey"
          >
            <el-radio-group
              @change="handleModuleChange()"
              style="margin-right: 10px; flex-wrap: nowrap"
              v-model="form.currentModule"
              size="default"
              ref="sort-buttons"
            >
              <el-radio-button
                v-for="(item, index) in moduleDTOList"
                :key="item.uuid"
                :label="index"
                :class="{ move1: index != 0 }"
                >{{ item.moduleName }}</el-radio-button
              >
              <el-button
                type=""
                size="default"
                title="添加模块"
                v-if="$route.query.type != 'detail' && !props.dialogView"
                @click="addModule"
                style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="plus"
              ></el-button>
              <el-button
                type=""
                size="default"
                title="从模板库选择"
                v-if="$route.query.type != 'detail' && !props.dialogView"
                @click="addTemplate(1)"
                style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="CopyDocument"
              ></el-button>
            </el-radio-group>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-row id="table_box">
      <div
        style="
          display: flex;
          cursor: pointer;
          justify-content: center;
          background-color: #ccc;
          width: 100%;
        "
        ref="heightAutoRef"
        @click="handleResetHeight"
      >
        <el-icon :style="{ transform: `rotate(${isFold ? '-90deg' : '90deg'})` }"
          ><DArrowLeft
        /></el-icon>
      </div>
    </el-row>
    <mySplit v-model="splitValue" min="0">
      <template v-slot:left>
        <div
          style="height: 100%; position: relative; padding: 5px; box-sizing: border-box"
          shadow="never"
        >
          <div
            v-show="moduleDTOList[form.currentModule].isHasClassify != 1 && form.currentModule != 0"
            ref="category_box"
          >
            <el-tag
              effect="plain"
              plain
              size="large"
              @click="handleScroll(index)"
              style="
                width: 100%;
                margin-bottom: 5px;
                cursor: pointer;
                overflow: hidden;
                align-items: center;
                justify-content: flex-start;
              "
              v-for="(item, index) in categoryList"
              :title="item"
              :key="item"
            >
              <el-icon v-if="$route.query.type != 'detail' && !props.dialogView" class="sort">
                <sort></sort>
              </el-icon>
              {{ item }}</el-tag
            >
          </div>
          <div
            v-show="moduleDTOList[form.currentModule].isHasClassify != 1 && form.currentModule != 0"
            style="display: flex; gap: 0; justify-content: space-between"
          >
            <el-button
              type=""
              title="添加分类"
              v-if="$route.query.type != 'detail' && !props.dialogView"
              style="width: 50%"
              @click="addCategory"
              icon="plus"
            ></el-button>
            <el-button
              type=""
              title="从模板库选择"
              v-if="$route.query.type != 'detail' && !props.dialogView"
              style="width: 50%"
              @click="addTemplate(2)"
              icon="CopyDocument"
            ></el-button>
          </div>
          <!-- <div
            style="
              height: 50%;
              width: auto;
              box-shadow: var(--el-box-shadow-light);
              position: absolute;
              bottom: 0;
              left: 5px;
              box-sizing: border-box;
              right: 5px;
              padding: 5px;
            "
          >
            <div style="height: 20px; overflow: hidden">
              <el-text type="primary">{{ currentProduct.customProductName }}</el-text>
            </div>
            <textarea
              style="
                height: calc(100% - 20px);
                overflow: auto;
                font-size: 14px;
                border: none;
                resize: none;
              "
              :disabled="$route.query.type == 'detail'"
              v-model="currentProduct.customProductDescription"
            >
            </textarea>
          </div> -->
        </div>
      </template>
      <template v-slot:right>
        <div
          ref="tableCard"
          class="myCard"
          shadow="never"
          :body-style="{ padding: 0 }"
          style="
            height: 100%;
            overflow: scroll;
            padding: 0;
            scroll-padding-top: 0px;
            background-color: #fff;
          "
        >
          <div v-if="form.currentModule == 0">
            <el-table
              class="avue_table avue-crud"
              style="margin-top: 0"
              :data="allData"
              ref="allTableRef"
              @select="handleSelect"
              @select-all="handleSelectAll"
              row-key="uuid"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              border
            >
              <!-- <el-table-column type="expand" width="50">
                <template #default="{ row }">
                  <div style="margin-left: 50px">
                    <el-table class="avue_table" :show-header="false" :data="row.children" border>
                      <el-table-column
                        label="序号"
                        width="80"
                        type="index"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        label="子系统名称"
                        prop="classify"
                        align="center"
                      ></el-table-column>
                      <el-table-column label="设备金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalProductPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="人工金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalLaborCost.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="延保金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalYbPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="其它金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalQtPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="报价金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.total.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="设备成本" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalCostPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="人工成本" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalRgcbPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="延保成本" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalYbcbPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="其它成本" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalQtcbPrice.toFixed(2) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="成本金额" prop="costTotal" align="center">
                        <template #default="{ row }">
                          {{ row.costTotal.toFixed(2) }}
                        </template></el-table-column
                      >
                      <el-table-column label="毛利润" prop="costTotal" align="center">
                        <template #default="{ row }">
                          {{ (row.total - row.costTotal).toFixed(2) }}
                        </template>
                      </el-table-column>

                      <el-table-column label="毛利润率" prop="costTotal" align="center">
                        <template #default="{ row }">
                          {{
                            isNaN(((row.total - row.costTotal) / row.total).toFixed(2))
                              ? '--'
                              : ((row.total - row.costTotal) / row.total).toFixed(2)
                          }}
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </el-table-column> -->
              <el-table-column type="selection" v-if="$route.query.type != 'detail' && !props.dialogView"  width="55">
                <template #header> 111 </template>
              </el-table-column>
              <el-table-column
                label="序号"
                width="80"
                type="index"
                align="center"
              ></el-table-column>
              <el-table-column label="模块名称" width="200" prop="moduleName" align="center"> </el-table-column>
              <el-table-column label="设备金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalProductPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="人工金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalLaborCost.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="延保金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalYbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="其它金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalQtPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="报价金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.total.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="设备成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalCostPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="人工成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalRgcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="延保成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalYbcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="其它成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalQtcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="成本金额" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{ row.costTotal.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="毛利润" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{ (row.total - row.costTotal).toFixed(2) }}
                </template>
              </el-table-column>

              <el-table-column label="毛利润率" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{
                    isNaN(((row.total - row.costTotal) / row.total).toFixed(2))
                      ? '--'
                      : ((row.total - row.costTotal) / row.total).toFixed(2)
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div style="width: 100%" v-else>
            <table style="text-align: center; position: relative" border="1">
              <colgroup>
                <col width="100px" />
                <!-- <col width="100px" /> -->
                <col width="150px" />
                <col width="80px" />
                <col width="200px" />
                <col width="150px" />
                <col width="80px" class="center" />
                <col width="80px" />
                <col
                  v-for="item in columnHideData?.filter(item => item.isShow)"
                  :key="item.value"
                  :width="`${item.width}px`"
                />
              </colgroup>
              <thead style="position: sticky; top: 0px; z-index: 100; background-color: #fff">
                <tr style="font-weight: bolder; color: black">
                  <td>序号</td>
                  <!-- <td>分类</td> -->
                  <td>产品名称</td>
                  <td>品牌</td>
                  <td>规格型号</td>
                  <td>产品描述</td>

                  <td class="center">单位</td>
                  <td class="center">数量</td>
                  <td
                    :class="item.align || 'right'"
                    v-for="item in columnHideData?.filter(item => item.isShow)"
                    :key="item.value"
                  >
                    {{ item.value }}
                  </td>
                </tr>
              </thead>

              <tbody :ref="index + '-cateGory'" v-for="(item, index) in tableData" :key="randomKey">
                <tr class="category">
                  <td colspan="24">
                    <span v-if="moduleDTOList[form.currentModule].isHasClassify != 1">{{
                      item.classify
                    }}</span>
                    <el-icon
                      v-if="
                        $route.query.type != 'detail' &&
                        moduleDTOList[form.currentModule].isHasClassify != 1 &&
                        !props.dialogView
                      "
                      @click="editCategory(item.classify)"
                      style="margin-left: 5px; cursor: pointer"
                      ><edit
                    /></el-icon>
                    <el-button
                      v-if="currentIndex == item.classify"
                      @click="currentIndex = null"
                      type="primary"
                      text
                      >退出编辑模式</el-button
                    >
                    <productSelectDrop
                      v-if="$route.query.type != 'detail' && !props.dialogView"
                      @select="
                        id => {
                          form.currentclassify = item.classify;
                          handleProductSelectConfirm(id);
                        }
                      "
                      style="margin-left: 5px"
                    ></productSelectDrop>

                    <el-button
                      class="delete_category"
                      type="danger"
                      size="small"
                      v-if="
                        $route.query.type != 'detail' &&
                        moduleDTOList[form.currentModule].isHasClassify != 1 &&
                        !props.dialogView
                      "
                      @click="delCategory(item.classify)"
                      style="margin-left: 10px"
                      circle
                      title="删除"
                      icon="delete"
                    ></el-button>

                    <el-button
                      class="delete_category"
                      type="primary"
                      size="small"
                      v-if="
                        $route.query.type != 'detail' &&
                        moduleDTOList[form.currentModule].isHasClassify != 1 &&
                        !props.dialogView
                      "
                      @click="copyCategory(item.classify)"
                      style="margin-left: 10px"
                      circle
                      title="复制"
                      icon="CopyDocument"
                    ></el-button>
                    <el-button
                      class="delete_category"
                      type="primary"
                      v-if="moduleDTOList[form.currentModule].isHasClassify != 1"
                      size="small"
                      @click="saveCategoryTemplate(item.classify)"
                      style="margin-left: 10px"
                      circle
                      title="存为分类模板"
                      icon="Document"
                    ></el-button>
                    <el-button
                      class="delete_category"
                      type="primary"
                      v-if="
                        moduleDTOList[form.currentModule].isHasClassify != 1 &&
                        $route.query.isFromOther == 1
                      "
                      size="small"
                      @click="exportProductByFromOhter(item.productList)"
                      style="margin-left: 10px"
                      circle
                      title="导入至分类"
                      icon="Position"
                    ></el-button>
                  </td>
                </tr>
                <tr
                  :class="[
                    'cell_hover',
                    'allow_td',
                    { 'current-editing': isCurrentEditingProduct(i) }
                  ]"
                  @click="setReferPrice(i)"
                  v-for="(i, ins) in item.productList"
                  :key="item.uuid"
                  :id="`tr_${i.uuid}`"
                >
                  <td class="index_product">
                    <div style="display: flex; align-items: center">
                      <span class="index_product_span">{{ customIndex(ins, index) }}</span>
                      <el-button
                        class="delete_category"
                        type="primary"
                        v-if="$route.query.isFromOther == 1"
                        size="small"
                        @click="exportProductByFromOhter([i])"
                        style="margin-left: 10px"
                        circle
                        title="导入至分类"
                        icon="Position"
                      ></el-button>
                      <span class="index_product_span source">{{
                        ['库', '模', '询', '空', '历'][i.source]
                      }}</span>
                    </div>

                    <div style="display: flex; align-items: center">
                      <el-button
                        @click="deleteProduct(i)"
                        class="delete_product"
                        type="danger"
                        size="small"
                        v-if="$route.query.type != 'detail' && !props.dialogView"
                        circle
                        icon="delete"
                      ></el-button>
                      <el-icon
                        v-if="$route.query.type != 'detail' && !props.dialogView"
                        class="sort delete_product"
                      >
                        <sort></sort>
                      </el-icon>
                      <el-icon
                        v-if="$route.query.type != 'detail' && !props.dialogView"
                        class="sort delete_product"
                        style="color: var(--el-color-primary)"
                      >
                        <edit></edit>
                      </el-icon>
                    </div>
                  </td>
                  <!-- <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                    <div
                      v-if="!i.productId && i.source == 1 && i.categoryId"
                      style="display: flex; justify-content: center; align-items: center"
                    >
                      <el-tag
                        effect="plain"
                        style="margin-right: 5px"
                        v-if="!i.productId && i.source == 1 && i.categoryId"
                        >{{ i.categoryName }}</el-tag
                      >
                    </div>
                  </td> -->
                  <!-- 名称 -->
                  <td
                    :class="{
                      active: currentIndex === item.classify,
                      error: !i.customProductName,
                    }"
                    @click="handleRowClick(item)"
                  >
                    <el-tooltip
                      :content="'产品名称不能为空'"
                      effect="light"
                      placement=""
                      :disabled="i.customProductName"
                    >
                      <div
                        style="white-space: wrap; overflow: hidden; height: 100%"
                        @focus="
                          currentModelKey = 'customProductName';
                        "
                        @blur="
                          e => {
                            i.customProductName = e.target.innerText;
                          }
                        "
                        :contenteditable="
                          $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                        "
                      >
                        {{ i.customProductName }}
                      </div>
                    </el-tooltip>
                  </td>
                  <!-- 品牌 -->
                  <td>
                    <el-popover
                      placement="bottom-start"
                      :offset="10"
                      :show-after="150"
                      :disabled="!(!i.productId && i.source == 1 && i.categoryId)"
                      style="padding: 0"
                      trigger="focus"
                    >
                      <template #default>
                        <ul
                          class="el-scrollbar__view el-select-dropdown__list"
                          style="max-height: 200px; overflow-y: auto"
                        >
                          <li
                            @click.capture="handleProductBrandClick(i, item)"
                            v-for="item in brandList"
                            class="el-select-dropdown__item"
                          >
                            {{ item }}
                          </li>
                        </ul>
                      </template>
                      <template #reference>
                        <div
                          style="white-space: wrap; overflow: auto; height: 100%"
                          @blur="
                            e => {
                              i.productBrand = e.target.innerText;
                            }
                          "
                          @focus="getBrand(i)"
                          :contenteditable="
                            $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                          "
                        >
                          {{ i.productBrand }}
                        </div>
                      </template>
                    </el-popover>
                  </td>
                  <!-- 型号 -->
                  <td
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                  >
                    <el-popover
                      placement="bottom-start"
                      :offset="10"
                      width="auto"
                      :disabled="!(!i.productId && i.source == 1 && i.categoryId)"
                      :show-after="150"
                      style="padding: 0"
                      trigger="focus"
                    >
                      <template #default>
                        <ul
                          class="el-scrollbar__view el-select-dropdown__list"
                          style="max-height: 200px; overflow-y: auto"
                        >
                          <li
                            @click="handleSpecificationClick(i, item)"
                            v-for="item in productSpecificationList"
                            class="el-select-dropdown__item"
                            style="height: 50px"
                          >
                            <div style="height: 25px; line-height: 25px">
                              <span>名称：</span>{{ item.productName }}
                            </div>
                            <div style="height: 25px; line-height: 25px">
                              <span>型号：</span>{{ item.productSpecification }}
                            </div>
                          </li>
                        </ul>
                      </template>
                      <template #reference>
                        <div
                          style="white-space: nowrap; overflow: auto;"
                          @focus="getProductSpecificationList(i)"
                          @blur="
                            e => {
                              i.customProductSpecification = e.target.innerText;
                            }
                          "
                          :contenteditable="
                            $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                          "
                        >
                          {{ i.customProductSpecification }}
                        </div>
                      </template>
                    </el-popover>
                  </td>
                  <!-- 描述 -->
                  <td
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.customProductDescription"
                      type="textarea"
                      placeholder=""
                    ></el-input>
                    <textarea
                      style="
                        white-space: nowrap;
                        white-space: nowrap;
                        overflow: hidden;
                        outline: none;
                        border: none;
                        resize: none;
                        height: 100%;
                      "
                      @focus="
                        currentModelKey = 'customProductDescription';
                      "
                      :disabled="$route.query.type == 'detail' || props.dialogView"
                      v-model="i.customProductDescription"
                      v-else
                    >
                    </textarea>
                  </td>

                  <!-- 单位 -->
                  <td class="center">
                    <div
                      @blur="
                        e => {
                          i.customUnit = e.target.innerText;
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                    >
                      {{ i.customUnit || i.unitName }}
                    </div>
                  </td>
                  <!-- 数量 -->
                  <td
                    :class="{
                      active: currentIndex === item.classify,
                      error: i.number <= 0 || !isNumber(i.number),
                    }"
                    style="position: relative"
                    @click="handleRowClick(item)"
                    class="center"
                  >
                    <el-tooltip
                      :content="!isNumber(i.number) ? '格式有误' : '数量不能小于等于0'"
                      effect="light"
                      placement=""
                      :disabled="!(i.number <= 0 || !isNumber(i.number))"
                    >
                      <div
                        @blur="
                          e => {
                            i.number = e.target.innerText;
                          }
                        "
                        :contenteditable="
                          $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                        "
                      >
                        {{ i.number }}
                      </div>
                    </el-tooltip>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 设备单价 -->
                  <td
                    v-if="isTrue('设备单价')"
                    :class="{
                      active: currentIndex === item.classify,
                      error:
                        i.sealPrice == '' ||
                        (i.sealPrice * 1 <= i.minSealPrice * 1 && i.sealPrice * 1 !== 0) ||
                        (i.sealPrice * 1 <= i.costPrice * 1 && i.sealPrice * 1 !== 0) ||
                        !isNumber(i.sealPrice),
                    }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                    class="right"
                  >
                    <el-tooltip
                      :content="
                        i.sealPrice == ''
                          ? '单价不能为空'
                          : !isNumber(i.sealPrice)
                          ? '格式有误'
                          : i.sealPrice * 1 < i.minSealPrice * 1
                          ? '单价不能小于最低销售单价'
                          : i.sealPrice * 1 < i.costPrice * 1
                          ? '单价不能小于成本单价'
                          : ''
                      "
                      placement=""
                      effect="light"
                      :disabled="
                        !(
                          i.sealPrice == '' ||
                          (i.sealPrice * 1 <= i.minSealPrice * 1 && i.sealPrice * 1 !== 0) ||
                          (i.sealPrice * 1 <= i.costPrice * 1 && i.sealPrice * 1 !== 0) ||
                          !isNumber(i.sealPrice)
                        )
                      "
                    >
                      <div
                        @blur="
                          e => {
                            i.sealPrice = e.target.innerText && e.target.innerText.trim();
                          }
                        "
                        :contenteditable="
                          $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                        "
                      >
                        {{ i.sealPrice }}
                      </div>
                    </el-tooltip>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 设备金额 -->
                  <td class="right" v-if="isTrue('设备金额')">
                    {{ (i.sealPrice * i.number).toFixed(2) }}
                  </td>
                  <!-- 人工单价 -->
                  <td
                    class="right"
                    v-if="isTrue('人工单价')"
                    :class="{ active: currentIndex === item }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.laborCost"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.laborCost = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.laborCost }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 人工金额 -->
                  <td class="right" v-if="isTrue('人工金额')">
                    {{ (i.laborCost * i.number).toFixed(2) }}
                  </td>

                  <!-- 其他单价 -->
                  <td
                    class="right"
                    v-if="isTrue('其他单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item"
                      v-model="i.qthsdj"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.qthsdj = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.qthsdj }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 其他金额 -->
                  <td class="right" v-if="isTrue('其他金额')">
                    {{ (i.qthsdj * i.number).toFixed(2) }}
                  </td>
                  <!-- 延保单价 -->
                  <td
                    class="right"
                    v-if="isTrue('延保单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.ybhsdj"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.ybhsdj = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.ybhsdj }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 延保金额 -->
                  <td class="right" v-if="isTrue('延保金额')">
                    {{ (i.ybhsdj * i.number).toFixed(2) }}
                  </td>
                  <!-- 综合单价 -->
                  <td class="right" v-if="isTrue('综合单价')">
                    {{
                      (
                        (i.sealPrice * 1 + i.laborCost * 1 + i.ybhsdj * 1 + i.qthsdj * 1) *
                        i.number
                      ).toFixed(2)
                    }}
                  </td>
                  <!-- 综合金额 -->
                  <td class="right" v-if="isTrue('综合金额')">
                    {{
                      (
                        (i.sealPrice * 1 + i.laborCost * 1 + i.ybhsdj * 1 + i.qthsdj * 1) *
                        i.number
                      ).toFixed(2)
                    }}
                  </td>

                  <!-- 备注 -->
                  <td
                    class="left"
                    v-if="isTrue('备注')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.remark"
                      type="textarea"
                      placeholder=""
                    ></el-input>
                    <textarea
                      style="
                        white-space: nowrap;
                        overflow: hidden;
                        outline: none;
                        border: none;
                        resize: none;
                        height: 100%;
                      "
                      @focus="
                        currentModelKey = 'remark';
                      "
                      v-model="i.remark"
                      :disabled="$route.query.type == 'detail' || props.dialogView"
                      v-else
                    >
                    </textarea>
                  </td>
                  <!-- 设备成本单价 -->
                  <td
                    class="right"
                    v-if="isTrue('设备成本单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.costPrice"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.costPrice = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.costPrice }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 设备成本金额 -->
                  <td class="right" v-if="isTrue('设备成本金额')">
                    {{ (i.costPrice * i.number).toFixed(2) }}
                  </td>
                  <!-- 人工成本单价 -->
                  <td
                    class="right"
                    v-if="isTrue('人工成本单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.rgcbdj"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.rgcbdj = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.rgcbdj }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 人工成本金额 -->
                  <td class="right" v-if="isTrue('人工成本金额')">
                    {{ (i.rgcbdj * i.number).toFixed(2) }}
                  </td>
                  <!-- 延保成本单价 -->
                  <td
                    class="right"
                    v-if="isTrue('延保成本单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.ybcbdj"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.ybcbdj = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.ybcbdj }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 延保成本金额 -->
                  <td class="right" v-if="isTrue('延保成本金额')">
                    {{ (i.ybcbdj * i.number).toFixed(2) }}
                  </td>
                  <!-- 其他成本单价 -->
                  <td
                    class="right"
                    v-if="isTrue('其他成本单价')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                    style="position: relative"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.qtcbdj"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.qtcbdj = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.qtcbdj }}
                    </div>
                    <!-- 一 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        top: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 二 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        right: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <!-- 四 -->
                    <div
                      style="
                        height: 10px;
                        border-left: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                    <div
                      style="
                        width: 10px;
                        border-top: 1px solid red;
                        position: absolute;
                        bottom: 0;
                        left: 0px;
                        z-index: 10;
                      "
                    ></div>
                  </td>
                  <!-- 其他成本金额 -->
                  <td class="right" v-if="isTrue('其他成本金额')">
                    {{ (i.qtcbdj * i.number).toFixed(2) }}
                  </td>
                  <!-- 综合成本单价 -->
                  <td class="right" v-if="isTrue('综合成本单价')">
                    {{
                      (
                        i.rgcbdj * 1 +
                        (i.specialCostPrice * 1 || i.costPrice * 1) +
                        i.ybcbdj * 1 +
                        i.qtcbdj * 1
                      ).toFixed(2)
                    }}
                  </td>
                  <!-- 综合成本金额 -->
                  <td class="right" v-if="isTrue('综合成本金额')">
                    {{
                      (
                        (i.rgcbdj * 1 +
                          (i.specialCostPrice * 1 || i.costPrice * 1) +
                          i.ybcbdj * 1 +
                          i.qtcbdj * 1) *
                        i.number
                      ).toFixed(2)
                    }}
                  </td>
                  <!-- 专项成本 -->
                  <td
                    class="right"
                    v-if="isTrue('专项成本')"
                    :class="{ active: currentIndex === item.classify }"
                    @click="handleRowClick(item)"
                  >
                    <el-input
                      @blur="handleBlur($event, item)"
                      v-if="currentIndex == item.classify"
                      v-model="i.specialCostPrice"
                      style="width: 100%"
                    ></el-input>
                    <div
                      @blur="
                        e => {
                          i.specialCostPrice = e.target.innerText && e.target.innerText.trim();
                        }
                      "
                      :contenteditable="
                        $route.query.type == 'detail' || props.dialogView ? 'false' : 'true'
                      "
                      v-else
                    >
                      {{ i.specialCostPrice }}
                    </div>
                  </td>
                  <!-- 专项供应商 -->
                  <td v-if="isTrue('专项供应商')">
                    <WfSupplierSelect
                      v-model="i.specialSupplierId"
                      placeholder="请选择专项供应商"
                      :disabled="$route.query.type == 'detail' || props.dialogView"
                      style="width: 100%"
                    ></WfSupplierSelect>
                  </td>
                </tr>
                <tr>
                  <td colspan="24">
                    <el-button
                      type="primary"
                      icon="plus"
                      title="从产品库选择产品"
                      v-if="$route.query.type != 'detail' && !props.dialogView"
                      @click="
                        $refs['product-select'].visible = true;
                        form.currentclassify = item.classify;
                      "
                      style="margin: 2px 0; margin-left: 3px"
                      plain
                      size="default"
                      >库</el-button
                    >
                    <el-button
                      type="primary"
                      icon="plus"
                      title="添加一个空产品"
                      v-if="$route.query.type != 'detail' && !props.dialogView"
                      @click="addEmptyProduct(item)"
                      plain
                      size="default"
                      >空</el-button
                    >
                    <el-button
                      type="primary"
                      icon="plus"
                      title="从询价单导入"
                      v-if="$route.query.type != 'detail' && !props.dialogView"
                      @click="addProductFromSheet(item)"
                      plain
                      size="default"
                      >询</el-button
                    >
                    <el-button
                      type="primary"
                      icon="plus"
                      title="从之前的方案导入"
                      v-if="$route.query.type != 'detail' && !props.dialogView"
                      @click="exportProductByHistory(item)"
                      plain
                      size="default"
                      >导</el-button
                    >
                    <!-- <el-button
                    type="primary"
                    icon="plus"
                    title="从模板库添加"
                    v-if="$route.query.type != 'detail'"
                    @click="addEmptyProduct(item)"
                    plain
                    size="default"
                    >模</el-button
                  > -->
                  </td>
                </tr>
                <tr v-if="index == tableData.length - 1" style="border: none">
                  <td colspan="24" style="height: 200px"></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </template>
    </mySplit>

    <!-- 可拖动的产品编辑面板 -->
    <div
      v-if="showEditPanel && currentProduct.uuid"
      class="floating-edit-panel"
      :style="panelStyle"
    >
      <div class="panel-header" @mousedown="startDrag">
        <div class="header-left">
          <h4>编辑产品信息</h4>
          <div class="product-navigation" v-if="allProducts.length > 0">
            <span class="nav-info">
              {{ currentProductIndex + 1 }} / {{ allProducts.length }}
            </span>
            <el-button
              type="text"
              icon="ArrowLeft"
              @click="switchToPreviousProduct"
              :disabled="currentProductIndex <= 0"
              style="color: #666; margin-left: 8px;"
              title="上一个产品 (←)"
              size="small"
            ></el-button>
            <el-button
              type="text"
              icon="ArrowRight"
              @click="switchToNextProduct"
              :disabled="currentProductIndex >= allProducts.length - 1"
              style="color: #666;"
              title="下一个产品 (→)"
              size="small"
            ></el-button>
          </div>
        </div>
        <div class="header-controls">
          <el-button
            type="text"
            icon="Plus"
            @click="insertEmptyProduct"
            style="color: #409eff; margin-right: 8px;"
            title="在当前产品下方插入空行"
            size="small"
            :disabled="$route.query.type == 'detail' || props.dialogView"
          ></el-button>
          <el-button
            type="text"
            :icon="isCollapsed ? 'ArrowDown' : 'ArrowUp'"
            @click="toggleCollapse"
            style="color: #666; margin-right: 8px;"
            title="折叠/展开"
          ></el-button>
          <el-button
            type="text"
            icon="close"
            @click="closeEditPanel"
            style="color: #666;"
            title="关闭"
          ></el-button>
        </div>
      </div>
      <div class="panel-content" v-show="!isCollapsed">
        <div class="operation-hints">
          <div class="keyboard-hint" v-if="allProducts.length > 1">
            <el-text size="small" type="info">
              <el-icon><ArrowLeft /></el-icon>
              <el-icon><ArrowRight /></el-icon>
              使用左右箭头键快速切换产品
            </el-text>
          </div>
          <div class="insert-hint" v-if="$route.query.type != 'detail' && !props.dialogView">
            <el-text size="small" type="success">
              <el-icon><Plus /></el-icon>
              点击标题栏的加号按钮在当前产品下方插入空行
            </el-text>
          </div>
        </div>
        <el-form :model="currentProduct" label-width="100px" size="small">
          <el-form-item label="产品名称">
            <el-input
              v-model="currentProduct.customProductName"
              placeholder="请输入产品名称"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>

          <el-form-item label="品牌">
            <el-input
              v-model="currentProduct.productBrand"
              placeholder="请输入品牌"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>

          <el-form-item label="规格型号">
            <el-input
              v-model="currentProduct.customProductSpecification"
              placeholder="请输入规格型号"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>

          <el-form-item label="产品描述">
            <el-input
              v-model="currentProduct.customProductDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入产品描述"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>

          <el-form-item label="单位">
            <el-input
              v-model="currentProduct.customUnit"
              :placeholder="currentProduct.unitName || '请输入单位'"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>

          <el-form-item label="数量">
            <el-input-number
              v-model="currentProduct.number"
              :min="0"
              :precision="2"
              placeholder="请输入数量"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="设备单价">
            <el-input-number
              v-model="currentProduct.sealPrice"
              :min="0"
              :precision="2"
              placeholder="请输入设备单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="人工单价">
            <el-input-number
              v-model="currentProduct.laborCost"
              :min="0"
              :precision="2"
              placeholder="请输入人工单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="设备成本单价">
            <el-input-number
              v-model="currentProduct.costPrice"
              :min="0"
              :precision="2"
              placeholder="请输入设备成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="人工成本单价">
            <el-input-number
              v-model="currentProduct.rgcbdj"
              :min="0"
              :precision="2"
              placeholder="请输入人工成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="延保单价">
            <el-input-number
              v-model="currentProduct.ybhsdj"
              :min="0"
              :precision="2"
              placeholder="请输入延保单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="延保成本单价">
            <el-input-number
              v-model="currentProduct.ybcbdj"
              :min="0"
              :precision="2"
              placeholder="请输入延保成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="其他单价">
            <el-input-number
              v-model="currentProduct.qthsdj"
              :min="0"
              :precision="2"
              placeholder="请输入其他单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="其他成本单价">
            <el-input-number
              v-model="currentProduct.qtcbdj"
              :min="0"
              :precision="2"
              placeholder="请输入其他成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView"
              style="width: 100%"
            ></el-input-number>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              v-model="currentProduct.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入备注"
              :disabled="$route.query.type == 'detail' || props.dialogView"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 调整大小控制点 -->
      <div
        class="resize-handle resize-right"
        @mousedown="startResize('right', $event)"
        v-show="!isCollapsed"
      ></div>
      <div
        class="resize-handle resize-bottom"
        @mousedown="startResize('bottom', $event)"
        v-show="!isCollapsed"
      ></div>
      <div
        class="resize-handle resize-corner"
        @mousedown="startResize('corner', $event)"
        v-show="!isCollapsed"
      ></div>
    </div>

    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
      @openIframe="handleOpenIframe"
    >
    </wf-product-select>
    <el-drawer title="列显隐" size="20%" v-model="colmunDrawer">
      <el-table border :data="columnHideData">
        <el-table-column label="列名称" prop="value"></el-table-column>
        <el-table-column label="隐藏/显示">
          <template #default="scope">
            <el-switch v-model="scope.row.isShow" />
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>

    <templateSelect
      ref="templateDialog"
      :templateType="templateType"
      :level="templateType"
      :businessType="$route.query.businessType"
      @change="handleTemplateChange"
    ></templateSelect>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, nextTick, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import productSelectDrop from '../../quotation/compoents/productSelectDrop.vue';
import { deepClone } from '@/utils/util.js';
const { proxy } = getCurrentInstance();
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
import templateSelect from './templateSelect.vue';
import mySplit from '@/components/my-split/my-split.vue';
let currentIndex = ref(null);
const route = useRoute();
let splitValue = ref('0px');
let form = ref({
  currentModule: 0,
  currentclassify: null,
  isHasTax: 1,
});
let oldSplitValue = ref('200px');
watch(
  () => form.value.currentModule,
  val => {
    if (val == 0 || moduleDTOList.value[val].isHasClassify == 1) {
      // oldSplitValue.value = splitValue.value;
      splitValue.value = '0px';
    } else {
      splitValue.value = '300px';
    }
  },
  {}
);
let moduleDTOList = ref([
  { moduleName: '汇总', uuid: randomLenNum(10) },
  //   { moduleName: '默认子项', id: null, detailDTOList: [] },
]); // 所有数据的操作都会存在这里
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  openSheetProductSelect: {
    type: Function,
    default: () => {},
  },
  dialogView: Boolean,
});
watch(
  () => props.data,
  () => {
    if (props.data) {
      form.value = props.data;
      form.value.currentModule =
        form.value.currentModule == 0 || !form.value.currentModule ? 0 : form.value.currentModule;
      moduleDTOList.value = [
        { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
        ...props.data.moduleVOList?.map(item => {
          return {
            ...item,
            uuid: item.id || randomLenNum(10),
            detailDTOList: item.detailVOList.map(item => {
              return {
                ...item,
                uuid: item.id || randomLenNum(10),
                minSealPrice: item.product?.minSealPrice,
                preSealPrice: item.product?.sealPrice,
                referSealPrice: item.product?.referSealPrice,
              };
            }),
          };
        }),
      ];
      if (props.data.columnHideData) {
        columnHideData.value = props.data.columnHideData;
      }
      if (props.data.isHasTax == null) {
        form.value.isHasTax = 1;
      }
      isFold.value = props.data.isFold;
      proxy.$nextTick(() => {
        form.value.currentModule = 0;
        setTableData();
        setCateGoryList();
        setAllData();
      
      });

      //    setTableData()
    }
  },
  { immediate: true }
);
function handleModuleChange() {
  setCateGoryList();
  setTableData();
  if (form.value.currentModule == 0) {
    setAllData();
  }
}
let categoryList = ref([]);
function setCateGoryList() {
  let arr = [];

  if (form.value.currentModule == 0 || !form.value.currentModule) return [];
  moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
    if (arr.includes(item.classify)) return;
    arr.push(item.classify);
  });
  categoryList.value = arr;
}
// 当前模块汇总
const currentModuleTotal = computed(() => {
  if (form.value.currentModule == 0 || !form.value.currentModule) return {};
  const list = moduleDTOList.value[form.value.currentModule].detailDTOList.filter(
    item => item.detailType == 0
  );
  const {
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalQtcbPrice,
    totalYbcbPrice,
  } = {
    totalProductPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.sealPrice;
    }, 0),
    totalRgcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.rgcbdj;
    }, 0),
    totalCostPrice: list.reduce((pre, cur) => {
      return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
    }, 0),
    totalLaborCost: list.reduce((pre, cur) => {
      return pre + cur.number * cur.laborCost;
    }, 0),
    totalYbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.ybhsdj;
    }, 0),
    totalQtPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.qthsdj;
    }, 0),
    totalYbcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.ybcbdj;
    }, 0),
    totalQtcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.qtcbdj;
    }, 0),
  };
  const { taxPrice } = {
    // 税金
    taxPrice:
      totalProductPrice * 1 * form.value.productRate +
      totalLaborCost * 1 * form.value.labourRate +
      totalYbPrice * 1 * form.value.warrantyRate +
      totalQtPrice * 1 * form.value.otherRate,
  };
  const { costTaxPrice } = {
    // 成本税金
    costTaxPrice:
      totalCostPrice * 1 * form.value.productRate +
      totalRgcbPrice * 1 * form.value.labourRate +
      totalYbcbPrice * 1 * form.value.warrantyRate +
      totalQtcbPrice * 1 * form.value.otherRate,
  };
  return {
    // 设备金额
    totalProductPrice,
    // 人工金额
    totalLaborCost,
    // 延保金额
    totalYbPrice,
    // 其他金额
    totalQtPrice,
    // 人工成本
    totalRgcbPrice,
    // 设备成本
    totalCostPrice,
    // 延保成本
    totalYbcbPrice,
    // 其他成本
    totalQtcbPrice,
    taxPrice,
    costTaxPrice,
    // 报价合计
    total:
      totalProductPrice * 1 +
      totalLaborCost * 1 +
      totalYbPrice * 1 +
      totalQtPrice * 1 +
      (form.value.isHasTax ? 0 : taxPrice * 1),
    //成本合计
    costTotal:
      totalRgcbPrice * 1 +
      totalCostPrice * 1 +
      totalYbcbPrice * 1 +
      totalQtcbPrice * 1 +
      (form.value.isHasTax ? 0 : costTaxPrice * 1),
  };
});
// 所有模块汇总
const allModuleTotal = computed(() => {
  const list = moduleDTOList.value
    .filter((item, index) => index !== 0 && item.isCheck != 0)
    .reduce((pre, cur) => {
      return pre.concat(cur.detailDTOList.filter(item => item.detailType == 0 && item.isCheck != 0));
    }, []);

  const {
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalQtcbPrice,
    totalYbcbPrice,
  } = {
    totalProductPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.sealPrice;
    }, 0),
    totalRgcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.rgcbdj;
    }, 0),
    totalCostPrice: list.reduce((pre, cur) => {
      return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
    }, 0),
    totalLaborCost: list.reduce((pre, cur) => {
      return pre + cur.number * cur.laborCost;
    }, 0),
    totalYbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.ybhsdj;
    }, 0),
    totalQtPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.qthsdj;
    }, 0),
    totalYbcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.ybcbdj;
    }, 0),
    totalQtcbPrice: list.reduce((pre, cur) => {
      return pre + cur.number * cur.qtcbdj;
    }, 0),
  };
  const { taxTotal } = {
    taxTotal:
      totalProductPrice * form.value.productRate +
      totalLaborCost * form.value.labourRate +
      totalYbPrice * form.value.warrantyRate +
      totalQtPrice * form.value.otherRate,
  };
  const { costTaxTotal } = {
    costTaxTotal:
      totalCostPrice * form.value.productRate +
      totalRgcbPrice * form.value.labourRate +
      totalYbcbPrice * form.value.warrantyRate +
      totalQtcbPrice * form.value.otherRate,
  };
  return {
    // 报价合计
    total:
      totalProductPrice * 1 +
      totalLaborCost * 1 +
      totalYbPrice * 1 +
      totalQtPrice * 1 +
      (form.value.isHasTax ? 0 : taxTotal * 1),
    //成本合计
    costTotal:
      totalRgcbPrice * 1 +
      totalCostPrice * 1 +
      totalYbcbPrice * 1 +
      totalQtcbPrice * 1 +
      (form.value.isHasTax ? 0 : costTaxTotal * 1),
  };
});
function handleBlur(value, item) {
  //   currentIndex.value = null;
}
let tableData = ref([]); //表格临时数据

function setTableData(c) {
  if (form.value.currentModule == 0) {
    tableData.value = [];
    return;
  }
  setCategotyAndProductDrag();
  const currentData = moduleDTOList.value[form.value.currentModule];
  tableData.value = currentData.detailDTOList.reduce((pre, cur) => {
    if (pre.findIndex(item => item.classify === cur.classify) === -1) {
      const {
        sealPrice,
        laborCost,
        ybhsdj,
        qthsdj,
        qtcbdj,
        rgcbdj,
        specialCostPrice,
        costPrice,
        ybcbdj,
      } = cur;
      pre.push({
        ...cur,
        classify: cur.classify,

        productList: [cur],
      });
    } else {
      const {
        sealPrice,
        laborCost,
        ybhsdj,
        qthsdj,
        qtcbdj,
        rgcbdj,
        specialCostPrice,
        costPrice,
        ybcbdj,
      } = cur;
      pre.find(item => item.classify === cur.classify).productList.push(cur);
    }
    return pre;
  }, []);
  proxy.$nextTick(() => {
    for (let i = 0; i < tableData.value.length; i++) {
      tableData.value[i].productList = tableData.value[i].productList.filter(
        item => item.detailType == 0
      );
      setProductDrag(i);
    }

    // 如果编辑面板打开，更新产品列表
    if (showEditPanel.value) {
      updateProductList();
      // 重新定位当前产品索引
      if (currentProduct.value) {
        currentProductIndex.value = allProducts.value.findIndex(p => p.uuid === currentProduct.value.uuid);
      }
    }
  });
  if (typeof c == 'function') {
    c();
  }
}
let allData = ref([]);
function setAllData() {
  allData.value = moduleDTOList.value
    .filter((item, index) => index != 0)
    .map((item,index1) => {
      const list = item.detailDTOList.filter(item => item.detailType == 0);
      const {
        totalProductPrice,
        totalLaborCost,
        totalYbPrice,
        totalQtPrice,
        totalRgcbPrice,
        totalCostPrice,
        totalQtcbPrice,
        totalYbcbPrice,
      } = {
        totalProductPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.sealPrice;
        }, 0),
        totalRgcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.rgcbdj;
        }, 0),
        totalCostPrice: list.reduce((pre, cur) => {
          return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
        }, 0),
        totalLaborCost: list.reduce((pre, cur) => {
          return pre + cur.number * cur.laborCost;
        }, 0),
        totalYbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.ybhsdj;
        }, 0),
        totalQtPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.qthsdj;
        }, 0),
        totalYbcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.ybcbdj;
        }, 0),
        totalQtcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.qtcbdj;
        }, 0),
      };
      const { taxPrice } = {
        // 税金
        taxPrice:
          totalProductPrice * 1 * form.value.productRate +
          totalLaborCost * 1 * form.value.labourRate +
          totalYbPrice * 1 * form.value.warrantyRate +
          totalQtPrice * 1 * form.value.otherRate,
      };
      const { costTaxTotal } = {
        costTaxTotal:
          totalCostPrice * form.value.productRate +
          totalRgcbPrice * form.value.labourRate +
          totalYbcbPrice * form.value.warrantyRate +
          totalQtcbPrice * form.value.otherRate,
      };
      const uuid = randomLenNum(10)
      return {
        // 设备金额
        totalProductPrice,
        // 人工金额
        totalLaborCost,
        // 延保金额
        totalYbPrice,
        // 其他金额
        totalQtPrice,
        // 人工成本
        totalRgcbPrice,
        // 设备成本
        totalCostPrice,
        // 延保成本
        totalYbcbPrice,
        // 其他成本
        totalQtcbPrice,
        taxPrice,
        // 报价合计
        total:
          totalProductPrice * 1 +
          totalLaborCost * 1 +
          totalYbPrice * 1 +
          totalQtPrice * 1 +
          (form.value.isHasTax ? 0 : taxPrice * 1),

        //成本合计
        costTotal:
          totalRgcbPrice * 1 +
          totalCostPrice * 1 +
          totalYbcbPrice * 1 +
          totalQtcbPrice * 1 +
          (form.value.isHasTax ? 0 : costTaxTotal * 1),
        moduleName: item.moduleName,
        isHasClassify: item.isHasClassify,
        index:index1 + 1,
        isCheck:item.isCheck,
        uuid,
        children:
          item.isHasClassify == 1 ? [] : childrenAllData(item.detailDTOList, uuid,index1 + 1),
      };
    });
    setToggle()
}
function setToggle() {
  allData.value.forEach((item) => {
 
   nextTick(() => {
     allTableRef.value.toggleRowSelection(item,!!item.isCheck)
     item.children.forEach((item1) => {
       allTableRef.value.toggleRowSelection(item1,!!item1.isCheck)
     })
   })
  })
}
function childrenAllData(data, parentUuid,parentIndex) {
  const list = data.reduce((pre, cur) => {
    if (pre.findIndex(item => item.classify === cur.classify) === -1) {
      pre.push({
        ...cur,
        classify: cur.classify,
        productList: [cur],
      });
    } else {
      pre.find(item => item.classify === cur.classify).productList.push(cur);
    }
    return pre;
  }, []);

  return list.map(item => {
    const list = item.productList.filter(item => item.detailType == 0);
    const {
      totalProductPrice,
      totalLaborCost,
      totalYbPrice,
      totalQtPrice,
      totalRgcbPrice,
      totalCostPrice,
      totalQtcbPrice,
      totalYbcbPrice,
    } = {
      totalProductPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
      totalRgcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.rgcbdj;
      }, 0),
      totalCostPrice: list.reduce((pre, cur) => {
        return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
      }, 0),
      totalLaborCost: list.reduce((pre, cur) => {
        return pre + cur.number * cur.laborCost;
      }, 0),
      totalYbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.ybhsdj;
      }, 0),
      totalQtPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.qthsdj;
      }, 0),
      totalYbcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.ybcbdj;
      }, 0),
      totalQtcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.qtcbdj;
      }, 0),
    };
    const { taxPrice } = {
      // 税金
      taxPrice:
        totalProductPrice * 1 * form.value.productRate +
        totalLaborCost * 1 * form.value.labourRate +
        totalYbPrice * 1 * form.value.warrantyRate +
        totalQtPrice * 1 * form.value.otherRate,
    };
    const { costTaxTotal } = {
      costTaxTotal:
        totalCostPrice * form.value.productRate +
        totalRgcbPrice * form.value.labourRate +
        totalYbcbPrice * form.value.warrantyRate +
        totalQtcbPrice * form.value.otherRate,
    };
    return {
      // 设备金额
      totalProductPrice,
      // 人工金额
      totalLaborCost,
      // 延保金额
      totalYbPrice,
      // 其他金额
      totalQtPrice,
      // 人工成本
      totalRgcbPrice,
      // 设备成本
      totalCostPrice,
      // 延保成本
      totalYbcbPrice,
      // 其他成本
      totalQtcbPrice,
      taxPrice,
      // 报价合计
      total:
        totalProductPrice * 1 +
        totalLaborCost * 1 +
        totalYbPrice * 1 +
        totalQtPrice * 1 +
        (form.value.isHasTax ? 0 : taxPrice * 1),

      //成本合计
      costTotal:
        totalRgcbPrice * 1 +
        totalCostPrice * 1 +
        totalYbcbPrice * 1 +
        totalQtcbPrice * 1 +
        (form.value.isHasTax ? 0 : costTaxTotal * 1),
      moduleName: item.classify,
     
      parentIndex,
      isCheck : item.isCheck,
      uuid:randomLenNum(10),
      parentUuid,
    };
  });
}

function handleSelectionChange(list) {
  console.log(list);
}
let isFold = ref(false);

function addModule(row = {}, type) {
  proxy.$refs.dialogForm.show({
    title: type ? '编辑' : '新增',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'input',
          label: '子系统名称',
          span: 24,
          prop: 'moduleName',
          value: row.moduleName,
          rules: [
            {
              required: true,
              message: '请输入子系统名称',
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 去掉 - 字符的限制
                const reg = /^[^/\\?？\[\]]*$/;
                console.log(value, rule, reg);
                if (!reg.test(value)) {
                  callback('不能包含特殊字符"/\?？[]"');
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          type: 'switch',
          label: '无分类',
          value: row.isHasClassify,
          prop: 'isHasClassify',
          disabled: type == 'edit',

          labelTip: '无分类可以直接添加产品，不需要先新建分类',

          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          value: row.remark,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      if (type) {
        row.remark = res.data.remark;
        row.moduleName = res.data.moduleName;
        row.isHasClassify = res.data.isHasClassify;
      } else {
        if (res.data.isHasClassify == 1) {
          // 无分类模块添加
          moduleDTOList.value.push({
            moduleName: res.data.moduleName,
            remark: res.data.remark,
            isHasClassify: res.data.isHasClassify,
            uuid: randomLenNum(10),
            detailDTOList: [
              {
                classify: '',
                detailType: 1,
                classifySort: 0,
              },
            ],
          });
        } else {
          moduleDTOList.value.push({
            moduleName: res.data.moduleName,
            remark: res.data.remark,
            isHasClassify: res.data.isHasClassify,
            uuid: randomLenNum(10),
            detailDTOList: [],
          });
        }
      }
      res.close();
    },
  });
}
// 添加分类
function addCategory() {
  const currentData = moduleDTOList.value[form.value.currentModule];
  proxy.$refs.dialogForm.show({
    title: '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          rules: [
            {
              required: true,
              message: '请输入分类名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      currentData.detailDTOList.push({
        classify: res.data.classify,
        detailType: 1,
        isCheck:1,
        classifySort: categoryList.value.length,
      });
      setTableData();
      setCateGoryList();
      res.close();
      sendMessage();
    },
  });
}
async function handleProductSelectConfirm(ids) {
  console.log(ids);
  const res = await axios.get('/api/vt-admin/product/detailByIds', {
    params: {
      idList: ids,
    },
  });

  const data = res.data.data
    .map(item => {
      return {
        customProductName: item.productName,
        customProductSpecification: item.productSpecification,
        customProductDescription: item.description,
        customUnit: item.unitName,
        productId: item.id,
        source: 0,
        classify: form.value.currentclassify,
        ...item,
        id: null,
        detailType: 0,
        number: '',
        rgcbdj: '',
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        isCheck:1,
        uuid: randomLenNum(10),
        classifySort: categoryList.value.findIndex(item => item == form.value.currentclassify),
      };
    })
    .reverse();

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
  setTableData();
  setCateGoryList();
}
// 删除分类
function delCategory(classify) {
  proxy
    .$confirm('是否删除该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value[form.value.currentModule].detailDTOList = moduleDTOList.value[
        form.value.currentModule
      ].detailDTOList.filter(item => item.classify != classify);
      setTableData();
      setCateGoryList();
    })
    .catch(() => {});
}
// 复制分类
function copyCategory(classify) {
  proxy
    .$confirm('是否复制该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const randomLenNumValue = randomLenNum(5);
      const data = [];
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          const itemCopy = {
            ...item,
            classify: item.classify + `--${randomLenNumValue}`,
            classifySort: categoryList.value.length,
            uuid: randomLenNum(10),
            id: null,
          };
          data.push(itemCopy);
        }
      });
      moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
      setTableData();
      setCateGoryList();
    })
    .catch(() => {});
}

function editCategory(classify) {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          value: classify,
          rules: [
            {
              required: true,
              message: '请输入模块名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          item.classify = res.data.classify;
        }
      });
      setTableData();
      setCateGoryList();
      res.close();
    },
  });
}
// 删除模块
function delModule() {
  proxy
    .$confirm('是否删除该模块?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value.splice(form.value.currentModule, 1);
      form.value.currentModule = 1;
      setTableData();
    })
    .catch(() => {});
}

function handleScroll(index) {
  console.log(proxy.$refs[index + '-cateGory'][0]);
  proxy.$refs[index + '-cateGory'][0].scrollIntoView({
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
  });
}

// 列显示隐藏
let colmunDrawer = ref(false);
let columnHideData = ref([
  // {
  //   value :'产品名称',
  //   isShow:true,
  // },
  // {
  //   value :'品牌',
  //   isShow:true,
  // },
  // {
  //   value :'规格型号',
  //   isShow:true,
  // },
  // {
  //   value :'详细描述',
  //   isShow:true,
  // },
  // {
  //   value :'产品图片',
  //   isShow:true,
  // },
  // {
  //   value :'单位',
  //   isShow:true,
  // },
  // {
  //   value :'数量',
  //   isShow:true,
  // },
  {
    value: '设备单价',
    isShow: true,
    width: 80,
  },

  {
    value: '设备金额',
    isShow: true,
    width: 80,
  },

  {
    value: '人工单价',
    isShow: true,
    width: 80,
  },

  {
    value: '人工金额',
    isShow: true,
    width: 80,
  },

  {
    value: '其他单价',
    isShow: false,
    width: 80,
  },

  {
    value: '其他金额',
    isShow: false,
    width: 80,
  },

  {
    value: '延保单价',
    isShow: false,
    width: 80,
  },

  {
    value: '延保金额',
    isShow: false,
    width: 80,
  },
  {
    value: '综合单价',
    isShow: false,
    width: 80,
  },
  {
    value: '综合金额',
    isShow: false,
    width: 80,
  },

  {
    value: '备注',
    isShow: true,
    width: 150,
    align: 'left',
  },

  {
    value: '设备成本单价',
    isShow: true,
    width: 100,
  },
  {
    value: '设备成本金额',
    isShow: true,
    width: 100,
  },
  {
    value: '人工成本单价',
    isShow: true,
    width: 100,
  },
  {
    value: '人工成本金额',
    isShow: true,
    width: 100,
  },
  {
    value: '其他成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '其他成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '综合成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '综合成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '专项成本',
    isShow: true,
    width: 100,
  },
  {
    value: '专项供应商',
    isShow: true,
    width: 100,
  },
]);
const tableWidth = computed(() => {
  const value = columnHideData.value
    .filter(item => item.isShow)
    .reduce((pre, cur) => {
      return pre + cur.width;
    }, 0);
  return `${value}px`;
});

function isTrue(value) {
  return columnHideData.value.find(item => item.value == value)?.isShow;
}

// 设置参考信息
let referInfo = ref({});
let currentProduct = ref({});
let preItem = ref({});
let currentHeight = ref(20);
let currentModelKey = ref('');

// 浮动面板相关变量
let showEditPanel = ref(false);
let panelPosition = ref({ x: window.innerWidth - 480, y: 80 });
let panelSize = ref({ width: 460, height: 720 });
let isDragging = ref(false);
let isResizing = ref(false);
let isCollapsed = ref(false);
let dragOffset = ref({ x: 0, y: 0 });
let resizeType = ref(''); // 'right', 'bottom', 'corner'
let resizeStartPos = ref({ x: 0, y: 0 });
let resizeStartSize = ref({ width: 0, height: 0 });

// 产品切换相关变量
let currentProductIndex = ref(-1);
let allProducts = ref([]); // 当前模块的所有产品列表

// 面板样式计算
const panelStyle = computed(() => ({
  position: 'fixed',
  left: panelPosition.value.x + 'px',
  top: panelPosition.value.y + 'px',
  width: panelSize.value.width + 'px',
  height: isCollapsed.value ? '70px' : panelSize.value.height + 'px',
  zIndex: 9999,
  cursor: isDragging.value ? 'grabbing' : 'default',
  transition: isCollapsed.value ? 'height 0.3s ease' : 'none'
}));

// 判断产品是否为当前编辑的产品
function isCurrentEditingProduct(product) {
  return showEditPanel.value && currentProduct.value && currentProduct.value.uuid === product.uuid;
}

function setReferPrice(item) {
  // 重置之前产品的高度
  if (preItem.value && preItem.value.height) {
    preItem.value.height = 20;
  }

  // 设置当前产品信息，但不改变高度
  preItem.value = item;
  referInfo.value = {
    productName: item.customProductName,
    preSealPrice: item.preSealPrice,
    minSealPrice: item.minSealPrice,
    referSealPrice: item.referSealPrice,
    customProductDescription: item.customProductDescription,
  };
  currentProduct.value = item;

  // 更新产品列表和当前索引
  updateProductList();
  currentProductIndex.value = allProducts.value.findIndex(p => p.uuid === item.uuid);

  // 显示编辑面板
  showEditPanel.value = true;

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
}

// 拖拽相关方法
function startDrag(event) {
  // 如果点击的是控制按钮，不触发拖拽
  if (event.target.closest('.header-controls')) {
    return;
  }

  if (event.target.closest('.panel-header') && !isResizing.value) {
    isDragging.value = true;
    dragOffset.value = {
      x: event.clientX - panelPosition.value.x,
      y: event.clientY - panelPosition.value.y
    };

    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopDrag);
    event.preventDefault();
  }
}

function onDrag(event) {
  if (isDragging.value && !isResizing.value) {
    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;

    // 限制在屏幕范围内
    const maxX = window.innerWidth - panelSize.value.width;
    const maxY = window.innerHeight - (isCollapsed.value ? 50 : panelSize.value.height);

    panelPosition.value = {
      x: Math.max(0, Math.min(maxX, newX)),
      y: Math.max(0, Math.min(maxY, newY))
    };
  }
}

function stopDrag() {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

function closeEditPanel() {
  showEditPanel.value = false;
  isCollapsed.value = false;
  currentProductIndex.value = -1;
  // 重置当前产品的高度
  if (currentProduct.value && currentProduct.value.height) {
    currentProduct.value.height = 20;
  }
  if (preItem.value && preItem.value.height) {
    preItem.value.height = 20;
  }
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
}

// 折叠/展开面板
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value;
}

// 开始调整大小
function startResize(type, event) {
  isResizing.value = true;
  resizeType.value = type;
  resizeStartPos.value = {
    x: event.clientX,
    y: event.clientY
  };
  resizeStartSize.value = {
    width: panelSize.value.width,
    height: panelSize.value.height
  };

  document.addEventListener('mousemove', onResize);
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
  event.stopPropagation();
}

// 调整大小过程中
function onResize(event) {
  if (!isResizing.value) return;

  const deltaX = event.clientX - resizeStartPos.value.x;
  const deltaY = event.clientY - resizeStartPos.value.y;

  let newWidth = resizeStartSize.value.width;
  let newHeight = resizeStartSize.value.height;

  if (resizeType.value === 'right' || resizeType.value === 'corner') {
    newWidth = Math.max(380, Math.min(900, resizeStartSize.value.width + deltaX));
  }

  if (resizeType.value === 'bottom' || resizeType.value === 'corner') {
    newHeight = Math.max(500, Math.min(window.innerHeight - 80, resizeStartSize.value.height + deltaY));
  }

  panelSize.value = {
    width: newWidth,
    height: newHeight
  };

  // 确保面板不会超出屏幕边界
  const maxX = window.innerWidth - panelSize.value.width;
  const maxY = window.innerHeight - (isCollapsed.value ? 50 : panelSize.value.height);

  if (panelPosition.value.x > maxX) {
    panelPosition.value.x = maxX;
  }
  if (panelPosition.value.y > maxY) {
    panelPosition.value.y = maxY;
  }
}

// 停止调整大小
function stopResize() {
  isResizing.value = false;
  resizeType.value = '';
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopResize);
}

// 更新产品列表
function updateProductList() {
  allProducts.value = [];
  if (form.value.currentModule === 0 || !tableData.value.length) {
    return;
  }

  // 收集当前模块的所有产品
  tableData.value.forEach(category => {
    if (category.productList && category.productList.length > 0) {
      allProducts.value.push(...category.productList.filter(item => item.detailType === 0));
    }
  });
}

// 键盘事件处理
function handleKeyDown(event) {
  if (!showEditPanel.value || allProducts.value.length === 0) {
    return;
  }

  // 左箭头键 - 上一个产品
  if (event.key === 'ArrowLeft') {
    event.preventDefault();
    switchToPreviousProduct();
  }
  // 右箭头键 - 下一个产品
  else if (event.key === 'ArrowRight') {
    event.preventDefault();
    switchToNextProduct();
  }
}

// 切换到上一个产品
function switchToPreviousProduct() {
  if (currentProductIndex.value > 0) {
    currentProductIndex.value--;
    switchToProduct(allProducts.value[currentProductIndex.value]);
  }
}

// 切换到下一个产品
function switchToNextProduct() {
  if (currentProductIndex.value < allProducts.value.length - 1) {
    currentProductIndex.value++;
    switchToProduct(allProducts.value[currentProductIndex.value]);
  }
}

// 切换到指定产品
function switchToProduct(product) {
  // 重置之前产品的高度
  if (currentProduct.value && currentProduct.value.height) {
    currentProduct.value.height = 20;
  }

  // 设置新产品，但不改变高度
  preItem.value = product;
  referInfo.value = {
    productName: product.customProductName,
    preSealPrice: product.preSealPrice,
    minSealPrice: product.minSealPrice,
    referSealPrice: product.referSealPrice,
    customProductDescription: product.customProductDescription,
  };
  currentProduct.value = product;

  // 滚动到产品位置
  scrollToProduct(product);
}

// 滚动到产品位置
function scrollToProduct(product) {
  proxy.$nextTick(() => {
    const productElement = document.getElementById(`tr_${product.uuid}`);
    if (productElement) {
      productElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });
    }
  });
}

// 在当前产品下方插入空产品
function insertEmptyProduct() {
  if (!currentProduct.value || !currentProduct.value.uuid) {
    return;
  }

  // 创建空产品对象
  const newProduct = {
    detailType: 0,
    source: 3,
    classify: currentProduct.value.classify,
    id: null,
    costPrice: 0,
    sealPrice: 0,
    rgcbdj: 0,
    ybcbdj: 0,
    qtcbdj: 0,
    ybhsdj: 0,
    qthsdj: 0,
    laborCost: 0,
    remark: '',
    uuid: randomLenNum(10),
    isCheck: 1,
    classifySort: currentProduct.value.classifySort,
    customProductName: '',
    customProductSpecification: '',
    customProductDescription: '',
    customUnit: '',
    productBrand: '',
    number: 1,
    height: 20,
  };

  // 找到当前产品在detailDTOList中的位置
  const currentData = moduleDTOList.value[form.value.currentModule];
  const currentProductIndex = currentData.detailDTOList.findIndex(
    item => item.uuid === currentProduct.value.uuid
  );

  if (currentProductIndex !== -1) {
    // 在当前产品后面插入新产品
    currentData.detailDTOList.splice(currentProductIndex + 1, 0, newProduct);

    // 重新设置表格数据
    setTableData();

    // 更新产品列表
    updateProductList();

    // 切换到新插入的产品
    proxy.$nextTick(() => {
      const newIndex = allProducts.value.findIndex(p => p.uuid === newProduct.uuid);
      if (newIndex !== -1) {
        currentProductIndex.value = newIndex;
        switchToProduct(allProducts.value[newIndex]);
      }
    });
  }
}

// 根据利润比计算价格
function setPrice(value) {
  moduleDTOList.value.forEach(cur => {
    if (!cur.detailDTOList) return;

    cur.detailDTOList.forEach(cur => {
      if (cur.detailType == 0) {
        cur.sealPrice = (cur.costPrice / (1 - value)).toFixed(2);
      }
    });
  });
  form.value.isLock = true;
}
// 根据利润比计算价格
function setLaborPrice(value) {
  moduleDTOList.value.forEach(cur => {
    if (!cur.detailDTOList) return;

    cur.detailDTOList.forEach(cur => {
      if (cur.detailType == 0) {
        cur.laborCost = (cur.rgcbdj / (1 - value)).toFixed(2);
      }
    });
  });
  form.value.isLock = true;
}
onMounted(() => {
  if (proxy.$route.query.type != 'detail' && !props.dialogView) {
    setSort();
  }

  form.value.currentModule = 0;
});
// 拖拽排序
const setSort = () => {
  // 设置 子系统拖拽
  console.log(proxy.$refs);
  const el = proxy.$refs['sort-buttons'].$el;

  new Sortable(el, {
    handle: '.move1',

    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log(evt);

      const targetRow = moduleDTOList.value.splice(evt.oldIndex, 1);
      console.log(form.value.currentModule, evt.newIndex, evt.oldIndex);
      // if (!targetRow[0]) return;
      moduleDTOList.value.splice(evt.newIndex, 0, targetRow[0]);
      if (form.value.currentModule == evt.oldIndex) {
        form.value.currentModule = evt.newIndex;
      } else if (form.value.currentModule == evt.newIndex) {
        form.value.currentModule = evt.oldIndex;
      }
      setTableData();
    },
  });
};
function setCategotyAndProductDrag(params) {
  // 设置分类拖拽
  const el = proxy.$refs.category_box;

  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const targetRow = tableData.value.splice(evt.oldIndex, 1);
      tableData.value.splice(evt.newIndex, 0, targetRow[0]);
      moduleDTOList.value[form.value.currentModule].detailDTOList = tableData.value.reduce(
        (acc, cur, index) => {
          cur.productList.forEach(item => {
            item.classifySort = index;
          });
          acc.push({
            ...cur,
            productList: null,
            classifySort: index,
          });
          acc.push(...cur.productList);
          return acc;
        },
        []
      );

      setTableData();
      setCateGoryList();
    },
  });
}
function setProductDrag(ins) {
  console.log(proxy.$refs, `${ins}-cateGory`);
  // 设置产品拖拽
  const el = proxy.$refs[`${ins}-cateGory`][0];
  console.log(el);
  new Sortable(el, {
    // group: 'shared',
    handle: '.sort',
    draggable: '.allow_td',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const newRow = tableData.value[ins].productList[evt.newIndex - 1];
      const targetRow = tableData.value[ins].productList[evt.oldIndex - 1];
      const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
        item => item.uuid == targetRow.uuid
      );
      // const row = moduleDTOList[form.value.currentModule].detailDTOList[index]
      const newIndex = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
        item => item.uuid == newRow.uuid
      );
      const row = moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      moduleDTOList.value[form.value.currentModule].detailDTOList.splice(newIndex, 0, row[0]);
      setTableData(setRandomKey);
    },
  });
}
// 自定义序号
function customIndex(index, parentIndex) {
  console.log(index, parentIndex);
  let i = 0;
  if (parentIndex == 0) {
    i = index + 1;
  } else {
    let sum = 0;
    for (let i = 0; i < parentIndex; i++) {
      sum += tableData.value[i].productList.filter(item => item.detailType == 0).length;
    }
    // 返回序号
    i = sum + index + 1;
  }

  return i;
}

function deleteProduct(i) {
  proxy
    .$confirm('此操作将删除该产品, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      if (i.id) {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.id == i.id
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      } else {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.uuid == i.uuid
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      }
      setTableData();
    })
    .catch(() => {});
}

function getData() {
  console.log(moduleDTOList.value);
  return {
    ...form.value,
    moduleDTOList: moduleDTOList.value
      .filter((item, index) => index != 0)
      .map(item => {
        return {
          ...item,
        };
      }),
    columnHideData: columnHideData.value,
    isFold: isFold.value,
  };
}
function getTree() {
  const treeData = formatTreeData(moduleDTOList.value.filter((item, index) => index != 0));
  return treeData;
}
// 将有分类的数据分发到各个分类
function setData(list) {
  list.forEach(item => {
    const moduleName = item.classifyName.split('?')[0];
    const classify = item.classifyName.split('?')[1];
    const index = moduleDTOList.value.findIndex(item => item.moduleName == moduleName);
    const {
      customProductName,
      customProductSpecification,
      customProductDescription,
      customUnit,
      productBrand,
      number,
      sealPrice,
      laborCost,
      qthsdj,
      ybhsdj,
      costPrice,
      source,
      rgcbdj,
      ybcbdj,
      qtcbdj,
      remark,
    } = item;
    const moduleDTOS = moduleDTOList.value.find(item => item.moduleName == moduleName);
    const classifySort = moduleDTOS.detailDTOList.find(
      item => item.classify == classify
    ).classifySort;
    const data = {
      customProductName,
      customProductSpecification,
      customProductDescription,
      customUnit,
      classify,
      source,
      productBrand,
      id: null,
      detailType: 0,
      isCheck:1,
      number,
      rgcbdj,
      ybcbdj,
      qtcbdj,
      costPrice,
      sealPrice,
      laborCost,
      qthsdj,
      ybhsdj,
      remark: remark,
      uuid: randomLenNum(10),
      classifySort,
    };

    moduleDTOS.detailDTOList.push(data);
  });
  console.log(moduleDTOList.value);
  setTableData();
}
function formatTreeData(data) {
  return data.map(item => {
    item.label = item.moduleName;
    item.value = item.moduleName;
    item.disabled = true;
    item.children = item.detailDTOList
      .map(i => {
        return {
          value: `${item.value}-${i.classify}`,
          label: i.classify || '---',
          parentId: item.uuid,
        };
      })
      .reduce((acc, cur) => {
        if (!acc.find(item => item.value === cur.value)) {
          acc.push({
            ...cur,
          });
        }
        return acc;
      }, []);
    return item;
  });
}
function handleRowClick(item) {
  return;
  if (proxy.$route.query.type == 'detail') return;
  currentIndex.value = item.classify;
}

let targetWindow = null;
function handleOpenIframe() {
  targetWindow = window.open('/productSelect', '_productSelect', 'width=1980,height=1500');
}
function sendMessage() {
  // 将分类处理好发送过去

  const treeData = moduleDTOList.value
    .filter((item, index) => index != 0)
    .map((item, index) => {
      item.label = item.moduleName;
      item.children = item.detailDTOList
        .map(i => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: index,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push(cur);
          }
          return acc;
        }, []);
      return item;
    });
  console.log(treeData);
  targetWindow.postMessage(
    JSON.stringify({
      projectName: proxy.$route.query.name,
      treeData: treeData,
      selectClassify: form.value.currentclassify,
      currentModule: form.value.currentModule - 1,
    }),
    '*'
  );
}
// 添加事件监听
function addEvent() {
  window.addEventListener('message', function (event) {
    // 接收子窗口发送的消息
    const data = event.data;
    console.log(data);
    // type :   0 页面加载完成  1 选择产品
    if (data.type == 0) {
      console.log(sendMessage);
      sendMessage();
    } else if (data.type == 1) {
      (form.value.currentModule = data.data.parentId + 1),
        (form.value.currentclassify = data.data.classify);

      handleProductSelectConfirm(data.data.ids);
    }
  });
}

let randomKey = ref(null);
function setRandomKey() {
  randomKey.value = randomLenNum(10);
}

function isNumber(str) {
  return /^[0-9]*(\.[0-9]+)?$/.test(str);
}
function copyModule() {
  const { moduleName, detailDTOList } = moduleDTOList.value[form.value.currentModule];
  moduleDTOList.value.push({
    moduleName: moduleName + '复制',
    detailDTOList: deepClone(
      detailDTOList.map(item => {
        return {
          uuid: randomLenNum(10),
          ...item,
          id: null,
        };
      })
    ),
  });
}
let templateType = ref('');
// 添加模板
async function handleTemplateChange(id, done) {
  const res = await axios.get('/api/vt-admin/optionTemplate/detailForAdd', {
    params: {
      id,
    },
  });

  const currentData = moduleDTOList.value[form.value.currentModule];
  if (templateType.value == 1) {
    // 子系统模块
    const { moduleName, remark, optionModuleDetailTemplateVOS } =
      res.data.data.moduleTemplateVOS[0];
    if (moduleDTOList.value.some(item => item.moduleName == moduleName))
      return proxy.$message.warning(`已存在模块：${moduleName},请先修改`);
    moduleDTOList.value.push({
      moduleName: moduleName,
      remark: remark,
      uuid: randomLenNum(10),
      detailDTOList: optionModuleDetailTemplateVOS.map(item => {
        return {
          ...item,
          ...item.productVO,
          productBrand: item.productBrand,
          sealPrice: '',
          rgcbdj: '',
          ybcbdj: '',
          qtcbdj: '',
          ybhsdj: '',
          qthsdj: '',
          source: 1,
          sealPrice: '',
          laborCost: '',
          id: null,
        };
      }),
    });
    done();
  } else if (templateType.value == 2) {
    //子分类模块
    const { classify, optionModuleDetailTemplateVOS } = res.data.data
      .optionModuleClassifyTemplateVO || {
      classify: '',
      optionModuleDetailTemplateVOS: [],
    };

    if (categoryList.value.some(item => item == classify))
      return proxy.$message.warning(`已存在分类：${classify},请先修改`);
    console.log(classify, optionModuleDetailTemplateVOS);
    // 1。添加分类
    currentData.detailDTOList.push({
      classify: classify,
      detailType: 1,
      classifySort: categoryList.value.length,
    });
    // 2.添加产品
    const data = optionModuleDetailTemplateVOS.map(item => {
      return {
        ...item.productVO,
        productId: item.id,
        classify: classify,
        ...item,
        id: null,
        detailType: 0,
        sealPrice: '',
        rgcbdj: '',
        source: 1,
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        uuid: randomLenNum(10),
        classifySort: categoryList.value.length,
      };
    });
    done();
    currentData.detailDTOList.push(...data);
    setTableData();
    setCateGoryList();
  }
}
function addTemplate(val) {
  templateType.value = val;
  proxy.$nextTick(() => {
    proxy.$refs.templateDialog.open();
  });
}

function addEmptyProduct(item) {
  form.value.currentclassify = item.classify;
  const data = {
    detailType: 0,
    source: 3,
    classify: form.value.currentclassify,
    id: null,
    detailType: 0,
    costPrice: 0,
    sealPrice: '',
    rgcbdj: 0,
    ybcbdj: 0,
    qtcbdj: 0,
    ybhsdj: 0,
    qthsdj: 0,
    sealPrice: 0,
    laborCost: 0,
    remark: '',
    uuid: randomLenNum(10),
    isCheck:1,
    classifySort: categoryList.value.findIndex(item => item == form.value.currentclassify),
  };

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(data);
  setTableData();
  setCateGoryList();
}

function addProductFromSheet(item) {
  const moduleName = moduleDTOList.value[form.value.currentModule].moduleName;
  const classify = item.classify;
  props.openSheetProductSelect({ moduleName, classify });
}
function saveCategoryTemplate(classify) {
  proxy.$refs.dialogForm.show({
    title: '保存为模板',
    option: {
      column: [
        {
          label: '模板名称',
          prop: 'templateName',
          type: 'input',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入模板名称',
            },
          ],
        },
        // {
        //   label: '模块名称',
        //   prop: 'moduleName',
        //   value: moduleDTOList.value[form.value.currentModule].moduleName,
        //   type: 'input',
        //   span: 24,
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入模块名称',
        //     },
        //   ],
        // },
        {
          type: 'tree',
          label: '模板分类',
          rules: [
            {
              required: true,
              message: '请选择模板分类',
            },
          ],
          width: 220,
          span: 12,
          // parent: false,

          span: 24,

          search: true,
          dicUrl: '/api/vt-admin/templateCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.map(item => {
              return {
                ...item,
                disabled: true,
                children:
                  item.children &&
                  item.children.map(item => {
                    return {
                      ...item,
                      disabled: true,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            disabled: false,
                          };
                        }),
                    };
                  }),
              };
            });
          },
          dataType: 'string',
          display: true,
          filterable: true,
          prop: 'templateCategory',
          checkStrictly: true,
        },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务类型',
          // multiple: true,
          span: 12,
          width: 250,
          overHidden: true,
          parent: true,
          span: 24,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          value: proxy.$route.query.businessType,
          search: true,
          display: true,
          filterable: true,

          prop: 'businessType',
          checkStrictly: true,
        },
        {
          label: '模板描述',
          prop: 'remark',
          type: 'textarea',
          search: true,
          span: 24,
        },
      ],
    },
    callback(res) {
      const moduleDTOS = [moduleDTOList.value[form.value.currentModule]].map((item, index) => {
        return {
          ...item,
          moduleName: moduleDTOList.value[form.value.currentModule].moduleName,
          classifyDTO: {
            ...item.detailDTOList.find(item => item.detailType == 1),
            detailDTOS: item.detailDTOList
              .filter(item => item.detailType != 1)
              .filter(item => item.classify == classify)
              .map((item, index) => {
                return {
                  ...item,
                  sortNumber: item.index,
                };
              }),
          },
          sortNumber: index,
        };
      });
      const value = {
        ...moduleDTOS[0],
        ...res.data,
        templateType: 2,
      };
      axios.post('/vt-admin/optionTemplate/save', value).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
      });
    },
  });
}
function saveModuleTemplate() {
  proxy.$refs.dialogForm.show({
    title: '保存为模板',
    option: {
      column: [
        {
          label: '模板名称',
          prop: 'templateName',
          type: 'input',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入模板名称',
            },
          ],
        },
        // {
        //   label: '子系统名称',
        //   prop: 'moduleName',
        //   value: moduleDTOList.value[form.value.currentModule].moduleName,
        //   type: 'input',
        //   span: 24,
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入模块名称',
        //     },
        //   ],
        // },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务类型',
          // multiple: true,
          span: 12,
          width: 250,
          overHidden: true,
          parent: true,
          span: 24,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          value: proxy.$route.query.businessType,
          search: true,
          display: true,
          filterable: true,

          prop: 'businessType',
          checkStrictly: true,
        },
        {
          type: 'tree',
          label: '模板分类',
          rules: [
            {
              required: true,
              message: '请选择模板分类',
            },
          ],
          width: 220,
          span: 12,
          // parent: false,

          span: 24,

          search: true,
          dicUrl: '/api/vt-admin/templateCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.map(item => {
              return {
                ...item,
                disabled: true,
                children:
                  item.children &&
                  item.children.map(item => {
                    return {
                      ...item,
                      disabled: false,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            disabled: true,
                          };
                        }),
                    };
                  }),
              };
            });
          },
          dataType: 'string',
          display: true,
          filterable: true,
          prop: 'templateCategory',
          checkStrictly: true,
        },
        {
          label: '模板描述',
          prop: 'remark',
          type: 'textarea',
          search: true,
          span: 24,
        },
      ],
    },
    callback(res) {
      const moduleDTOS = [moduleDTOList.value[form.value.currentModule]].map((item, index) => {
        return {
          ...item,
          moduleName: moduleDTOList.value[form.value.currentModule].moduleName,
          classifyDTOS: item.detailDTOList.reduce((pre, cur, index) => {
            if (cur.detailType == 1) {
              const detailDTOS = item.detailDTOList
                .filter(item => item.classify == cur.classify && item.detailType != 1)
                .map(item => {
                  return {
                    ...item,
                    sortNumber: index,
                  };
                });
              pre.push({
                ...cur,
                classifySort: index,
                detailDTOS,
              });
            }
            return pre;
          }, []),
          sortNumber: index,
        };
      });
      const value = {
        moduleDTOS,
        ...res.data,
        templateType: 1,
      };
      axios.post('/vt-admin/optionTemplate/save', value).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
      });
    },
  });
}
onMounted(() => {
  addEvent();
});
let brandList = ref([]);

function getBrand(i) {
  if (!(!i.productId && i.source == 1 && i.categoryId)) {
    return;
  }
  axios.get('/api/vt-admin/product/getBrandListByCategory?categoryId=' + i.categoryId).then(res => {
    brandList.value = res.data.data;
  });
}
let productSpecificationList = ref([]);
function getProductSpecificationList(i) {
  if (!(!i.productId && i.source == 1 && i.categoryId)) {
    return;
  }
  axios
    .get('/api/vt-admin/product/getSpecificationListByCategory', {
      params: {
        categoryId: i.categoryId,
        productBrand: i.productBrand,
      },
    })
    .then(res => {
      productSpecificationList.value = res.data.data;
    });
}
function handleSpecificationClick(i, item) {
  i.customProductName = item.productName;
  i.customProductSpecification = item.productSpecification;
  i.customProductDescription = item.description;
  // i.productId = item.id;
  i.customUnit = item.unitName;
  i.productBrand = item.productBrand;
  i.rgcbdj = item.laborCost || '';
  i.ybcbdj = item.ybcbdj || '';
  i.qtcbdj = item.qtcbdj || '';
  i.costPrice = item.costPrice || '';
}
function handleProductBrandClick(i, item) {
  console.log(i, item);
  i.customProductName = '';
  i.customProductSpecification = '';
  i.customProductDescription = '';
  // i.productId = item.id;
  i.customUnit = '';
  i.productBrand = item;
}
function editDiscountsPrice() {
  proxy.$refs.dialogForm.show({
    title: '报价折减',
    width: '30%',
    option: {
      column: [
        {
          label: '折减金额',
          prop: 'discountsPrice',
          type: 'number',
          span: 24,
          value: form.value.discountsPrice || 0,
          rules: [
            {
              required: true,
              message: '请输入折减金额',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      form.value.discountsPrice = res.data.discountsPrice;
      res.close();
    },
  });
}
const emits = defineEmits(['exportProductByHistory']);
function exportProductByHistory(item) {
  const moduleName = moduleDTOList.value[form.value.currentModule].moduleName;
  const classify = item.classify;
  emits('exportProductByHistory', { moduleName, classify });
  window.setData = setData;
}
function exportProductByFromOhter(list) {
  const data = list.map(item => {
    return {
      ...item,
      source: 4,
      classifyName: `${route.query.moduleName}?${route.query.classify}`,
    };
  });
  window.opener.setData(data);
  proxy.$message.success('导入成功');
}

function handleResetHeight() {
  isFold.value = !isFold.value;
}
let allTableRef = ref(null);
function handleSelectAll(val) {
   const isCheck =  val.length > 0;
  moduleDTOList.value.forEach(item => {
    item.checked = isCheck;
    item.detailDTOList.forEach(i => {
      i.isCheck = isCheck
    })
  })
}

function handleSelect(val, row) {
 
  if (row.parentUuid) {
    
    //选中子级
    const parent = allData.value.find(item => item.uuid == row.parentUuid);
    if (val.findIndex(item => item.uuid == row.uuid) > -1) {
      // 选中

      allTableRef.value.toggleRowSelection(parent, true);

     
      const children = parent.children;
      children.forEach(item => {
      
        const bol = val.findIndex(i => i.uuid == item.uuid) > -1;
        allTableRef.value.toggleRowSelection(item, bol);

        if (item.uuid == row.uuid) {
          allTableRef.value.toggleRowSelection(item, true);
        }
      });

 moduleDTOList.value[row.parentIndex].isCheck = 1
 moduleDTOList.value[row.parentIndex].detailDTOList.forEach(item => {
  if(item.classify == row.moduleName){
    item.isCheck = 1
  }
 })

    } else {
      let bol = false
      const children = parent.children;
      val.forEach(item => {
        children.forEach(i => {
          if (item.uuid == i.uuid) {
            bol = true
          }
        })
      })
      moduleDTOList.value[row.parentIndex].detailDTOList.forEach(item => {
        if(item.classify == row.moduleName){
          item.isCheck = 0
        }
      })
      if(!bol){
        allTableRef.value.toggleRowSelection(parent, false);
         moduleDTOList.value[row.parentIndex].isCheck = 0
      }
    
    }
   

  }else{  //选中父级
      const isCheck = val.findIndex(item => item.uuid == row.uuid) > -1? 1: 0;
      moduleDTOList.value[row.index].isCheck = isCheck
      moduleDTOList.value[row.index].detailDTOList.forEach(item => {
        item.isCheck = isCheck
      })

  }
}

defineExpose({
  getData,
  getTree,
  setData,
  showEditPanel,
  closeEditPanel,
  toggleCollapse,
  switchToPreviousProduct,
  switchToNextProduct,
  isCurrentEditingProduct,
  insertEmptyProduct,
});
</script>

<style lang="scss" scoped>
.wrap {
  height: calc(100% - 20px);
  width: calc(100%);
  // background: #eee;
  box-sizing: border-box;
  padding: 15px;
  margin-bottom: 0;
  border-radius: 5px;
  .barbox {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .box {
    height: calc(100% - 60px);
    width: 100%;
  }
}
table {
  width: 100%;
  border-collapse: collapse;
  //   margin: 25px 0;
  //   height: 100%;
  font-size: 0.9em;
  min-width: 400px;
  color: #111;
  border-radius: 5px 5px 0 0;
  border-color: #ccc;
  table-layout: fixed;
  // overflow: hidden;
  // box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}
th,
td {
  //   padding: 5px;
  text-align: left;
  height: 25px;
  white-space: nowrap;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #dddddd;
  // border-bottom: 1px solid #dddddd;
}
.active {
  white-space: wrap;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
  border-bottom: 1px solid #dddddd;
}
thead {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
  // border-bottom: 1px solid #dddddd;
  td {
    // word-break: unset;
    white-space: unset;
  }
}
th {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
}
tr {
  // background-color: #f3f3f3;
  height: 25px;
}
.category {
  background-color: var(--el-color-primary-light-8);
  .delete_category {
    display: none;
  }
  &:hover .delete_category {
    display: inline-block;
  }
}
.cell_hover:hover {
  background-color: var(--el-color-info-light-8);
}

.index_product {
  &:hover .delete_product {
    display: inline-block;
  }
  &:hover .index_product_span {
    display: none;
  }
  .delete_product {
    display: none;
    cursor: pointer;
  }
  .source {
    display: inline-block;
    height: 20px;
    width: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 15px;
    border: 1px solid var(--el-color-success);
    color: var(--el-color-success);
  }
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.right {
  text-align: right;
}
.error {
  background-color: var(--el-color-danger-light-8);
}

/* 当前编辑产品的背景色 */
.current-editing {
  background-color: #e6f7ff !important;
  border-left: 3px solid #1890ff !important;
}

.current-editing:hover {
  background-color: #bae7ff !important;
}

/* 浮动编辑面板样式 */
.floating-edit-panel {
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  overflow: hidden;
  user-select: none;
  position: relative;
  min-width: 380px;
  min-height: 50px;
}

.panel-header {
  background: #f5f7fa;
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab;
}

.panel-header:active {
  cursor: grabbing;
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.panel-header h4 {
  margin: 0;
  font-size: 16px;
  color: #303133;
  font-weight: 600;
}

.product-navigation {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.nav-info {
  color: #666;
  font-weight: 500;
}

.header-controls {
  display: flex;
  align-items: center;
}

.panel-content {
  padding: 16px;
  overflow-y: auto;
  height: calc(100% - 50px);
}

.operation-hints {
  margin-bottom: 16px;
}

.keyboard-hint {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.keyboard-hint .el-icon {
  font-size: 14px;
  color: #0ea5e9;
}

.insert-hint {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.insert-hint .el-icon {
  font-size: 14px;
  color: #16a34a;
}

.panel-content :deep(.el-form-item) {
  margin-bottom: 16px;
}

.panel-content :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.panel-content :deep(.el-input__wrapper) {
  border-radius: 4px;
}

.panel-content :deep(.el-input-number) {
  width: 100%;
}

.panel-content :deep(.el-textarea__inner) {
  border-radius: 4px;
}

/* 调整大小控制点样式 */
.resize-handle {
  position: absolute;
  background: transparent;
}

.resize-right {
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
}

.resize-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: ns-resize;
}

.resize-corner {
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: nw-resize;
  background: linear-gradient(-45deg, transparent 0%, transparent 30%, #dcdfe6 30%, #dcdfe6 70%, transparent 70%);
}

.resize-corner::before {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(-45deg, transparent 0%, transparent 30%, #909399 30%, #909399 70%, transparent 70%);
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
