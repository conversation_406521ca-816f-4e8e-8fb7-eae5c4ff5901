import { createApp } from 'vue';
// 2. 引入组件样式
import 'vant/lib/index.css';
// 按需引入Element Plus组件
import { 
  ElTable, 
  ElTableColumn, 
  ElButton, 
  ElInput, 
  ElInputNumber,
  ElSelect, 
  ElOption,
  ElSwitch
} from 'element-plus';
import 'element-plus/dist/index.css';
// import Router from './router';
import App from './App.vue';
// 
console.log('第二个页面');

const app = createApp(App);
// 注册Element Plus组件
app.component('ElTable', ElTable);
app.component('ElTableColumn', ElTableColumn);
app.component('ElButton', ElButton);
app.component('ElInput', ElInput);
app.component('ElInputNumber', ElInputNumber);
app.component('ElSelect', ElSelect);
app.component('ElOption', ElOption);
app.component('ElSwitch', ElSwitch);
// app.use(Router)
app.mount('#app1');