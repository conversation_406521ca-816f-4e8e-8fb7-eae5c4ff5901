<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {dateFormat} from '@/utils/date';
let { proxy } = getCurrentInstance();
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'tree',
      label: '费用类型',
      dicUrl: '/blade-system/dict-biz/dictionary-tree?code=expenseType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
        
      },
      prop: 'expenseType',parent:false,
    },
    // {
    //   type: 'number',
    //   label: '票据张数',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      type: 'number',
      label: '费用金额',
      span: 12,
      display: true,
      prop: 'expensePrice',
    },

    {
      type: 'textarea',
      label: '用途',
      span: 24,
      display: true,
      prop: 'purpose',
      showWordLimit: true,
    },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
    {
      type: 'input',
      label: '登记人',
      span: 12,
      display: true,
      component: 'wf-user-select',
      prop: 'reimbursementUser',
      value: proxy.$store.getters.userInfo.user_id,
      showWordLimit: true,
      formatter: (row, column, cellValue) => {
        return row.reimbursementUserName
      }
    },
    {
      type: 'date',
      label: '日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'expenseDate',
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractExpense/save';
const delUrl = '/api/vt-admin/sealContractExpense/remove?ids=';
const updateUrl = '/api/vt-admin/sealContractExpense/update';
const tableUrl = '/api/vt-admin/sealContractExpense/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId:props.sealContractId
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId :props.sealContractId
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
   
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
