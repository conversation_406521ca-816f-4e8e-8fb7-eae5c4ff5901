<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
    v-if="props.isNeedPurchase == 1"
  >
    <template #menu-left>
      <div style="display: flex; align-items: center">
        <el-button type="primary" @click="handleAdd" icon="plus">新增</el-button>
        <el-checkbox
          v-model="isNeedPurchase"
          style="margin-left: 10px"
          @change="changeIsNeedPurchase"
          label="无需采购"
        ></el-checkbox>
      </div>
    </template>
    <template #menu="{ row }">
      <el-button
        text
        type="primary"
        @click="handleView(row, false)"
        v-if="row.applyStatus == 0"
        icon="edit"
        >编辑</el-button
      >
      <el-button
        text
        type="primary"
        @click="$refs.crud.rowDel(row)"
        v-if="row.applyStatus == 0"
        icon="delete"
        >删除</el-button
      >
      <el-button
        text
        type="primary"
        @click="handleComplete(row)"
        v-if="row.deliveryStatus == 0 && row.orderStatus == 3"
        icon="CircleCheckFilled"
        >完成</el-button
      >
      <el-button text type="primary" @click="handleView(row)" icon="view">详情</el-button>
      <el-button
        text
        type="primary"
        @click="approve(row)"
        v-if="route.query.type == 1"
        icon="notification"
        >批示</el-button
      >

      <el-button type="primary" text icon="Back" @click="back(row)" v-if="row.orderStatus == 0"
        >撤回</el-button
      >
    </template>
    <template #orderStatus="{ row }">
      <el-tag effect="plain" size="small" v-if="row.orderStatus == 0" type="warning">待采购</el-tag>
      <el-tag effect="plain" size="small" v-if="row.orderStatus == 1" type="warning">采购中</el-tag>
      <el-tag effect="plain" size="small" type="success" v-else-if="row.orderStatus == 3"
        >采购完成</el-tag
      >
      <el-tag effect="plain" size="small" type="warning" v-else-if="row.orderStatus == 2"
        >询价中</el-tag
      >
    </template>
    <template #file="{ row }">
      <file :fileList="row.attachList"></file>
    </template>
    <template #content="{ row }">
      <el-badge is-dot type="danger" style="margin-top: 5px" :hidden="row.isRead != 0">
        <el-text style="cursor: pointer" @click="handleClickApprovel(row)" type="primary">{{
          row.newComments || '---'
        }}</el-text>
      </el-badge>
    </template>
  </avue-crud>
  <el-empty v-else description="该项目无需采购">
    <el-button type="primary" @click="handleOpen">开启</el-button>
  </el-empty>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer v-model="drawer" size="90%" title="请购申请">
    <el-row style="height: 100%" :gutter="8">
      <el-col style="height: 100%" :span="8">
        <el-card shadow="never" class="box-card" style="height: 100%; overflow: auto">
          <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
            <template #customerInvoiceInfoId>
              <wfInvoiceDrop
                v-model="form.customerInvoiceInfoId"
                :id="form.customerId"
              ></wfInvoiceDrop>
            </template>
          </avue-form>
        </el-card>
      </el-col>

      <el-col style="height: 100%" :span="16">
        <el-card shadow="never" class="box-card" style="height: 100%; overflow: auto">
          <avue-crud
            v-if="!isDetail"
            :option="selectProductOption"
            @row-del="productRowDel"
            :data="selectProductList"
          >
            <template #menu-left>
              <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button>
            </template>
            <template #number="scope">
              <el-input-number
                :min="0"
                
                size="small"
                v-model="scope.row.number"
              ></el-input-number>
            </template>
            <template #remark="{ row }">
              <el-input v-model="row.remark" type="textarea"></el-input>
            </template>
          </avue-crud>
          <avue-crud v-else :option="selectDetailProductOption" :data="selectProductList">
          </avue-crud>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <div style="flex: auto">
        <el-button
          v-if="!isDetail"
          @click="
            isDraf = true;
            $refs.addFormRef.submit();
          "
          >保存 草稿</el-button
        >
        <el-button
          v-if="!isDetail"
          type="primary"
          @click="
            isDraf = false;
            $refs.addFormRef.submit();
          "
          >确认 申请</el-button
        >
      </div>
    </template>
    <el-drawer title="选择产品" v-model="innerDrawer" size="80%" append-to-body>
      <productSelect
        ref="productSelectRef"
        :type="0"
        :disableList="selectProductList"
      ></productSelect>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="confirmAddProduct">确 认</el-button>
        </div>
      </template>
    </el-drawer>
  </el-drawer>
  <el-drawer title="产品详情" size="60%" v-model="productListDrawer">
    <ProductList :id="currentId"></ProductList>
  </el-drawer>
  <el-drawer title="批示记录" size="40%" @close="onLoad" v-model="approveDrawer">
    <el-timeline>
      <el-timeline-item
        center
        v-for="(item, index) in approveList"
        :timestamp="item.createTime"
        placement="top"
      >
        <el-card>
          <el-form>
            <el-form-item label="批示人:" style="margin: 0; color: var(--el-color-primary)"
              >{{ item.createName }}
            </el-form-item>
            <el-form-item label="批示内容:" style="margin: 0; white-space: wrap">
              {{ item.comment }}
            </el-form-item>
            <el-form-item label="附件:">
              <File :fileList="item.attachList || []"></File>
            </el-form-item>
          </el-form>
        </el-card>
      </el-timeline-item>
    </el-timeline>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
import productSelect from './selectNew.vue';
import { ElMessageBox,ElMessage } from 'element-plus';
const props = defineProps({
  isNeedPurchase: {
    type: Number || String,
    default: 0,
  },
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '请购人',
      prop: 'applyName',
      width: 120,
      overHidden: true,
    },
    {
      label: '请购时间',
      prop: 'createTime',
      width: 140,
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm',
      overHidden: true,
    },

    {
      label: '备注',
      prop: 'remark',
      overHidden: true,
    },

    {
      label: '收货人',
      prop: 'deliveryUser',
      width: 120,
    },
    {
      label: '收货地址',
      prop: 'deliveryAddress',
      type: 'select',
    },
    {
      label: '联系方式',
      prop: 'deliveryContact',
      searchSpan: 6,
      searchRange: true,
      type: 'date',
      width: 150,
    },
    {
      label: '申请状态',
      prop: 'applyStatus',
      dicData: [
        {
          value: 0,
          label: '草稿',
        },
        {
          value: 1,
          label: '已确认',
        },
      ],
    },
    {
      label: '订单状态',
      prop: 'orderStatus',
    },
    // {
    //   label: '发货状态',
    //   prop: 'sendStatus',
    //   type: 'select',
    //   width: 100,
    //   dicData: [
    //     {
    //       value: 0,
    //       label: '未发货',
    //     },
    //     {
    //       value: 1,
    //       label: '已发货',
    //     },
    //   ],
    //   slot: true,
    // },
    {
      label: '收货状态',
      prop: 'deliveryStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '未收货',
        },
        {
          value: 1,
          label: '已收货',
        },
      ],
    },
    {
      label: '附件',
      prop: 'file',
    },
    {
      label: '批示内容',
      prop: 'content',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/projectApplyPurchase/delete?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectApplyPurchase/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

//   请购新增

let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      column: [
        {
          label: '申请人',
          type: 'input',
          value: proxy.$store.getters.userInfo.nick_name,
          prop: 'applyName',
          width: 80,
          span: 24,
          readonly: true,
        },
        {
          type: 'date',
          label: '申请时间',
          span: 24,
          display: true,
          format: 'YYYY-MM-DD',
          readonly: true,
          width: 100,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
          prop: 'createTime',
        },
        {
          type: 'date',
          label: '期望收货时间',
          span: 24,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'expectArriveDate',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },

        // {
        //   label: '附件',
        //   prop: 'file',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'object',
        //   loadText: '附件上传中，请稍等',
        //   span: 12,
        //   slot: true,
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
      ],
    },
    {
      label: '收货信息',
      prop: 'receiveInfo',
      column: [
        {
          label: '收货人',
          prop: 'deliveryUser',
          span: 24,
        },
        {
          label: '收货地址',
          prop: 'deliveryAddress',
          type: 'input',
          span: 24,
        },
        {
          label: '联系方式',
          prop: 'deliveryContact',

          span: 24,
        },
        {
          label: '实际到货时间',
          prop: 'actualDeliveryDate',
          type: 'date',
          valueFormat: 'YYYY-MM-DD',
          format: 'YYYY-MM-DD',
          span: 24,
          disabled: true,
        },
      ],
    },
  ],
});

let productOption = ref({
  header: true,
  menu: false,
  addBtn: false,
  height: 'auto',
  calcHeight: '20',
  selection: true,
  reserveSelection: true,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      formatter: row => {
        return row.product.unitName;
      },
      span: 12,
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '是否开票',
      prop: 'isInvoice',
    },
  ],
});
let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: '650',
  menuWidth: 100,
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',

      formatter: row => {
        return row.productBrand || row.product?.productBrand;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 90,
      bind: 'product.unitName',
      formatter: row => {
        return row.customUnit || row.unitName || row.productVO?.unitName;
      },
      span: 12,
    },
    {
      label: '清单数量',
      prop: 'deepenNumber',
      type: 'number',
      align: 'center',
      span: 12,
      cell: false,
    },
    {
      label: '已请购数量',
      prop: 'purchaseNums',
      width: 100,
      align: 'center',
    },
    {
      label: '未采购数量',
      prop: 'noPurchaseNums',
      width: 100,
      align: 'center',
    },
    {
      label: '采购数量',
      prop: 'number',
      type: 'number',
      align: 'center',
      span: 12,
      width: 160,
      cell: false,
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      cell: false,
      width: 160,
      type: 'textarea',
    },
  ],
});
let selectDetailProductOption = ref({
  header: true,
  menu: false,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: 'auto',
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',
      cell: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',

      formatter: row => {
        return row.productBrand || row.product?.productBrand;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 90,
      bind: 'productVO.unitName',
      formatter: row => {
        return row.customUnit || row.unitName || row.productVO?.unitName;
      },
      span: 12,
    },
    //   {
    //     label: '清单数量',
    //     prop: 'deepenNumber',
    //     type: 'number',
    //     align: 'center',
    //     span: 12,
    //     cell: false,
    //   },
    {
      label: '采购数量',
      prop: 'number',
      type: 'number',
      align: 'center',

      span: 12,
      cell: false,
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      overHidden: true,
      align: 'center',
      span: 12,
      cell: false,
    },
  ],
});
let contractList = ref([]);
let currentContractId = ref('');
// 查询客户合同
function queryContract(id) {
  axios
    .get('/api/vt-admin/sealContract/pageForStatement', {
      params: {
        customerId: form.value.customerId,
        ...applyQuery.value,
        ...pageContract.value,
        selectType: 5,
      },
    })
    .then(res => {
      contractList.value = [...contractList.value, ...res.data.data.records];
    });
}

function handleAdd() {
  isDetail.value = false;
  formOption.value.detail = false;
  drawer.value = true;

  proxy.$nextTick(() => {
    clearAll();
  });
}
function addProduct() {
  innerDrawer.value = true;
}
let productList = ref([]);
let selectLoading = ref(false);

let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  const data = proxy.$refs.productSelectRef.getSelectList();
  console.log(data);
  selectProductList.value.push(
    ...data.map(i => {
      return {
        ...i,
        detailId: i.id,
        id: null,
        number: parseFloat(i.noPurchaseNums),
        noPurchaseNums: parseFloat(i.noPurchaseNums),
        purchaseNums: parseFloat(i.purchaseNums),
        remark: i.deepenRemark,
        product: {
          ...i.product,
        },
      };
    })
  );
  //   selectList.value = [];
  // 清空选择
  proxy.$refs.productSelectRef.clearSelect();
  innerDrawer.value = false;
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(
        item => item.detailId !== row.detailId
      );
      setTotalAmount();
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.totalPrice * 1),
    0
  );
  form.value.invoicePrice = totalPrice;
}
let isDraf = ref(false);
function submit(form, done, loading) {
  form.purchaseDetailDTOList = selectProductList.value.map(item => {
    return {
      ...item,
    };
  });
  form.createTime = null;
  form.projectId = route.query.id;
  form.applyStatus = isDraf.value ? 0 : 1;

  axios.post(`/api/vt-admin/projectApplyPurchase/${form.id ? 'edit' : 'save'}`, form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
      clearAll();
    }
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();

  selectProductList.value = [];
  productList.value = [];

  form.value.applyName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTime = dateFormat(new Date(), 'yyyy-MM-dd');
  form.value.id = null;
  pageOption.value.currentPage = 1;
  pageContract.value.current = 1;
}
let pageContract = ref({
  current: 1,
  size: 20,
});
function loadMore() {
  console.log(1111);
  pageContract.value.current += 1;
  queryContract();
}

// 产品列表
let productListDrawer = ref(false);
let currentId = ref(null);
function viewProductList(row) {
  productListDrawer.value = true;
  currentId.value = row.id;
}
function handleComplete(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'datetime',
          label: '实际收货时间',
          span: 24,
          value: dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDeliveryDate',
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectApplyPurchase/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let isDetail = ref(false);
function handleView(row, type = true) {
  drawer.value = true;
  isDetail.value = type;
  formOption.value.detail = type;
  axios
    .get('/api/vt-admin/projectApplyPurchase/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      selectProductList.value = res.data.data.detailVOS.map(item => {
        return {
          ...item,
          number: parseFloat(item.number),
          noPurchaseNums: parseFloat(item.noPurchaseNums),
          purchaseNums: parseFloat(item.purchaseNums),
        };
      });
      form.value = res.data.data;
    });
}
function approve(row) {
  proxy.$refs.dialogForm.show({
    title: '批示',
    option: {
      column: [
        {
          label: '批示内容',
          prop: 'comment',
          type: 'textarea',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入批示内容',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/projectApplyPurchaseComments/save', {
          ...res.data,
          applyId: row.id,
          files: res.data.files.map(item => item.value).join(','),
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          res.close();
          onLoad();
        });
    },
  });
}
function editAddress(row) {
  proxy.$refs.dialogForm.show({
    title: '调整',
    option: {
      column: [
        {
          label: '收货人',
          prop: 'deliveryUser',
          span: 24,
          value: row.deliveryUser,
        },
        {
          label: '收货地址',
          prop: 'deliveryAddress',
          type: 'input',
          span: 24,
          value: row.deliveryAddress,
        },
        {
          label: '联系方式',
          prop: 'deliveryContact',
          value: row.deliveryContact,
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        ...row,
        ...res.data,
      };
      axios.post(`/api/vt-admin/projectApplyPurchase/editInfo`, data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
      axios
        .post('/api/vt-admin/projectApplyPurchaseComments/save', {
          ...res.data,
          applyId: row.id,
          files: res.data.files.map(item => item.value).join(','),
        })
        .then(r => {});
    },
  });
}
function handleClickApprovel(row) {
  approveList.value = [];
  approveDrawer.value = true;
  queryApprove(row.id);
}
let approveList = ref([]);
function queryApprove(id) {
  axios
    .get('/api/vt-admin/projectApplyPurchaseComments/page', {
      params: {
        applyId: id,
      },
    })
    .then(res => {
      approveList.value = [...approveList.value, ...res.data.data.records];
    });
}

let approveDrawer = ref(false);

// 撤回请购单
function back(row) {
  proxy
    .$confirm('是否确认退回?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/purchaseOrder/cancelProjectPurchase', { id: row.orderId })
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success('退回成功');
            onLoad();
          } else {
            proxy.$message.error(res.data.msg);
          }
        });
    })
    .catch(() => {});
}
let isNeedPurchase = ref(false);
const emits = defineEmits(['success']);
function changeIsNeedPurchase(value) {
  if (value) {
    ElMessageBox.confirm('是否确认不需要采购', '提示', {
      type: 'warning',
    }).then(() => {
      isNeedPurchase.value = false;
      axios
        .post('/api/vt-admin/project/updateIsNeedPurchase', {
          id: route.query.id,
          isNeedPurchase: 0,
        })
        .then(res => {
          ElMessage.success(res.data.msg);
          emits('success');
        });
    }).catch(() => {
      isNeedPurchase.value = false
    });
  }
}
function handleOpen() {
  axios
    .post('/api/vt-admin/project/updateIsNeedPurchase', { id: route.query.id, isNeedPurchase: 1 })
    .then(res => {
      ElMessage.success(res.data.msg);
      emits('success');
    });
}
</script>

<style lang="scss" scoped></style>
