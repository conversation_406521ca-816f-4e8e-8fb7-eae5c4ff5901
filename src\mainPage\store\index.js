import { createStore } from 'vuex';
import user from './modules/user';
import common from './modules/common';
import tags from './modules/tags';
import logs from './modules/logs';
import dict from './modules/dict';
import notice from './modules/notice';
import getters from './getters';
import agent from './modules/agent';

const store = createStore({
  modules: {
    user,
    common,
    logs,
    tags,
    dict,
    notice,
    agent
  },
  getters,
});

export default store;
