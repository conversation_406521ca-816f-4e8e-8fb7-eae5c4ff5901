<template>
  <basic-container style="height: 100%">
    <Title
      >询价单详情
      <template #foot>
        <el-button type="primary" @click="confirm" v-if="type == 1">确认</el-button>

        <el-button
          type="primary"
          @click="submitAudit"
          v-if="form.auditType == 0 || form.auditStatus == 2"
          >提交</el-button
        >
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="计划名称:">
            <el-tag effect="plain" size="large">{{ form.orderNo }}</el-tag>
          </el-form-item>
          <el-form-item label="报价名称">
            <el-tag effect="plain" size="large">{{ form.offerName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="下单时间">
            <el-tag effect="plain" size="large">{{ form.orderDate || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="审核状态">
            <div v-if="form.auditStatus == 1 || form.auditStatus == 2">
              <el-tag effect="plain" v-if="form.auditStatus == 1" size="large" type="success"
                >审核成功</el-tag
              >
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="form.auditReason"
                placement="top-start"
                v-if="form.auditStatus == 2"
              >
                <el-tag effect="plain" size="large" type="danger">审核失败</el-tag>
              </el-tooltip>
            </div>
            <div v-else>
              <el-tag effect="plain" v-if="form.auditType == 1" size="large" type="info"
                >待采购主管审核</el-tag
              >
              <el-tag effect="plain" v-if="form.auditType == 2" size="large" type="info"
                >待总经理审核</el-tag
              >
              <el-tag effect="plain" v-if="form.auditType == 0" size="large" type="info"
                >待提交</el-tag
              >
            </div>
          </el-form-item>

          <el-form-item label="合同名称:" v-if="type == 1 && form.purchaseType != 2">
            <el-tag effect="plain" size="large">
              <el-link type="primary" @click="toDetail(form)">
                {{ form.offerName }}
              </el-link>
            </el-tag>
          </el-form-item>
          <el-form-item label="合同总额:" v-if="type == 1 && form.purchaseType != 2">
            <el-tag effect="plain" size="large">{{ form.contractTotalPrice }}</el-tag>
          </el-form-item>
          <el-form-item label="毛利润:" v-if="type == 1 && form.purchaseType != 2">
            <el-tag effect="plain" size="large">{{ form.grossProfit }}</el-tag>
          </el-form-item>
          <el-form-item label="毛利润率:" v-if="type == 1 && form.purchaseType != 2">
            <el-tag effect="plain" size="large">{{ form.grossProfitRate }}</el-tag>
          </el-form-item>
          <el-form-item label="采购注意事项">
            {{ form.purchaseRemark || '---' }}
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <el-divider content-position="left"
      ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
        >询价结果</span
      ></el-divider
    >
    <el-table
      class="avue-crud"
      show-summary
      :summary-method="summaryMethod"
      v-loading="loading"
      :data="form.detailList"
      size="small"
      border
      :row-style="rowStyle"
      @selection-change="handleSelectionChange"
      ref="tableRef"
    >
      <el-table-column type="selection" fixed="left" :selectable="row => row.productId" width="50">
      </el-table-column>
      <el-table-column
        label="产品名称"
        show-overflow-tooltip
        #default="{ row }"
        width="150"
        prop="productVO.productName"
      >
        <el-popover
          :disabled="!row.productVO"
          placement="top-start"
          title="请购原产品详情"
          width="400"
          trigger="hover"
        >
          <el-form inline label-position="top" label-width="80px">
            <el-form-item label="产品名称">
              <el-tag effect="plain" size="large">{{ row.customProductName }}</el-tag>
            </el-form-item>
            <el-form-item label="品牌">
              <el-tag effect="plain" size="large">{{ row.productBrand }}</el-tag>
            </el-form-item>
            <el-form-item label="产品型号">
              <el-tag effect="plain" size="large">{{ row.customProductSpecification }}</el-tag>
            </el-form-item>
            <el-form-item label="单位">
              <el-tag effect="plain" size="large">{{ row.customUnit }}</el-tag>
            </el-form-item>
          </el-form>
          <template #reference>
            <el-link type="primary" class="m-2">{{
              row.productVO?.productName || row.customProductName
            }}</el-link>
          </template>
        </el-popover>
      </el-table-column>
      <el-table-column
        label="规格型号"
        prop="customProductSpecification"
        show-overflow-tooltip
        width="150"
        #default="{ row }"
      >
        {{ row.productVO?.productSpecification || row.customProductSpecification }}
      </el-table-column>
      <!-- <el-table-column label="产品图片" width="100" #default="{ row }">
        <el-image
          style="width: 50px"
          :preview-src-list="[row.productVO?.coverUrl]"
          :src="row.productVO?.coverUrl"
        ></el-image>
      </el-table-column> -->
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        width="200"
        #default="{ row }"
        prop="customDescription"
      >
        {{ row.productVO? row.productVO.description : row.customProductDescription }}
      </el-table-column>
      <el-table-column label="品牌" #default="{ row }" prop="productVO.productBrand" width="100">{{
        row.productVO?.productBrand || row.productBrand
      }}</el-table-column>
      <el-table-column label="单位" #default="{ row }" prop="unitName" width="60">
        {{ row.productVO?.unitName || row.customUnit }}
      </el-table-column>
      <el-table-column label="数量" align="center" width="60" #default="{ row }" prop="number">
      </el-table-column>
      <el-table-column
        label="使用库存数"
        align="center"
        width="100"
        #default="{ row }"
        prop="inventoryNumber"
      >
        <span>{{ row.inventoryNumber || 0 }}</span>
      </el-table-column>
      <el-table-column label="单价" align="center" width="100" #default="{ row }" prop="unitPrice">
      </el-table-column>
      <el-table-column
        label="金额"
        align="center"
        width="100"
        #default="{ row }"
        prop="totalAmount"
      >
        <span>
          {{ row.totalPrice }}
        </span>
      </el-table-column>
      <el-table-column
        label="库存数量"
        align="center"
        width="70"
        #default="{ row }"
        prop="surplusNumber"
      >
      </el-table-column>
      <!-- <el-table-column
        label="专项成本"
        #default="{ row, $index }"
        width="120"
        prop="specialCostPrice"
      >
        <span>{{ row.specialCostPrice }}</span>
      </el-table-column>
      <el-table-column
        label="专项供应商"
        #default="{ row, $index }"
        width="200"
        show-overflow-tooltip
        prop="specialSupplierId"
      >
        <span>{{ row.specialSupplierName }}</span>
      </el-table-column> -->

      <!-- <el-table-column label="报价(采购价)" #default="{ row }" prop="unitPrice">
          <span>{{ row.unitPrice }}</span>
        </el-table-column> -->

      <el-table-column
        label="供应商"
        #default="{ row }"
        width="160"
        show-overflow-tooltip
        prop="supplier"
      >
        <el-popover
          placement="left"
          @show="handleShow(row)"
          trigger="click"
          title="关联供应商"
          :width="800"
        >
          <avue-crud :option="option" :data="supplierForm.supplierList">
            <template #supplierName="{ row }">
              <div style="display: flex; align-items: center">
                <i
                  title="专项供应商"
                  v-if="row.isSpecial"
                  style="color: var(--el-color-primary); font-size: 30px"
                  class="element-icons el-icon-gongyingshang"
                ></i>
                <span type="primary">{{ row.supplierName }}</span>
              </div>
            </template>
          </avue-crud>
          <el-input
            v-model="row.remark"
            readonly
            type="textarea"
            style="margin-top: 10px"
            rows="5"
            placeholder=""
          ></el-input>
          <template #reference>
            <el-link type="primary" ref="buttonRef">{{ row.supplierName }}</el-link>
          </template>
        </el-popover>
      </el-table-column>
       <el-table-column label="自服务" prop="remark" show-overflow-tooltip width="85">
        <template #header>
          <el-tooltip
            effect="dark"
            content="如果此产品是公司自服务，不涉及采购且不涉及库存,请勾选"
            placement="top"
          >
            <div style="display: flex; align-items: center">
              <el-icon size="15"><QuestionFilled /></el-icon>自服务
            </div>
          </el-tooltip>
        </template>
        <template #default="{ row }">
          <el-switch
            size="small"
            title="如果此产品是公司自服务，不涉及采购且不涉及库存,请勾选"
            @change="handleDelete(row)"
            v-model="row.isDelete"
          />
        </template>
      </el-table-column>
      <el-table-column label="采购类型" prop="purchaseType" show-overflow-tooltip width="150">
        <template #default="{ row }">
          <el-tag type="primary" v-if="row.purchaseType == 1" effect="plain">库存采购</el-tag>
          <el-tag type="success" v-else effect="plain">报价采购</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="备注"
        prop="remark"
        show-overflow-tooltip
        width="150"
      ></el-table-column>
     
      <el-table-column
        label="操作"
        #default="{ row }"
        width="240"
        fixed="right"
        v-if="form.auditStatus !== 1"
        align="center"
      >
        <div v-if="row.productId">
          <el-button
            type="primary"
            icon="QuestionFilled"
            @click="inquiry(row)"
            text
            :disabled="
              (type == 0 && form.auditStatus != 0) ||
              (row.number * 1 - row.inventoryNumber * 1 <= 0 && row.number * 1 != 0)
            "
            >询价</el-button
          >
          <el-button
            type="primary"
            icon="Grid"
            @click="decompose(row)"
            text
            v-if="form.purchaseType != 2 && row.purchaseType != 1"
            :disabled="
              (type == 0 && form.auditStatus != 0) ||
              (row.number * 1 - row.inventoryNumber * 1 <= 0 && row.number * 1 != 0)
            "
            >拆解</el-button
          >
          <el-button
            type="primary"
            icon="edit"
            @click="addMore(row, 1)"
            text
            v-if="form.purchaseType != 2 && row.purchaseType == 1"
            :disabled="
              (type == 0 && form.auditStatus != 0) ||
              (row.number * 1 - row.inventoryNumber * 1 <= 0 && row.number * 1 != 0)
            "
            >更改数量</el-button
          >
          <el-button
            type="primary"
            icon="delete"
            @click="handleDelete(row)"
            text
            v-if="form.purchaseType != 2 && row.purchaseType == 1"
            :disabled="
              (type == 0 && form.auditStatus != 0) ||
              (row.number * 1 - row.inventoryNumber * 1 <= 0 && row.number * 1 != 0)
            "
            >删除</el-button
          >
          <el-button
            type="primary"
            icon="edit"
            @click="editNumber(row)"
            text
            v-if="form.purchaseType != 2 && row.purchaseType != 1"
            title="出库"
            :disabled="
              (type == 0 && form.auditStatus != 0) || row.number * 1 - row.inventoryNumber * 1 <= 0
            "
            >使用库存</el-button
          >
          <el-button
            type="primary"
            icon="Operation"
            @click="addMore(row, 0)"
            text
            v-if="form.purchaseType != 2 && row.purchaseType != 1"
            title="采购"
            >库存采购</el-button
          >
          <el-button type="primary" icon="plus" @click="relationProduct(row)" text
            >重新关联</el-button
          >
          
        </div>
        <div v-else>
          <el-button type="primary" icon="plus" @click="relationProduct(row)" text
            >关联产品</el-button
          >
        </div>
      </el-table-column>
    </el-table>
    <el-divider content-position="left"
      ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
        >询价过程</span
      ></el-divider
    >

    <el-collapse v-model="activeName">
      <el-collapse-item
        :icon="CaretRight"
        v-for="(item, index) in list"
        :key="item.id"
        :title="'询价单' + index"
        :name="index"
      >
        <template #title>
          <el-text size="large" type="primary">{{ index + 1 }}丶{{ item.inquiryCode }}</el-text>
          <el-button
            v-if="item.inquiryStatus == 1"
            style="margin-left: 5px"
            type="primary"
            @click.stop="editInquiryFn(item)"
            plain
            size="small"
            icon="edit"
            >编辑</el-button
          >
          <!-- 删除 -->
           <el-button
            v-if="item.inquiryStatus == 1"
            style="margin-left: 5px"
            type="danger"
            @click.stop="deleteInquiryFn(item)"
            plain
            size="small"
            icon="delete"
            >删除</el-button
          >
          <!-- <el-tag type="warning" effect="plain">待报价</el-tag> -->
        </template>
        <inquiryResult
          :ref="'inquiryResultRef' + index"
          @statusChange="handleStatusChange"
          :inquiryId="item.id"
        ></inquiryResult>
      </el-collapse-item>
    </el-collapse>
    <el-drawer v-model="drawer" size="80%" title="询价">
      <el-row :gutter="20" style="height: 100%">
        <el-col :span="6">
          <el-row v-for="item in supplierInfoList">
            <el-col :span="24">
              <el-card shadow="hover" class="myCard" :body-style="{ padding: '5px' }">
                <template #header>
                  <el-text type="primary">{{ item.supplierName }}</el-text>
                </template>
                <el-form v-if="item.supplierConcatVOList.length > 0">
                  <el-row v-for="i in item.supplierConcatVOList">
                    <el-col :span="12">
                      <el-form-item style="margin: 0" label="联系人:">{{
                        i.concatName
                      }}</el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item style="margin: 0" label="电话:">{{
                        i.concatPhone
                      }}</el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
                <el-empty style="height: 150px" image-size="100" v-else></el-empty>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="18">
          <el-card shadow="hover">
            <el-collapse v-model="activeNames">
              <el-collapse-item name="1" >
                <template #title>
                  <span>产品信息</span>
                  <!-- 淘宝搜索 -->
                   <el-button size="small" style="margin-left:5px" type="primary" @click.stop="searchPrice(detailForm,2)">淘宝搜索</el-button>
                   <!-- 京东搜索 -->
                    <el-button size="small" type="primary" style="margin-left:5px"  @click.stop="searchPrice(detailForm,1)">京东搜索</el-button>
                </template>
                <avue-form :option="detailOption" v-model="detailForm"> </avue-form>
              </el-collapse-item>
              <el-collapse-item name="2" title="供应商信息">
                <avue-form
                  :option="editOption"
                  v-model="editForm"
                  ref="editFormRef"
                  :submit="submit"
                >
                  <template #radio="{ row }">
                    <el-radio
                      v-model="editForm.selectRow"
                      @change="handleChange(row)"
                      :label="row.supplierId"
                      :disabled="!row.purchasePrice && row.purchasePrice != 0"
                      :value="row.supplierId"
                    >
                      <!-- <el-icon title="专项供应商" v-if="row.isSpecial" size="15" style="color:var(--el-color-primary)" ><Location /></el-icon> -->
                      <i
                        title="专项供应商"
                        v-if="row.isSpecial == 1"
                        style="color: var(--el-color-primary); font-size: 30px"
                        class="element-icons el-icon-gongyingshang"
                      ></i>
                      <span v-else> {{ `` }}</span>
                    </el-radio>
                  </template>
                  <template #supplierName="{ row }">
                    <i
                      title="专项供应商"
                      v-if="row.isSpecial == 1"
                      style="color: var(--el-color-primary); font-size: 30px"
                      class="element-icons el-icon-gongyingshang"
                    ></i>
                    <el-link type="primary" @click="getSupplierInfo(row)">{{
                      row.supplierName
                    }}</el-link>
                  </template>
                </avue-form>
              </el-collapse-item>
              <el-collapse-item name="3" title="备注">
                <el-input
                  v-model="detailForm.remark"
                  placeholder="请填写备注"
                  type="textarea"
                  style="margin-top: 10px"
                  rows="5"
                  @blur="editRemark"
                ></el-input>
              </el-collapse-item>
            </el-collapse>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="drawer = false">确 认</el-button>
        </div>
      </template>
    </el-drawer>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 供应商选择弹窗 -->
    <wf-supplier-select
      ref="supplier-select"
      check-type="box"
      :userUrl="`/api/vt-admin/supplier/pageForSupplier?productId=${detailForm.productId}`"
      :params="{
        brand: detailForm?.productBrand,
      }"
      @onConfirm="handleUserSelectConfirm"
    ></wf-supplier-select>
    <el-drawer size="50%" v-model="supplierDrawer">
      <supplierBaseInfo :form="supplierInfo"></supplierBaseInfo>
    </el-drawer>
    <el-drawer v-model="decomposeDrawer" size="50%" title="拆解">
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <el-text type="primary">{{ detailForm.productVO?.productName }}</el-text>
            </template>
            <avue-form v-model="detailForm" :option="decomposeOption"></avue-form>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-card shadow="hover">
            <template #header>
              <div style="display: flex; justify-content: space-between; align-items: center">
                <div>
                  拆解产品
                  <productSelectDrop
                    v-if="$route.query.type != 'detail'"
                    @select="handleProductSelectConfirm"
                    style="margin-left: 5px"
                  ></productSelectDrop>
                </div>
                <el-form v-if="detailForm.productId">
                  <el-form-item label="保留原产品">
                    <el-switch v-model="detailForm.isPre" @change="handleIsPreChange"></el-switch>
                  </el-form-item>
                </el-form>
              </div>
            </template>
            <avue-form v-model="decomposeEditForm" :option="decomposeEditFormOption"></avue-form>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <el-button type="primary" icon="check" @click="decomposeSubmit">确 定</el-button>
        <el-button icon="close" @click="decomposeDrawer = false">取 消</el-button>
      </template>
    </el-drawer>
    <wfProductSelect
      @onConfirm="handleProductSelectConfirm"
      :checkType="addStatus == 1 ? 'radio' : 'checkbox'"
      :productForm="customProductDetail"
      :searchParams="addStatus == 1 ? searchParams : {}"
      :addUrl="'/api/vt-admin/product/save'"
      ref="productSelectRef"
    ></wfProductSelect>
    <inquiry-select ref="inquirySelectRef" @onConfirm="handleInquirySelectConfirm"></inquiry-select>
    <el-dialog
      title="生成询价单"
      v-model="dialogVisible"
      append-to-body
      class="avue-dialog avue-dialog--top"
    >
      <avue-form
        ref="addFormRef"
        :option="addOption"
        @submit="addSubmit"
        v-model="addForm"
      ></avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button @click="$refs.addFormRef.submit()" type="primary">提 交</el-button>
      </div>
    </el-dialog>
    <el-drawer
      title="查看询价结果"
      v-model="resultDrawer"
      direction="rtl"
      size="90%"
      @opened="handleOpend"
      :show-close="true"
      :wrapperClosable="true"
    >
      <inquiryList shadow="never" ref="inquiryListRef" :order-id="$route.query.id"></inquiryList>
    </el-drawer>
    <div class="inquiry_box" v-if="selectList.length > 0">
      <transition>
        <addInquiryNew :tableData="selectList" @cancel="cancelAdd"></addInquiryNew>
      </transition>
    </div>
    <editInquiry
      ref="editInquiryRef"
      :id="currentInquiryId"
      :purchaseId="$route.query.id"
    ></editInquiry>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfSupplierSelect from '@/components/Y-UI/wf-supplier-select.vue';
import supplierBaseInfo from '../../supplier/detail/supplierBaseInfo.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
import inquirySelect from './inquirySelect.vue';
import { useStore } from 'vuex';
import { computed } from 'vue';
import inquiryList from '../../inquiry/inquiryList.vue';
import addInquiryNew from './addInquiryNew.vue';
import inquiryResult from './inquiryResult.vue';
import editInquiry from './editInquiry.vue';
import axios from 'axios';
import { ElMessage,ElMessageBox } from 'element-plus';
let route = useRoute();
let router = useRouter();
const store = useStore();
let userInfo = computed(() => store.getters.userInfo);
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
const { proxy } = getCurrentInstance();
onMounted(() => {
  getDetail();
  getList();
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
const type = ref(route.query.type); // 0 采购进入页面  1 采购主管进入页面

function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };

  form.products = keys.reduce((pre, cur) => {
    pre.push(
      ...data.detailMap[cur].map(i => {
        return {
          ...i.productVO,
          ...i,
        };
      })
    );
    return pre;
  }, []);

  return form;
}
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseOrder/detailForInquiry', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      form.value = res.data.data;
    });
}
let supplierForm = ref({
  supplierList: [],
});
const option = ref({
  submitBtn: false,
  emptyBtn: false,
  header: false,
  border: true,
  menu: false,
  size: 'small',
  column: [
    {
      label: '供应商',
      prop: 'supplierName',
      cell: false,
      width: 150,
      overHidden: true,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      span: 12,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: false,
    },
    {
      label: '质保期（年）',
      prop: 'warrantyPeriod',
      type: 'number',
      width: 120,
      controls: false,
      cell: false,
      span: 12,
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'radio',
      value: 1,
      cell: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      span: 12,
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter: row => {
        return parseFloat(row.taxRate) + '%';
      },
    },
    {
      label: '单价',
      prop: 'purchasePrice',
      type: 'number',
      controls: false,
      cell: false,
      span: 12,
    },
  ],
});
let drawer = ref(false);
let activeNames = ref(['1', '2', '3']);
let detailOption = ref({
  detail: true,
  emptyBtn: false,
  submitBtn: false,

  labelWidth: 100,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
      span: 6,
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
      span: 6,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      search: true,
      span: 18,
      type: 'input',
    },
    {
      label: '库存数量',
      prop: 'surplusNumber',
      overHidden: true,
      search: true,
      span: 6,
      type: 'input',
    },
    {
      label: '专项价格',
      prop: 'specialCostPrice',
      type: 'number',
      span: 12,
      hide: true,
      editDisplay: false,
      overHidden: true,
    },
    {
      label: '专项供应商',
      prop: 'specialSupplierName',
      type: 'input',
      hide: true,
      span: 12,
      editDisplay: false,
      overHidden: true,
    },
    {
      label: '成本价',
      prop: 'costPrice',
      type: 'number',
      hide: true,
      span: 12,
      editDisplay: false,
      overHidden: true,
    },

    {
      label: '采购价',
      prop: 'costPrice',
      type: 'number',
      span: 12,
      hide: true,
      editDisplay: false,
      overHidden: true,
    },

    {
      label: '商品描述',
      prop: 'description',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },
  ],
});
const editOption = ref({
  submitBtn: false,
  emptyBtn: false,

  size: 'small',
  column: [
    {
      label: '',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      labelWidth: 0,
      size: 'small',
      children: {
        align: 'center',
        size: 'small',
        headerAlign: 'center',
        delBtn: false,
        rowAdd: done => {
          addProduct();
          // done({});
        },
        rowDel: (row, done) => {
          proxy.$message.warning('无效操作');
          //   done();
        },

        column: [
          {
            label: '选择',
            prop: 'radio',
            width: 60,
            hide: false,
          },
          {
            label: '供应商',
            prop: 'supplierName',
            overHidden: true,
          },
          {
            label: '报价时间',
            prop: 'offerDate',
            type: 'date',
            shortcuts: [
              {
                text: '今天',
                value: new Date(),
              },
              {
                text: '昨天',
                value: () => {
                  const date = new Date();
                  date.setTime(date.getTime() - 3600 * 1000 * 24);
                  return date;
                },
              },
              {
                text: '前天',
                value: () => {
                  const date = new Date();
                  date.setTime(date.getTime() - 3600 * 1000 * 24 * 2);
                  return date;
                },
              },
              {
                text: '一周前',
                value: () => {
                  const date = new Date();
                  date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
                  return date;
                },
              },
            ],
            span: 12,
            blur: ({ value, row }) => {
              if (!value) return;
              confirmClick(row);
            },
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
          {
            label: '质保期（年）',
            prop: 'warrantyPeriod',
            type: 'input',
            width: 120,
            controls: false,
            blur: ({ value, row }) => {
              if (!value) return;
              confirmClick(row);
            },
            span: 12,
          },
          {
            label: '是否含税',
            prop: 'isHasTax',
            type: 'radio',
            value: 1,
            change: ({ value, row }) => {
              proxy.$nextTick(() => {
               
                confirmClick(row);
              });
            },
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            span: 12,
          },
          {
            label: '税率',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            blur: ({ value, row }) => {
              if (!value) return;
              confirmClick(row);
            },
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
          },
          {
            label: '采购报价',
            prop: 'purchasePrice',
            type: 'number',
            controlsPosition: 'right',
           
            blur: ({ value, row }) => {
              if (!value && value != 0) return;
              confirmClick(row);
            },
            span: 12,
          },
        ],
      },
    },
  ],
});
let detailForm = ref({});
function inquiry(row) {
  drawer.value = true;
  detailForm.value = { ...row.productVO, ...row };
  editForm.value.selectRow = row.supplierId;
  //   查询关联的供应商信息
  getSupplierList(row);
}
function getSupplierList(row, type) {
  axios
    .get('/api/vt-admin/purchaseOrderInquiry/list', {
      params: {
        orderId: route.query.id,
        orderDetailId: row.id,
        productId: row.productId,
      },
    })
    .then(res => {
      
      if (type == 'detail') {
        supplierForm.value.supplierList = res.data.data.map(item => {
           return {
            ...item,
            taxRate:item.taxRate?parseFloat(item.taxRate) :null
          }
        }
        );
      } else {
        const special = res.data.data.find(item => item.isSpecial == 1);
        editForm.value.supplierList = res.data.data.map(item => {
          return {
            ...item,
             taxRate:item.taxRate? '' + parseFloat(item.taxRate) :null
          }
        });
        getSupplierInfoList(editForm.value.supplierList.map(item => item.supplierId));
        if (special && !editForm.value.selectRow) {
          editForm.value.selectRow = special.supplierId;
          handleChange({ id: special.id }, '此产品存在专项供应商,已为你自动关联专项供应商');
        }
      }
    });
}
let editForm = ref({
  selectRow: '',
});
function confirmClick(row) {
  console.log(row);
  axios
    .post('/api/vt-admin/purchaseOrderInquiry/saveOrUpdate', {
      ...row,
      productId: detailForm.value.productId,
      orderId: route.query.id,
      isCheck: editForm.value.selectRow == row.supplierId ? 1 : 0,
      orderDetailId: detailForm.value.id,
    })
    .then(res => {
      proxy.$message.success(res.data.msg);
      getDetail();
    });
}
function handleChange(row, text) {
  console.log(row);
  if (!row.id) return proxy.$message.error('请先编辑询价信息');
  axios
    .post('/api/vt-admin/purchaseOrderInquiry/relationSupplier', {
      orderDetailId: detailForm.value.id,
      id: row.id,
    })
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(text || res.data.msg);
      } else {
        proxy.$message.success(res.data.msg);
      }

      getDetail();
    });
}
function handleShow(row) {
  getSupplierList(row, 'detail');
}
function submitAudit(params) {
  axios
    .post('/api/vt-admin/purchaseOrder/submitAudit', {
      id: route.query.id,
    })
    .then(res => {
      proxy.$message.success(res.data.msg);
      proxy.$store.dispatch('getMessageList');
      getDetail();
    });
}
function addProduct() {
  proxy.$refs['supplier-select'].visible = true;
}

function handleUserSelectConfirm(ids) {
  const data = ids.split(',').map(item => {
    return {
      productId: detailForm.value.productId,
      supplierId: item,
    };
  });
  axios.post('/api/vt-admin/supplierProduct/saveBatch', data).then(res => {
    proxy.$message.success('添加成功');
    getSupplierList(detailForm.value);
    getSupplierInfoList(ids.split(','), true);
  });
}
function confirm() {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 1,
          dicData: [
            {
              value: 1,
              label: '通过',
            },
            {
              value: 2,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 2,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseOrder/audit', {
          id: form.value.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');

          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        });
    },
  });
}
function editRemark(text) {
  axios
    .post('/api/vt-admin/pruchaseOrderDetail/updateRemark', {
      ...detailForm.value,
    })
    .then(res => {
      proxy.$message.success('操作成功');
      getDetail();
    });
}
let supplierDrawer = ref(false);
let supplierInfo = ref({});
function getSupplierInfo(row) {
  return new Promise((resolve, reject) => {
    axios
      .get('/api/vt-admin/supplier/detail', {
        params: {
          id: row.supplierId,
        },
      })
      .then(res => {
        supplierInfo.value = res.data.data;
        supplierDrawer.value = true;
        resolve(res.data.data);
      });
  });
}
function editNumber(row) {
  if (row.surplusNumber < 1) {
    return proxy.$message.error('库存不足');
  }
  proxy.$refs.dialogForm.show({
    title: '使用库存',
    tip: '如果存在库存,此处做出库操作,则订单数量相应减少',
    option: {
      column: [
        {
          label: '出库数量',
          prop: 'inventoryNumber',
          type: 'number',
          span: 12,
          max: row.number - row.inventoryNumber,
          value: row.number * 1 < row.surplusNumber * 1 ? row.number * 1 : row.surplusNumber * 1,
          controls: true,
          min: 1,
          rules: [
            {
              required: true,
              message: '请输入数量',
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                if (value > row.surplusNumber) {
                  callback(new Error('出库数量不能大于库存数量'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
        },
        {
          label: '出库单价',
          prop: 'costPrice',
          type: 'number',
          span: 12,
          // value: row.productVO.costPrice,
          controls: false,
          type: 'select',
          dicUrl: '/api/vt-admin/inventoryRecord/getInStoragePrice?productId=' + row.productId,
          dicFormatter: (res, item) => {
            return res.data.map(item => {
              return {
                label: item,
                value: item,
              };
            });
          },

          rules: [
            {
              required: true,
              message: '出库单价',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '序列号',
          prop: 'serialNumber',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/pruchaseOrderDetail/inventoryOutStorage', {
          id: row.id,
          inventoryNumber: res.data.inventoryNumber,
          costPrice: res.data.costPrice,
          serialNumber: res.data.serialNumber,
        })
        .then(r => {
          proxy.$message.success(r.data.data);
          getDetail();
          res.close();
        });
    },
  });
}
function decompose(row) {
  decomposeDrawer.value = true;
  detailForm.value = { ...row, orderDetailId: row.id, id: null };
}
let decomposeDrawer = ref(false);
let decomposeOption = ref({
  submitBtn: false,
  emptyBtn: false,
  detail: true,
  column: [
    {
      label: '规格型号',
      prop: 'productSpecification',
      bind: 'productVO.productSpecification',
      overHidden: true,

      span: 24,
      type: 'input',
    },
    {
      label: '描述',
      prop: 'description',
      bind: 'productVO.description',
      span: 24,
      type: 'textarea',
    },
    {
      label: '品牌',
      prop: 'productBrand',
      bind: 'productVO.productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      bind: 'productVO.unitName',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],

      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
  ],
});
let decomposeEditForm = ref({
  detailDTOList: [],
  supplierListForContactList: [],
});
let decomposeEditFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'dynamic',
      span: 24,
      labelWidth: 0,
      children: {
        rowAdd: done => {
          addStatus.value = 0;
          proxy.$refs.productSelectRef.visible = true;
        },
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '产品分类',
            prop: 'categoryId',

            hide: true,
            filterable: true,
            type: 'tree',
            cell: false,
            rules: [
              {
                required: true,
                message: '请选择产品分类',
                trigger: 'change',
              },
            ],
            children: 'hasChildren',
            parent: false,
            addDisplay: false,
            cell: false,
            dicUrl: '/api/vt-admin/productCategory/tree',
            props: {
              label: 'categoryName',
              value: 'id',
            },
          },
          {
            label: '品牌',
            cell: false,
            prop: 'productBrand',
            overHidden: true,
          },
          {
            label: '单位',
            type: 'select',
            cell: false,
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            rules: [
              {
                required: true,
                message: '请选择单位',
                trigger: 'blur',
              },
            ],
            value: '1693833617402880001',
            prop: 'unit',
            dicUrl: '/blade-system/dict/dictionary?code=unit',
            remote: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            rules: [
              {
                required: true,
                message: '请输入数量',
                trigger: 'blur',
              },
            ],
          },
        ],
      },
      prop: 'detailDTOList',
    },
  ],
});
async function handleProductSelectConfirm(ids) {
  if (addStatus.value == 0) {
    ids.split(',').forEach(async item => {
      const res = await axios.get('/api/vt-admin/product/detail?id=' + item);
      decomposeEditForm.value.detailDTOList.push({
        productId: res.data.data.id,
        productName: res.data.data.productName,
        productSpecification: res.data.data.productSpecification,
        productBrand: res.data.data.productBrand,
        unit: res.data.data.unitName,
        number: detailForm.value.number,
      });
    });
  } else if (addStatus.value == 1) {
    const data = {
      id: customProductDetail.value.id,
      productId: ids,
    };
    axios.post('/api/vt-admin/pruchaseOrderDetail/relationProduct', data).then(res => {
      proxy.$message.success('关联成功');
      getDetail();
    });
  }
}
function decomposeSubmit() {
  const { orderDetailId } = detailForm.value;
  const { detailDTOList } = decomposeEditForm.value;
  const params = {
    orderDetailId,
    detailDTOList,
  };
  axios.post('/api/vt-admin/pruchaseOrderDetail/splitProducts', params).then(res => {
    proxy.$message.success('操作成功');
    decomposeDrawer.value = false;
    getDetail();
  });
}
let supplierInfoList = ref([]);
function getSupplierInfoList(ids, type = false) {
  if (type == false) {
    supplierInfoList.value = [];
  }
  ids.forEach(item => {
    axios
      .get('/api/vt-admin/supplier/detail', {
        params: {
          id: item,
        },
      })
      .then(res => {
        supplierInfoList.value.push(res.data.data);
      });
  });
}
function handleIsPreChange(val) {
  console.log(val);
  if (val) {
    handleProductSelectConfirm(detailForm.value.productId);
  } else {
    decomposeEditForm.value.detailDTOList = decomposeEditForm.value.detailDTOList.filter(
      item => item.productId != detailForm.value.productId
    );
  }
}

function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
      delBtn: 1,
    },
  });
}
function summaryMethod({ columns, data }) {
  const arr = [];
  columns.forEach(item => {
    if (item.property == 'totalAmount') {
      const value = data.reduce((pre, cur) => {
        return pre + cur.totalPrice * 1;
      }, 0);
      arr.push(value.toFixed(2));
    } else {
      arr.push('');
    }
  });
  return arr;
}
let addStatus = ref(''); // 0 拆解选择产品 1 关联选择或添加产品
// 关联产品
let customProductDetail = ref({});
let searchParams = ref({});
function relationProduct(row) {
  addStatus.value = 1;

  const {
    customProductName: productName,
    customProductDescription: description,
    customProductSpecification: productSpecification,
    productBrand,
  } = row;
  searchParams.value = {
    productSpecification,
    productBrand,
  };
  customProductDetail.value = {
    ...row,
    productName,
    productSpecification,
    description,
    productBrand,
  };
  proxy.$refs.productSelectRef.visible = true;
}
function handleDelete(row) {
  proxy
    .$confirm('确定不采购该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/pruchaseOrderDetail/delete?id=' + row.id).then(res => {
        proxy.$message.success('操作成功');
        getDetail();
      });
    })
    .catch(() => {});
}
// function editNumber(params) {

// }
let selectList = ref([]);
function handleSelectionChange(list) {
  selectList.value = list.map(item => {
    return {
      ...item,
      ...item.productVO,
      id: item.id,
      number:item.number * 1,
    };
  });
}
let inquirySelectRef = ref();
function addInquiry() {
  if (selectList.value.length == 0) {
    return proxy.$message.warning('请选择要添加的产品');
  }

  proxy.$refs.inquirySelectRef.open();
}
function handleInquirySelectConfirm(value) {}
let dialogVisible = ref(false);
let addOption = ref({
  labelWidth: 120,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '询价有效期',
      prop: 'overDays',
      span: 24,
      type: 'number',
      width: 110,
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
      overHidden: true,
    },
    {
      label: '关联供应商',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '关联供应商',
            prop: 'supplierId',
            // search: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
            component: 'wf-supplier-select',

            change: value => {
              setBaseInfo(value);
            },
            overHidden: true,
          },
          {
            label: '供应商联系人',
            prop: 'supplierContactId',
            type: 'select',
            props: {
              value: 'id',
              label: 'concatName',
              desc: 'concatPhone',
            },
            dicData: [],
            formatter: row => {
              return row.supplierContactName;
            },
          },
        ],
      },
      formatter: row => {
        return row.inquiryNumber;
      },
    },
  ],
});
let addForm = ref({});

function inquiryAdd() {
  if (selectList.value.length <= 0) {
    return proxy.$message.warning('请选择要添加的产品');
  }
  dialogVisible.value = true;
  if (proxy.$refs.addFormRef) {
    proxy.$refs.addFormRef.resetForm();
  }
}
function addSubmit(form, done, loading) {
  axios.get('/api/blade-system/user/info').then(res => {
    const data = {
      ...form,
      contactPerson: userInfo.value.user_id,
      contactPhone: res.data.data.phone,
      inquirySource: 0,
      detailDTOList: selectList.value.map(item => {
        return {
          ...item,
          id: null,

          productId: item.productId,
          orderDetailId: item.id,
        };
      }),
    };
    axios
      .post('/api/vt-admin/purchaseInquiry/save', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          done();
          resultDrawer.value = true;
          dialogVisible.value = false;
        }
      })
      .catch(err => {
        done();
      });
  });
}

function setBaseInfo(value) {
  axios.get(`/api/vt-admin/supplier/detail?id=${value.value}`).then(res => {
    const supplierContactId = proxy.findObject(addOption.value.column, 'supplierContactId');
    supplierContactId.dicData = res.data.data.supplierConcatVOList;
  });
}
function handleOpend() {
  proxy.$refs.inquiryListRef.onLoad();
}
let resultDrawer = ref(false);
let tableRef = ref();
function cancelAdd(type) {
  tableRef.value.clearSelection();
  if (type) {
    getList();
  }
}

let list = ref([]);
let activeName = ref([]);
function getList() {
  axios
    .get('/api/vt-admin/purchaseInquiry/page', {
      params: {
        size: 1000,
        orderId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      list.value = res.data.data.records.reverse();
      activeName.value = list.value.map((item, index) => index);
    });
}
let currentInquiryId = ref(null);
let editInquiryRef = ref();
function editInquiryFn(item) {
  currentInquiryId.value = item.id;

  editInquiryRef.value.open();
}
let inquiryStatus = ref(null);
function handleStatusChange(status) {
  inquiryStatus.value = status;
}
function addMore(row, value) {
  proxy.$refs.dialogForm.show({
    title: value == 0 ? '额外采购(即出货后库存数量)' : '编辑数量(即出货后库存数量)',
    width: '30%',
    option: {
      column: [
        {
          label: '数量',

          type: 'number',
          prop: 'number',
          value: value == 0 ? 1 : row.number,
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入数量',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      axios
        .post(
          `/api/vt-admin/pruchaseOrderDetail/${
            value == 0 ? 'addInventoryPurchase' : 'updateInventoryPurchase'
          }`,
          {
            id: row.id,
            ...res.data,
          }
        )
        .then(r => {
          proxy.$message.success(r.data.msg);
          getDetail();
          res.close();
        });
    },
  });
}
function rowStyle({ row, index }) {
  if (row.purchaseType == 1) {
    return {
      background: 'var(--el-color-primary-light-9)',
    };
  }
}
function deleteInquiryFn(row) {
  
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    axios.post('/api/vt-admin/purchaseInquiry/delete?ids=' + row.id).then(res => { 
      if (res.data.code == 200) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.data.message);
      }
    });
  });
}




// 京东淘宝搜索
function searchPrice(row, type) {
  if (type == 1) {
    window.open(
      'https://search.jd.com/Search?keyword=' +
        `${row.productName} ${row.productSpecification} ${row.productBrand}`
    );
  } else if (type == 2) {
    window.open(
      'https://s.taobao.com/search?q=' +
        `${row.productName} ${row.productSpecification} ${row.productBrand}`
    );
  }
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
:deep(.el-card__header) {
  padding: 5px;
}
.inquiry_box {
  position: fixed;
  right: 0;
  top: 0;
  width: 50vw;
  min-height: 100vh;
  .el-card {
    height: 100%;
  }

  background-color: #fff;
  z-index: 2000;
  border-radius: 5px;
  border: 1px solid var(--el-color-primary);
}
</style>
