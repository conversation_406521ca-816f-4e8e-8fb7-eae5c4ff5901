<!-- <template>
  <div>
    
    <settlementList
      :is-apply="isApply"
      :is-audit="isAudit"
      :form="props.form"
      :get-url="isApply?'/api/vt-admin/sealContractSettlement/page':'/api/vt-admin/sealContractSettlement/pageForSettlementPlatform'"

    ></settlementList>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import settlementList from '../settlementList.vue';
import { useStore } from 'vuex';
const store = useStore();
const isApply = computed(() => {
  return store.getters.userInfo.tenant_id == props.form.tenantId;
})
const isAudit = computed(() => {
  return store.getters.userInfo.tenant_id != props.form.tenantId;
})


const props = defineProps({
  form: Object,
});
</script>

<style lang="scss" scoped></style> -->



<template>
  <avue-crud
    :option="props.cooperationType == 0 ? collectOption : payOption"
    :data="props.tableData"
    v-model:page="page"
    v-model:search="params"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @row-del="rowDel"
    :beforeOpen="beforeOpen"
    v-model="form"
  >
  <template #paymentFile="{ row }">
    <File :fileList="row.attachList" ></File>
  </template>
  <template #collectionFile="{ row }">
    <File :fileList="row.collectionAttachList" ></File>
  </template>
  <template #payStatus="{ row }">
    <div style="display: flex;align-items: center;justify-content: center;">
       <span>{{ row.$payStatus }}</span>
    <el-icon
      v-if="row.payStatus == 2"
      @click="viewBackDetail(row)"
      style="margin-left: 8px; cursor: pointer; color: var(--el-color-primary);"
      title="查看退回详情"
    >
      <InfoFilled />
    </el-icon>
    </div>
  </template>
  <template #menu="{row}">

    <el-button type="primary" @click="$refs.crud.rowDel(row)" v-if="row.settlementStatus == 1" icon="delete" text>删除</el-button>
    <!-- 付款按钮：在结算确认状态（3）且为付款方时显示 -->
    <el-button type="primary" @click="pay(row)" v-if="row.settlementStatus == 1 && props.form.cooperationType == 1" icon="Tickets" text>付款</el-button>
    <!-- 收款按钮：在已付款状态（4）且为收款方时显示 -->
    <el-button type="primary" @click="Collection(row)" v-if="row.settlementStatus == 1 && props.form.cooperationType == 0" icon="Tickets" text>收款</el-button>
  </template>

  </avue-crud>
  <dialogForm ref="dialogFormRef"></dialogForm>

  <!-- 退回详情弹窗 -->
  <el-dialog v-model="backDetailVisible" title="退回详情" width="600px">
    <el-form label-width="100px">
      <el-form-item label="退回时间">
        <el-input v-model="backDetailData.backTime" disabled />
      </el-form-item>
      <el-form-item label="退回附件">
        <File :fileList="backDetailData.backFileList || []" />
      </el-form-item>
      <el-form-item label="退回备注">
        <el-input
          v-model="backDetailData.backRemark"
          type="textarea"
          :rows="4"
          disabled
        />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { InfoFilled } from '@element-plus/icons-vue';
import { dateFormat } from '@/utils/date';
import moment from 'moment';


const props = defineProps({
  sealContractId: String,
  form: Object,
  cooperationType: Number,
  tableData: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['data-changed']);

let collectOption = ref({
 
  align: 'center',
  addBtn: true,
  header:false,
  editBtn: false,
  delBtn: false,
 
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
   
    {
      label: '结算金额',
      prop: 'applySettlementPrice',
    },
    {
      label: '状态',
      prop: 'settlementStatus',
      type: 'select',
      display: false,
      slot: true,
      dicData: [
        {
          value: 0,
          label: '草稿',
        },
        {
          value: 1,
          label: '申请结算',
        },
        {
          value: 2,
          label: '结算待确认',
        },
        {
          value: 3,
          label: '结算确认',
        },
        {
          value: 4,
          label: '已收款',
        },
        // {
        //   value: 5,
        //   label: '已退回',
        // },
      ],
      overHidden: true,
    },
       {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'datetime',
      label: '收款日期',
      display:false,
      span: 12,
     
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
     
      prop: 'collectionTime',
    },
    {
      type: 'select',
      label: '收款账号',
display:false,
      cascader: [],
      span: 12,
     

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'collectionAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'change',
        },
      ],
    },
    {
      label: '收款凭证',
      prop: 'collectionFile',
      type: 'upload',
      dataType: 'object',
     display:false,
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '收款备注',
      display:false,
      span: 24,
      
      prop: 'collectionRemark',
    },
  ],
});
let payOption = ref({
  
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  header:false,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
   
    {
      label: '结算金额',
      prop: 'applySettlementPrice',
    },
     {
      label: '状态',
      prop: 'settlementStatus',
      type: 'select',
      display: false,
      slot: true,
      dicData: [
        {
          value: 0,
          label: '草稿',
        },
        {
          value: 1,
          label: '申请结算',
        },
        {
          value: 2,
          label: '结算待确认',
        },
        {
          value: 3,
          label: '结算确认',
        },
        {
          value: 4,
          label: '已付款',
        },
        // {
        //   value: 5,
        //   label: '已退回',
        // },
      ],
      overHidden: true,
    },
       {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'datetime',
      label: '付款日期',
      span: 12,
    display:false,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
     
      prop: 'paymentTime',
    },
    {
      type: 'select',
      label: '付款账号',

      cascader: [],
      span: 12,
     
display:false,
      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'change',
        },
      ],
    },
    {
      label: '付款凭证',
      prop: 'paymentFile',
      type: 'upload',
      dataType: 'object',
    display:false,
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '付款备注',
      span: 24,
      display:false,
      prop: 'paymentRemark',
    },
  ],
});
let form = ref({
    paymentFile: [],
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractSettlement/addPayment';
const delUrl = '/api/vt-admin/sealContractSettlement/remove?ids=';
const updateUrl = '';
let params = ref({});
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let backDetailVisible = ref(false);
let backDetailData = ref({});
let router = useRouter();
let imgId = ref('');

function rowSave(form, done, loading) {

  const data = {
    ...form,
    sealContractId: props.sealContractId,
    paymentFile: form.paymentFile && form.paymentFile.map(item => item.value).join(','),
      collectionFile: form.collectionFile && form.collectionFile.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emits('data-changed');
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emits('data-changed');
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        emits('data-changed');
      });
    })
    .catch(() => {});
}
function reset() {
  // 不需要重新加载数据,因为数据来自 props
}
function searchChange(params, done) {
  // 不需要重新加载数据,因为数据来自 props
  done();
}
function beforeOpen(done,type) {
    if(type == 'edit'){
        form.value.paymentFile = form.value.attachList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
    }
    done()
}



function viewBackDetail(row) {
  backDetailData.value = {
    backTime: row.backTime,
    backRemark: row.backRemark,
    backFileList: row.backFileList || [],
  };
  backDetailVisible.value = true;
}

// 付款操作
function pay(row) {
  proxy.$refs.dialogFormRef.show({
    title: '项目付款',
    option: {
      column: [
        {
          type: 'number',
          label: '付款金额',
          span: 12,
          disabled: true,
          value: row.applySettlementPrice,
          prop: 'payPrice',
        },
        {
          type: 'datetime',
          label: '付款日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: moment().format('YYYY-MM-DD HH:mm:ss'),
          prop: 'payTime',
        },
        {
          type: 'select',
          label: '付款账号',
          cascader: [],
          span: 12,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentAccount',
          rules: [
            {
              required: true,
              message: '请选择付款账号',
              trigger: 'change',
            },
          ],
        },
        {
          label: '付款凭证',
          prop: 'paymentFile',
          type: 'upload',
          dataType: 'object',
         
      
          span: 24,
          slot: true,
          limit: 1,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'paymentRemark',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContractSettlement/payment', {
          id: row.id,
          ...res.data,
          paymentFile: res.data.paymentFile && res.data.paymentFile.map(item => item.value).join(','),
        })
        .then(e => {
          proxy.$message.success('付款成功');
          res.close();

          emits('data-changed');
        })
        .catch(() => {
          imgId.value = '';
        });
    },
  });
}

// 收款操作
function Collection(row) {
  proxy.$refs.dialogFormRef.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          disabled: true,
          display: true,
          value: row.applySettlementPrice,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'collectionTime',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',
          cascader: [],
          span: 12,
          display: true,
          value: row.collectionAccount,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '收款凭证',
          prop: 'collectionFile',
          type: 'upload',
          dataType: 'object',
          
       
          span: 24,
          slot: true,
          limit: 1,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
         
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'collectionRemark',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        collectionFile: res.data.collectionFile && res.data.collectionFile.map(item => item.value).join(','),
      };
      axios
        .post('/api/vt-admin/sealContractSettlement/collection', data)
        .then(r => {
          proxy.$message.success('收款成功');
          res.close();
          imgId.value = '';
          emits('data-changed');
        })
        .catch(() => {
          imgId.value = '';
        });
    },
  });
}

</script>

<style lang="scss" scoped></style>

