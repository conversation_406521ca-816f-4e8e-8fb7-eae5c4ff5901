<template>
  <basic-container>
    <!-- 筛选条件区域 -->
    <div class="filter-container" v-if="false">
      <!-- 第一行：性别和姓名 -->
      <div class="filter-row">
        <span class="filter-label">性别</span>
        <div class="filter-tags">
          <span
            v-for="item in sexOptions"
            :key="item.value"
            :class="['filter-tag', { active: filterParams.sex === item.value }]"
            @click="toggleSex(item.value)"
          >
            {{ item.label }}
          </span>
        </div>
        <span class="filter-label">姓名</span>
        <el-input v-model="filterParams.name" placeholder="请输入姓名" clearable class="name-input"></el-input>
      </div>

      <!-- 第二行：技能标签 -->
      <div class="filter-row">
        <span class="filter-label">技能标签</span>
        <div class="filter-tags">
          <span
            v-for="item in specialtyOptions"
            :key="item"
            :class="['filter-tag', { active: filterParams.specialty.includes(item) }]"
            @click="toggleSpecialty(item)"
          >
            {{ item }}
          </span>
        </div>
      </div>

      <!-- 第三行：地区 -->
      <div class="filter-row">
        <span class="filter-label">地区</span>
        <div class="filter-tags">
          <el-popover
            v-for="province in provinceOptions"
            :key="province.name"
            placement="bottom"
            :width="400"
            trigger="click"
          >
            <template #reference>
              <span :class="['filter-tag', { active: isProvinceActive(province.name) }]">
                {{ province.name }} <i class="el-icon-arrow-down"></i>
              </span>
            </template>
            <div class="popover-content">
              <div class="popover-tags">
                <span
                  v-for="city in province.cities"
                  :key="city"
                  :class="['filter-tag', { active: filterParams.address.includes(city) }]"
                  @click="toggleAddress(city)"
                >
                  {{ city }}
                </span>
              </div>
            </div>
          </el-popover>
        </div>
      </div>

      <!-- 第四行：高级筛选 -->
      <div class="filter-row">
        <span class="filter-label">高级筛选</span>
        <div class="filter-tags">
          <!-- 年龄筛选 -->
          <el-popover
            placement="bottom"
            :width="400"
            trigger="hover"
          >
            <template #reference>
              <span :class="['filter-tag', { active: filterParams.ageRange }]">
                年龄 <i class="el-icon-arrow-down"></i>
              </span>
            </template>
            <div class="popover-content">
              <div class="popover-tags">
                <span
                  v-for="item in ageRangeOptions"
                  :key="item.value"
                  :class="['filter-tag', { active: filterParams.ageRange === item.value }]"
                  @click="toggleAgeRange(item.value)"
                >
                  {{ item.label }}
                </span>
              </div>
            </div>
          </el-popover>

          <!-- 工种筛选 -->
          <el-popover
            placement="bottom"
            :width="500"
            trigger="hover"
          >
            <template #reference>
              <span :class="['filter-tag', { active: filterParams.workType.length > 0 }]">
                工种 <i class="el-icon-arrow-down"></i>
              </span>
            </template>
            <div class="popover-content">
              <div class="popover-tags">
                <span
                  v-for="item in workTypeOptions"
                  :key="item.value"
                  :class="['filter-tag', { active: filterParams.workType.includes(item.value) }]"
                  @click="toggleWorkType(item.value)"
                >
                  {{ item.label }}
                </span>
              </div>
            </div>
          </el-popover>

          <!-- 是否保险 -->
          <el-popover
            placement="bottom"
            :width="250"
            trigger="hover"
          >
            <template #reference>
              <span :class="['filter-tag', { active: filterParams.isInsurance !== null }]">
                是否保险 <i class="el-icon-arrow-down"></i>
              </span>
            </template>
            <div class="popover-content">
              <div class="popover-tags">
                <span
                  v-for="item in insuranceOptions"
                  :key="item.value"
                  :class="['filter-tag', { active: filterParams.isInsurance === item.value }]"
                  @click="toggleInsurance(item.value)"
                >
                  {{ item.label }}
                </span>
              </div>
            </div>
          </el-popover>

          <!-- 是否开票 -->
          <el-popover
            placement="bottom"
            :width="250"
            trigger="hover"
          >
            <template #reference>
              <span :class="['filter-tag', { active: filterParams.isInvoice !== null }]">
                是否开票 <i class="el-icon-arrow-down"></i>
              </span>
            </template>
            <div class="popover-content">
              <div class="popover-tags">
                <span
                  v-for="item in invoiceOptions"
                  :key="item.value"
                  :class="['filter-tag', { active: filterParams.isInvoice === item.value }]"
                  @click="toggleInvoice(item.value)"
                >
                  {{ item.label }}
                </span>
              </div>
            </div>
          </el-popover>
        </div>
        <div class="filter-actions">
          <el-button @click="applyFilter" type="primary" size="small">筛选</el-button>
          <el-button @click="resetFilter" size="small">重置</el-button>
        </div>
      </div>
    </div>

    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #specialty-form="{ type }">
        <el-autocomplete
          :disabled="type == 'view'"
          style="width: 100%"
          v-model="form.specialty"
          value-key="value"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入技能标签"
        />
      </template>
      <template #name="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.name }}</el-link>
      </template>
      <template #workType1="{ row }">
        {{ row.salaryVOS && row.salaryVOS[0]?.workTypeName }}
      </template>
      <template #salary1="{ row }">
        {{ row.salaryVOS && row.salaryVOS[0]?.salary }}
      </template>
      <template #files-form>
        <File :fileList="form.attachList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

function validateName(rule, value, callback) {
  axios
    .get('/api/vt-admin/humanResource/existName?', {
      params: {
        name: value,
        type: 0,
        id: form.value.id || null,
      },
    })
    .then(res => {
      if (res.data.data == 1) {
        callback(new Error('资源库已经存在此人，请重新输入'));
      } else {
        callback();
      }
    });
}
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  labelWidth: 120,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  column: [
    // {
    //   label: '姓名',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label: '姓名',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      // search: true,
    },
    {
      label: '年龄',
      prop: 'age',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '性别',
      prop: 'sex1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      dicData: [
        {
          label: '男',
          value: 1,
        },
        {
          label: '女',
          value: 0,
        },
      ],
      props: {
        label: 'label',
        value: 'value',
      },
      formatter: (row, value) => {
        return row.sex == 1 ? '男' : '女';
      },
    },

    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },

    {
      label: '技能标签',
      prop: 'specialty',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
    },

    {
      label: '态度标签',
      prop: 'attitudeLabel',
      type: 'input',

      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },

    {
      label: '居住区域',
      prop: 'address1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.address;
      },
    },
    {
      label: '是否保险',
      prop: 'isInsurance',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '是否开票',
      prop: 'isInvoice1',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],

      formatter: (row, value) => {
        return row.$isInsurance;
      },
    },
    {
      label: '工种',
      prop: 'workType1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,

      type: 'select',
    },
    {
      label: '工价(元/天)',
      prop: 'salary1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      placeholder: '请输入工价',
    },
    // {
    //   label: '开户行',
    //   prop: 'openBank1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.openBank;
    //   },
    // },
    // {
    //   label: '银行账号',
    //   prop: 'bankNumber1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.bankNumber;
    //   },
    // },
    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
          rules: [
            {
              validator: validateName,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '性别',
          prop: 'sex',
          type: 'radio',
          dicData: [
            {
              label: '男',
              value: 1,
            },
            {
              label: '女',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '年龄',
          prop: 'age',
          width: 250,
          overHidden: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },

        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },
        {
          label: '是否开票',
          prop: 'isInvoice',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
          control: val => {
            return {
              invoiceType: {
                display: val == 1,
              },
              taxRate: {
                display: val == 1,
              },
            };
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },

        {
          label: '居住区域',
          prop: 'address',
        },
        {
          label: '引进人',
          prop: 'createUser',
          component: 'wf-user-select',
          disabled: true,
          value: proxy.$store.getters.userInfo.user_id,
        },
        {
          label: '工种',
          prop: 'workType',
          dicUrl: '/blade-system/dict-biz/dictionary?code=workType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          type: 'select',
        },
        {
          label: '工价(元/天)',
          prop: 'salary',
          placeholder: '请输入工价',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
        {
          label: '附件',
          prop: 'fileIds',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '附件',
          prop: 'files',
          span:24,
          addDisplay: false,
          editDisplay: false,
        },
      ],
    },
    // {
    //   label: '工种信息',
    //   column: [
    //     {
    //       type: 'dynamic',
    //       hide: true,
    //       span: 24,
    //       prop: 'salaryDTOList',
    //       children: {
    //         column: [
    //           {
    //             label: '工种',
    //             prop: 'workType',
    //             dicUrl: '/blade-system/dict-biz/dictionary?code=workType',
    //             props: {
    //               label: 'dictValue',
    //               value: 'id',
    //             },
    //             type: 'select',
    //           },
    //           {
    //             label: '工价(元/天)',
    //             prop: 'salary',
    //             placeholder: '请输入工价',
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/humanResource/save';
const delUrl = '/api/vt-admin/humanResource/remove?ids=';
const updateUrl = '/api/vt-admin/humanResource/update';
const tableUrl = '/api/vt-admin/humanResource/page';
let params = ref({});
let tableData = ref([]);

// 筛选相关数据
let filterParams = ref({
  sex: null,
  name: '',
  specialty: [],
  address: [],
  ageRange: null,
  workType: [],
  isInsurance: null,
  isInvoice: null,
});

// 模拟数据：性别选项
let sexOptions = ref([
  { label: '男', value: 1 },
  { label: '女', value: 0 },
]);

// 模拟数据：技能标签选项
let specialtyOptions = ref([
  '木工',
  '瓦工',
  '钢筋工',
  '混凝土工',
  '电工',
  '焊工',
  '管道工',
  '油漆工',
  '防水工',
  '装修工',
  '架子工',
  '抹灰工',
]);

// 模拟数据：省份和城市选项
let provinceOptions = ref([
  {
    name: '北京',
    cities: ['东城区', '西城区', '朝阳区', '海淀区', '丰台区', '石景山区', '通州区', '顺义区', '昌平区', '大兴区']
  },
  {
    name: '上海',
    cities: ['黄浦区', '徐汇区', '长宁区', '静安区', '普陀区', '虹口区', '杨浦区', '浦东新区', '闵行区', '宝山区']
  },
  {
    name: '广东',
    cities: ['广州', '深圳', '珠海', '佛山', '东莞', '中山', '惠州', '江门', '湛江', '茂名']
  },
  {
    name: '浙江',
    cities: ['杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州']
  },
  {
    name: '江苏',
    cities: ['南京', '苏州', '无锡', '常州', '镇江', '南通', '扬州', '盐城', '徐州', '连云港']
  },
  {
    name: '湖北',
    cities: ['武汉', '黄石', '十堰', '宜昌', '襄阳', '鄂州', '荆门', '孝感', '荆州', '黄冈']
  },
  {
    name: '四川',
    cities: ['成都', '绵阳', '德阳', '南充', '宜宾', '自贡', '乐山', '泸州', '达州', '内江']
  },
  {
    name: '陕西',
    cities: ['西安', '宝鸡', '咸阳', '渭南', '铜川', '延安', '榆林', '汉中', '安康', '商洛']
  },
  {
    name: '重庆',
    cities: ['渝中区', '江北区', '南岸区', '九龙坡区', '沙坪坝区', '大渡口区', '渝北区', '巴南区', '北碚区', '万州区']
  },
  {
    name: '天津',
    cities: ['和平区', '河东区', '河西区', '南开区', '河北区', '红桥区', '滨海新区', '东丽区', '西青区', '津南区']
  },
]);

// 模拟数据：年龄范围选项
let ageRangeOptions = ref([
  { label: '18-25岁', value: '18-25' },
  { label: '26-35岁', value: '26-35' },
  { label: '36-45岁', value: '36-45' },
  { label: '46-55岁', value: '46-55' },
  { label: '55岁以上', value: '55+' },
]);

// 模拟数据：工种选项
let workTypeOptions = ref([
  { label: '木工', value: 1 },
  { label: '瓦工', value: 2 },
  { label: '钢筋工', value: 3 },
  { label: '混凝土工', value: 4 },
  { label: '电工', value: 5 },
  { label: '焊工', value: 6 },
  { label: '管道工', value: 7 },
  { label: '油漆工', value: 8 },
]);

// 模拟数据：是否保险选项
let insuranceOptions = ref([
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]);

// 模拟数据：是否开票选项
let invoiceOptions = ref([
  { label: '是', value: 1 },
  { label: '否', value: 0 },
]);

let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,

        type: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    type: 0,
    files: form.fileIds?.map(item => item.value).join(','),
    salaryDTOList: [
      {
        workType: form.workType,
        salary: form.salary,
      },
    ],
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    files: form.value.fileIds?.map(item => item.value).join(','),
    salaryDTOList: [
      {
        workType: row.workType,
        salary: row.salary,
      },
    ],
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function querySearchAsync(value, cb) {
  axios.get('/api/vt-admin/humanResource/getSpecialtyList').then(res => {
    console.log(res.data.data);
    cb(
      res.data.data.map(item => {
        return {
          value: item,
        };
      })
    );
  });
}
function beforeOpen(done, type) {
  if (type == 'edit' || type == 'view') {
    form.value.salaryDTOList = form.value.salaryVOS;
    form.value.workType = form.value.salaryVOS[0]?.workType;
    form.value.salary = form.value.salaryVOS[0]?.salary;
    form.value.fileIds = form.value.attachList?.map(item => {
      return { value: item.id, label: item.originalName };
    });
  }

  done();
}

// 切换性别筛选
function toggleSex(value) {
  if (filterParams.value.sex === value) {
    filterParams.value.sex = null;
  } else {
    filterParams.value.sex = value;
  }
}

// 切换技能标签筛选
function toggleSpecialty(item) {
  const index = filterParams.value.specialty.indexOf(item);
  if (index > -1) {
    filterParams.value.specialty.splice(index, 1);
  } else {
    filterParams.value.specialty.push(item);
  }
}

// 切换地区筛选
function toggleAddress(item) {
  const index = filterParams.value.address.indexOf(item);
  if (index > -1) {
    filterParams.value.address.splice(index, 1);
  } else {
    filterParams.value.address.push(item);
  }
}

// 判断省份是否有选中的城市
function isProvinceActive(provinceName) {
  const province = provinceOptions.value.find(p => p.name === provinceName);
  if (!province) return false;
  return province.cities.some(city => filterParams.value.address.includes(city));
}

// 切换年龄范围筛选
function toggleAgeRange(value) {
  if (filterParams.value.ageRange === value) {
    filterParams.value.ageRange = null;
  } else {
    filterParams.value.ageRange = value;
  }
}

// 切换工种筛选
function toggleWorkType(value) {
  const index = filterParams.value.workType.indexOf(value);
  if (index > -1) {
    filterParams.value.workType.splice(index, 1);
  } else {
    filterParams.value.workType.push(value);
  }
}

// 切换保险筛选
function toggleInsurance(value) {
  if (filterParams.value.isInsurance === value) {
    filterParams.value.isInsurance = null;
  } else {
    filterParams.value.isInsurance = value;
  }
}

// 切换开票筛选
function toggleInvoice(value) {
  if (filterParams.value.isInvoice === value) {
    filterParams.value.isInvoice = null;
  } else {
    filterParams.value.isInvoice = value;
  }
}

// 应用筛选
function applyFilter() {
  // 构建筛选参数
  const filterData = {};

  if (filterParams.value.sex !== null && filterParams.value.sex !== undefined) {
    filterData.sex = filterParams.value.sex;
  }
  if (filterParams.value.name) {
    filterData.name = filterParams.value.name;
  }
  if (filterParams.value.specialty && filterParams.value.specialty.length > 0) {
    filterData.specialty = filterParams.value.specialty.join(',');
  }
  if (filterParams.value.address && filterParams.value.address.length > 0) {
    filterData.address = filterParams.value.address.join(',');
  }
  if (filterParams.value.ageRange) {
    filterData.ageRange = filterParams.value.ageRange;
  }
  if (filterParams.value.workType && filterParams.value.workType.length > 0) {
    filterData.workType = filterParams.value.workType.join(',');
  }
  if (filterParams.value.isInsurance !== null && filterParams.value.isInsurance !== undefined) {
    filterData.isInsurance = filterParams.value.isInsurance;
  }
  if (filterParams.value.isInvoice !== null && filterParams.value.isInvoice !== undefined) {
    filterData.isInvoice = filterParams.value.isInvoice;
  }

  // 更新params并重新加载数据
  params.value = filterData;
  page.value.currentPage = 1;
  onLoad();
}

// 重置筛选
function resetFilter() {
  filterParams.value = {
    sex: null,
    name: '',
    specialty: [],
    address: [],
    ageRange: null,
    workType: [],
    isInsurance: null,
    isInvoice: null,
  };
  params.value = {};
  page.value.currentPage = 1;
  onLoad();
}
</script>

<style lang="scss" scoped>
.filter-container {
  background-color: #fff;
  padding: 0;
  margin-bottom: 15px;

  .filter-row {
    display: flex;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;

    &:last-child {
      border-bottom: none;
    }
  }

  .filter-label {
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    min-width: 70px;
    font-weight: 500;
  }

  .filter-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    flex: 1;
  }

  .filter-tag {
    display: inline-block;
    padding: 4px 12px;
    font-size: 13px;
    color: #666;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;

    i {
      margin-left: 4px;
      font-size: 12px;
    }

    &:hover {
      color: #409eff;
      border-color: #409eff;
      background-color: #ecf5ff;
    }

    &.active {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }
  }

  .name-input {
    width: 200px;

    :deep(.el-input__inner) {
      height: 32px;
      line-height: 32px;
    }
  }

  .filter-actions {
    display: flex;
    gap: 10px;
    margin-left: auto;
  }
}

// Popover 内容样式
.popover-content {
  .popover-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .filter-tag {
    display: inline-block;
    padding: 4px 12px;
    font-size: 13px;
    color: #666;
    background-color: #f5f5f5;
    border: 1px solid #e0e0e0;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;

    &:hover {
      color: #409eff;
      border-color: #409eff;
      background-color: #ecf5ff;
    }

    &.active {
      color: #fff;
      background-color: #409eff;
      border-color: #409eff;
    }
  }
}
</style>
