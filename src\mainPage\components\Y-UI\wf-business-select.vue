<template>
  <el-dialog
    ref="nf-dialog"
    class="nf-dialog"
    v-model="visible"
    title="商机选择"
    width="60%"
    :before-close="handleClose"
    append-to-body
  >
    <avue-crud
      v-if="isInit && visible"
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model:page="page"
      v-model:search="query"
      v-model="form"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionList = $event"
      @current-change="page.currentPage = $event"
      @size-change="page.pageSize = $event"
      @row-click="rowClick"
      @on-load="onLoad"
    >
      <template #menu-left>
        <!-- <el-button type="primary" @click="drawer = true" icon="Plus">新增</el-button> -->
      </template>
      <template v-if="checkType == 'radio'" #radio="{ row }">
        <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
      </template>
      <template #stage="{ row }">
        <el-tag effect='plain'>{{
          row.$stage == '方案'
            ? `方案(${row.optionStatus == 1 ? '进行中' : '完成'})`
            : row.$stage == '报价'
            ? `报价(${row.offerStatus == 1 ? '进行中' : '完成'})`
            : row.$stage
        }}</el-tag>
      </template>
    </avue-crud>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose" size="default">取 消</el-button>
        <el-button type="primary" @click="handleConfirm" size="default">确 定</el-button>
      </span>
    </template>
    <el-drawer v-model="drawer" append-to-body size="60%" title="新增商机" :with-header="false">
      <AddBusiness></AddBusiness>
    </el-drawer>
  </el-dialog>
</template>
<script>
import { getUser } from '@/api/system/user';
import { businessOpportunityData } from '@/const/const.js';
import AddBusiness from '@/views/CRM/businessOpportunity/compoents/update.vue';
export default {
  props: {
    defaultChecked: String,
    userUrl: {
      type: String,
      default: () => {
        return '/vt-admin/businessOpportunity/page?selectType=0';
      },
    },
    customOption: Object,
    checkType: {
      type: String,
      default: () => {
        return 'radio';
      },
    },
    isNew:{
      type: Boolean,
      default: false
    }
  },
  components:{
    AddBusiness
  },  
  watch: {
    checkType: {
      handler(val) {
        if (val == 'radio') {
          this.option.selection = false;
          this.findObject(this.option.column, 'radio').hide = false;
        } else {
          this.option.selection = true;
          this.findObject(this.option.column, 'radio').hide = true;
        }
      },
      immediate: true,
    },
  },
  computed: {
    ids() {
      let ids = new Set();
      this.selectionList.forEach(ele => {
        ids.add(ele.id);
      });
      return Array.from(ids).join(',');
    },
    names() {
      let names = new Set();
      this.selectionList.forEach(ele => {
        names.add(ele.name);
      });
      return Array.from(names).join(',');
    },
  },
  data() {
    return {
      isInit: false,
      visible: false,
      form: {},
      query: {
        
      },
      drawer:false,
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      data: [],
      props: {
        id: 'id',
        name: 'name',
        records: 'data.data.records',
        total: 'data.data.total',
      },
      option: {
        size: 'default',
        searchSize: 'default',
        align: 'center',
        menu: false,
        addBtn: false,
        header: true,
        border: true,
        tip: false,
        reserveSelection: true,
        highlightCurrentRow: true,
        gutter: 5,
        searchMenuSpan: 6,
        selection: true,
        column: [
          {
            label: '',
            prop: 'radio',
            type: 'radio',
            width: 55,
            hide: true,
          },
          {
            label: '商机名称',
            prop: 'name',
            width: 250,
            overHidden: true,
            search: true,
          },
          // {
          //   label: '商机分类',
          //   type: 'radio',
          //   prop: 'classify',
          //   span: 24,
          //   overHidden: true,
          //   dicData: [
          //     {
          //       value: 0,

          //       label: '产品',
          //     },
          //     {
          //       value: 1,
          //       label: '项目',
          //     },
          //   ],
          // },
          {
            type: 'select',
            label: '业务板块',
            span: 12,
            search: true,
            overHidden: true,
            rules: [
              {
                required: true,
                message: '请选择业务板块',
              },
            ],
            display: true,
            prop: 'type',
            dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
            props: {
              label: 'dictValue',
              value: 'id',
            },
          },
          {
            label: '商机描述',
            prop: 'address',
            overHidden: true,
          },

          {
            type: 'select',
            label: '商机来源',
            
            span: 12,
            rules: [
              {
                required: true,
                message: '请选择商机来源',
              },
            ],
            display: true,
            prop: 'source',
            dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
            props: {
              label: 'dictValue',
              value: 'id',
            },
          },
          {
            label: '商机阶段',
            prop: 'stage',
            width: 100,
            slot: true,
            type: 'select',
           
            dicData: businessOpportunityData,
          },
          {
            label: '客户名称',
            prop: 'customerName',
            search: true,
            overHidden: true,
          },
          {
            label: '关联联系人',
            prop: 'contactPersonName',
          },
          {
            label: '介绍人',
            width: 80,
            prop: 'introducer',
            type: 'input',
          },
        ],
      },
    };
  },
  mounted() {
    this.init();
    if(this.isNew){
      this.query.stage = 0
      this.onLoad(this.page)
    }
  },
  methods: {
    init() {
      if (!this.isInit) {
        if (this.customOption) {
          const { column, userProps } = this.customOption;
          if (column) this.option.column = column;
          if (userProps) this.props = userProps;
        }
        this.isInit = true;
      }
    },
    handleConfirm() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$emit('onConfirm', this.ids, this.names);
      this.handleClose();
    },
    handleClose(done) {
      // this.selectionClear()
      this.visible = false;
      if (done && typeof done == 'function') done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionClear() {
      this.selectionList = [];
      if (this.$refs.crud) this.$refs.crud.toggleSelection();
    },
    rowClick(row) {
      if (this.checkType == 'radio') {
        this.selectionList = [row];
        this.form.radio = row.id;
      } else this.$refs.crud.toggleSelection([row]);
    },
    async changeDefaultChecked() {
      if (!this.defaultChecked) return;
      let defaultChecked = this.defaultChecked;

      if (this.checkType == 'checkbox') {
        // this.selectionClear()
        const checks = defaultChecked.split(',');
        if (checks.length > 0) {
          setTimeout(() => {
            checks.forEach(async c => {
              let row = this.data.find(d => d.id == c); // 当前页查找
              if (!row) {
                row = this.selectionList.find(d => d.id == c); // 勾选列表查找
                if (!row) {
                  let res = await this.getDetail(c); // 接口查找
                  if (res.data.data) row = res.data.data;
                }
              }
              if (row && this.$refs.crud) this.$refs.crud.toggleRowSelection(row, true);
            });
          }, 500);
        }
      } else {
        let row = this.data.find(d => d.id == defaultChecked);
        if (!row) {
          let res = await this.getDetail(defaultChecked);
          if (res.data.data) row = res.data.data;
        }

        if (row) {
          this.selectionList = [row];
          this.form.radio = defaultChecked;
        } else {
          this.selectionList = [];
          this.form.radio = '';
        }
      }
    },
    onLoad(page, params = {}) {
      this.loading = true;
      const param = {
        current: page.currentPage,
        size: page.pageSize,
        ...Object.assign(params, this.query),
        
      };
      this.$axios.get(this.userUrl, { params: param }).then(res => {
        this.page.total = this.getAsVal(res, this.props.total);
        this.data = this.getAsVal(res, this.props.records) || [];
        this.loading = false;

        this.changeDefaultChecked();
      });
    },
    getAsVal(obj, bind = '') {
      let result = this.deepClone(obj);
      if (this.validatenull(bind)) return result;
      bind.split('.').forEach(ele => {
        if (!this.validatenull(result[ele])) {
          result = result[ele];
        } else {
          result = '';
        }
      });
      return result;
    },
    getDetail(c) {
      return axios.get('/api/vt-admin/businessOpportunity/detail', {
        params:{
          id:c
        }
      });
    },
    handleAdd() {
      router.push({
        path: '/CRM/businessOpportunity/compoents/update',
        query: {
          customerId: route.query.id,
        },
      });
    },
  },
};
</script>
<style lang="scss">
.nf-dialog {
  display: flex;
  flex-direction: column;
  margin: 0 !important;
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -40%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  .el-dialog__body {
    flex: 1;
    overflow: auto;
  }
}
</style>
