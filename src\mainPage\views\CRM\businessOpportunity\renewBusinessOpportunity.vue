<template>
  <div class="project-main">
    
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex == index ? 'active' : ''"
        @click="handleTabClick(item, index)"
      >
        {{ item.label }}
      </div>
      
    </div>
    <div class="tab-main">
      <keep-alive >
        <component :is="currentComponent"/>
      </keep-alive>
    </div>
  </div>
</template>

<script>
import renewBusinessOpportunityForself from './renewBusinessOpportunityForself.vue';
import renewBusinessOpportunityForall from './renewBusinessOpportunityForall.vue';

export default {
  name: 'renewBusinessOpportunity',
  computed: {
    currentComponent() {
      return this.tabIndex === 0 ? renewBusinessOpportunityForself : renewBusinessOpportunityForall
    }
  },
  components: {
    renewBusinessOpportunityForself,
    renewBusinessOpportunityForall,
  },
  data() {
    return {
      // 标签索引
      tabIndex: 0,
      tabs: [
        {
          value: 0,
          label: '我的续费',
        },
        {
          value: 1,
          label: '所有续费',
        },
      ],
    };
  },
  activated() {},
  mounted() {
   
    const data = [
      {
        value: 0,
        label: '我的续费',
        isHas: this.$store.getters.permission['programme:myRenew'],
      },
      {
        value: 1,
        label: '所有续费',
        isHas: this.$store.getters.permission['programme:allRenew'],
      },
    ];
    this.tabs = data.filter(item => item.isHas);
  },
  methods: {
    handleTabClick(item, index) {
      const { value } = item;
      if (this.tabIndex == index) {
        return;
      }
      this.tabIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin: 0 6px 0 7px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
</style>
