<template>
  <el-empty description="深化设计未完成" v-if="props.deepenStatus != 3 && props.isView"></el-empty>
  <div v-else style="height: 100%" :class="{ 'fullscreen-mode': isFullscreen }">
    <!-- <div style="height: 100px; margin-bottom: 20px">
      <el-card shadow="never">
        <avue-form :option="baseOption" ref="addForm" v-model="form">
          <template #baseInfo-header="column">
            <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
            <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
          </template>
        </avue-form>
      </el-card>
    </div> -->
    <div style="height: 100%" v-loading="loading" element-loading-text="刷新数据中">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="5">
          <el-card shadow="never" style="height: 100%">
            <avue-tree
              ref="tree"
              :option="treeOption"
              :data="treeData"
              @node-click="handleNodeClick"
            ></avue-tree>
          </el-card>
        </el-col>
        <el-col :span="19">
          <el-card
            shadow="never"
            style="height: 500px; overflow-y: auto"
            :style="{ height: height + 'px' }"
          >
            <template #header>
              <div style="display: flex; justify-content: space-between; align-items: center;">
                <span>产品清单</span>
                <el-button
                  type="primary"
                  :icon="isFullscreen ? 'compress' : 'full-screen'"
                  size="small"
                  @click="toggleFullscreen"
                >
                  {{ isFullscreen ? '退出全屏' : '全屏查看' }}
                </el-button>
              </div>
            </template>
            <!-- <el-button
              type="primary"
              icon="plus"
              v-if="!props.detail"
              style="margin-bottom: 5px"
              @click="$refs.productSelectRef.visible = true"
            >
              新增</el-button
            > -->
            <div :ref="`box_${item.moduleId}`" v-for="(item, index) in treeData" :key="item.id">
              <h3>{{ item.moduleName }}</h3>
              <el-table
                class="avue-crud"
                v-if="item.children && item.children.length > 0"
                :span-method="arraySpanMethod"
                :data="item.children"
                :row-class-name="rowClassName"
                border
                :expand-row-keys="item.children && item.children.map(item => item.id)"
                row-key="id"
                align="center"
              >
                <el-table-column type="expand">
                  <template #default="{ row: rowP }">
                    <div
                      style="
                        box-sizing: border-box;
                        margin-top: -9px;
                        display: flex;
                        align-items: flex-end;
                      "
                    >
                      <div style="width: 49px; text-align: center">
                        <el-button
                          type="primary"
                          circle=""
                          icon="plus"
                          v-if="!props.detail"
                          style="margin-bottom: 5px"
                          @click="handleAddProduct(rowP, item)"
                        ></el-button>
                      </div>
                      <el-table
                        class="avue-crud"
                        :show-header="false"
                        ref="tableItem"
                        :data="rowP.detailVOList"
                        :row-class-name="tableRowClassName"
                        border
                        row-key="id"
                        :tree-props="{
                          children: 'splitDTOList',
                        }"
                        align="center"
                      >
                        <el-table-column
                          label="设备名称"
                          #default="{ row }"
                          show-overflow-tooltip
                          prop="customProductName"
                          width="130"
                        >
                          <el-tag
                            size="small"
                            type="danger"
                            v-if="row.splitDTOList && row.splitDTOList.length > 0"
                            effect="plain"
                            >拆</el-tag
                          >
                          <el-tag
                            type="danger"
                            effect="plain"
                            size="small"
                            v-if="!row.detailId && row.isChange == 1"
                            >新</el-tag
                          >{{ row.productName }}
                          <span>{{ row.customProductName }}</span>
                        </el-table-column>
                        <el-table-column
                          label="规格型号"
                          show-overflow-tooltip
                          #default="{ row }"
                          prop="customProductSpecification"
                          width="130"
                        >
                          {{
                            row.customProductSpecification || row.product?.productSpecification
                          }}</el-table-column
                        >

                        <el-table-column
                          label="产品描述"
                          show-overflow-tooltip
                          width="200"
                          #default="{ row }"
                          prop="customProductDescription"
                        >
                          {{ row.customProductDescription || row.product?.description }}
                        </el-table-column>
                        <el-table-column
                          label="品牌"
                          align="center"
                          #default="{ row }"
                          prop="productBrand"
                          width="80"
                        >
                          {{ row.productBrand || row.product?.productBrand }}
                        </el-table-column>
                        <el-table-column
                          label="单位"
                          align="center"
                          #default="{ row }"
                          width="80"
                          prop="product.unitName"
                        >
                          {{ row.customUnit || row.product?.unitName }}
                        </el-table-column>
                         <el-table-column label="数量" align="center" prop="deepenNumber">
                        </el-table-column>
                                 
                        <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                             
                          <template #default="{ row }">
                              {{ parseFloat(row.purchaseNums || row.purchaseNumber || '0') }}       
                             
                          </template>
                                             
                        </el-table-column>
                                 
                        <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                                   
                          <template #default="{ row }">
                              {{ parseFloat(row.noPurchaseNums || noPurchaseNumber || '0') }}    
                          </template>
                               
                        </el-table-column>

                        <el-table-column
                          show-overflow-tooltip
                          label="深化备注"
                          #default="{ row }"
                          :width="!props.detail ? 210 : 300"
                          prop="deepenRemark"
                        >
                          <el-input
                            v-if="!props.detail"
                            v-model="row.deepenRemark"
                            type="textarea"
                            placeholder=""
                          ></el-input>
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          #default="{ row, $index }"
                          align="center"
                          fixed="right"
                          v-if="!props.detail"
                          width="90"
                          prop="menu"
                        >
                          <el-button
                            type="primary"
                            text
                            icon="delete"
                            v-if="row.uuid"
                            @click="deleteProduct(rowP, $index)"
                            >删除</el-button
                          >
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="所属分类" prop="classify" width="120"> </el-table-column> -->
                <el-table-column
                  label="设备名称"
                  #default="{ row }"
                  show-overflow-tooltip
                  prop="customProductName"
                  width="130"
                >
                  <span style="font-size: 18px; font-weight: bolder; color: black">{{
                    row.value
                  }}</span></el-table-column
                >
                <el-table-column
                  label="规格型号"
                  show-overflow-tooltip
                  #default="{ row }"
                  prop="customProductSpecification"
                  width="130"
                >
                  {{
                    row.customProductSpecification || row.product?.productSpecification
                  }}</el-table-column
                >

                <el-table-column
                  label="产品描述"
                  show-overflow-tooltip
                  width="200"
                  #default="{ row }"
                  prop="customProductDescription"
                >
                  {{ row.customProductDescription || row.product?.description }}
                </el-table-column>
                <el-table-column
                  label="品牌"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="productBrand"
                >
                  {{ row.productBrand || row.product?.productBrand }}
                </el-table-column>
                <el-table-column
                  label="单位"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="product.unitName"
                >
                  {{ row.customUnit || row.product?.unitName }}
                </el-table-column>
                 <el-table-column label="数量" align="center" prop="deepenNumber">
                </el-table-column>
                         
                <el-table-column
                  label="已请购数量"
                  align="center"
                  #default="{ row }"
                  prop="purchaseNums"
                >
                               
                </el-table-column>
                         
                <el-table-column
                  label="未采购数量"
                  align="center"
                  #default="{ row }"
                  prop="noPurchaseNums"
                >
                               
                </el-table-column>

                <el-table-column
                  show-overflow-tooltip
                  label="深化备注"
                  #default="{ row }"
                  width="300"
                  prop="deepenRemark"
                >
                </el-table-column>
              </el-table>
              <div v-else style="padding-left: 48px; box-sizing: border-box">
                <el-table
                  class="avue-crud"
                  ref="tableItem"
                  :data="item.detailVOList"
                  :row-class-name="tableRowClassName"
                  border
                  row-key="id"
                  align="center"
                  :tree-props="{
                    children: 'splitDTOList',
                  }"
                >
                  <el-table-column
                    label="设备名称"
                    #default="{ row }"
                    show-overflow-tooltip
                    prop="customProductName"
                    width="130"
                  >
                    <el-tag
                      size="small"
                      type="danger"
                      v-if="row.splitDTOList && row.splitDTOList.length > 0"
                      effect="plain"
                      >拆</el-tag
                    >
                    <span>{{ row.customProductName }}</span>
                  </el-table-column>
                  <el-table-column
                    label="规格型号"
                    show-overflow-tooltip
                    #default="{ row }"
                    prop="customProductSpecification"
                    width="130"
                  >
                    {{
                      row.customProductSpecification || row.product?.productSpecification
                    }}</el-table-column
                  >

                  <el-table-column
                    label="产品描述"
                    show-overflow-tooltip
                    width="200"
                    #default="{ row }"
                    prop="customProductDescription"
                  >
                    {{ row.customProductDescription || row.product?.description }}
                  </el-table-column>
                  <el-table-column
                    label="品牌"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="productBrand"
                  >
                    {{ row.productBrand || row.product?.productBrand }}
                  </el-table-column>
                  <el-table-column
                    label="单位"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="product.unitName"
                  >
                    {{ row.customUnit || row.product?.unitName }}
                  </el-table-column>
                   <el-table-column label="数量" align="center" prop="deepenNumber">
                  </el-table-column>
                           
                  <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                       
                    <template #default="{ row }">
                        {{ parseFloat(row.purchaseNums || row.purchaseNumber) }}         
                    </template>
                                       
                  </el-table-column>
                           
                  <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                             
                    <template #default="{ row }">
                        {{ parseFloat(row.noPurchaseNums || noPurchaseNumber) }}    
                    </template>
                         
                  </el-table-column>

                  <el-table-column
                    show-overflow-tooltip
                    label="深化备注"
                    #default="{ row }"
                    :width="!props.detail ? 210 : 300"
                    prop="deepenRemark"
                  >
                    <el-input
                      v-if="!props.detail"
                      v-model="row.deepenRemark"
                      type="textarea"
                      placeholder=""
                    ></el-input>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    #default="{ row, $index }"
                    align="center"
                    fixed="right"
                    v-if="!props.detail"
                    width="90"
                    prop="menu"
                  >
                    <el-button
                      type="primary"
                      text
                      icon="delete"
                      v-if="row.uuid"
                      @click="deleteProduct(rowP, $index)"
                      >删除</el-button
                    >
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <wfProductSelect
        @onConfirm="handleProductSelectConfirm"
        ref="productSelectRef"
      ></wfProductSelect>
    </div>
  </div>
</template>

<script setup>
import { watchEffect, getCurrentInstance, onMounted } from 'vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { useRoute } from 'vue-router';
import { randomLenNum } from '@/utils/util';
const route = useRoute();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  detail: {
    type: Boolean,
    default: false,
  },
  isView: {
    type: Boolean,
    default: false,
  },
  deepenStatus: {
    type: Number,
    default: 0,
  },
  height: Number,
});
const { proxy } = getCurrentInstance();
let treeData = ref([]);
let allData = ref([]);
let tableData = ref([]);
let form = ref({});
let loading = ref(false);
watchEffect(() => {
  if (props.id) {
    getDetail();
  }
});

function getDetail() {
  allData.value = [];
  tableData.value = [];
  loading.value = true;
  if (!route.query.id) return;
  axios.post('/api/vt-admin/project/projectProducts?id=' + route.query.id, {}).then(res => {
    form.value = res.data.data;
    form.value.businessOpportunityId = props.id;
    const data = res.data.data.moduleVOList.map(item => {
      return {
        ...item,
        moduleId: item.id,
      };
    });
    formatData(data);
    form.value.moduleVOList = null;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}
let treeOption = ref({
  addBtn: false,
  defaultExpandAll: true,
  props: {
    value: 'value',
    label: 'label',
  },
});

function formatData(data) {
  treeData.value = data.map(item => {
    item.label = item.moduleName;
    const isHasClassify = item.detailVOList.every(item => item.classify);
    if (isHasClassify) {
      item.children = item.detailVOList
        .map(i => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: item.moduleId,
            id: i.moduleId + i.classify,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push({
              ...cur,
              // deepenNumber: cur.deepenNumber ? cur.deepenNumber * 1 : cur.number * 1,
              // leaderDeepenNumber: cur.leaderDeepenNumber
              //   ? cur.leaderDeepenNumber * 1
              //   : cur.deepenNumber * 1,
              detailVOList: item.detailVOList
                .filter(i => i.classify == cur.value)
                .map(item => {
                  return {
                    ...item,
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                    splitDTOList:
                      item.splitVOS && item.splitVOS.length > 0
                        ? item.splitVOS.map(item => {
                            item.number = 0;
                            return {
                              ...item,
                              uuid: randomLenNum(10),
                              number: parseFloat(item.number),
                              deepenNumber: item.deepenNumber
                                ? item.deepenNumber * 1
                                : item.number * 1,
                              leaderDeepenNumber:
                                form.value.deepenType == 0
                                  ? null
                                  : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                                  ? item.leaderDeepenNumber * 1
                                  : item.deepenNumber * 1,
                            };
                          })
                        : [],
                  };
                }),
            });
          }
          return acc;
        }, []);
    } else {
      item.detailVOList = item.detailVOList.map(item => {
        return {
          ...item,
          deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
          leaderDeepenNumber:
            form.value.deepenType == 0
              ? null
              : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
              ? item.leaderDeepenNumber * 1
              : item.deepenNumber * 1,
          splitDTOList:
            item.splitVOS && item.splitVOS.length > 0
              ? item.splitVOS.map(item => {
                  item.number = 0;
                  return {
                    ...item,
                    uuid: randomLenNum(10),
                    number: parseFloat(item.number),
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                  };
                })
              : [],
        };
      });
    }
    item.isHasClassify = isHasClassify;

    return item;
  });
  ;
  getChangeList();
  allData.value = data;
}
let changeList = ref();
function getChangeList() {
  const uuid = randomLenNum(10);
  axios.post('/api/vt-admin/projectChange/completeListById?id=' + route.query.id).then(res => {
    changeList.value = res.data.data
      .filter(item => item.changeStatus == 1)
      .map(item => {
        return {
          ...item,
          moduleId: item.id,
          value: item.title,
          label: item.title,
          parentId: uuid,
          id: item.id + item.title,
          detailVOList: item.detailVOS.map(item1 => {
            return {
              ...item1,
              isLeaf: true,
              deepenNumber:
                item1.changeType == 2 ? `-${parseFloat(item1.number)}` : parseFloat(item1.number),
              parentId: item.id,
              customProductName: item1.productName,
              customProductDescription: item1.description,
              customProductSpecification: item1.productSpecification,
              customUnit: item1.unitName,

              customProductName: item1.productName,
              uuid: randomLenNum(10),
              isChange: 1,
            };
          }),
        };
      });

    if (changeList.value.length > 0) {
      treeData.value.push({
        label: '签证项',
        moduleName: '签证项',
        id: uuid,
        moduleId: uuid,
        children: [...changeList.value],
      });
    }
  });
}
// function handleNodeClick(node) {
//   if (!node) {
//     allData.value.forEach(item => {
//       item.detailVOList.forEach(element => {
//         element.deepenNumber =
//           element.deepenNumber || element.deepenNumber == 0
//             ? element.deepenNumber * 1
//             : element.number * 1;
//         element.leaderDeepenNumber =
//           element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//             ? element.leaderDeepenNumber * 1
//             : element.deepenNumber * 1;
//         tableData.value.push(element);
//       });
//     });
//   }
//   if (!node.parentId) {
//     ;
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     node.detailVOList.forEach(element => {
//       element.deepenNumber =
//         element.deepenNumber || element.deepenNumber == 0
//           ? element.deepenNumber * 1
//           : element.number * 1;
//       element.leaderDeepenNumber =
//         element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//           ? element.leaderDeepenNumber * 1
//           : element.deepenNumber * 1;
//       tableData.value.push(element);
//     });
//   } else {
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     if (node.children && node.children.length > 0) return;
//     allData.value
//       .find(item => item.moduleId === node.parentId)
//       .detailVOList.forEach(element => {
//         if (element.classify === node.value) {
//           element.deepenNumber =
//             element.deepenNumber || element.deepenNumber == 0
//               ? element.deepenNumber * 1
//               : element.number * 1;
//           element.leaderDeepenNumber =
//             element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//               ? element.leaderDeepenNumber * 1
//               : element.deepenNumber * 1;
//           tableData.value.push(element);
//         }
//       });
//   }
// }

function handleNodeClick(node) {
  const id = 'box_' + node.moduleId;
  ;
  proxy.$nextTick(() => {
    if (node.parentId) {
      const ele = document.querySelector(`.row_${node.parentId}${node.value}`);
      ele.scrollIntoView({ behavior: 'smooth' });
    } else {
      proxy.$refs[id][0].scrollIntoView({ behavior: 'smooth' });
    }
  });
}
function rowClassName(val) {
  return 'row_' + val.row.parentId + val.row.value;
}
let currModuleId = ref(null);
let currClassfiy = ref(null);
let isFullscreen = ref(false);
function handleAddProduct(row) {
  currModuleId.value = row.parentId;
  currClassfiy.value = row.value;
  proxy.$refs.productSelectRef.visible = true;
}
function handleProductSelectConfirm(id) {
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      const {
        productName,
        productSpecification,
        description,
        productBrand,
        unitName,
        id,
        number = 0,
      } = res.data.data;
      const data = {
        customProductName: productName,
        customProductSpecification: productSpecification,
        customProductDescription: description,
        product: {
          productBrand: productBrand,
          unitName: unitName,
        },
        uuid: id,
        productId: id,
        number: number,
        deepenNumber: '',
        leaderDeepenNumber: 1,
        classify: currClassfiy.value,
      };
      // tableData.value.push(data);
      // proxy.$nextTick(() => {
      //   proxy.$refs.table.setScrollTop(10000);
      // });

      treeData.value
        .find(item => item.moduleId === currModuleId.value)
        .children.find(item => item.value == currClassfiy.value)
        .detailVOList.push(data);
    });
}
function getData() {
  const data = {
    ...form.value,
    moduleHistoryDTOList: treeData.value.map(item => {
      return {
        ...item,
        detailHistoryDTOS: item.isHasClassify
          ? item.children.reduce((pre, cur) => {
              return [...pre, ...cur.detailVOList];
            }, [])
          : item.detailVOList,
        detailVOList: null,
      };
    }),
  };
  ;
  return data;
}
function setId(id) {
  form.value.id = id;
}

function tableRowClassName(row) {
  return row.number == 0 ? 'success-row' : '';
}

// 全屏切换功能
function toggleFullscreen() {
  isFullscreen.value = !isFullscreen.value;
  
  if (isFullscreen.value) {
    // 进入全屏模式 - 使用自定义全屏样式
    document.body.style.overflow = 'hidden';
  } else {
    // 退出全屏模式
    document.body.style.overflow = '';
  }
}

// 组件挂载时添加键盘事件监听
onMounted(() => {
  // ESC键退出全屏
  const handleKeydown = (event) => {
    if (event.key === 'Escape' && isFullscreen.value) {
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeydown);
  
  // 组件卸载时清理
  return () => {
    document.removeEventListener('keydown', handleKeydown);
    if (isFullscreen.value) {
      document.body.style.overflow = '';
    }
  };
});
defineExpose({ getData, setId, getDetail });
// 删除产品
function deleteProduct(rowp, index) {
  rowp.detailVOList.splice(index, 1);
  // const index = treeData.value
  //   .find(i => i.moduleId === item.moduleId).children(item => item.value === row.classify)
  //   .detailVOList.findIndex(item => item.uuid === row.uuid);
  //   treeData.value.find(i => i.moduleId === item.moduleId).children.find(item => item.value == row.classify).detailVOList.splice(index, 1);
}
// function arraySpanMethod({  row,
//   column,
//   rowIndex,
//   columnIndex,}) {
//   if(column == 1){
//     return
//   }
// }
</script>

<style lang="scss" scoped>
:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 30px !important;
}

// 全屏模式下的样式优化
:deep(.el-card) {
  transition: all 0.3s ease;
}

// 自定义全屏模式样式
.fullscreen-mode {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  background: white;
  animation: fadeIn 0.3s ease-in-out;
  
  .el-row {
    height: 100vh !important;
  }
  
  .el-card {
    height: 100vh !important;
    border-radius: 0;
    
    .el-card__body {
      height: calc(100vh - 60px) !important;
      overflow-y: auto;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>
