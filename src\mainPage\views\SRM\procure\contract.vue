<template>
  <basic-container :shadow="props.supplierId || props.orderId ? 'never' : 'always'">
    <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
      @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud" :before-open="beforeOpen"
      @row-del="rowDel" @search-reset="onLoad" @search-change="searchChange" @current-change="onLoad"
      @refresh-change="onLoad" @size-change="onLoad" @keyup.enter="onLoad" v-model="form">
      <template #menu-left>
        <div style="display: flex">
          <el-button type="primary" icon="plus" v-if="!route.query.id" @click="handllAdd">新增</el-button>
          <el-button type="primary" icon="plus" v-if="!route.query.id"
            @click="handllAddForCooperation">新增代采合同</el-button>
          <div style="display: flex; align-items: center; gap: 20px">
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large">￥{{ (contractTotalPrice * 1).toLocaleString() }}</el-text>
            <span style="font-weight: bolder">已付款总额：</span>
            <el-text type="primary" size="large">￥{{ (hasPaymentPrice * 1).toLocaleString() }}</el-text>
            <span style="font-weight: bolder">已收票总额：</span>
            <el-text type="primary" size="large">￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text>
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="Edit" v-if="row.purchaseContractType == 1"
          @click="handleEditForCooperation(row)">编辑</el-button>
        <el-button type="primary" text icon="Edit" v-else @click="handleEdit(row)">编辑</el-button>
        <el-button type="primary" text icon="select" v-if="row.purchaseContractType == 1 && row.invoiceStatus != 0"
          @click="toReceiveTicket(row)">收票</el-button>
      </template>
      <template #contractCode="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.contractCode }}</el-link>
      </template>
      <template #arriveStatus="{ row }">
        <span effect="plain" size="large" v-if="row.purchaseContractType == 1" type="info">---</span>
        <div v-else>
          <el-tag effect="plain" size="large" v-if="row.arriveStatus == 0" type="info">未到货</el-tag>
          <el-tag effect="plain" size="large" v-if="row.arriveStatus == 1" type="warning">部分到货</el-tag>
          <el-tag effect="plain" size="large" type="success" v-else-if="row.arriveStatus == 2">全部到货</el-tag>
        </div>
      </template>
      <template #invoiceStatus="{ row }">
        <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
        <el-tag effect="plain" size="large" v-if="row.isNeedInvoice == 1"
          :type="row.invoiceStatus == 0 ? 'success' : row.invoiceStatus == 1 ? 'danger' : 'warning'">{{
            row.$invoiceStatus }}</el-tag>
        <el-tag size="large" effect="plain" v-else type="info">无需开票</el-tag>
      </template>
      <template #paymentStatus="{ row }">
        <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
        <el-tag effect="plain" size="large"
          :type="row.paymentStatus == 1 ? 'success' : row.paymentStatus == 0 ? 'danger' : 'warning'">{{
            row.$paymentStatus }}</el-tag>
      </template>
      <template #contractFiles="{ row }">
        <File :fileList="row.attachList || []"></File>
      </template>
      <template #productListBtn-form>
        <el-button type="primary" plain icon="plus" @click="visible = true">核对产品</el-button>
      </template>
      <template #contractPrice="{ row }">
        <el-tooltip v-if="row.discountPrice && row.discountPrice > 0" :content="`优惠金额：¥${Number(row.discountPrice).toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`" placement="top">
          <span class="contract-price-with-discount">{{ row.contractPrice }}</span>
        </el-tooltip>
        <span v-else>{{ row.contractPrice }}</span>
      </template>
      <template #hasInvoice="{ row }">
        <el-link type="primary" @click="showInvoiceList(row)">{{ (row.hasInvoice * 1).toFixed(2) }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <el-drawer title="确认产品信息" size="90%" v-model="visible">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card shadow="never">
            <avue-form :option="addOption" ref="addFormRef" @submit="handleSubmit" v-model="addForm"></avue-form>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <el-table class="avue-crud" show-summary :summary-method="summaryMethod" :data="productList" border
              align="center">
              <el-table-column label="设备名称" width="150" show-overflow-tooltip
                prop="productVO.productName"></el-table-column>
              <el-table-column label="规格型号" show-overflow-tooltip
                prop="productVO.productSpecification"></el-table-column>
              <!-- <el-table-column label="产品图片" #default="{ row }">
        <el-image
            style="width: 80px"
            :preview-src-list="[row.coverUrl]"
            :src="row.coverUrl"
          ></el-image>
      </el-table-column> -->
              <el-table-column label="产品描述" show-overflow-tooltip width="200"
                prop="productVO.description"></el-table-column>
              <el-table-column label="品牌" width="80" prop="productVO.productBrand"></el-table-column>
              <el-table-column label="单位" width="80" prop="productVO.unitName"></el-table-column>
              <!-- <el-table-column
                label="供应商"
                show-overflow-tooltip
                prop="supplierName"
              ></el-table-column> -->
              <el-table-column label="数量" width="100" #default="{ row }" prop="number">
              </el-table-column>

              <el-table-column label="单价" #default="{ row, $index }" prop="unitPrice">
                <el-input v-model="productList[$index].unitPrice" @blur="setContractTotalAmount"
                  size="small"></el-input>
              </el-table-column>
              <el-table-column label="金额" width="180" #default="{ row }" prop="totalPrice">
                {{ row.unitPrice ? (row.number * row.unitPrice).toFixed(2) : '---' }}
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <!-- 取消 -->
          <el-button @click="visible = false">取 消</el-button>
          <el-button type="primary" @click="$refs.addFormRef?.submit()">确 定</el-button>
        </div>
      </template>
    </el-drawer>
    <el-drawer :title="drawerMode === 'add' ? '新增合同' : '编辑合同'" size="500" v-model="visibleForCooperation">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-card shadow="never">
            <avue-form :option="addOptionForCooperation" ref="addFormRefForCooperation"
              @submit="handleSubmitForCooperation" v-model="addFormForCooperation"></avue-form>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <!-- 取消 -->
          <el-button @click="visibleForCooperation = false">取 消</el-button>
          <el-button type="primary" @click="$refs.addFormRefForCooperation?.submit()">确 定</el-button>
        </div>
      </template>
    </el-drawer>
    <el-drawer v-model="drawer" size="1300" @closed="handleClose" title="新增收票">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-divider content-position="left"><span
              style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder">发票信息</span></el-divider>
          <el-card shadow="never" class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRefForInvoice" @submit="submitForInvoice"
              v-model="addFormForInvoice">
              <template #purchaseContractId>
                <el-select style="width: 100%" v-model="addFormForInvoice.purchaseContractId" placeholder="请选择采购合同">
                  <el-option v-for="item in purchaseContractList" :key="item.id" :label="item.contractCode"
                    :value="item.id">
                    <span style="float: left">{{ item.contractCode }}</span>
                    <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
                      {{ item.supplierName }}-(￥{{ item.contractPrice }})
                    </span>
                  </el-option>
                </el-select>
              </template>
            </avue-form>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRefForInvoice.submit()">确认 收票</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 已收票列表抽屉 -->
    <el-drawer v-model="invoiceListDrawer" size="90%" title="已开发票列表">
      <InvoiceList
        :contractId="selectedContractId"
        :contractCode="selectedContractCode"
        :supplierName="selectedSupplierName"
        :purchaseContractType="purchaseContractType"
      />
    </el-drawer>
  </basic-container>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
import { dateFormat } from '@/utils/date';
import InvoiceList from '@/views/Finance/ticket/ticket.vue';
let route = useRoute();

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  cellBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 5,
  menuWidth: 150,
  border: true,
  labelWidth: 120,
  column: [
    {
      label: '合同编号',
      prop: 'contractCode',
      width: 160,
      searchSpan: 4,
      overHidden: true,
      addDisplay: false,
      search: !props.orderId,
    },
    {
      label: '产品名称',
      prop: 'productName',
      hide: true,
      addDisplay: false,
      search: !route.query.id,
    },
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
      change: ({ value }) => {
        setUrl(value);
      },
      control: val => {
        return {
          orderId: {
            disabled: !val,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      span: 24,
      component: 'wf-order-select',
      overHidden: true,
      params: {
        checkType: 'checkbox',
        Url: '/api/vt-admin/purchaseOrder/pageForAddContract',
      },
      hide: true,
      change: ({ value }) => {
        console.log(value);
        getProductList(value);
      },
    },
    {
      label: '',
      hide: true,
      // labelWidth:0,
      prop: 'productListBtn',
    },
    {
      label: '供应商',
      prop: 'supplierName',
      overHidden: true,
      component: 'wf-supplier-drop',
      // width: 250,
      searchSpan: 4,
      hide: !!props.supplierId,
      addDisplay: false,
      searchLabelWidth: 100,
      search: !route.query.id,
    },
    {
      label: '对方订单编号',
      prop: 'supplierOrderNo',
      span: 24,
      overHidden: true,
    },
    {
      label: '关联合同',
      prop: 'sealContractName',
      addDisplay: false,
      overHidden: true,
      search:true,
      // width: 180,
      // search: !route.query.id,
    },
    {
      label: '采购数量',
      prop: 'purchaseNumber',
      addDisplay: false,
      width: 100,
      formatter: (row, value) => {
        return row.purchaseContractType == 1 ? '--' : value;
      },
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      search: !route.query.id,
      searchSpan: 3,
      addDisplay: false,
      width: 100,
      dicData: [
        {
          label: '未到货',
          value: '0',
        },
        {
          label: '部分到货',
          value: '1',
        },
        {
          label: '全部到货',
          value: '2',
        },
      ],
    },
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      width: 130,
    },
    {
      label: '付款状态',
      prop: 'paymentStatus',
      type: 'select',
      width: 100,
      addDisplay: false,
      dicData: [
        {
          label: '未付款',
          value: 0,
        },
        {
          label: '部分付款',
          value: 2,
        },
        {
          label: '已付款',
          value: 1,
        },
      ],
    },
    {
      label: '已付款金额',
      prop: 'paymentPrice',
      addDisplay: false,
      width: 130,
    },
    {
      label: '发票状态',
      prop: 'invoiceStatus',
      addDisplay: false,
      width: 100,
      dicData: [
        {
          label: '未收票',
          value: 1,
        },
        {
          label: '已收票',
          value: 0,
        },
        {
          label: '部分收票',
          value: 2,
        },
      ],

    },

    {
      label: '已收票金额',
      prop: 'hasInvoice',
      addDisplay: false,
      width: 130,
      slot: true,
    },
    // {
    //   label: '支付时间',
    //   prop: 'paymentTime',
    //   addDisplay: false,
    //   width: 150,

    //   type: 'date',

    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    // },
    // {
    //   label: '已付款金额',
    //   prop: 'totalmoney',
    // },
    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-daterange-search',
      search: true,
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      search: !route.query.id,
      span: 24,
      searchSpan: 5,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: true,
      width: 150,
      hide: true,
    },
    {
      label: '送货地址',
      type: 'input',
      prop: 'deliveryAddress',
      value: '深圳市龙华区民治街道北站社区鸿荣源北站中心B塔2104-2105',
      span: 24,
      hide: true,
    },
    {
      label: '送货方式',
      type: 'radio',
      prop: 'deliveryMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '付款期限',
      type: 'radio',
      span: 15,
      prop: 'paymentTerm',
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_term',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '自定义账期',
      span: 9,
      labelTip: '填1-30之间的数字,即每个月这个日期结账',
      prop: 'fixedBillingDate',
      hide: true,
    },
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    // {
    //   label: '付款状态',
    //   type: 'payStatus',
    //   prop: 'select',
    //   dicData: [
    //     {
    //       label: '未付款',
    //       value: '0',
    //     },
    //     { label: '已付款', value: '1' },
    //   ],
    // },
    // {
    //   label: '创建日期',
    //   type: 'date',
    //   prop: 'createTime',
    //   width: 150,
    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD HH:mm',
    //   valueFormat: 'YYYY-MM-DD HH:mm',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContract/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContract/update';
const tableUrl = '/api/vt-admin/purchaseContract/pageForList';
let params = ref({
  signDate:
    props.supplierId || props.orderId
      ? []
      : [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

const props = defineProps({
  orderId: {
    type: String,
    default: null,
  },
  supplierId: {
    type: String,
    default: null,
  },
});
onMounted(() => {
  onLoad();
});
watch(() => props.orderId, () => {
  onLoad()
})
let loading = ref(false);
let contractTotalPrice = ref(0);
let hasInvoicePrice = ref(0);
let hasPaymentPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          contractFiles: [],
        };
      });
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      contractTotalPrice.value = res.data.data.contractTotalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      hasPaymentPrice.value = res.data.data.hasPaymentPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
      };
    }),
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  ;
  const data = {
    ...row,
    contractFiles: row.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}
function toDetail(row) {
  router.push({
    path: '/procure/contractDetail',
    query: {
      id: row.id,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleRowDBLClick(row, event) {
  row.$cellEdit = true;
}
function handleEdit(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑合同',
    option: {
      labelWidth: 120,
      column: [
        {
          label: '合同编号',
          prop: 'contractCode',
          width: 170,
          searchSpan: 4,
          value: row.contractCode,
          disabled: true,
          overHidden: true,
          search: !route.query.id,
        },
        {
          label: '关联供应商',
          prop: 'supplierName',
          overHidden: true,
          value: row.supplierName,
          disabled: true,
          width: 180,
          searchSpan: 4,
          searchLabelWidth: 100,
          search: !route.query.id,
        },
        {
          label: '对方订单编号',
          prop: 'supplierOrderNo',
          span: 12,
          overHidden: true,
        },
        // {
        //   label: '合同总额',
        //   prop: 'contractPrice',
        //   value: row.contractPrice,
        //   width: 130,
        // },
        {
          label: '采购日期',
          type: 'date',
          prop: 'purchaseDate',
          searchRange: true,
          value: row.purchaseDate,
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          search: true,
          span: 12,
          searchSpan: 5,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          cell: true,
          width: 150,
          rules: [
            {
              required: true,
              message: '请选择采购日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '是否开票',
          prop: 'isNeedInvoice',
          type: 'radio',
          span: 24,
          width: 100,
          value: row.isNeedInvoice,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },

        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          dataType: 'object',
          cell: true,
          loadText: '附件上传中，请稍等',
          span: 24,
          width: 150,
          slot: true,
          value: [],
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      ;
      const data = {
        ...row,
        contractFiles:
          res.data.contractFiles && res.data.contractFiles.map(item => item.value).join(','),
        purchaseDate: res.data.purchaseDate,
        isNeedInvoice: res.data.isNeedInvoice,
        contractPrice: res.data.contractPrice,
      };
      axios
        .post(updateUrl, data)
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          }
        })
        .catch(err => { });
    },
  });
}
let addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
      change: ({ value }) => {
        setUrl(value);
      },
      control: val => {
        return {
          orderId: {
            disabled: !val,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      span: 24,
      component: 'wf-order-select',
      overHidden: true,
      params: {
        checkType: 'checkbox',
        Url: '/api/vt-admin/purchaseOrder/pageForAddContract',
      },
      hide: true,
      change: ({ value }) => {
        console.log(value);
        getProductList(value);
      },
    },
    {
      label: '对方订单编号',
      prop: 'supplierOrderNo',
      span: 24,
      overHidden: true,
    },
    {
      label: '优惠金额',
      prop: 'discountPrice',
      span: 24,
      type: 'number',
      value: 0,
      width: 150,

      blur: ({ value }) => {
        const totalAmount = getContractTotalAmount();
        addForm.value.contractPrice = totalAmount;
      },
    },

    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      span: 24,
      readonly: true,
      type: 'number',
      width: 150,
      rules: [
        {
          required: true,
          message: '请输入合同总额',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 150,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '是否开票',
      prop: 'isNeedInvoice',
      type: 'radio',
      span: 24,
      width: 100,
      value: 1,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      control: (res) => {
        return {
          billingCompany: {
            display: res == 1
          }
        }
      }
    },
    //  {
    //       label: '收票公司',
    //       type: 'select',
    //       prop: 'billingCompany',
    //       props: {
    //         label: 'companyName',
    //         value: 'id',
    //       },
    //       dicFormatter: res => {
    //         return res.data.records;
    //       },
    //       span:24,
    //       search: true,
    //       hide: true,
    //       cell: false,
    //       dicUrl: '/api/vt-admin/company/page?size=100',
    //     },
    {
      label: '送货地址',
      type: 'input',
      prop: 'deliveryAddress',
      value: '深圳市龙华区民治街道北站社区鸿荣源北站中心B塔2104-2105',
      span: 24,
      hide: true,
    },
    {
      label: '送货方式',
      type: 'radio',
      prop: 'deliveryMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '付款期限',
      type: 'radio',
      span: 24,
      prop: 'paymentTerm',
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_term',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '自定义账期',
      span: 9,
      span: 24,
      labelTip: '填1-30之间的数字,即每个月这个日期结账',
      prop: 'fixedBillingDate',
      hide: true,
    },
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    // {
    //   label: '付款状态',
    //   type: 'payStatus',
    //   prop: 'select',
    //   dicData: [
    //     {
    //       label: '未付款',
    //       value: '0',
    //     },
    //     { label: '已付款', value: '1' },
    //   ],
    // },
    // {
    //   label: '创建日期',
    //   type: 'date',
    //   prop: 'createTime',
    //   width: 150,
    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD HH:mm',
    //   valueFormat: 'YYYY-MM-DD HH:mm',
    // },
  ],
});
let addForm = ref({
  discountPrice: 0,
});
function setUrl(value) {
  const orderRef = proxy.findObject(addOption.value.column, 'orderId');
  orderRef.params.Url = '/api/vt-admin/purchaseOrder/pageForAddContract?supplierId=' + value;
}
let visible = ref(false);
let productList = ref([]);
function getProductList(value) {
  axios
    .get('/api/vt-admin/purchaseOrder/detailForAddContract', {
      params: {
        ids: value,
        supplierId: addForm.value.supplierId,
      },
    })
    .then(res => {
      productList.value = res.data.data;
      const totalAmount = getContractTotalAmount();

      nextTick(() => {
        addForm.value.contractPrice = totalAmount;
      });
    });
}

function getContractTotalAmount() {
  // 计算产品总额
  const totalAmount = productList.value.reduce((sum, item) => {
    const num = Number(item.number) || 0;
    const price = Number(item.unitPrice) || 0;
    return sum + num * price;
  }, 0);

  return totalAmount - (addForm.value.discountPrice || 0);
}
function beforeOpen(done, type) {
  if (type == 'add') {
    form.value.supplierId = props.supplierId;
    form.value.discountPrice = 0;
  }
  done();
}

const summaryMethod = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'totalPrice') {
      const values = data.map(item => {
        const num = Number(item.number) || 0;
        const price = Number(item.unitPrice) || 0;
        return num * price;
      });
      const subtotal = values.reduce((prev, curr) => prev + curr, 0);
      const discountPrice = Number(addForm.value.discountPrice) || 0;
      const contractPrice = Number(addForm.value.contractPrice) || 0;

      sums[index] = (
        <div class="summary-content">
          <div class="summary-item subtotal">
            <span class="label">小计：</span>
            <span class="value">
              ¥
              {subtotal.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
          {discountPrice > 0 && (
            <div class="summary-item discount">
              <span class="label">优惠金额：</span>
              <span class="value discount-value">
                -¥
                {discountPrice.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </span>
            </div>
          )}
          <div class="summary-item total">
            <span class="label">合同总额：</span>
            <span class="value total-value">
              ¥
              {contractPrice.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
        </div>
      );
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
let addFormRef = ref();
function handllAdd() {
  if (addFormRef.value) {
    addFormRef.value.resetFields();
    addFormRef.value.resetForm();
  }
  // 清空新增表单数据
  addForm.value = {
    discountPrice: 0,
    isNeedInvoice: 1,
  };
  // 重置关联供应商和订单的URL
  const orderRef = proxy.findObject(addOption.value.column, 'orderId');
  orderRef.params.Url = '/api/vt-admin/purchaseOrder/pageForAddContract';
  // 清空产品列表
  productList.value = [];
  // 打开drawer
  visible.value = true;
}
function handleSubmit(form, done, loading) {
  // 新增合同逻辑
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
        // 计算总价
        totalPrice: item.number * item.unitPrice,
      };
    }),
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        visible.value = false;
        done();
      }
    })
    .catch(err => {
      done();
    });
}
// 新增一个方法用于设置合同总额
function setContractTotalAmount() {
  const totalAmount = getContractTotalAmount();
  addForm.value.contractPrice = totalAmount.toFixed(2);
}



// 合作合同 中的采购合同

function handleEditForCooperation(row) {
  drawerMode.value = 'edit';
  editingRow.value = row;
  debugger
  // 填充编辑表单数据
  addFormForCooperation.value = {
    id: row.id,
    supplierId: row.supplierId,
    sealContractId: row.sealContractId,
    contractCode: row.contractCode,
    supplierName: row.supplierName,
    purchaseDate: row.purchaseDate,
    isNeedInvoice: row.isNeedInvoice,
    contractPrice: row.contractPrice,
    contractFiles: row.attachList && row.attachList.map(item => {
      return {
        value: item.id,
        label: item.originalName,
      }
    }) || [],
  };

  visibleForCooperation.value = true;
}

let addOptionForCooperation = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联合同',
      prop: 'sealContractId',
      component: 'wf-contract-select',
      span: 24,
      params: {
        Url: '/api/vt-admin/sealContract/page?selectType=2&contractType=5',
      },
    },
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      span: 24,

      type: 'number',
      width: 150,
      rules: [
        {
          required: true,
          message: '请输入合同总额',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 150,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '是否开票',
      prop: 'isNeedInvoice',
      type: 'radio',
      span: 24,
      width: 100,
      value: 1,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      control: res => {
        return {
          billingCompany: {
            display: res == 1,
          },
        };
      },
    },
    //  {
    //       label: '收票公司',
    //       type: 'select',
    //       prop: 'billingCompany',
    //       props: {
    //         label: 'companyName',
    //         value: 'id',
    //       },
    //       dicFormatter: res => {
    //         return res.data.records;
    //       },
    //       span:24,
    //       search: true,
    //       hide: true,
    //       cell: false,
    //       dicUrl: '/api/vt-admin/company/page?size=100',
    //     },

    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },


    {
      label: '合同附件',
      prop: 'contractFiles',
      type: 'upload',
      dataType: 'object',
      cell: true,
      loadText: '附件上传中，请稍等',
      span: 24,
      width: 150,
      slot: true,
      value: [],
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});
let addFormForCooperation = ref({});

let visibleForCooperation = ref(false);
let drawerMode = ref('add'); // 'add' 或 'edit'
let editingRow = ref(null); // 存储编辑中的行数据



let addFormRefForCooperation = ref({});
function handllAddForCooperation() {
  drawerMode.value = 'add';
  editingRow.value = null;

  if (addFormRef.value) {
    addFormRefForCooperation.value.resetFields();
    addFormRefForCooperation.value.resetForm();
  }
  // 清空新增表单数据
  addFormRefForCooperation.value = {
    isNeedInvoice: 1,
  };

  visibleForCooperation.value = true;
}
function handleSubmitForCooperation(form, done, loading) {
  if (drawerMode.value === 'add') {
    // 新增合同逻辑
    const data = {
      ...form,
      contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
      purchaseContractType: 1
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          visibleForCooperation.value = false;
          done();
        }
      })
      .catch(() => {
        done();
      });
  } else if (drawerMode.value === 'edit') {
    // 编辑合同逻辑
    const data = {
      ...editingRow.value,
      ...form,
      contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          visible.value = false;
          done();
        }
      })
      .catch(() => {
        done();
      });
  }
}
// 收票

let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      width: 120,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },

      tip: '先上传发票可以自动填充下面部分类容',
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: supplierName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
                disabled: true,
              };
            });

            if (addFormForInvoice.value.detailEntityList) {
              addFormForInvoice.value.detailEntityList.push(...list);
            } else {
              addFormForInvoice.value.detailEntityList = list;
            }
            addFormForInvoice.value.invoiceDate = invoiceDate;

            addFormForInvoice.value.invoicePrice = invoicePrice;
            addFormForInvoice.value.invoiceNumber = invoiceNumber;
          });
      },
      uploadPreview: file => {
        return;
      },

      action: '/blade-resource/attach/upload',
    },


    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      width: 120,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      width: 180,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    // {
    //   type: 'textarea',
    //   label: '发票内容',
    //   span: 24,
    //   overHidden: true,
    //   display: true,
    //   prop: 'invoiceContent',
    // },

    {
      type: 'input',
      label: '发票总额',
      span: 12,
      overHidden: true,
      display: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      width: 120,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
    {
      label: '收票公司',
      type: 'select',
      prop: 'billingCompany',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },
      span: 24,
      search: true,
      hide: true,
      cell: false,

      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      type: 'input',
      labelPosition: 'left',
      label: '明细信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      rules: [
        {
          required: true,
          message: '请输入明细信息',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
          },
          {
            label: '数量',
            prop: 'number',
            width: 80,
            change: (a, b, c) => {
              const { row } = a;
              if (row.productTotalPrice && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '单价',
            prop: 'price',
            width: 100,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.number) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
            },
          },
          {
            label: '金额',
            prop: 'productTotalPrice',
            width: 120,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.taxRate) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.number && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width: 100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.productTotalPrice) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.totalPrice && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width: 120,
          },
          {
            label: '小计',
            prop: 'totalPrice',
            width: 120,
            change: a => {
              form.value.invoicePrice = form.value.detailEntityList
                .reduce((pre, cur) => {
                  return (pre += cur.totalPrice * 1);
                }, 0)
                .toFixed(2);
              const { row } = a;
              if (row.taxRate && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
        ],
      },
    },

    {
      type: 'textarea',
      label: '备注',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let addFormForInvoice = ref({});
let drawer = ref(false);
let currentRow = ref({});
function toReceiveTicket(row) {
  drawer.value = true;
  currentRow.value = row
  proxy.$nextTick(() => {
    // form.value.supplierName = dateFormat(new Date(), 'YYYY-MM-DD');
  });
}
let purchaseContractList = ref([]);

function submitForInvoice(form, done, loading) {
  form.createTime = null;
  form.invoiceFiles = form.invoiceFiles.map(item => item.value).join(',');
  form.sealContractId = currentRow.value.sealContractId;
  form.supplierId = currentRow.value.supplierId;
  form.purchaseContractId = currentRow.value.id;
  axios.post('/api/vt-admin/purchaseContractInvoice/save', form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
     addFormForInvoice.value.detailEntityList = []
     addFormForInvoice.value.invoiceFiles = []
     addFormForInvoice.value.invoicePrice = ''
     addFormForInvoice.value.invoiceNumber = ''
    }
  });
}

// 显示已收票列表
let invoiceListDrawer = ref(false);
let selectedContractId = ref('');
let selectedContractCode = ref('');
let selectedSupplierName = ref('');
let purchaseContractType = ref(0);

function showInvoiceList(row) {
  selectedContractId.value = row.id;
  selectedContractCode.value = row.contractCode;
  selectedSupplierName.value = row.supplierName;
  invoiceListDrawer.value = true;
  purchaseContractType.value = row.purchaseContractType;
}
</script>

<style lang="scss" scoped>
// 合计行样式优化
:deep(.el-table__footer-wrapper) {
  .el-table__footer {
    .summary-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px 0;
      font-size: 13px;
      line-height: 1.4;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
        }

        .value {
          font-weight: 600;
          color: #303133;
        }

        &.subtotal {
          .value {
            color: #409eff;
          }
        }

        &.discount {
          .discount-value {
            color: #67c23a;
          }
        }

        &.total {
          border-top: 1px solid #ebeef5;
          padding-top: 4px;
          margin-top: 2px;

          .label {
            font-weight: 600;
            color: #303133;
          }

          .total-value {
            color: #e6a23c;
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
    }
  }
}

// 表格合计行整体样式
:deep(.el-table__footer) {
  .el-table__cell {
    background-color: #fafafa;
    border-top: 2px solid #409eff;

    &:first-child {
      font-weight: 600;
      color: #303133;
    }
  }
}

// 合同价格悬浮样式
.contract-price-with-discount {
  cursor: help;
  color: #409eff;
  border-bottom: 1px dashed #409eff;

  &:hover {
    color: #66b1ff;
    border-bottom-color: #66b1ff;
  }
}
</style>
