<template>
  <basic-container style="height: 100%">
    <Title
      >采购详情
      <template #foot>
        <el-button
          type="primary"
          icon="view"
          @click="toInquiry(form)"
          v-if="!form.auditStatus && !form.auditType"
          >询价</el-button
        >
        <el-button
          type="primary"
          icon="TakeawayBox"
          @click="wareHousing(row)"
          v-if="form.orderStatus == 1"
          >入库</el-button
        >
        <el-button
          type="primary"
          icon="TakeawayBox"
          @click="outHouse(row)"
          v-if="
            (form.inStorageStatus == 1 || form.inStorageStatus == 2) && form.outStorageStatus !== 2
          "
          >出库</el-button
        >
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >

    <div class="header" style="width: 100%;">
      <div style="width: 100%;">
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-row :gutter="10" style="width: 100%;">
          <el-col :span="24">
            <el-form inline label-position="top">
              <el-form-item label="采购订单编号:">
                <el-tag effect="plain" size="large">{{ form.orderNo }}</el-tag>
              </el-form-item>
              <el-form-item label="报价名称">
                <el-tag effect="plain" size="large">{{ form.offerName || '---' }}</el-tag>
              </el-form-item>
              <el-form-item label="订单状态">
                <el-tag effect="plain" size="large" v-if="form.orderStatus == 0" type="warning"
                  >待采购</el-tag
                >
                <el-tag effect="plain" size="large" type="warning" v-else-if="form.orderStatus == 2"
                  >询价中</el-tag
                >
                <el-tag effect="plain" size="large" v-if="form.orderStatus == 1" type="warning"
                  >采购中</el-tag
                >
                <el-tag effect="plain" size="large" type="success" v-else-if="form.orderStatus == 3"
                  >采购完成</el-tag
                >
              </el-form-item>
              <el-form-item label="采购类型">
                <el-tag effect="plain" size="large" type="primary" plain>{{
                  ['订单采购', '项目采购', '储备采购'][form.purchaseType]
                }}</el-tag>
              </el-form-item>
              <el-form-item label="审核状态">
                <div v-if="form.auditStatus == 1 || form.auditStatus == 2">
                  <el-tag effect="plain" v-if="form.auditStatus == 1" size="large" type="success"
                    >审核成功</el-tag
                  >
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="form.auditReason"
                    placement="top-start"
                    v-if="form.auditStatus == 2"
                  >
                    <el-tag effect="plain" size="large" type="danger">审核失败</el-tag>
                  </el-tooltip>
                </div>
                <div v-else>
                  <el-tag effect="plain" v-if="form.auditType == 1" size="large" type="info"
                    >待采购主管审核</el-tag
                  >

                  <el-tag effect="plain" v-if="form.auditType == 2" size="large" type="info"
                    >待总经理审核</el-tag
                  >
                  <el-tag effect="plain" v-else-if="form.auditType == 0" size="large" type="info"
                    >待提交</el-tag
                  >
                </div>
              </el-form-item>
            </el-form>
          </el-col>
          <el-col :span="0">
            <el-form inline label-position="top">
              <!-- 产品统计 -->
              <el-form-item label="产品统计">
                <div style="display: flex; gap: 20px; align-items: center;">
                  <el-tag effect="plain" size="large" type="info">
                    产品总数: {{ productStats.totalCount }}
                  </el-tag>
                  <el-tag effect="plain" size="large" type="success">
                    已入库: {{ productStats.inStorageCount }}
                  </el-tag>
                  <el-tag effect="plain" size="large" type="warning">
                    已出库: {{ productStats.outStorageCount }}
                  </el-tag>
                </div>
              </el-form-item>
              
              <!-- 合同统计 -->
              <el-form-item label="合同统计">
                <div style="display: flex; gap: 20px; align-items: center;">
                  <el-tag effect="plain" size="large" type="primary">
                    合同额: ¥{{ contractStats.totalAmount.toLocaleString() }}
                  </el-tag>
                  <el-tag effect="plain" size="large" type="success">
                    已付款: ¥{{ contractStats.paidAmount.toLocaleString() }}
                  </el-tag>
                  <el-tag effect="plain" size="large" type="info">
                    已收票: ¥{{ contractStats.invoicedAmount.toLocaleString() }}
                  </el-tag>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- 标签页导航栏 -->
    <div class="tab-navigation">
      <div class="nav-tabs">
        <div 
          class="nav-tab" 
          :class="{ active: activeTab === 'baseInfo' }"
          @click="scrollToModule('baseInfo')"
        >
          基本信息
        </div>
        <div 
          class="nav-tab" 
          v-for="item in tabArray" 
          :key="item.name"
          :class="{ active: activeTab === item.name }"
          @click="scrollToModule(item.name)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>

    <!-- 垂直滚动的卡片式布局 -->
    <div class="scroll-container" ref="scrollContainer" @scroll="handleScroll">
      <!-- 基本信息卡片 -->
      <div class="module-card" :id="'module-baseInfo'" ref="baseInfoRef">
        <!-- <div class="card-header">
          <h3 class="card-title">基本信息</h3>
        </div> -->
        <div class="card-content">
          <BaseInfo :form="form" v-loading="loading" :isEdit="isEdit"></BaseInfo>
        </div>
      </div>

      <!-- 其他模块卡片 -->
      <div 
        class="module-card" 
        v-for="item in tabArray" 
        :key="item.name"
        :id="'module-' + item.name"
      >
        <div class="card-header">
          <h3 class="card-title">{{ item.label }}</h3>
        </div>
        <div class="card-content">
          <component
            :userId="form.businessPerson"
            :info="{
              type: 3,
              logicId: form.id,
              customerId: form.customerId,
            }"
            :is="item.component"
            :supplierList="form.supplier"
            @update="getDetail"
            :orderId="route.query.id"
          ></component>
        </div>
      </div>
    </div>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import BaseInfo from '../detail/baseInfo.vue';
// 跟进
import Follow from '@/views/CRM/follow/allFollow.vue';
// 商品信息
import productInfo from '../detail/productInfo.vue';
// 合同信息
import contractInfo from '../contract.vue';
// 入库记录
import wareHouseHistory from '../../warehouse/inhouse.vue';
// 出库记录
import outHouseHistory from '../../warehouse/outhouse.vue';
// 收票记录
import invoiceInfo from '@/views/Finance/ticket/ticket.vue';
// 付款记录
import paymentInfo from '@/views/Finance/payment/goodsHistory.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);

// 当前激活的标签页
let activeTab = ref('baseInfo');
// 滚动容器引用
let scrollContainer = ref(null);

// 产品统计数据
const productStats = ref({
  totalCount: 0,
  inStorageCount: 0,
  outStorageCount: 0
});

// 合同统计数据
const contractStats = ref({
  totalAmount: 0,
  paidAmount: 0,
  invoicedAmount: 0
});
onMounted(() => {
  getDetail();
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});

const tabArray = ref([
  {
    label: '商品信息',
    name: 'product',
    component: shallowRef(productInfo),
  },
  {
    label: '采购合同',
    name: 'contract',
    component: shallowRef(contractInfo),
  },
  {
    label: '入库记录',
    name: 'wareHouse',
    component: shallowRef(wareHouseHistory),
  },
  {
    label: '出库记录',
    name: 'outHouse',
    component: shallowRef(outHouseHistory),
  },
  // 收票记录
  {
    label: '收票记录',
    name: 'invoiceInfo',
    component: shallowRef(invoiceInfo),
  },
  // 付款记录
  {
    label: '付款记录',
    name: 'paymentInfo',
    component: shallowRef(paymentInfo),
  },

  // {
  //   label: '跟进',
  //   name: 'follow',
  //   component: shallowRef(Follow),
  // },
]);
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };

  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  form.supplier.sort((a, b) => {
    // 如果 a 的 supplierName 是 "暂无供应商"，将其排在前面
    if (a.supplierName === '暂无供应商') {
      return -1;
    }

    // 如果 b 的 supplierName 是 "暂无供应商"，将其排在前面
    if (b.supplierName === '暂无供应商') {
      return 1;
    }

    // 如果都不是 "暂无供应商"，按原始顺序排列
    return 0;
  });
  return form;
}
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseOrder/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;

      form.value = formatData(res.data.data);
      
      // 计算统计数据
      calculateStats();
    });
}

// 计算统计数据
function calculateStats() {
  // 计算产品统计
  let totalCount = 0;
  let totalInStorage = 0;
  let totalOutStorage = 0;
  
  if (form.value.supplier && Array.isArray(form.value.supplier)) {
    form.value.supplier.forEach(supplier => {
      if (supplier.products && Array.isArray(supplier.products)) {
        supplier.products.forEach(product => {
          totalCount += Number(product.number) || 0;
          totalInStorage += Number(product.arriveNumber) || 0;
          totalOutStorage += Number(product.outNumber) || 0;
        });
      }
    });
  }
  
  productStats.value = {
    totalCount,
    inStorageCount: totalInStorage,
    outStorageCount: totalOutStorage
  };
  
  // 计算合同统计 - 需要调用合同相关接口获取数据
  calculateContractStats();
}

// 计算合同统计数据
function calculateContractStats() {
  // 获取合同信息
  axios.get('/api/vt-admin/purchaseContract/page', {
    params: {
      orderId: route.query.id,
      current: 1,
      size: 1000
    }
  }).then(res => {
    const contracts = res.data.data.records || [];
    let totalAmount = 0;
    let paidAmount = 0;
    let invoicedAmount = 0;
    
    contracts.forEach(contract => {
      totalAmount += Number(contract.contractPrice) || 0;
      paidAmount += Number(contract.paymentPrice) || 0;
      invoicedAmount += Number(contract.hasInvoice) || 0;
    });
    
    contractStats.value = {
      totalAmount,
      paidAmount,
      invoicedAmount
    };
  }).catch(err => {
    console.error('获取合同统计数据失败:', err);
    // 如果接口失败，设置默认值
    contractStats.value = {
      totalAmount: 0,
      paidAmount: 0,
      invoicedAmount: 0
    };
  });
}
onMounted(() => {
  getDetail();
});

let moreInfoDetail = ref(false);
function loadMore() {
  moreInfoDetail.value = true;
}

// 滚动到指定模块
function scrollToModule(moduleName) {
  activeTab.value = moduleName;
  const element = document.getElementById('module-' + moduleName);
  if (element && scrollContainer.value) {
    const containerTop = scrollContainer.value.offsetTop;
    const elementTop = element.offsetTop;
    scrollContainer.value.scrollTo({
      top: elementTop - containerTop - 20, // 减去20px作为间距
      behavior: 'smooth'
    });
  }
}

// 处理滚动事件，自动高亮当前模块标签
function handleScroll() {
  if (!scrollContainer.value) return;
  
  const scrollTop = scrollContainer.value.scrollTop;
  const containerTop = scrollContainer.value.offsetTop;
  
  // 获取所有模块元素
  const modules = ['baseInfo', ...tabArray.value.map(item => item.name)];
  
  for (let i = modules.length - 1; i >= 0; i--) {
    const element = document.getElementById('module-' + modules[i]);
    if (element) {
      const elementTop = element.offsetTop - containerTop;
      if (scrollTop >= elementTop - 100) { // 提前100px触发
        activeTab.value = modules[i];
        break;
      }
    }
  }
}
let { proxy } = getCurrentInstance();
function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '商务',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistant,
        },
        {
          label: '技术',
          prop: 'technicalPersonnel',
          component: 'wf-user-select',
          value: form.value.technicalPersonnel,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          ...res.data,
          id: route.query.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const store = useStore();
const tag = computed(() => store.getters.tag);
function wareHousing(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      orderId: form.value.id,
    },
  });
}
function outHouse(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addOuthouse',
    query: {
      orderId: form.value.id,
    },
  });
}
function toInquiry(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/inquirySheet',
    query: {
      id: row.id,
      type: 0,
    },
  });
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;

.header {
  display: flex;
  // margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}

/* 标签页导航栏样式 */
.tab-navigation {
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  padding: 0 20px;
  margin-bottom: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  // box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.nav-tabs {
  display: flex;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.nav-tabs::-webkit-scrollbar {
  display: none;
}

.nav-tab {
  padding: 12px 20px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  color: #606266;
  font-weight: 500;
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
  user-select: none;
}

.nav-tab:hover {
  color: $color-primary;
  background-color: rgba(64, 158, 255, 0.05);
}

.nav-tab.active {
  color: $color-primary;
  border-bottom-color: $color-primary;
  background-color: rgba(64, 158, 255, 0.08);
}

.nav-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, $color-primary 0%, lighten($color-primary, 20%) 100%);
  border-radius: 2px 2px 0 0;
}

/* 垂直滚动容器样式 */
.scroll-container {
  padding: 12px;
  max-height: calc(100vh - 350px);
  overflow-y: auto;
}

/* 模块卡片样式 */
.module-card {
  background: #fff;
  border-radius: 6px;
  // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.module-card:hover {
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.12);
}

.module-card:last-child {
  margin-bottom: 0;
}

/* 卡片头部样式 */
.card-header {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
}

.card-title {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
}

.card-title::before {
  content: '';
  width: 3px;
  height: 14px;
  background: $color-primary;
  margin-right: 6px;
  border-radius: 2px;
}

/* 卡片内容样式 */
.card-content {
  padding: 16px;
  min-height: 150px;
}

/* 滚动条样式优化 */
.scroll-container::-webkit-scrollbar {
  width: 6px;
}

.scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
