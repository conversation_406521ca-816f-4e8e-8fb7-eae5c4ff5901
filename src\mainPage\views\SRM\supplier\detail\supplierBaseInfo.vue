<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #contractFiles> <File :fileList="form.attachList || []"></File> </template
    ></avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
  form: Object,
  isEdit: Boolean,
});
const form1 = ref({});

watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
});
const route = useRoute();
let detailOption = ref({
  detail: true,
  emptyBtn:false,
  submitBtn: false,
  labelWidth: 150,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '供应商名称',
          prop: 'supplierName',
          rules: [
            {
              required: true,
              message: '请填写供应商名称',
            },
          ],
        },
        {
          label: '供应商地址',
          prop: 'address',
          rules: [
            {
              required: true,
              message: '请填写供应商地址',
            },
          ],
        },
        {
          type: 'select',
          label: '供应商类型',
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择供应商类型',
            },
          ],
          display: true,
          prop: 'supplierClassify',
          dicUrl: '/blade-system/dict-biz/dictionary?code=supplierClassify',
          props: {
            label: 'dictValue',
            value: 'id',
          },
        },
        {
          type: 'input',
          label: '供应商特色',
          span: 12,
          // rules: [
          //   {
          //     required: true,
          //     message: '请选择供应商级别',
          //   },
          // ],
          display: true,
          prop: 'supplierFeature',
        },
        {
          label: '统一社会信用代码',
          prop: 'unifySocialCreditCode',
          rules: [
            {
              required: true,
              message: '请填写供应商地址',
            },
          ],
        },
        {
          label: '主要供应产品',
          prop: 'mainProduct',
        },
        {
          type: 'radio',
          label: '账期',
          cascader: [],
          rules: [
            {
              required: true,
              message: '请选择账期',
            },
          ],
          span: 12,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentMethod',
          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          remote: false,
        },
        {
          label: '主要供应品牌',
          prop: 'brand',
     },
        {
          label: '是否是我们客户',
          prop: 'isMyCustomer',
          type: 'radio',
          span: 12,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          label: '供应商网址',
          prop: 'webUrl',
        },
      ],
    },
    {
      label: '联系人信息',
      prop: 'contact',
      column: [
        {
          label: '',
          prop: 'supplierConcatVOList',
          type: 'dynamic',
          span: 24,
          labelWidth: 0,
          children: {
            align: 'center',
            headerAlign: 'center',
            rowAdd: done => {
              done();
            },
            rowDel: (row, done) => {
              done();
            },
            column: [
              {
                type: 'select',
                label: '联系人标签',
               
                span: 12,
                dataType: 'string',
                display: true,
                props: {
                  label: 'dictValue',
                  value: 'id',
                },
                rules: [
                  {
                    required: true,
                    message: '请选择联系人标签',
                  },
                ],
                width: 150,
                prop: 'concatType',
                
                dicUrl: '/blade-system/dict-biz/dictionary?code=supplier_concat_type',
                remote: false,
              },
              {
                label: '姓名',
                prop: 'concatName',
                width:130,
              },
              {
                label: '手机',
                prop: 'concatPhone',
                type: 'number',
                controls: false,
                width:200,
              },
              
              {
                label: '微信',
                prop: 'wxCode',   width:150,
              },
              {
                label: '邮箱',
                prop: 'mail',
                overhidden:true,   width:250,
              },
              {
                label: '性别',
                prop: 'sex',
                type: 'select',
                width:100,
                dicData: [
                  {
                    value: 1,
                    label: '男',
                  },
                  {
                    value: 2,
                    label: '女',
                  },
                ],
              },
              {
                label: '部门',
                prop: 'dept',
                type: 'select',
                width:130,
                props: {
                  label: 'dictValue',
                  value: 'id',
                  desc: 'desc',
                },
                dicUrl: '/blade-system/dict-biz/dictionary?code=dept',
              },
              // {
              //   label: '职务',
              //   prop: 'post',
              //   type: 'select',
              //   props: {
              //     label: 'dictValue',
              //     value: 'id',
              //     desc: 'desc',
              //   },
              //   dicUrl: '/blade-system/dict-biz/dictionary?code=position',
                
              // },
              

              {
                label: '备注',
                prop: 'remark',
                type: 'input',
                span: 24,
                rows: 2,
              },
            ],
          },
        },
      ],
    },
    {
      label: '银行信息',
      column: [
        {
          label: '对公账户名称',
          prop: 'bankAccount',
          span: 8,
        },
        {
          label: '对公开户行名称',
          prop: 'bankName',
          span: 8,
        },
        {
          label: '对公账号',
          prop: 'bankAccountNumber',
          span: 8,
        },
        {
          label: '对私账户名称',
          prop: 'privateBankAccount',
          span: 8,
        },
        {
          label: '对私开户行名称',
          prop: 'privateBankName',
          span: 8,
        },
        {
          label: '对私账号',
          prop: 'privateBankAccountNumber',
          span: 8,
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: props.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
