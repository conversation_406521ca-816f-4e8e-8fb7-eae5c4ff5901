<template>
  <div>
    <div>
      <div
        style="
          font-size: 12px;
          margin-left: 374px;
          width: 96px;
          height: 50px;

          transform: translateY(-16px);
          border: 1px solid #ccc;
          display: flex;
          align-items: center;
          justify-content: center;
        "
      >
        <span style="white-space: nowrap">总工时:</span>
        <el-tag effect="plain" style="" size="large" type="">{{
          parseFloat(totalAllHours * 1).toFixed(2)
        }}</el-tag>
      </div>
    </div>
    <el-row>
      <el-col :span="2">
        <div style="margin-bottom: 10px">项目经理：</div>
        <el-avatar
          :class="{ active: params.humanId === props.humanInfo.technologyUserInfo?.id }"
          shape="square"
          :size="60"
          style="cursor: pointer; position: relative; overflow: visible"
          @click="handleClick(props.humanInfo.technologyUserInfo, 0)"
        >
          <el-text style="font-weight: bolder; color: #fff">{{
            props.humanInfo.technologyUserInfo?.name
          }}</el-text>
          <!-- <el-icon class="closeIcon" @click="handleDelete(item)" :size="20"
          ><CircleCloseFilled
        /></el-icon> -->
        </el-avatar>
        <!-- <el-button
        type="primary"
        circle
        icon="plus"
        @click="$refs.humanSelectRef.open()"
        plain
        style="height: 60px; width: 60px"
      ></el-button> -->
      </el-col>
      <el-col :span="6">
        <div style="margin-bottom: 10px">内部员工：</div>
        <el-avatar
          :class="{ active: params.humanId === item.id }"
          v-for="item in props.humanInfo.interiorUserList"
          shape="square"
          :size="60"
          style="cursor: pointer; position: relative; overflow: visible; margin-right: 10px"
          @click="handleClick(item, 1)"
        >
          <el-text
            style="font-weight: bolder; color: #fff; overflow: hidden; white-space: nowrap"
            >{{ item.name }}</el-text
          >
          <el-icon class="closeIcon" @click="deleteInner(item)" :size="20"
            ><CircleCloseFilled
          /></el-icon>
        </el-avatar>
        <el-button
          type="primary"
          circle
          icon="plus"
          @click="$refs.userSelectRef.visible = true"
          plain
          style="height: 60px; width: 60px"
        ></el-button>
      </el-col>
      <el-col :span="15">
        <div style="margin-bottom: 10px">外部员工：</div>
        <div style="display: flex; align-items: center;gap: 10px">
          <div
            style="display: flex; align-items: center; gap: 10px"
            v-for="item in props.humanInfo.humanResourceEntities"
          >
            <div
              style="
                display: flex;
                flex-direction: column;
                align-items: center;gap: 3px;
                justify-content: flex-start;
              "
            >
              <el-avatar
                :class="{ active1: params.humanId === item.id }"
                shape="square"
                :size="61"
                style="cursor: pointer; position: relative; overflow: visible"
                @click="handleClick(item, 2)"
              >
                <el-text
                  style="
                    font-weight: bolder;
                    color: #fff;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                  :title="`${item.name}-${item.isAllSettlement == 1 ? '已结算' : '在做中'}`"
                  >{{ item.name }}</el-text
                >
                <el-icon class="closeIcon" @click.stop="handleDelete(item)" :size="20"
                  ><CircleCloseFilled
                /></el-icon>
              </el-avatar>
              <el-tag  effect="dark" :type="item.isAllSettlement == 1 ? 'info' : 'success'" size="small">{{ item.isAllSettlement == 1? '已结算' : '在做中' }}</el-tag>
            </div>
          </div>

          <el-button
            type="primary"
            circle
            icon="plus"
            @click="handleAddHumanResource"
            plain
            style="height: 60px; width: 60px"
          ></el-button>
        </div>
      </el-col>
    </el-row>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      :before-open="beforeOpen"
      ref="crud"
      @row-del="rowDelMain"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      :row-style="rowStyle"
      @selection-change="handleChange"
    >
      <template #menu="{ size, row, index }">
        <el-button
          text
          type="primary"
          icon="el-icon-edit"
          :size="size"
          v-if="!row.$cellEdit"
          @click="$refs.crud.rowCellEdit(row, index)"
          >编辑</el-button
        >
        <el-button
          text
          type="primary"
          v-if="row.isCheck == 0 && !row.$cellEdit"
          @click="$refs.crud.rowDel(row)"
          icon="el-icon-delete"
          :size="size"
          >删除</el-button
        >
      </template>
      <template #customSalary-type="{ item, value, label }">
        {{ item.workTypeName }} -- {{ item.salary }}
      </template>
      <template #index-header>
        <!-- <el-button
          type="primary"
          icon="plus"
          circle
          v-if="actualPriceData.type == 0"
          @click="params?.humanId && $refs.crud.rowCellAdd()"
        ></el-button> -->
      </template>
      <template #index="{ row, index }">
        {{ index + 1 }}
      </template>
      <template #planName-form="{ row }">
        <el-input
          v-model="row.planName"
          @clear="row.planId = ''"
          clearable
          placeholder="请选择计划"
          @click="selectPlan(row)"
        ></el-input>
      </template>
      <template #menu-left>
        <div style="display: flex; align-items: center; justify-content: flex-start">
          <div v-if="type == 2">
            <el-form inline v-if="humanInfo.type == 0">
              <el-form-item style="margin: 0; margin-right: 20px" label="姓名"
                ><el-tag effect="plain" type="">{{ humanInfo.name }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="性别"
                ><el-tag effect="plain" type="">{{
                  humanInfo.sex == 1 ? '男' : '女'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="年龄"
                ><el-tag effect="plain" type="">{{ humanInfo.age || '---' }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="联系电话"
                ><el-tag effect="plain" type="">{{
                  humanInfo.phone || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="工种"
                ><el-tag effect="plain" type="">{{
                  humanInfo.salaryVOS[0]?.workTypeName || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="类型">
                <el-tag
                  :type="actualPriceData.type == 0 ? 'danger' : 'success'"
                  effect="dark"
                  size="small"
                  >{{ actualPriceData.type == 0 ? '外包' : '点工' }}</el-tag
                >
              </el-form-item>

              <el-form-item style="margin: 0; margin-right: 20px" label="工价"
                ><el-tag effect="plain" type="">{{
                  humanInfo.salaryVOS[0]?.salary || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="工时"
                ><el-tag effect="plain" type="">{{
                  isNaN(parseFloat(statisticData?.totalHours))
                    ? '0'
                    : parseFloat(statisticData?.totalHours)
                }}</el-tag></el-form-item
              >
              <el-form-item
                v-if="type == 2"
                style="margin: 0; margin-right: 20px"
                label="应付金额："
              >
                <div style="display: flex; align-items: center; justify-content: space-between">
                  <el-tag effect="plain" type=""
                    >￥{{ parseFloat(actualPriceData?.planAmount).toFixed(2) }}</el-tag
                  >
                  <el-popover
                    placement="right"
                    v-if="actualPriceData.type == 0"
                    ref="pop_price"
                    :width="160"
                    trigger="click"
                  >
                    <template #reference>
                      <span><el-button type="primary" icon="edit" text></el-button></span>
                    </template>
                    <template #default>
                      <div
                        style="
                          display: flex;
                          flex-direction: column;
                          align-items: flex-end;
                          gap: 5px;
                        "
                      >
                        <el-input
                          v-model="humanInfo.planAmount"
                          type="number"
                          placeholder="请输入应付费用"
                        ></el-input>
                        <div style="display: flex; align-items: center; gap: 10px">
                          <el-button
                            @click="updatePrice"
                            size="small"
                            icon="el-icon-chec"
                            style="border-radius: 3px"
                            type="primary"
                            >确认</el-button
                          >
                        </div>
                      </div>
                    </template>
                  </el-popover>
                </div>
              </el-form-item>
              <el-form-item
                v-if="type == 2"
                style="margin: 0; margin-right: 20px"
                label="实付金额："
                ><div style="display: flex; align-items: center; justify-content: space-between">
                  <el-tag effect="plain" type="">￥{{ actualPriceData.actualPrice }}</el-tag
                  ><el-icon
                    size="25"
                    style="cursor: pointer"
                    title="查看付款记录"
                    color="var(--el-color-primary)"
                    @click="handleView()"
                    ><List
                  /></el-icon>
                </div>
                <el-button type="primary" icon="plus" :loading="loading" @click="handleApply" size="small"
                  >付款申请</el-button
                ><el-button type="primary" plain icon="download" @click="exportExcel" size="small"
                  >导出</el-button
                ></el-form-item
              >
            </el-form>
            <el-form inline v-else>
              <el-form-item style="margin: 0; margin-right: 20px" label="公司名称"
                ><el-tag effect="plain" type="">{{ humanInfo.name || '---' }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="公司地址"
                ><el-tag effect="plain" type="">{{
                  humanInfo.address || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="联系人"
                ><el-tag effect="plain" type="">{{
                  humanInfo.contact || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="联系电话"
                ><el-tag effect="plain" type="">{{
                  humanInfo.phone || '---'
                }}</el-tag></el-form-item
              >
              <el-form-item style="margin: 0; margin-right: 20px" label="类型">
                <el-tag
                  :type="actualPriceData.type == 0 ? 'danger' : 'success'"
                  effect="dark"
                  size="small"
                  >{{ actualPriceData.type == 0 ? '外包' : '点工' }}</el-tag
                >
              </el-form-item>
              <el-form-item style="margin: 0; margin-right: 20px" label="工时"
                ><el-tag effect="plain" type="">{{
                  isNaN(parseFloat(statisticData?.totalHours))
                    ? '0'
                    : parseFloat(statisticData?.totalHours)
                }}</el-tag></el-form-item
              >
              <el-form-item
                v-if="type == 2"
                style="margin: 0; margin-right: 20px"
                label="应付金额："
              >
                <div style="display: flex; align-items: center; justify-content: space-between">
                  <el-tag effect="plain" type=""
                    >￥{{ parseFloat(actualPriceData?.planAmount).toFixed(2) }}</el-tag
                  >
                  <el-popover
                    v-if="actualPriceData.type == 0"
                    placement="right"
                    ref="pop_price"
                    :width="160"
                    trigger="click"
                  >
                    <template #reference>
                      <span><el-button type="primary" icon="edit" text></el-button></span>
                    </template>
                    <template #default>
                      <div
                        style="
                          display: flex;
                          flex-direction: column;
                          align-items: flex-end;
                          gap: 5px;
                        "
                      >
                        <el-input
                          v-model="humanInfo.planAmount"
                          type="number"
                          placeholder="请输入应付费用"
                        ></el-input>
                        <div style="display: flex; align-items: center; gap: 10px">
                          <el-button
                            @click="updatePrice"
                            size="small"
                            icon="el-icon-chec"
                            style="border-radius: 3px"
                            type="primary"
                            >确认</el-button
                          >
                        </div>
                      </div>
                    </template>
                  </el-popover>
                </div>
              </el-form-item>
              <el-form-item
                v-if="type == 2"
                style="margin: 0; margin-right: 20px"
                label="实付金额："
                ><div style="display: flex; align-items: center; justify-content: space-between">
                  <el-tag effect="plain" type="">￥{{ actualPriceData.actualPrice }}</el-tag
                  ><el-icon
                    size="25"
                    style="cursor: pointer"
                    title="查看付款记录"
                    color="var(--el-color-primary)"
                    @click="handleView()"
                    ><List
                  /></el-icon>
                </div>
                <el-button type="primary" icon="plus" :loading="loading" @click="handleApply" size="small"
                  >付款申请</el-button
                >
                <el-button type="primary" plain icon="download" @click="exportExcel" size="small"
                  >导出</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <el-form ref="form" :model="form" inline>
            <el-form-item v-if="type !== 2" style="margin: 0; margin-right: 20px" label="工时"
              ><el-tag effect="plain" type="">{{
                isNaN(parseFloat(statisticData?.totalHours))
                  ? '0'
                  : parseFloat(statisticData?.totalHours)
              }}</el-tag></el-form-item
            >
            <!-- <el-form-item style="margin: 0; margin-right: 20px" label-width="90px" label="项目总工时"
          ><el-tag effect='plain' type="">{{ parseFloat(totalAllHours * 1) }}</el-tag></el-form-item
        > -->
          </el-form>
        </div>
      </template>
      <template #isCheck="{ row }">
        <el-tag
          effect="plain"
          :type="
            row.isCheck == 0 ? 'info' : row.isCheck == 1 || row.isCheck == 2 ? 'warning' : 'success'
          "
          >{{ row.$isCheck }}</el-tag
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <wfUserSelect
      ref="userSelectRef"
      :user-url="'/blade-system/search/user?functionKeys=internalStaff'"
      :show-tree="false"
      @onConfirm="addInterior"
    ></wfUserSelect>
    <el-dialog
      class="avue-dialog avue-dialog--top"
      title="计划选择"
      width="80%"
      v-model="planDialogVisible"
    >
      <projectPlan check ref="planRef"></projectPlan>
      <div class="avue-dialog__footer">
        <el-button @click="planDialogVisible = false">取 消</el-button>
        <el-button @click="handlePlanSelect" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <el-drawer title="申请付款" @close="cancel" v-model="applyDrawer" size="80%">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card shadow="never">
            <avue-form
              ref="addFormRef"
              :option="applyOptionFormFormOther"
              v-model="addForm"
            ></avue-form>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <avue-crud
              :option="applyOptionFortable"
              :summary-method="summaryMethod"
              @row-del="rowDel"
              :data="selectList"
            ></avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <el-button icon="close" @click="cancel">取消</el-button>
        <el-button type="primary" icon="check" :loading="submitLoading" @click="submit"
          >提交</el-button
        >
      </template>
    </el-drawer>
    <payRecord
      ref="payRecordRef"
      :projectName="projectName"
      :humanName="humanInfo.name"
    ></payRecord>
    <el-dialog
      class="avue-dialog avue-dialog--top"
      :title="`添加${addTypeText}员工`"
      v-model="addHumanDialogVisible"
      width="30%"
    >
      <avue-form
        @submit="submitAddHumanForm"
        v-model="addHumanForm"
        ref="addHumanFormRef"
        :option="addHumanOption"
      >
        <template #name>
          <humanSelect @select="handleSelect" ref="humanSelectRef"></humanSelect>
        </template>
      </avue-form>

      <span class="avue-dialog__footer">
        <el-button icon="close" @click="addHumanDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          :loading="addHumanLoading"
          icon="check"
          @click="$refs.addHumanFormRef.submit()"
          >确认</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import humanSelect from '../compoents/humanSelect.vue';
import projectPlan from './projectPlan.vue';
import { downloadXls } from '@/utils/download.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import { ElMessage } from 'element-plus';
import payRecord from './payRecord.vue';
let option = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  addRowBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  cellBtn: true,
  searchSpan: 4,
  labelWidth: 120,
  reserveSelection: true,
  menuWidth: 200,
  // showSummary: true,
  selectable: row => {
    return row.isCheck == 0;
  },
  border: true,
  column: {
    index: {
      type: 'input',
      width: 80,
      label: '序号',
    },
    userName: {
      label: '工人/团队姓名',
      hide: true,
      width: 120,
      overHidden: true,
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 120,
      //   addDisplay: false,
      cell: true,
      type: 'input',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'input',
      width: 200,
      cell: true,
      // hide: true,
    },
    type: {
      label: '工人类型',
      width: 120,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
      hide: true,
    },
    startTime: {
      label: '工作开始时间',

      type: 'date',
      width: 185,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 185,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    planName: {
      label: '关联计划',
      width: 150,
      type: 'input',
      clearable: false,
      overHidden: true,
      click: () => {
        planDialogVisible.value = true;
      },
      span: 24,
      cell: true,
    },
    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      row: 2,
    },
    isCheck: {
      label: '结算状态',
      type: 'select',
      width: 120,
      dicData: [
        {
          label: '未结算',
          value: 0,
        },
        {
          label: '审核中',
          value: 1,
        },
        {
          label: '待付款',
          value: 2,
        },
        {
          label: '已付款',
          value: 3,
        },
      ],
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 1000,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  humanInfo: {
    type: Object,
    default: () => {
      return {
        humanResourceEntities: [],
      };
    },
  },
  projectName: {
    type: String,
    default: '',
  },
});
const addUrl = '/api/vt-admin/projectHours/save';
const delUrl = '/api/vt-admin/projectHours/remove?ids=';
const updateUrl = '/api/vt-admin/projectHours/update';
const tableUrl = '/api/vt-admin/projectHours/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
onMounted(() => {
  getAllHours();
  params.value = {
    humanId: props.humanInfo.humanResourceEntities[0]?.id,
    userName: props.humanInfo.humanResourceEntities[0]?.name,
  };
  option.value.column.customSalary.dicData = props.humanInfo.humanResourceEntities[0]?.salaryVOS;

  onLoad();

  if (props.humanInfo.humanResourceEntities[0]) {
    handleClick(props.humanInfo.humanResourceEntities[0], 2);
  } else {
    getStatisicData();
  }
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          hours: parseFloat(item.hours),
        };
      });
      page.value.total = res.data.data.total;
    });
}
watch(
  () => route.query,
  val => {
    if (route.query.id && route.fullPath.indexOf('/Project/detail/detail') > -1) {
      params.value = {
        humanId: props.humanInfo.humanResourceEntities[0]?.id,
        userName: props.humanInfo.humanResourceEntities[0]?.name,
      };
      form.value.customSalary = props.humanInfo.humanResourceEntities[0]?.salaryVOS[0]?.salary;
      onLoad();
      getStatisicData();
      getAllHours();
    }
  },
  { deep: true }
);
// watch(() => props.humanInfo,() => {

//   params.value = {
//     humanId: props.humanInfo.humanResourceEntities[0]?.id,
//     userName: props.humanInfo.humanResourceEntities[0]?.name,
//   };
//   onLoad();
// },{
//   deep:true
// })
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    humanId: params.value.humanId,
    projectId: route.query.id,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');
        onLoad();
        getStatisicData();
        getAllHours();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  console.log(row);
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        getStatisicData();
        done();
        getActualPrice();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(row, index) {
  selectList.value.splice(index, 1);
  setTotal();
}
function rowDelMain(row, index) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + row.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchChange(params, done) {
  onLoad();
  done();
}
const emits = defineEmits(['success']);
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.customSalary = form.value.humanSalaryVOs[0]?.salary;
  }
  if (type == 'add') {
    form.value.humanId = params.value.humanId;
    form.value.userName = params.value.userName;
  }
  console.log(form.value);
  done();
}

function handleClick(item, t) {
  type.value = t;
  params.value.humanId = item.id;
  params.value.userName = item.name;
  console.log(item);

  form.value.customSalary = item.salaryVOS && item.salaryVOS[0]?.salary;
  if (type.value == 0 || type.value == 1) {
    option.value.column.customSalary.hide = true;
  } else {
    option.value.column.customSalary.hide = false;
  }
  onLoad();
  getStatisicData();
  if (type.value == 2) {
    getActualPrice();
  }

  // 获取用户信息
  humanInfo.value = {
    ...item,
    type: item.type == 1 || item.userType == 1 ? 1 : 0,
  };
}

let planDialogVisible = ref(false);
function handlePlanSelect() {
  currentRow.value.planId = proxy.$refs.planRef.selectRowInfo.id;
  currentRow.value.planName = proxy.$refs.planRef.selectRowInfo.planName;
  currentRow.value.workContent = proxy.$refs.planRef.selectRowInfo.planContent;
  planDialogVisible.value = false;
  proxy.$refs.planRef.selectRow = null;
}
function handleDelete(row) {
  if (tableData.value.length > 0) {
    return proxy.$message.warning('请先删除工作记录');
  }
  proxy
    .$confirm('确定将该人员删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(res => {
      const data = {
        projectId: route.query.id,
        humanId: row.id,
      };
      axios.post('/api/vt-admin/project/deleteHumanIds', data).then(res => {
        proxy.$message.success(res.data.msg);
        emits('success');
      });
    });
}
let currentRow = ref({});
function selectPlan(row) {
  currentRow.value = row;
  planDialogVisible.value = true;
}
function addInterior(id) {
  if (props.humanInfo.interiorUsers && props.humanInfo.interiorUsers.indexOf(id) > -1) {
    return proxy.$message.warning('已添加此人');
  }

  axios
    .post('/api/vt-admin/project/updateInteriorUsers', {
      id: route.query.id,
      interiorUsers: props.humanInfo.interiorUsers ? props.humanInfo.interiorUsers + ',' + id : id,
    })
    .then(res => {
      proxy.$message.success(res.data.msg);
      emits('success');
    });
}
function deleteInner(row) {
  if (tableData.value.length > 0) {
    return proxy.$message.warning('请先删除工作记录');
  }
  proxy
    .$confirm('确定将该人员删除?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(res => {
      const data = {
        id: route.query.id,
        interiorUsers: props.humanInfo.interiorUsers
          .split(',')
          .filter(item => item != row.id)
          .join(','),
      };
      axios.post('/api/vt-admin/project/updateInteriorUsers', data).then(res => {
        proxy.$message.success(res.data.msg);
        emits('success');
      });
    });
}

function summaryMethod({ columns, data }) {
  console.log(columns);
  let sum = [];
  columns.forEach((item, index) => {
    if (index == 2) {
      const totalPrice = data.reduce((prev, curr) => {
        prev += curr.hours * curr.customSalary;
        return prev;
      }, 0);
      sum[index] = `合计:${totalPrice}`;
    } else if (index == 1) {
      const hours = data.reduce((prev, curr) => {
        prev += curr.hours * 1;
        return prev;
      }, 0);
      sum[index] = `合计:${hours.toFixed(2)}`;
    } else {
      sum[index] = '';
    }
  });
  return sum;
}
let statisticData = ref({});
let totalAllHours = ref(0);
let actualPriceData = ref(0);
function getStatisicData() {
  axios
    .get('/api/vt-admin/projectHours/pageStatistics', {
      params: {
        projectId: route.query.id,
        humanId: params.value.humanId,
      },
    })
    .then(res => {
      console.log(res.data.data);
      statisticData.value = res.data.data;
    });
}
function getActualPrice() {
  axios
    .get('/api/vt-admin/projectHumanSalary/actualPay', {
      params: {
        projectId: route.query.id,
        humanId: params.value.humanId,
      },
    })
    .then(res => {
      actualPriceData.value = res.data.data;
    });
}
function getAllHours(params) {
  axios
    .get('/api/vt-admin/projectHours/pageStatistics', {
      params: {
        projectId: route.query.id,
      },
    })
    .then(res => {
      console.log(res.data.data);
      totalAllHours.value = res.data.data.totalHours;
    });
}
let humanInfo = ref({});
function getOutUserInfo() {
  axios.get('/api/vt-admin/humanResource/detail?id=' + params.value.humanId).then(res => {
    humanInfo.value = res.data.data;
  });
}
let type = ref(0); // 0 项目经理 1 内部员工 2 外部员工

let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(item => {
    return {
      ...item,
    };
  });
}

let applyDrawer = ref(false);
let applyOptionFortable = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  addRowBtn: false,
  editBtn: false,
  delBtn: true,
  header: false,
  calcHeight: 150,
  searchMenuSpan: 4,
  cellBtn: true,
  searchSpan: 4,
  labelWidth: 120,
  menu: true,
  size: 'small',
  index: true,
  menuWidth: 100,
  showSummary: true,

  border: true,
  column: {
    userName: {
      label: '工人/团队姓名',
      hide: true,
      width: 120,
      overHidden: true,
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 120,
      //   addDisplay: false,
      cell: true,
      type: 'number',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'input',
      width: 130,
      cell: true,
      hide: true,

      props: {
        value: 'salary',
        label: 'salary',
      },
    },
    totalPrice: {
      label: '金额',
      formatter: row => {
        return row.hours * row.customSalary;
      },
    },
    type: {
      label: '工人类型',
      width: 120,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
      hide: true,
    },
    startTime: {
      label: '工作开始时间',

      type: 'date',
      width: 130,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 130,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    planName: {
      label: '关联计划',
      width: 150,
      type: 'input',
      clearable: false,
      overHidden: true,
      click: () => {
        planDialogVisible.value = true;
      },
      span: 24,
      cell: true,
    },
    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      // width:160,
      row: 2,
    },
  },
});
let applyOptionFormFormOther = ref({
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label:'总计',
           prop: 'totalSalary',
          span: 24,
          type: 'number',
          disabled: true,
        },
        {
          label:'变更金额',
          prop: 'changeSalary',
          span: 24,
          type: 'number',
        
          blur:setTotal

        },
        {
          label: '付款金额',
          prop: 'salary',
          // disabled: true,
          span: 24,
          type: 'number',
          placeholder: ' ',
          blur:() => {
            addForm.value.totalSalary = addForm.value.salary
          }
        },
        
        {
          label: '总工时',
          prop: 'hours',
          span: 24,
        },
        {
          label: '备注',
          prop: 'workContent',
          type: 'textarea',
          span: 24,
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '工人信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          span: 24,
          readonly: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
          readonly: true,
          span: 24,
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          readonly: true,
          span: 24,
          // search: true,
          display: true,
          disabled: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          span: 24,
          readonly: true,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          disabled: true,
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '开户行',
          prop: 'openBank',
          span: 24,
          readonly: true,
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
          span: 24,
          readonly: true,
        },
      ],
    },
  ],
});
let applyOptionFormFormCompany = ref({
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '付款金额',
          prop: 'salary',
          span: 24,
        },
        {
          label: '总工时',
          prop: 'hours',
          span: 24,
        },
        {
          label: '备注',
          prop: 'workContent',
          type: 'textarea',
          span: 24,
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '公司信息',
      column: [
        {
          label: '公司名称',
          prop: 'name',
          span: 24,
          readonly: true,
        },
        {
          label: '联系人',
          prop: 'contact',
          readonly: true,
          span: 24,
        },
        {
          label: '联系电话',
          prop: 'phone',
          readonly: true,
          span: 24,
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          readonly: true,
          span: 24,
          // search: true,
          display: true,
          disabled: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          span: 24,
          readonly: true,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          disabled: true,
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '开户行',
          prop: 'openBank',
          span: 24,
          readonly: true,
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
          span: 24,
          readonly: true,
        },
      ],
    },
  ],
});
let addForm = ref({});
function handleApply() {
  selectList.value = tableData.value
    .filter(item => !item.isCheck)
    .map(item => {
      return {
        ...item,
      };
    });
  applyDrawer.value = true;
  const humanId = params.value.humanId;
  axios.get('/api/vt-admin/humanResource/detail?id=' + humanId).then(res => {
    const { name, phone, bankNumber, invoiceType, taxRate, openBank, contact } = res.data.data;
    const hours = selectList.value
      .reduce((pre, cur) => {
        pre += cur.hours * 1;
        return pre;
      }, 0)
      ?.toFixed(2);
    const totalSalary = selectList.value
      .reduce((pre, cur) => {
        pre += cur.hours * 1 * (cur.customSalary * 1);
        return pre;
      }, 0)
      ?.toFixed(2);
    setTimeout(() => {
      const changeSalaryRef = proxy.findObject(applyOptionFormFormOther.value.group[0].column,'changeSalary')
      const totalSalaryRef = proxy.findObject(applyOptionFormFormOther.value.group[0].column,'totalSalary')
      const salaryRef = proxy.findObject(applyOptionFormFormOther.value.group[0].column,'salary')
      changeSalaryRef.display = actualPriceData.value.type == 1
      totalSalaryRef.display = actualPriceData.value.type == 1
      salaryRef.disabled = actualPriceData.value.type == 1
      addForm.value = {
        name,
        phone,
        bankNumber,
        bankNumber,
        invoiceType,
        taxRate,
        openBank,
        hours,
        totalSalary,
        changeSalary: 0,
        salary:totalSalary,
        contact,
      };
    }, 200)?.toFixed(2);
  });
}
function setTotal() {
  const hours = selectList.value
    .reduce((pre, cur) => {
      pre += cur.hours * 1;
      return pre;
    }, 0)
    ?.toFixed(2);
  const salary = selectList.value
    .reduce((pre, cur) => {
      pre += cur.hours * 1 * (cur.customSalary * 1);
      return pre;
    }, 0)
    ?.toFixed(2);
    
  addForm.value = {
    ...addForm.value,
    hours,
    salary: salary * 1 +  addForm.value.changeSalary * 1,
    totalSalary: salary,
  };
}
let crud = ref(null);
let addFormRef = ref(null);
let submitLoading = ref(false);
function submit() {
  // if (selectList.value.length == 0) return ElMessage.error('暂无可结算的工时列表');
  submitLoading.value = true;
  axios
    .post('/api/vt-admin/projectHumanSalary/add', {
      ...addForm.value,
      files: addForm.value.files?.map(item => item.value).join(','),
      projectId: route.query.id,
      humanId: params.value.humanId,
      detailDTOList: selectList.value.map(item => {
        return {
          ...item,
          hoursId: item.id,
          id: null,
        };
      }),
    })
    .then(res => {
      ElMessage.success('申请成功');
      applyDrawer.value = false;
      selectList.value = [];
      submitLoading.value = false;
      addFormRef.value.resetForm();
      onLoad();
    })
    .catch(() => {
      submitLoading.value = true;
    });
}
function cancel(params) {
  applyDrawer.value = false;
  selectList.value = [];
  addFormRef.value.resetForm();
}

function rowStyle({ row }) {
  return {
    backgroundColor: row.isCheck === 3 ? 'var(--el-color-success-light-9)' : '',
  };
}
let payRecordRef = ref(null);
function handleView(row) {
  payRecordRef.value.open();
}
let pop_price = ref(null);
function updatePrice() {
  axios
    .post('/api/vt-admin/project/updateHumanIds', {
      humanId: params.value.humanId,
      projectId: route.query.id,
      planAmount: humanInfo.value.planAmount,
    })
    .then(res => {
      ElMessage.success('更新成功');
      getActualPrice();
      pop_price.value.hide();
    });
}

// 添加新员工
let addHumanDialogVisible = ref(false);
let addHumanForm = ref({});
let addHumanFormRef = ref(null);
let addTypeText = ref('');
let addHumanOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '姓名/公司名称',
      prop: 'name',
      readonly: true,
      placeholder: '请选择员工或者公司',
      suffixIcon: 'el-icon-user',
      span: 24,
      rules: [{ required: true, message: '请选择员工或者公司', trigger: 'change' }],
      click: () => {
        proxy.$refs.humanSelectRef.open();
      },
    },
    {
      label: '工人类型',
      type: 'radio',
      span: 24,
      prop: 'type',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      control: res => {
        return {
          planAmount: {
            display: res == 0,
          },
        };
      },
    },
    {
      label: '应付金额',
      prop: 'planAmount',
      span: 24,
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入应付金额',
          trigger: 'blur',
        },
      ],
    },
  ],
});

function handleAddHumanResource() {
  addHumanDialogVisible.value = true;
}
let addHumanLoading = ref(false);
function submitAddHumanForm(form, done, loading) {
  addHumanLoading.value = true;
  axios
    .post('/api/vt-admin/project/addHumanIds', {
      projectId: route.query.id,
      ...form,
    })
    .then(res => {
      proxy.$message.success(res.data.msg);
      emits('success');
      done();
      addHumanDialogVisible.value = false;
      addHumanFormRef.value.resetForm();
      addHumanLoading.value = false;
      onLoad();
    })
    .catch(err => {
      done();
      addHumanLoading.value = false;
    });
}

function handleSelect(row) {
  if (props.humanInfo.humanIds && props.humanInfo.humanIds.indexOf(row.id) > -1) {
    return proxy.$message.warning('已添加此人或此团队');
  }
  addHumanForm.value.name = row.name;
  addHumanForm.value.humanId = row.id;
  addHumanForm.value.type = row.type == 1 || (row.type == 2 && row.userType == 1) ? 0 : 1;
  form.value.userName = row.name;
  form.value.humanId = row.id;
  form.value.customSalary = row.salaryVOS[0]?.salary;
}

function exportExcel() {
  downloadXls({
    url: '/api/vt-admin/projectHours/exportList',
    method: 'post',
    params: {
      ...params.value,
      projectId: route.query.id,
    },
  });
}
</script>

<style lang="scss" scoped>
.active {
  border: 2px solid var(--el-color-primary);
}
.active1 {
  border: 3px solid var(--el-color-danger);
}
.active2 {
  border: 1px solid var(--el-color-success);
}
.closeIcon {
  right: -10px;
  top: -10px;
  position: absolute;
  color: var(--el-color-danger);
  display: none;
}
.el-avatar:hover .closeIcon {
  display: block;
}
:deep(.el-tooltip__trigger) {
  width: 100%;
}
</style>
