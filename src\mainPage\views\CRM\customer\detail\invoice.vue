<template>
  <div>
<invoiceMy v-if="props.info.type == 0 || props.info.type == 2" :customerId="props.info.customerId"></invoiceMy>
<invoiceAll v-else :customerId="props.info.customerId"></invoiceAll>
  </div>
</template>

<script setup>
import invoiceMy from '@/views/Contract/invoiceApply.vue'
import invoiceAll from '@/views/Finance/invoice/invoice.vue'
const props = defineProps(['info']);
</script>

<style lang="scss" scoped>

</style>