<template>
  <el-drawer title="详情" v-model="drawer" size="50%">
    <avue-form :option="detailOption" v-model="detailForm">
      <template #taskName>
        <el-descriptions :title="detailForm.objectName">
          <el-descriptions-item label="预约时间">{{
            (detailForm.reservationTime && detailForm.reservationTime.split(' ')[0]) || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务客户">{{
            detailForm.finalCustomer || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务联系人">{{
            detailForm.contact || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailForm.contactPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">
            {{ detailForm.distributionAddress || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单描述" :span="3">
            <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <template #planTime>
        {{ formatTimeToHourMinute(detailForm.serviceStartTime) }} -
        {{ formatTimeToHourMinute(detailForm.serviceEndTime) }}
      </template>
      <template #files>
        <File :fileList="detailForm.fileList"></File>
      </template>
      <template #completeFiles>
        <File :fileList="detailForm.completeFilesList"></File>
      </template>
      <template #engineerServiceInfo>
        <!-- 工单流程时间线 -->
        <div class="task-process-timeline">
          <el-timeline>
            <el-timeline-item
              v-for="(step, index) in detailForm.milestoneVOList"
              :key="index"
              :timestamp="step.createTime"
              :size="22"
              :hollow="!step.completed"
              placement="top"
            >
              <div class="timeline-content">
                <h5>{{ step.handleContent }}</h5>
                <p v-if="step.description">{{ step.description }}</p>
                <span v-if="step.operator" class="operator">操作人：{{ step.operator }}</span>
                <el-button
                  v-if="step.hasDetail"
                  type="primary"
                  size="small"
                  plain
                  class="detail-btn"
                  @click="showStepDetail(step)"
                >
                  查看详情
                </el-button>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </template>
      <template #completeReport>
        <div class="complete-report-container">
          <!-- 多人工程师完工报告 -->
          <div
            v-if="
              detailForm.sealContractObjectResultVOList &&
              detailForm.sealContractObjectResultVOList.length > 0
            "
            class="engineer-complete-report"
          >
            <el-collapse v-model="activeEngineerPanels" accordion>
              <el-collapse-item
                v-for="(engineer, index) in detailForm.sealContractObjectResultVOList"
                :key="index"
                :title="`${engineer.handleName || '工程师' + (index + 1)} - 完工报告`"
                :name="index.toString()"
                class="engineer-panel"
              >
                <template #title>
                  <div class="engineer-panel-title">
                    <el-icon class="panel-icon"><User /></el-icon>
                    <span class="engineer-name">{{
                      engineer.handleName || '工程师' + (index + 1)
                    }}</span>
                  </div>
                </template>

                <div class="engineer-report-content">
                  <!-- 签到信息折叠面板 -->
                  <el-collapse
                    v-model="activeSigninPanels[index]"
                    v-if="detailForm.isNeedSign == 1"
                    class="info-collapse"
                  >
                    <el-collapse-item title="签到信息" name="signin" class="info-panel">
                      <template #title>
                        <div class="info-panel-title">
                          <el-icon class="info-icon"><MapLocation /></el-icon>
                          <span>签到信息</span>
                          <el-tag v-if="engineer.isSign == 1" type="success" size="small"
                            >已签到</el-tag
                          >
                          <el-tag v-else type="info" size="small">未签到</el-tag>
                        </div>
                      </template>

                      <div class="signin-info">
                        <el-descriptions :column="1" border size="small">
                          <el-descriptions-item label="签到时间">
                            {{ engineer.signTime || '--' }}
                          </el-descriptions-item>
                          <el-descriptions-item label="签到地址">
                            {{ engineer.signAddress || '--' }}
                          </el-descriptions-item>
                          <!-- <el-descriptions-item label="GPS坐标" :span="2">
                            {{ engineer.signCoordinates || '--' }}
                          </el-descriptions-item> -->
                        </el-descriptions>

                        <!-- 签到照片 -->
                        <div
                          v-if="engineer.signPhotoUrl && engineer.signPhotoUrl.length > 0"
                          class="photo-section"
                        >
                          <h5>签到现场照片</h5>
                          <div class="photo-grid">
                            <el-image
                              v-for="(photo, photoIndex) in engineer.signPhotoUrl.split(',')"
                              :key="photoIndex"
                              :src="photo"
                              preview-teleported
                              :preview-src-list="engineer.signPhotoUrl.split(',')"
                              class="signin-photo"
                              fit="cover"
                            />
                          </div>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>

                  <!-- 完成信息折叠面板 -->
                  <el-collapse v-model="activeCompletePanels[index]" class="info-collapse">
                    <el-collapse-item title="完成信息" name="complete" class="info-panel">
                      <template #title>
                        <div class="info-panel-title">
                          <el-icon class="info-icon"><CircleCheck /></el-icon>
                          <span>完成信息</span>
                          <el-tag
                            v-if="engineer.objectStatus == 2 || engineer.objectStatus == 4"
                            type="success"
                            size="small"
                            >已完成</el-tag
                          >
                          <el-tag v-else type="warning" size="small">进行中</el-tag>
                        </div>
                      </template>

                      <div class="complete-info">
                        <el-descriptions :column="2" border size="small">
                          <el-descriptions-item label="服务开始时间">
                            {{ engineer.serviceStartTime || '--' }}
                          </el-descriptions-item>
                          <el-descriptions-item label="服务结束时间">
                            {{ engineer.serviceEndTime || '--' }}
                          </el-descriptions-item>
                          <el-descriptions-item label="实际工时">
                            {{ engineer.useTimes ? engineer.useTimes + '小时' : '--' }}
                          </el-descriptions-item>
                        </el-descriptions>

                        <!-- 现场图片 -->
                        <div
                          v-if="
                            engineer.workOrderPhotoList && engineer.workOrderPhotoList.length > 0
                          "
                          class="photo-section"
                        >
                          <h5>现场图</h5>
                          <div class="photo-grid">
                            <el-image
                              v-for="(photo, photoIndex) in engineer.workOrderPhotoList"
                              :key="photoIndex"
                              :src="photo.link"
                              preview-teleported
                              :preview-src-list="engineer.workOrderPhotoList.map(p => p.link)"
                              class="work-photo"
                              fit="cover"
                            />
                          </div>
                        </div>

                        <!-- 处理结果图片 -->
                        <div
                          v-if="
                            engineer.handleResultPhotoList &&
                            engineer.handleResultPhotoList.length > 0
                          "
                          class="photo-section"
                        >
                          <h5>处理结果图</h5>
                          <div class="photo-grid">
                            <el-image
                              v-for="(photo, photoIndex) in engineer.handleResultPhotoList"
                              :key="photoIndex"
                              :src="photo.link"
                              preview-teleported
                              :preview-src-list="engineer.handleResultPhotoList.map(p => p.link)"
                              class="result-photo"
                              fit="cover"
                            />
                          </div>
                        </div>
                         <!-- 完成附件 -->
                        <div v-if="engineer.completeFileList" class="text-section">
                          <h5>完成附件</h5>
                          <div class="content-box">
                            <File :fileList="engineer.completeFileList"></File>
                          </div>
                        </div>
                        <!-- 服务复盘 -->
                        <div v-if="engineer.serviceReorder" class="text-section">
                          <h5>服务复盘</h5>
                          <div class="content-box">
                            <pre>{{ engineer.serviceReorder }}</pre>
                          </div>
                        </div>

                        <!-- 完成备注 -->
                        <div v-if="engineer.completeRemark" class="text-section">
                          <h5>完成备注</h5>
                          <div class="content-box">
                            {{ engineer.completeRemark }}
                          </div>
                        </div>
                      </div>
                    </el-collapse-item>
                  </el-collapse>
                </div>
              </el-collapse-item>
            </el-collapse>
          </div>

          <!-- 无工程师数据时的提示 -->
          <div v-else class="no-data-tip">
            <el-empty description="暂无完工报告数据" />
          </div>
        </div>
      </template>
    </avue-form>
    <template #footer>
      <div style="border-top: 1px solid #eee; padding-top: 5px">
        <!-- <el-button type="primary" icon="edit" @click="submit">保存工单</el-button> -->
        <!-- <el-button type="primary" icon="upload" @click="submit">发布工单</el-button> -->
        <el-button
          type="primary"
          icon="SuccessFilled"
          v-if="
            props.objectStatus == 3 &&
             detailForm.handleUser.indexOf($store.getters.userInfo.user_id) > -1
          "
          @click="accept(detailForm)"
          >接受工单</el-button
        >
        <!-- <el-button type="primary" icon="edit" @click="submit">工单改派</el-button> -->
        <el-button
          type="primary"
          icon="position"
          v-if="
             props.objectStatus == 1 && detailForm.isNeedSign == 1 && detailForm.isSign == 0
          "
          @click="submit"
          >确认签到</el-button
        >
        <!-- <el-button type="primary" icon="RemoveFilled" v-if="detailForm.objectStatus == 3" @click="submit">确认签退</el-button> -->
        <el-button
          type="primary"
          icon="check"
          :loading="acceptLoading"
          v-if="detailForm.isNeedSign == 1 ? (detailForm.isSign == 1 && props.objectStatus == 1 ) :  props.objectStatus == 1"
          @click="completeTask(detailForm)"
          >确认完成</el-button
        >
      </div>
      <div v-if="props.payStatus == 0">
         <div style="border-top: 1px dashed #ccc; padding-top: 5px">

            <!-- 申请信息展示 -->
            <div class="apply-info" style="margin-bottom: 10px">
              <el-descriptions :column="3" border size="small">
                <el-descriptions-item label="工单金额">
                  {{ (props.applyInfo && props.applyInfo.orderPrice) || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="其他金额">
                  {{ (props.applyInfo && props.applyInfo.otherPrice) || '--' }}
                </el-descriptions-item>
                <el-descriptions-item label="结算金额">
                  {{ (props.applyInfo && props.applyInfo.totalPrice) || '--' }}
                </el-descriptions-item>
              </el-descriptions>
              <div
                v-if="props.applyInfo && props.applyInfo.fileList && props.applyInfo.fileList.length"
                class="photo-section"
                style="margin-top: 10px"
              >
                <h5 style="text-align: left;">其他金额证明图片</h5>
                <File :fileList="props.applyInfo.fileList"></File>
              </div>
              <div class="detail-section" style="margin-top: 10px">
                <h5 style="text-align: left;">申请备注</h5>
                <div class="content-box" style="text-align: left;">
                  {{ (props.applyInfo && props.applyInfo.applyContent) || '--' }}
                </div>
              </div>
            </div>

                <el-form ref="form" :model="form" style="width: 100%" label-width="80px">
                  <el-row :gutter="10">
                    <el-col :span="12">
                      <el-form-item label="审核结果">
                        <el-radio-group v-model="auditForm.payStatus">
                          <el-radio :label="1">通过</el-radio>
                          <el-radio :label="3">驳回</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="支付金额">
                        <el-input
                          style="width: 100%"
                          v-model="auditForm.totalPrice"
                          type="number"
                        ></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-form-item label="备注">
                    <el-input
                      style="width: 100%"
                      v-model="auditForm.auditRemark"
                      placeholder="请输入审核备注"
                      type="textarea"
                    ></el-input>
                  </el-form-item>
                </el-form>
              </div>
              <div style="width: 100%; text-align: right">
                <el-button @click="drawerVisible = false">关 闭</el-button>
                <el-button type="primary" @click="confirm">提 交</el-button>
              </div>
      </div>
    </template>
  </el-drawer>

  <!-- 步骤详情弹窗 -->
  <el-dialog
    v-model="stepDetailVisible"
    :title="currentStepDetail?.title + ' - 详情'"
    width="600px"
    destroy-on-close
  >
    <div v-if="currentStepDetail" class="step-detail-content">
      <!-- 签到详情 -->
      <div v-if="currentStepDetail.title === '确认签到'" class="signin-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="签到时间">
            {{ currentStepDetail.detailInfo.signTime }}
          </el-descriptions-item>
          <el-descriptions-item label="签到设备">
            {{ currentStepDetail.detailInfo.deviceInfo }}
          </el-descriptions-item>
          <el-descriptions-item label="签到地址" :span="2">
            {{ currentStepDetail.detailInfo.location }}
          </el-descriptions-item>
          <el-descriptions-item label="GPS坐标" :span="2">
            {{ currentStepDetail.detailInfo.coordinates }}
          </el-descriptions-item>
        </el-descriptions>

        <div v-if="currentStepDetail.detailInfo.photos?.length" class="detail-photos">
          <h4>签到现场照片</h4>
          <div class="photo-grid">
            <el-image
              v-for="(photo, index) in currentStepDetail.detailInfo.photos"
              :key="index"
              :src="photo.link"
              :preview-src-list="currentStepDetail.detailInfo.photos.map(p => p.link)"
              class="detail-photo"
              fit="cover"
            />
          </div>
        </div>
      </div>

      <!-- 完成详情 -->
      <div v-if="currentStepDetail.title === '确认完成'" class="completion-detail">
        <el-descriptions :column="2" border label-width="200px">
          <el-descriptions-item label="服务开始时间">
            {{ currentStepDetail.detailInfo.serviceStartTime }}
          </el-descriptions-item>
          <el-descriptions-item label="服务结束时间">
            {{ currentStepDetail.detailInfo.serviceEndTime }}
          </el-descriptions-item>
          <el-descriptions-item label="实际使用工时">
            {{ currentStepDetail.detailInfo.actualUsedTime }}
          </el-descriptions-item>
          <el-descriptions-item label="完成情况">
            <el-tag type="success">{{ currentStepDetail.detailInfo.completionStatus }}</el-tag>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 现场图片 -->
        <div v-if="currentStepDetail.detailInfo.sitePhotos?.length" class="detail-photos">
          <h4>现场图</h4>
          <div class="photo-grid">
            <el-image
              v-for="(photo, index) in currentStepDetail.detailInfo.sitePhotos"
              :key="index"
              :src="photo.link"
              :preview-src-list="currentStepDetail.detailInfo.sitePhotos.map(p => p.link)"
              class="detail-photo"
              fit="cover"
            />
          </div>
        </div>

        <!-- 处理结果图片 -->
        <div v-if="currentStepDetail.detailInfo.resultPhotos?.length" class="detail-photos">
          <h4>处理结果图</h4>
          <div class="photo-grid">
            <el-image
              v-for="(photo, index) in currentStepDetail.detailInfo.resultPhotos"
              :key="index"
              :src="photo.link"
              preview-teleported
              :preview-src-list="currentStepDetail.detailInfo.resultPhotos.map(p => p.link)"
              class="detail-photo"
              fit="cover"
            />
          </div>
        </div>

        <!-- 服务复盘 -->
        <div class="detail-section">
          <h4>服务复盘</h4>
          <div class="content-box">
            {{ currentStepDetail.detailInfo.serviceSummary }}
          </div>
        </div>

        <!-- 备注 -->
        <div class="detail-section">
          <h4>备注</h4>
          <div class="content-box">
            {{ currentStepDetail.detailInfo.remarks }}
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <el-button @click="stepDetailVisible = false">关闭</el-button>
    </template>
  </el-dialog>

  <!-- 完成工单对话框 -->
  <el-dialog
    v-model="completeDialogVisible"
    title="完成工单"
    width="50%"
    :before-close="() => (completeDialogVisible = false)"
  >
    <avue-form ref="completeFormRef" :option="completeFormOption" v-model="completeFormData">
       <template #useTimes>
          <el-input-number style="width:50%" v-model="completeFormData.useTimes"  :min="1" :max="10" label="label"></el-input-number> -  预估工时：{{ completeFormData.preUseTimes }}
        </template>
    </avue-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="completeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitCompleteForm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { dateFormat } from '@/utils/date.js';
import { ElMessage } from 'element-plus';
const { proxy } = getCurrentInstance();

// 时间格式化函数：将完整时间格式转换为年月日时分格式（去掉秒）
const formatTimeToHourMinute = timeString => {
  if (!timeString) return '--';
  // 如果时间格式是 "2025-09-06 14:15:36"，去掉秒数部分
  if (timeString.includes(':') && timeString.length > 16) {
    return timeString.substring(0, 16); // 返回 "2025-09-06 14:15"
  }
  return timeString;
};

let drawer = ref(false);
let detailForm = ref({});

// 步骤详情弹窗相关
const stepDetailVisible = ref(false);
const currentStepDetail = ref(null);

// 完工报告折叠面板控制
const activeEngineerPanels = ref([]);
const activeSigninPanels = ref({});
const activeCompletePanels = ref({});

// 显示步骤详情
const showStepDetail = step => {
  currentStepDetail.value = step;
  stepDetailVisible.value = true;
};

// 工单流程步骤模拟数据
const taskProcessSteps = ref([
  {
    title: '保存工单',
    description: '工单信息已保存',
    timestamp: '2024-01-15 09:00',
    type: 'primary',
    icon: 'Edit',
    completed: true,
    operator: '张三',
  },
  {
    title: '发布工单',
    description: '工单已发布到工程师',
    timestamp: '2024-01-15 09:30',
    type: 'success',
    icon: 'Upload',
    completed: true,
    operator: '李四',
  },
  {
    title: '接受工单',
    description: '工程师已接受工单',
    timestamp: '2024-01-15 10:00',
    type: 'success',
    icon: 'SuccessFilled',
    completed: true,
    operator: '王工程师',
  },
  {
    title: '工单改派',
    description: '工单重新分配',
    timestamp: '2024-01-15 11:00',
    type: 'warning',
    icon: 'Edit',
    completed: true,
    operator: '',
  },
  {
    title: '确认签到',
    description: '工程师现场签到',
    timestamp: '2024-01-15 14:00',
    type: 'primary',
    icon: 'Position',
    completed: true,
    operator: '王工程师',
    hasDetail: true,
    detailInfo: {
      location: '北京市朝阳区建国门外大街1号',
      signTime: '2024-01-15 14:00:32',
      deviceInfo: 'iPhone 12 Pro',
      coordinates: '116.4074, 39.9042',
      photos: [
        { link: 'https://via.placeholder.com/300x200/409eff/fff?text=签到现场1' },
        { link: 'https://via.placeholder.com/300x200/67c23a/fff?text=签到现场2' },
      ],
    },
  },
  {
    title: '确认签退',
    description: '工程师完成服务签退',
    timestamp: '2024-01-15 17:30',
    type: 'success',
    icon: 'RemoveFilled',
    completed: true,
    operator: '王工程师',
  },
  {
    title: '确认完成',
    description: '工单已完成确认',
    timestamp: '2024-01-15 18:00',
    type: 'success',
    icon: 'Check',
    completed: true,
    operator: '项目经理',
    hasDetail: true,
    detailInfo: {
      // 基本信息
      serviceStartTime: '2024-01-15 13:30:00',
      serviceEndTime: '2024-01-15 18:00:00',
      actualUsedTime: '4小时30分钟',
      completionStatus: '已完成',

      // 现场图片
      sitePhotos: [
        { link: 'https://via.placeholder.com/300x200/67c23a/fff?text=现场图1' },
        { link: 'https://via.placeholder.com/300x200/409eff/fff?text=现场图2' },
        { link: 'https://via.placeholder.com/300x200/e6a23c/fff?text=现场图3' },
      ],

      // 处理结果图片
      resultPhotos: [
        { link: 'https://via.placeholder.com/300x200/f56c6c/fff?text=处理结果1' },
        { link: 'https://via.placeholder.com/300x200/909399/fff?text=处理结果2' },
      ],

      // 服务复盘
      serviceSummary:
        '本次服务顺利完成，设备维护工作按计划执行。客户对服务质量表示满意，现场环境整洁有序。所有设备功能正常运行，达到预期效果。',

      // 备注
      remarks:
        '客户要求下次维护时提前通知，建议增加设备巡检频率。现场工作人员配合度高，为后续服务打下良好基础。',
    },
  },
]);

let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        // {
        //   type: 'date',
        //   label: '预约时间',
        //   span: 12,

        //   width: 140,
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //   prop: 'reservationTime',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择预约时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        // {
        //   label: '故障类型',
        //   prop: 'lables',
        //   type: 'select',
        //   props: {
        //     value: 'id',
        //     label: 'dictValue',
        //   },
        //   dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
        //   span: 12,
        // },
        // // {
        // //   label: '现场图',
        // //   type: 'upload',
        // //   value: [],
        // //   dataType: 'string',
        // //   loadText: '附件上传中，请稍等',
        // //   span: 24,
        // //   slot: true,
        // //   prop: 'workOrderPhotos',
        // //   addDisplay: true,
        // //   editDisplay: true,
        // //   viewDisplay: false,
        // //   listType: 'picture-card',
        // //   // align: 'center',
        // //   propsHttp: {
        // //     res: 'data',
        // //     url: 'id',
        // //     name: 'originalName',
        // //     // home: 'https://www.w3school.com.cn',
        // //   },
        // //   action: '/blade-resource/attach/upload',
        // // },
        // {
        //   label: '服务要求',
        //   prop: 'taskDescription',
        //   type: 'textarea',
        //   span: 24,
        // },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          label: '服务时间',
          prop: 'planTime',
          type: 'date',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,

          overHidden: true,
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 发布人
        {
          label: '发布人',
          prop: 'createName',
          span: 12,
        },

        // 派单人
        {
          label: '派单人',
          prop: 'projectLeaderName',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
          {
          label:'预估工时',
          prop:'preUseTimes',
          type:'number'
        },
        {
          label: '是否需要签到',
          prop: 'isNeedSign',
          type: 'radio',
          span: 12,

          props: {
            value: 'value',
            label: 'label',
          },
          dicData: [
            {
              label: '需要',
              value: 1,
            },
            {
              label: '不需要',
              value: 0,
            },
          ],
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 24,
        },
        {
          label: '工单描述',
          prop: 'taskDescription',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '工单执行',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '',
          prop: 'engineerServiceInfo',
          labelWidth: 0,
          span: 24,
          formslot: true,
        },
      ],
    },
    // 完工报告
    {
      label: '完工报告',
      prop: 'completeReportInfo',
      placeholder: '请输入完工报告',
      type: 'textarea',
      span: 24,
      column: [
        {
          labelWidth: 0,
          prop: 'completeReport',
        },
      ],
    },
    {
      label: '关单情况',
      prop: 'closeInfo',
      column: [
        {
          label: '关单时间',
          prop: 'completeTime',
          type: 'date',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          overHidden: true,
        },
        {
          label: '完成情况',
          prop: 'completeConditionName',
          span: 12,
          type: 'input',
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
  ],
});
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  objectStatus: {
    type: Number,
    default: 0,
  },
  payId: {
    type: String,
    default: '',
  },
  payStatus:{
    type: Number,
    default: null,
  },
  applyInfo:{
    type: Object,
    default: () => {},
  }
});

function viewDetail(id) {
  if (!id) return;
  return new Promise((resolve, reject) => {
    axios
      .get('/api/vt-admin/sealContractObject/detail', {
        params: {
          id,
        },
      })
      .then(res => {
        if (res.data.code == 200) {
          detailForm.value = res.data.data;
          resolve(res.data);
        } else {
          reject(res.data.msg);
        }
      });
  });
}
function open(id) {
  viewDetail(id).then(res => {
    drawer.value = true;
  });
}
function setAuditForm(data) {
  auditForm.value = {
    ...data
  };
}
let acceptLoading = ref(false);
function accept(row) {
  proxy
    .$confirm('确认是否接单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      acceptLoading.value = true;
      const url = '/api/vt-admin/sealContractObject/acceptOrder?id=' + row.id;
      axios
        .post(url)
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success(res.data.msg);
            drawer.value = false;
            emits('success');
            viewDetail(row.id);
          }
          acceptLoading.value = false;
        })
        .catch(err => {
          proxy.$message.error('接单失败');
          acceptLoading.value = false;
        });
    })
    .catch(() => {
      // 用户取消操作，不做处理
    });
}
// 完成工单相关变量
const completeDialogVisible = ref(false);
const completeFormRef = ref(null);
const completeFormData = ref({});
const currentRow = ref(null);

// 完成工单表单配置
const completeFormOption = ref({
  labelWidth: 120,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'datetime',
      label: '服务开始时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceStartTime',
      rules: [{ required: true, message: '请选择服务开始时间', trigger: 'blur' }],
    },
    {
      type: 'datetime',
      label: '服务结束时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceEndTime',
      rules: [{ required: true, message: '请选择服务结束时间', trigger: 'blur' }],
    },
    // {
    //   type: 'date',
    //   label: '实际完成时间',
    //   span: 12,
    //   value: dateFormat(new Date(), 'yyyy-MM-dd'),
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
    //   prop: 'completeTime',
    //   rules: [{ required: true, message: '请选择实际完成时间', trigger: 'blur' }],
    // },
    {
      label: '实际使用工时',
      prop: 'useTimes',
      span:24,
      type: 'number',
      rules: [{ required: true, message: '请输入实际使用工时', trigger: 'blur' }],
    },
    // // 完成情况
    // {
    //   label: '完成情况',
    //   prop: 'completeStatus',
    //   type: 'select',
    //   props: {
    //     value: 'id',
    //     label: 'dictValue',
    //   },
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=completeType',
    // },
    {
      label: '现场图',
      prop: 'workOrderPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
    {
      label: '处理结果图',
      prop: 'handleResultPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
      {
          label: '完成附件',
          prop: 'completeFiles',
          type: 'upload',
          span: 24,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',

          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
    {
      label: '服务复盘',
      prop: 'serviceReorder',
      type: 'textarea',
      span: 24,
      // rules: [{ required: true, message: '请输入服务复盘', trigger: 'blur' }],
    },
    {
      label: '备注',
      prop: 'completeRemark',
      type: 'textarea',
      span: 24,
    },
  ],
});

function completeTask(row) {
  // 设置表单数据和对话框可见性
  completeFormData.value = {
    serviceStartTime: row.serviceStartTime,
    serviceEndTime: row.serviceEndTime,
    // serviceReorder: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
    handleResultPhotos: [],
    preUseTimes: row.preUseTimes,
  };

  completeDialogVisible.value = true;
  currentRow.value = row;
}

// 提交完成表单
function submitCompleteForm() {
  completeFormRef.value.validate((valid, done) => {
    if (valid) {
      if(completeFormData.value.preUseTimes){
        const value = completeFormData.value.preUseTimes * 1  <  completeFormData.value.useTimes * 1
        if(value && !completeFormData.value.completeRemark){
            done()
           return  ElMessage.warning('实际使用工时超过了预估工时 ，请在备注填写超时原因')
        }
      }
      let url = '/api/vt-admin/sealContractObject/completeWorkOrder';
      const data = {
        id: currentRow.value.id,
        ...completeFormData.value,
        workOrderPhotos: completeFormData.value.handleResultPhotos
          ?.map(item => item.label)
          .join(','),
        handleResultPhotos: completeFormData.value.handleResultPhotos
          ?.map(item => item.label)
          .join(','),
        completeFiles: completeFormData.value.completeFiles
          ? completeFormData.value.completeFiles.map(item => item.value).join(',')
          : '',
      };
      axios
        .post(url, data)
        .then(r => {
          proxy.$message.success(r.data.msg);
          proxy.$store.dispatch('getMessageList');
          completeDialogVisible.value = false;
          emits('success')
          done();
          drawer.value = false
        })
        .catch(err => {
          done();
        });
    }
  });
}

const emits = defineEmits(['success']);
let auditForm = ref({});
function confirm() {
  axios
    .post('/api/vt-admin/sealContractObjectPayment/audit', {
      id: props.payId,
      ...auditForm.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
      drawer.value = false
      emits('success')
    });
}
function submit() {
  alert('请前往小程序签到')
}
defineExpose({ open ,setAuditForm});
</script>

<style lang="scss" scoped>
// 工单流程时间线样式
.task-process-timeline {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;

  .horizontal-timeline {
    :deep(.el-timeline) {
      padding-left: 0;
    }

    :deep(.el-timeline-item) {
      display: inline-block;
      vertical-align: top;
      margin-right: 40px;
      width: auto;
      min-width: 120px;

      &:last-child {
        margin-right: 0;
      }

      .el-timeline-item__wrapper {
        position: relative;
        padding-left: 0;
        padding-top: 0;
      }

      .el-timeline-item__tail {
        position: absolute;
        left: 50%;
        top: 15px;
        height: 2px;
        width: 40px;
        border-left: none;
        border-top: 2px solid #e4e7ed;
        transform: translateX(-50%);
      }

      .el-timeline-item__node {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        background-color: #fff;
        border-radius: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 30px;
        height: 30px;

        &.is-hollow {
          border: 2px solid #e4e7ed;

          .el-icon {
            color: #c0c4cc;
          }
        }
      }

      .el-timeline-item__content {
        margin-top: 40px;
        text-align: center;
      }

      .el-timeline-item__timestamp {
        position: absolute;
        top: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
        color: #909399;
        white-space: nowrap;
      }
    }
  }

  .timeline-content {
    h5 {
      margin: 0 0 5px 0;
      font-size: 14px;
      font-weight: 600;
      color: #303133;
    }

    p {
      margin: 0 0 5px 0;
      font-size: 12px;
      color: #606266;
    }

    .operator {
      font-size: 11px;
      color: #909399;
    }

    .detail-btn {
      margin-top: 8px;
      font-size: 11px;
      padding: 4px 8px;
      height: auto;
      border-radius: 4px;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      }
    }
  }
}

// 工程师服务时间线样式
.engineer-service-timeline {
  .engineer-timeline-section {
    margin-bottom: 30px;
    padding: 20px;
    border: 1px solid #ebeef5;
    border-radius: 8px;
    background: #fff;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .engineer-name {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #409eff;
    color: #409eff;
    font-size: 16px;
    font-weight: 600;
  }

  .service-timeline {
    :deep(.el-timeline-item__content) {
      .timeline-content {
        h6 {
          margin: 0 0 8px 0;
          font-size: 14px;
          font-weight: 600;
          color: #303133;
        }

        p {
          margin: 0 0 5px 0;
          font-size: 13px;
          color: #606266;

          &.taskDescription {
            color: #909399;
            font-style: italic;
          }
        }

        .image-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          margin-top: 10px;

          .timeline-image {
            width: 80px;
            height: 80px;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.2s;

            &:hover {
              transform: scale(1.05);
            }
          }
        }

        .service-reorder {
          margin-top: 10px;
          padding: 12px;
          background: #f5f7fa;
          border-radius: 6px;
          border-left: 4px solid #409eff;

          pre {
            margin: 0;
            font-family: inherit;
            font-size: 13px;
            color: #606266;
            white-space: pre-wrap;
            word-break: break-word;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .task-process-timeline {
    .horizontal-timeline {
      :deep(.el-timeline-item) {
        display: block;
        margin-right: 0;
        margin-bottom: 20px;
        width: 100%;

        .el-timeline-item__tail {
          display: none;
        }

        .el-timeline-item__node {
          left: 20px;
          transform: none;
        }

        .el-timeline-item__content {
          margin-top: 0;
          margin-left: 50px;
          text-align: left;
        }

        .el-timeline-item__timestamp {
          position: static;
          transform: none;
          display: block;
          margin-bottom: 5px;
        }
      }
    }
  }

  .engineer-service-timeline {
    .timeline-content {
      .image-list {
        .timeline-image {
          width: 60px;
          height: 60px;
        }
      }
    }
  }
}
// 步骤详情弹窗样式
.step-detail-content {
  .detail-photos {
    margin-top: 20px;

    h4 {
      margin: 0 0 15px 0;
      color: #303133;
      font-size: 16px;
      font-weight: 600;
      border-bottom: 2px solid #409eff;
      padding-bottom: 8px;
    }

    .photo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      gap: 15px;

      .detail-photo {
        width: 100%;
        height: 120px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid #ebeef5;

        &:hover {
          transform: scale(1.05);
          border-color: #409eff;
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
    }
  }

  .signin-detail,
  .completion-detail {
    .el-descriptions {
      margin-bottom: 20px;
    }

    .detail-section {
      margin-top: 20px;

      h4 {
        margin: 0 0 15px 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
        border-bottom: 2px solid #409eff;
        padding-bottom: 8px;
      }

      .content-box {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        line-height: 1.6;
        color: #495057;
        font-size: 14px;
        min-height: 60px;
      }
    }
  }
}
:deep(.el-descriptions__cell.el-descriptions__label) {
  width: 120px;
}

// 完工报告样式
.complete-report-container {
  .engineer-complete-report {
    .engineer-panel {
      margin-bottom: 20px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      overflow: hidden;

      :deep(.el-collapse-item__header) {
        background-color: #f8f9fa;
        border-bottom: 1px solid #ebeef5;
        padding: 15px 20px;

        &:hover {
          background-color: #f0f2f5;
        }
      }

      :deep(.el-collapse-item__content) {
        padding: 0;
      }

      .engineer-panel-title {
        display: flex;
        align-items: center;
        gap: 10px;
        width: 100%;

        .panel-icon {
          color: #409eff;
          font-size: 18px;
        }

        .engineer-name {
          font-weight: 600;
          font-size: 16px;
          color: #303133;
          flex: 1;
        }

        .status-tag {
          margin-left: auto;
        }
      }
    }

    .engineer-report-content {
      padding: 20px;

      .info-collapse {
        margin-bottom: 15px;

        &:last-child {
          margin-bottom: 0;
        }

        .info-panel {
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          overflow: hidden;

          :deep(.el-collapse-item__header) {
            background-color: #fafbfc;
            border-bottom: 1px solid #e4e7ed;
            padding: 12px 16px;

            &:hover {
              background-color: #f5f7fa;
            }
          }

          :deep(.el-collapse-item__content) {
            padding: 16px;
          }

          .info-panel-title {
            display: flex;
            align-items: center;
            gap: 8px;

            .info-icon {
              color: #67c23a;
              font-size: 16px;
            }

            span {
              font-weight: 500;
              color: #606266;
            }

            .el-tag {
              margin-left: auto;
            }
          }
        }
      }

      .signin-info,
      .complete-info {
        .photo-section {
          margin-top: 20px;
          text-align: left;
          h5 {
            margin: 0 0 12px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
            text-align: left;
            border-bottom: 2px solid #409eff;
            padding-bottom: 6px;
            display: inline-block;
          }

          .photo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;

            .signin-photo,
            .work-photo,
            .result-photo {
              width: 100%;
              height: 90px;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.3s ease;
              border: 2px solid #ebeef5;

              &:hover {
                transform: scale(1.05);
                border-color: #409eff;
                box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
              }
            }
          }
        }

        .text-section {
          margin-top: 20px;

          h5 {
            margin: 0 0 12px 0;
            color: #303133;
            font-size: 14px;
            font-weight: 600;
            border-bottom: 2px solid #409eff;
            padding-bottom: 6px;
            display: inline-block;
          }

          .content-box {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            line-height: 1.6;
            color: #495057;
            font-size: 13px;
            min-height: 50px;

            pre {
              margin: 0;
              font-family: inherit;
              white-space: pre-wrap;
              word-break: break-word;
            }
          }
        }
      }
    }
  }

  .no-data-tip {
    text-align: center;
    padding: 40px 20px;
    color: #909399;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .complete-report-container {
    .engineer-complete-report {
      .engineer-report-content {
        padding: 15px;

        .signin-info,
        .complete-info {
          .photo-section {
            .photo-grid {
              grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
              gap: 8px;

              .signin-photo,
              .work-photo,
              .result-photo {
                height: 60px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
