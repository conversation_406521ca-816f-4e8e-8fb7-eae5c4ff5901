<template>
  <div class="project-main" >
    <div class="tabs" style="margin-left: 5px;">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex === item.value ? 'active' : ''"
        @click="handleTabClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
    <self v-if="tabIndex == 0"></self>
    <company v-if="tabIndex == 1"></company>
    <other v-if="tabIndex == 2"></other>
  </div>
</template>

<script setup>
import self from './self.vue'
import company from './company.vue'
import other from './other.vue'
const tabs = [
  {
    label: '本地个人',
    value: 0,
  },
  {
    label: '本地劳务公司',
    value: 1,
  },
  {
    label: '外地交付资源',
    value: 2,
  },
];
let tabIndex = ref(0)
function handleTabClick(item) {
  tabIndex.value = item.value
}
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin-bottom: -10px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
</style>
