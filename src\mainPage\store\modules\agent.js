import axios from "axios";
import {setStore, getStore } from '@/utils/store';
import store from '../index';
import xuqiucaiji from '@/assets/agentImg/需求采集.png'
import xiansuofaxian from '@/assets/agentImg/线索发现.png'
export default {
  state: {
    agentTypeList: getStore({ name: 'agentTypeList' }) || [],
  },
  actions: {
    getAgentTypeList({ commit }) {
       
      return new Promise((resolve, reject) => {
        axios
          .get('/api/blade-system/dict/dictionary?code=agentType')
          .then(res => {
            commit('SET_AGENT_TYPE_LIST', res.data.data);
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
  },
  mutations: {
    SET_AGENT_TYPE_LIST: (state, data) => {
      let map = {
        '线索发现智能体': 'agent:discover',
        '需求采集智能体': 'agent:requirementProperty'
      }
      let logoMap = {
        '线索发现智能体': xiansuofaxian ,
        '需求采集智能体': xuqi<PERSON>iji
      }
     
      state.agentTypeList = data.filter(item => {
        item.logo = logoMap[item.dictValue];
        try {
          const key = map[item && item.dictValue];
          const perms = store && store.getters ? store.getters.permission : null;

          // 如果没有映射或没有权限数据，则不保留该项
          if (!key || !perms) return false;

          // 明确返回布尔值，避免 undefined 导致过滤异常
          return !!perms[key];
        } catch (err) {
          // 出现任何异常时，记录并过滤掉该项，避免整个流程抛错
          console.error('SET_AGENT_TYPE_LIST filter error:', err);
          return false;
        }
      });
      console.log(store.getters.permission,'store.getters.permission');
      
      setStore({ name: 'agentTypeList', content: state.agentTypeList });
    },
  },
};
