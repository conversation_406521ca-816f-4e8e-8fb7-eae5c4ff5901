<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    :before-open="beforeOpen"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    @keyup.enter="onLoad"
    v-model="form"
  >
    <template #menu-left>
      <div style="display: flex">
        <el-button type="primary" icon="plus" @click="handllAdd">新增</el-button>
        <!-- <div style="display: flex; align-items: center; gap: 20px">
          <span style="font-weight: bolder">合同总额：</span>
          <el-text type="primary" size="large"
            >￥{{ (contractTotalPrice * 1).toLocaleString() }}</el-text
          >
          <span style="font-weight: bolder">已付款总额：</span>
          <el-text type="primary" size="large"
            >￥{{ (hasPaymentPrice * 1).toLocaleString() }}</el-text
          >
          <span style="font-weight: bolder">已收票总额：</span>
          <el-text type="primary" size="large"
            >￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text
          >
        </div> -->
      </div>
    </template>
    <template #menu="{ row }">
      <el-button type="primary" text icon="Edit"  v-if="!row.$cellEdit && row.paymentStatus == 0" @click="handleEdit(row)"
        >编辑</el-button
      >
        <el-button
          type="primary"
          text
          icon="Pointer"
          @click="pay(row)"
          v-if="row.paymentStatus != 1"
          >付款</el-button
        >
    </template>
    <!-- <template #contractCode="{ row }">
      <el-link type="primary" @click="toDetail(row)">{{ row.contractCode }}</el-link>
    </template> -->
    <template #arriveStatus="{ row }">
      <el-tag effect="plain" size="large" v-if="row.arriveStatus == 0" type="info">未到货</el-tag>
      <el-tag effect="plain" size="large" v-if="row.arriveStatus == 1" type="warning"
        >部分到货</el-tag
      >
      <el-tag effect="plain" size="large" type="success" v-else-if="row.arriveStatus == 2"
        >全部到货</el-tag
      >
    </template>
    <template #invoiceStatus="{ row }">
      <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
      <el-tag
        effect="plain"
        size="large"
        v-if="row.isNeedInvoice == 1"
        :type="row.invoiceStatus == 0 ? 'success' : row.invoiceStatus == 1 ? 'danger' : 'warning'"
        >{{ row.$invoiceStatus }}</el-tag
      >
      <el-tag size="large" effect="plain" v-else type="info">无需开票</el-tag>
    </template>
    <template #paymentStatus="{ row }">
      <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
      <el-tag
        effect="plain"
        size="large"
        :type="row.paymentStatus == 1 ? 'success' : row.paymentStatus == 0 ? 'danger' : 'warning'"
        >{{ row.$paymentStatus }}</el-tag
      >
    </template>
    <template #contractFiles="{ row }">
      <File :fileList="row.attachList || []"></File>
    </template>
    <template #productListBtn-form>
      <el-button type="primary" plain icon="plus" @click="visible = true">核对产品</el-button>
    </template>
    <template #contractPrice="{ row }">
      <el-tooltip
        v-if="row.discountPrice && row.discountPrice > 0"
        :content="`优惠金额：¥${Number(row.discountPrice).toLocaleString('zh-CN', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })}`"
        placement="top"
      >
        <span class="contract-price-with-discount">{{ row.contractPrice }}</span>
      </el-tooltip>
      <span v-else>{{ row.contractPrice }}</span>
    </template>
  </avue-crud>

  <el-drawer :title="drawerMode === 'add' ? '新增合同' : '编辑合同'" size="500" v-model="visible">
    <el-row :gutter="10">
      <el-col :span="24">
        <el-card shadow="never">
          <avue-form
            :option="addOption"
            ref="addFormRef"
            @submit="handleSubmit"
            v-model="addForm"
          ></avue-form>
        </el-card>
      </el-col>
    </el-row>

    <template #footer>
      <div style="flex: auto">
        <!-- 取消 -->
        <el-button @click="visible = false">取 消</el-button>
        <el-button type="primary" @click="$refs.addFormRef?.submit()">确 定</el-button>
      </div>
    </template>
  </el-drawer>
   <dialogForm ref="dialogFormRef"></dialogForm>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
let route = useRoute();

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  cellBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 160,
  border: true,
  labelWidth: 120,
  column: [
    {
      label: '合同编号',
      prop: 'contractCode',
    //   width: 160,
      searchSpan: 4,
      overHidden: true,
      addDisplay: false,
     
    },
  
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
    },
    
   
    {
      label: '供应商',
      prop: 'supplierName',
      overHidden: true,
      component: 'wf-supplier-drop',
      // width: 250,
      searchSpan: 4,

      addDisplay: false,
      searchLabelWidth: 100,
     
    },
  
    
   
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      width: 130,
    },
    {
      label: '付款状态',
      prop: 'paymentStatus',
      type: 'select',
      width: 100,
      addDisplay: false,
      dicData: [
        {
          label: '未付款',
          value: 0,
        },
        {
          label: '部分付款',
          value: 2,
        },
        {
          label: '已付款',
          value: 1,
        },
      ],
    },
    {
      label: '已付款金额',
      prop: 'paymentPrice',
      addDisplay: false,
      width: 130,
    },
    // {
    //   label: '发票状态',
    //   prop: 'invoiceStatus',
    //   addDisplay: false,
    //   width: 100,
    //   dicData: [
    //     {
    //       label: '未收票',
    //       value: 1,
    //     },
    //     {
    //       label: '已收票',
    //       value: 0,
    //     },
    //     {
    //       label: '部分收票',
    //       value: 2,
    //     },
    //   ],
    // },

    // {
    //   label: '已收票金额',
    //   prop: 'hasInvoice',
    //   addDisplay: false,
    //   width: 130,
    //   formatter: row => {
    //     return (row.hasInvoice * 1).toFixed(2);
    //   },
    // },
    // {
    //   label: '支付时间',
    //   prop: 'paymentTime',
    //   addDisplay: false,
    //   width: 150,

    //   type: 'date',

    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    // },
    // {
    //   label: '已付款金额',
    //   prop: 'totalmoney',
    // },
    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-daterange-search',
      search: true,
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      search: !route.query.id,
      span: 24,
      searchSpan: 5,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: true,
      width: 150,
      hide: true,
    },
   
    
 
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

   
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContract/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContract/update';
const tableUrl = '/api/vt-admin/purchaseContract/pageForList';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

const props = defineProps({
  sealContractId: {
    type: String,
    default: null,
  },
  form: Object,
});
onMounted(() => {
  onLoad();
});
watch(
  () => props.orderId,
  () => {
    onLoad();
  }
);
let loading = ref(false);
let contractTotalPrice = ref(0);
let hasInvoicePrice = ref(0);
let hasPaymentPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          contractFiles: [],
        };
      });
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      contractTotalPrice.value = res.data.data.contractTotalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      hasPaymentPrice.value = res.data.data.hasPaymentPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
      };
    }),
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
    
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  ;
  const data = {
    ...row,
    contractFiles: row.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function toDetail(row) {
  router.push({
    path: '/procure/contractDetail',
    query: {
      id: row.id,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleRowDBLClick(row, event) {
  row.$cellEdit = true;
}
function beforeOpen(done, type) {
  if (type == 'add') {
    form.value.supplierId = props.supplierId;
    form.value.discountPrice = 0;
  }
  done();
}
function handleEdit(row) {
  drawerMode.value = 'edit';
  editingRow.value = row;

  // 填充编辑表单数据
  addForm.value = {
    id: row.id,
    supplierId: row.supplierId,
    
    contractCode: row.contractCode,
    supplierName: row.supplierName,
    purchaseDate: row.purchaseDate,
    isNeedInvoice: row.isNeedInvoice,
    contractPrice: row.contractPrice,
    contractFiles: row.attachList && row.attachList.map(item => {
        return {
            value: item.id,
            label: item.originalName,
        }
    }) || [],
  };

  visible.value = true;
}

let addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
    },

    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      span: 24,
   
      type: 'number',
      width: 150,
      rules: [
        {
          required: true,
          message: '请输入合同总额',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 150,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '是否开票',
      prop: 'isNeedInvoice',
      type: 'radio',
      span: 24,
      width: 100,
      value: 1,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      control: res => {
        return {
          billingCompany: {
            display: res == 1,
          },
        };
      },
    },
    //  {
    //       label: '收票公司',
    //       type: 'select',
    //       prop: 'billingCompany',
    //       props: {
    //         label: 'companyName',
    //         value: 'id',
    //       },
    //       dicFormatter: res => {
    //         return res.data.records;
    //       },
    //       span:24,
    //       search: true,
    //       hide: true,
    //       cell: false,
    //       dicUrl: '/api/vt-admin/company/page?size=100',
    //     },

    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    
    {
      label: '合同附件',
      prop: 'contractFiles',
      type: 'upload',
      dataType: 'object',
      cell: true,
      loadText: '附件上传中，请稍等',
      span: 24,
      width: 150,
      slot: true,
      value: [],
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});
let addForm = ref({
  discountPrice: 0,
});

let visible = ref(false);
let drawerMode = ref('add'); // 'add' 或 'edit'
let editingRow = ref(null); // 存储编辑中的行数据



let addFormRef = ref();
function handllAdd() {
  drawerMode.value = 'add';
  editingRow.value = null;

  if (addFormRef.value) {
    addFormRef.value.resetFields();
    addFormRef.value.resetForm();
  }
  // 清空新增表单数据
  addForm.value = {
    isNeedInvoice: 1,
  };

  visible.value = true;
}
function handleSubmit(form, done, loading) {
  if (drawerMode.value === 'add') {
    // 新增合同逻辑
    const data = {
      ...form,
      contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
      sealContractId:props.sealContractId,
      purchaseContractType:1
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          visible.value = false;
          done();
        }
      })
      .catch(() => {
        done();
      });
  } else if (drawerMode.value === 'edit') {
    // 编辑合同逻辑
    const data = {
      ...editingRow.value,
      ...form,
      contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          visible.value = false;
          done();
        }
      })
      .catch(() => {
        done();
      });
  }
}
// 付款操作
function pay(row) {
  proxy.$refs.dialogFormRef.show({
    title: '项目付款',
    option: {
      column: [
        {
          type: 'number',
          label: '付款金额',
          span: 12,
          disabled: true,
          value: row.contractPrice,
          prop: 'actualPaymentPriceList',
        },
        {
          type: 'datetime',
          label: '付款日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: moment().format('YYYY-MM-DD HH:mm:ss'),
          prop: 'actualPaymentDate',
        },
        {
          type: 'select',
          label: '付款账号',
          cascader: [],
          span: 12,
         display: !(props.form.contractType == 5 && props.form.cooperationType == 0) ,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentAccount',
          rules: [
            {
              required: true,
              message: '请选择付款账号',
              trigger: 'change',
            },
          ],
        },
        {
          label: '付款凭证',
          prop: 'paymentFiles',
          type: 'upload',
          dataType: 'object',
         
      
          span: 24,
          slot: true,
          limit: 1,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseContractActualPayment/save', {
          purchaseContractId: row.id,
          ...res.data,
          paymentFiles: res.data.paymentFiles && res.data.paymentFiles.map(item => item.value).join(','),
        })
        .then(e => {
          proxy.$message.success('付款成功');
          res.close();
          onLoad();

         
        })
        .catch(() => {
          imgId.value = '';
        });
    },
  });
}
</script>

<style lang="scss" scoped>
// 合计行样式优化
:deep(.el-table__footer-wrapper) {
  .el-table__footer {
    .summary-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px 0;
      font-size: 13px;
      line-height: 1.4;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
        }

        .value {
          font-weight: 600;
          color: #303133;
        }

        &.subtotal {
          .value {
            color: #409eff;
          }
        }

        &.discount {
          .discount-value {
            color: #67c23a;
          }
        }

        &.total {
          border-top: 1px solid #ebeef5;
          padding-top: 4px;
          margin-top: 2px;

          .label {
            font-weight: 600;
            color: #303133;
          }

          .total-value {
            color: #e6a23c;
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
    }
  }
}

// 表格合计行整体样式
:deep(.el-table__footer) {
  .el-table__cell {
    background-color: #fafafa;
    border-top: 2px solid #409eff;

    &:first-child {
      font-weight: 600;
      color: #303133;
    }
  }
}

// 合同价格悬浮样式
.contract-price-with-discount {
  cursor: help;
  color: #409eff;
  border-bottom: 1px dashed #409eff;

  &:hover {
    color: #66b1ff;
    border-bottom-color: #66b1ff;
  }
}
</style>
