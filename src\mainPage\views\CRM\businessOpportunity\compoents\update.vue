<template>
  <basic-container>
    <Title
      >新增
      <!-- <template #foot>
        <el-button type="primary" @click="active--" v-if="active !== 0">上一步</el-button>
        <el-button @click="nextStep" type="primary" v-if="active < stepList.length - 1"
          >下一步</el-button
        >
        <el-button type="primary" @click="submit" v-else>提交</el-button>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      > -->
    </Title>
    <el-steps :active="active" style="margin: 20px 0" simple finish-status="success">
      <el-step :title="item" v-for="(item, index) in stepList" :key="index" />
    </el-steps>
    <avue-form v-show="active == 0" :option="option" ref="baseForm" v-model="form">
      <template #menu-form="{ size }"> </template>
      <template #name>
        <el-autocomplete
          style="width: 100%"
          v-model="form.name"
          :fetch-suggestions="querySearch"
          :trigger-on-focus="false"
          @change="handleChange"
          value-key="name"
          placeholder="请输入商机名称"
        ></el-autocomplete>
      </template>
    </avue-form>
    <avue-form
      v-show="stepList[active] == '报备'"
      :option="reportOption"
      v-model="reportForm"
      ref="reportFormRef"
    >
      <template #manufacturer>
        <el-select
          style="width: 100%"
          v-model="reportForm.manufacturer"
          placeholder="请选择厂家名称"
          allow-create
          filterable
          remote
          :remote-method="loadManufacturerOptions"
          :loading="manufacturerLoading"
        >
          <el-option
            v-for="item in manufacturerOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <span style="float: left">{{ item.label }}</span>
            <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
              {{ item.description }}
            </span>
          </el-option>
        </el-select>
      </template>
    </avue-form>
    <div
      style="display: flex; flex-direction: column; gap: 30px; margin-bottom: 20px"
      v-show="stepList[active] == '售前指引'"
    >
      <el-alert type="success" v-if="preSaleFileList.length > 0">点击文件即可下载</el-alert>
      <el-card v-for="item in preSaleFileList" :key="item.id" shadow="hover">
        <File :fileList="[item]"></File>
      </el-card>
    </div>
    <div style="display: flex; justify-content: center">
      <el-button type="primary" @click="active--" v-if="active !== 0">上一步</el-button>
      <el-button @click="nextStep" type="primary" v-if="active < stepList.length - 1"
        >下一步</el-button
      >
      <el-button type="primary" @click="submit" v-else>提交</el-button>
      <el-button
        @click="
          $router.$avueRouter.closeTag();
          $router.back();
        "
        >关闭</el-button
      >
    </div>
    <div class="programmeSelect">
      <Title style="margin-bottom: 10px">历史方案</Title>
      <div
        class="content"
        @mouseenter="handleClick(index)"
        :class="{ select: currentSelectProgramme == index }"
        style="box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); margin: 5px; border-radius: 5px"
        v-for="(i, index) in 5"
      >
        <div class="title">
          <el-text size="large" type="primary">方案名称</el-text>
          <el-text type="warning" size="small">方案场景</el-text>
        </div>
        <span style="font-size: 12px" class="random-color-example">服务器</span>
        <div class="btn" style="display: flex; justify-content: space-between">
          <div class="left_btn">
            <el-button type="primary" size="small" plain round>预览</el-button>
          </div>
          <div class="right_btn">
            <el-button type="success" plain size="small" round>使用</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      :userUrl="url"
      @onConfirm="handleUserSelectConfirm"
      :categoryId="firstCategoryId"
    >
    </wf-product-select>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { ref, getCurrentInstance, onMounted, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
onMounted(() => {
  if (route.query.id) {
    getDetail();
  }
});
let addUrl = '/api/vt-admin/supplier/save';
let updateUrl = '/api/vt-admin/supplier/update';
let detailUrl = '/api/vt-admin/supplier/detail';
const router = useRouter();
const route = useRoute();
let stepList = ref(['商机基本信息', '报备']);
let customerId = ref(0);
let option = ref({
  labelWidth: 150,
  submitBtn: false,
  emptyBtn: false,
  column: [
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   display: true,
    //   span: 12,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择商机分类',
    //     },
    //   ],
    //   dicData: [
    //     {
    //       value: 0,
    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    //   control: val => {
    //     return {
    //       projectLeader: {
    //         display: val == 1,
    //       },
    //       post: {
    //         display: val == 1,
    //       },
    //       leaderPhone: {
    //         display: val == 1,
    //       },
    //     };
    //   },
    // },
    {
      label: '商机名称',
      prop: 'name',

      rules: [
        {
          required: true,
          message: '请输入商机名称',
        },
      ],
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 12,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'type',
      checkStrictly: true,
    },

    {
      label: '关联需求',
      prop: 'clueIds',
      type: 'select',
      remote: true,
      dicUrl: '/api/vt-admin/clue/page?size=50&current=1&selectType=0&clueName={{key}}',
      props: { label: 'clueName', value: 'id', desc: 'status' },
      dicFormatter: res => {
        return res.data.records.map(item => {
          return {
            ...item,
            status: item.requirementStatus == 0 ? '未填写' : '已填写',
          };
        });
      },
    },
    {
      label: '对应客户',
      prop: 'customerId',
      component: 'wf-customer-select',
      value: route.query.customerId,
      params: {},
      change: val => {
        handleCustomerIdChange(val.value);
      },
      control: val => {
        console.log(val);
        return {
          contactPerson: {
            disabled: val ? false : true,
          },
        };
      },
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      type: 'tree',
      label: '商机关联',
      multiple: true,
      width: 100,
      span: 12,
      // parent: false,
      overHidden: true,
      change(val) {
        setUrlValue(val);
      },
      // rules: [
      //   {
      //     required: true,
      //     message: '请选择商机关联',
      //   },
      // ],

      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
      dataType: 'string',
      display: true,
      filterable: true,
      prop: 'businessCategory',
      checkStrictly: true,
    },
    {
      type: 'select',
      label: '商机场景',
      span: 12,
      multiple: true,
      // rules: [
      //   {
      //     required: true,
      //     message: '请选择商机来源',
      //   },
      // ],
      display: true,
      prop: 'scene',
      dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityScence',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },

    {
      label: '协作业务员',
      placeholder: '请选择协作业务员',
      type: 'select',
      dicData: [],
      display: false,
      prop: 'businessPerson',
      rules: [
        {
          required: true,
          message: '请选择协作业务员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '项目落地地址',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '所在区域必须填写',
          trigger: 'blur',
        },
      ],
      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      component: 'wf-contact-select',
      placeholder: '请先选择客户',
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      disabled: false,
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
    {
      type: 'select',
      label: '商机来源',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '介绍人',
      prop: 'introducer',
      type: 'input',
    },
    {
      label: '关联产品',
      prop: 'isProduct',
      labelTip: '简单商机可直接关联产品',
      type: 'switch',
      control: val => {
        return {
          productVOList: {
            display: val == 1,
          },
        };
      },
      display: false,
      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
    },
    {
      label: '下次跟进时间',
      prop: 'nextFollowTime',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '业务配合人',
      component: 'wf-user-select',
      prop: 'businessCoordinationPerson',
    },
    {
      label:'是否协议商机',
      prop:'isFrameworkAgreement',
      type:'radio',
      value:0,
      rules:[{
        required: true,
        message: '请选择是否协议商机',

      }],
      dicData:[{
        value:0,
        label:'否'
      },{
        value:1,
        label:'是'
      }]
    },
    //   {
    //   label:'是否合作商机',
    //   prop:'isCooperation',
    //   type:'radio',
    //   value:0,
     
    //   dicData:[{
    //     value:0,
    //     label:'否'
    //   },{
    //     value:1,
    //     label:'是'
    //   }],
    //   control: val => {
    //     return {
    //       cooperationTenantId: {
    //         display: val == 1,
    //       },
    //     };
    //   },
    // },
    // // 租户列表
    // {
    //   label: '合作方',
    //   prop: 'cooperationTenantId',
    //   type: 'select',
    //   dicUrl: '/api/blade-system/tenant/selectList',
    //   // dicData: [],
    //   display: false,
    //   props: {
    //     label: 'tenantName',
    //     value: 'tenantId',
    //   },
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择合作方',
    //     },
    //   ],
    // },
    {
      label: '产品',
      prop: 'productVOList',
      type: 'dynamic',
      display: false,
      span: 24,

      children: {
        rowAdd: done => {
          // console.log(proxy.$refs['product-select']);
          proxy.$refs['product-select'].visible = true;
          // proxy.$refs['product-select'].getTreeData()
        },
        column: [
          {
            label: '产品编号',
            prop: 'productCode',
            overHidden: true,
            cell: false,
            // placeholder: '自动生成',
          },
          {
            label: '产品名称',
            prop: 'productName',
            cell: false,
            overHidden: true,
          },
          {
            label: '品牌',
            prop: 'productBrand',
            cell: false,
            overHidden: true,
          },
          {
            label: '单位',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            cell: false,
            prop: 'unit',
            dicUrl: '/blade-system/dict/dictionary?code=unit',
            remote: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            cell: false,
            span: 24,
            type: 'input',
          },

          {
            label: '产品图片',
            prop: 'coverUrl',
            type: 'upload',
            dataType: 'object',
            listType: 'picture-img',
            loadText: '图片上传中，请稍等',
            span: 24,
            slot: true,
            cell: false,
            limit: 1,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/blade-resource/attach/upload',
          },

          {
            label: '商品描述',
            prop: 'description',
            overHidden: true,
            cell: false,
            type: 'textarea',
            span: 24,
          },

          {
            label: '用途',
            prop: 'purpose',
            overHidden: true,
            type: 'textarea',
            span: 24,

            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            change: a => {
              const { value, row } = a;
              row.totalPrice = row.referPurchasePrice * value;
            },
          },
        ],
      },
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '项目联系人',
      prop: 'projectLeader',
      display: false,
      type: 'input',
    },
    {
      label: '岗位',
      prop: 'post',
      display: false,
      type: 'input',
    },
    {
      label: '联系人手机',
      prop: 'leaderPhone',
      display: false,
      type: 'input',
    },
    {
      label: '商机描述',
      prop: 'description',
      span: 24,
      type: 'textarea',
    },
    {
      label: '报备',
      prop: 'isReport',
      span: 24,
      type: 'switch',
      change: val => {
        stepList.value = val.value == 0 ? ['商机基本信息'] : ['商机基本信息', '报备'];
      },
      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
    },
  ],
});
let reportOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      disabled: true,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      disabled: true,
      component: 'wf-customer-select',
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      label: '产品类型',
      prop: 'productType',
      search: true,
      type: 'input',
    },
    {
      label: '产品名称',
      prop: 'productName',
    },
    {
      label: '厂家名称',
      prop: 'manufacturer',
      type: 'select',
      filterable: true,
      remote: true,
      remoteMethod: 'loadManufacturerOptions',
      loading: 'manufacturerLoading',
      props: {
        label: 'label',
        value: 'value',
      },
      slot: true,
      rules: [
        {
          required: true,
          message: '请选择厂家名称',
          trigger: 'change',
        },
      ],
    },
    {
      label: '报备方式',
      prop: 'reportMethod',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '报备方式不能为空',
          trigger: 'change',
        },
      ],
      dicUrl: '/blade-system/dict-biz/dictionary?code=reportingMethods',
      overHidden: true,
    },
    {
      label: '报备平台',
      prop: 'reportPlatform',
      overHidden: true,
    },
    {
      label: '报备时间',
      prop: 'reportTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择报备时间',
          trigger: 'change',
        },
      ],
    },
    {
      label: '报备有效期(天)',
      prop: 'reportValidity',
      overHidden: true,
      type: 'number',
      hide: true,
      rules: [
        {
          required: true,
          message: '请输入报备有效期',
          trigger: 'blur',
        },
      ],
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
  if (route.query.renewIds) {
    getRenewDetail();
  }
});
onMounted(() => {
  if (route.query.customerId) {
    setUrl(route.query.customerId);
    setBaseInfo(route.query.customerId);
  }
  if (route.query.type == 'assist') {
    console.log(111);
    setCustomerUrl();
  }
  loadManufacturerOptions();
});
let form = ref({
  type: '',
  scene: '',
});
let reportForm = ref({});
let active = ref(0);
let manufacturerOptions = ref([]);
let manufacturerLoading = ref(false);
const { proxy } = getCurrentInstance();

// function submit(form, done) {
//   axios
//     .post(route.query.id ? updateUrl : addUrl, { ...form })
//     .then(res => {
//       proxy.$message.success(res.data.msg);
//       router.back();
//       router.$avueRouter.closeTag();
//     })
//     .catch(err => {
//       done();
//     });
// }
function getDetail() {
  axios
    .get(detailUrl, {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      form.value = res.data.data;
      
      // 处理报备表单数据回显
      if (res.data.data.businessOpportunityReportEntity) {
        const reportEntity = res.data.data.businessOpportunityReportEntity;
        reportForm.value = { ...reportEntity };
        
        // 如果有supplierId，需要转换为manufacturer字段用于显示
        if (reportEntity.supplierId && !reportEntity.manufacturer) {
          reportForm.value.manufacturer = reportEntity.supplierId;
        }
      }
    });
}
function nextStep() {
  if (active.value == 0) {
    proxy.$refs.baseForm.validate((valid, done) => {
      if (valid) {
        active.value++;
        done();
        if (stepList.value[active.value] == '售前指引') {
          // 获取售前指引文件
          getPreSaleFile();
        }
      }
    });
  } else {
    active.value++;
    if (stepList.value[active.value] == '售前指引') {
      // 获取售前指引文件
      getPreSaleFile();
    }
  }
}
function submit() {
  if (active.value == 0) {
    proxy.$refs.baseForm.validate((valid, done) => {
      if (valid) {
        done();
        const data = {
          ...form.value,
          businessOpportunityReportEntity: reportForm.value,
          fileIds: form.value.fileIds.map(item => item.value).join(','),
          provinceCode: form.value.province_city_area[0],
          cityCode: form.value.province_city_area[1],
          areaCode: form.value.province_city_area[2],
          productRenewList: route.query.renewIds?.split(','),
        };
        axios.post('/api/vt-admin/businessOpportunity/save', data).then(res => {
          proxy.$message.success(res.data.msg);
          router.back();
          router.$avueRouter.closeTag();
        });
      }
    });
  } else {
    proxy.$refs.reportFormRef.validate((valid, done) => {
      if (valid) {
        // 处理报备表单数据
        const reportData = { ...reportForm.value };
        
        // 判断manufacturer是否为ID（数字或UUID格式）
        if (reportData.manufacturer && (typeof reportData.manufacturer === 'number' || /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(reportData.manufacturer) || /^\d+$/.test(reportData.manufacturer))) {
          reportData.supplierId = reportData.manufacturer;
          delete reportData.manufacturer;
        }
        
        const data = {
          ...form.value,
          businessOpportunityReportEntity: reportData,
          fileIds: form.value.fileIds.map(item => item.value).join(','),
          provinceCode: form.value.province_city_area[0],
          cityCode: form.value.province_city_area[1],
          areaCode: form.value.province_city_area[2],
          productRenewList: route.query.renewIds?.split(','),
        };
        axios.post('/api/vt-admin/businessOpportunity/save', data).then(res => {
          proxy.$message.success(res.data.msg);
          router.back();
          router.$avueRouter.closeTag();
        });
      }
    });
  }
}
function setUrl(id) {
  const contactPerson = proxy.findObject(option.value.column, 'contactPerson');
  contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
  customerId.value = id;
  reportForm.value.customerId = id;
}
function handleChange(val) {
  reportForm.value.name = val;
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode, businessPerson, businessPersonName } =
        res.data.data;
      form.value.province_city_area = [provinceCode, cityCode, areaCode];
      form.value.address = res.data.data.address;
      const mainPerson = res.data.data.customerContactVO?.id;
      form.value.contactPerson = mainPerson;
      const businessPersonRef = proxy.findObject(option.value.column, 'businessPerson');
      if (businessPerson.split(',').length > 1 && route.query.type == 'assist') {
        businessPersonRef.display = true;
        const dicData = businessPerson.split(',').map((item, index) => {
          return {
            value: item,
            label: businessPersonName.split(',')[index],
          };
        });
        businessPersonRef.dicData = dicData;
      } else if (businessPerson.split(',').length == 1 && route.query.type == 'assist') {
        form.value.businessPerson = businessPerson;
        businessPersonRef.display = false;
      } else {
        businessPersonRef.display = false;
      }
    });
}
function setCustomerUrl() {
  const customer = proxy.findObject(option.value.column, 'customerId');
  customer.params.Url = '/vt-admin/customer/page?type=2';
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form.value.productVOList.push({
        ...r.data.data,
        productId: r.data.data.id,
        number: 1,
        id: null,
      });
    });
  });
}
// 选择方案
let currentSelectProgramme = ref('');

function handleClick(index) {
  currentSelectProgramme.value = index;
}

let currentChecked = ref('');

let url = ref('');
let firstCategoryId = ref('');
function setUrlValue(val) {
  const categoryIds = val.value;
  firstCategoryId.value = val.value.split(',')[0];
  url.value = `/vt-admin/product/pageForBusiness?categoryIds=${categoryIds}`;
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/businessOpportunity/list', {
      params: {
        size: 1000,
        name: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
// 售前指引文件
let preSaleFileList = ref([]);
function getPreSaleFile() {
  axios
    .get('/api/vt-admin/businessGatherTemplate/selectFilesByCategoryIds', {
      params: {
        categoryIds: form.value.businessCategory,
      },
    })
    .then(res => {
      preSaleFileList.value = res.data.data;
    });
}
function getRenewDetail() {
  if (route.query.businessOpportunityId && route.query.businessOpportunityId != 0) {
    axios
      .get('/api/vt-admin/businessOpportunity/detail', {
        params: {
          id: route.query.businessOpportunityId,
        },
      })
      .then(res => {
        const { provinceCode, cityCode, areaCode } = res.data.data;
        axios
          .get('/api/vt-admin/customerProductRenew/productList?ids=' + route.query.renewIds, {})
          .then(r => {
            form.value = {
              ...res.data.data,
              name: res.data.data.name + '的续费商机',
              province_city_area: [provinceCode, cityCode, areaCode],
              id: null,
              preSealPrice: '',
              preSignDate: '',
              fileIds: [],
              productVOList: r.data.data.map(item => {
                return {
                  productId: item.productVO?.id,
                  number: item.number,
                };
              }),
            };
          });
      });
  } else {
    axios
      .get('/api/vt-admin/offer/detail', {
        params: {
          id: route.query.offerId,
        },
      })
      .then(res => {
        axios
          .get('/api/vt-admin/customerProductRenew/productList?ids=' + route.query.renewIds, {})
          .then(r => {
            form.value = {
              ...res.data.data,
              name: res.data.data.offerName + '的续费商机',
              type: res.data.data.businessTypeId,

              id: null,
              preSealPrice: '',
              preSignDate: '',
              fileIds: [],
              productVOList: r.data.data.map(item => {
                return {
                  productId: item.productVO?.id,
                  number: item.number,
                };
              }),
            };
            handleCustomerIdChange(form.value.customerId);
          });
      });
  }
}
async function loadManufacturerOptions(query = '') {
  manufacturerLoading.value = true;
  try {
    const res1 = axios.get('/api/vt-admin/businessOpportunityReport/getManufacturer', {
      params: {
        manufacturer: query,
      },
    });
    const res2 = await axios.get('/api/vt-admin/supplier/pageForPlatformSupplier',{
      params: {
        supplierName: query,
      },  
    });
    const res = await Promise.all([res1, res2]);
    const manufacturer = res[0].data.data.map(item => {
      return {
        value: item,
        label: item,
       description: ''
      };
    });
    const supplier = res[1].data.data.records.map(item => {
      return {
        value: item.id,
        label: item.supplierName,
          description: '平台入驻'
       
      };
    });
    manufacturerOptions.value = supplier.concat(manufacturer);
  } catch (error) {
    console.error('加载厂家选项失败:', error);
  } finally {
    manufacturerLoading.value = false;
  }
}
function handleCustomerIdChange(id) {
  if (!id) return;
  setUrl(id);
  setBaseInfo(id);
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
@use 'sass:math';

.random-color-example {
  color: rgb(math.random() * 255, math.random() * 255, math.random() * 255);
}
.programmeSelect {
  overflow-y: auto;
  position: fixed;
  padding: 5px;
  top: calc(50% - 250px);
  right: -520px;
  border-radius: 5px;
  height: 500px;
  width: 300px;
  transition: all 0.3s;
  z-index: 999;
  // border: 1px solid #ccc;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  .content {
    padding: 15px;
    padding-bottom: 0px;
    overflow: hidden;
    .title {
      display: flex;
      justify-content: space-between;
    }
    .btn {
      // background-color: red;
      height: 0px;
      transition: all 0.2s;
      line-height: 25px;
      div {
        width: 50%;
        text-align: center;
      }
    }
  }
}
.active {
  right: 20px;
}
.select {
  border: 1px solid $color-primary;
  .btn {
    height: 25px !important;
    margin-bottom: 5px;
  }
}
</style>
