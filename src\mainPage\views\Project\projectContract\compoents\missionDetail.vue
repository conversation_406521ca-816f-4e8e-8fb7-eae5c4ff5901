<template>
  <el-drawer
    size="50%"
    title="标的详情"
    v-model="dialogVisible"
    class="avue-dialog avue-dialog--top"
  >
    <avue-form ref="dialogForm" :option="detailOption" v-model="detailForm">
      <template #productList>
        <missionProductList :objectId="currentObjectId" :delBtn="props.delBtn"></missionProductList>
      </template>
      <template #delayList>
        <delayList :objectId="currentObjectId"></delayList>
      </template>
      <template #completeFiles>
        <File :fileList="detailForm.completeFileList || []"></File>
      </template>
    </avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import missionProductList from './missionProductList.vue';
import delayList from './delayList.vue';
let dialogVisible = ref(false);
let detailForm = ref({});
let detailOption = ref({
  detail: true,
  tabs: true,
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'applyInfo',
      labelWidth: 140,
      column: [
        {
          type: 'input',
          label: '标的名称',
          span: 24,
          display: true,
          prop: 'objectName',
        },
        {
          type: 'input',
          label: '任务描述',
          span: 24,
          display: true,
          prop: 'durationNode',
        },
        {
          type: 'datetime',
          label: '时间要求',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
        },
        {
          type: 'input',
          label: '技术人员',
          span: 12,
          display: true,
          prop: 'technologyUserName',
        },

        {
          type: 'select',
          label: '状态',
          span: 24,

          editDisplay: false,
          addDisplay: false,
          value: '0',
          dicData: [
            {
              label: '未开始',
              value: 0,
            },
            {
              label: '进行中',
              value: 1,
            },
            {
              label: '已完成',
              value: 2,
            },
          ],
          prop: 'objectStatus',
        },

        // {
        //   type: 'textarea',
        //   label: '延期说明',
        //   span: 24,
        //   editDisplay: false,
        //   addDisplay: false,
        //   prop: 'a170072237641194426',
        // },
        {
          type: 'switch',
          label: '上门服务',
          span: 24,
          display: true,
          hide: true,
          value: '0',
          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
          prop: 'isVisitingService',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },

    {
      label: '配送信息',
      prop: 'invoiceInfo',
      column: [
      {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          value:'1',
          dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              distributionDate: {
                label: val == 3 ? '送货日期' : val == 1 ? '发货日期' : val == 4 ?'交付日期':'自提日期',
              },
              distributionAddress: {
                label: val == 3 ? '送货地址' : val == 1 ? '发货地址' : val == 4 ? '交付地址' : '',
                display: val != 2 ? true : false,
              },
            };
          },
         
        },
        {
          label: '送货人',
          component: 'wf-user-select',
          prop: 'distributionUser',

          span: 12,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请选择送货人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货日期',
          prop: 'distributionDate',
          span: 12,
          type: 'datetime',

          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [
            {
              required: true,
              message: '请选择送货日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货地址',
          prop: 'distributionAddress',
          span: 24,

          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入送货地址',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系人',
          prop: 'contact',
          span: 12,
          type: 'input',

          rules: [
            {
              required: true,
              message: '请输入联系人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          span: 12,

          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'change',
            },
          ],
        },
      ],
    },

    {
      label: '关联产品',
      prop: 'productInfo',

      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'productList',
          span: 24,
        },
      ],
    },

    {
      label: '完成信息',
      prop: 'completeInfo',

      column: [
        {
          label: '完成时间',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
          span: 24,
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '延期信息',
      prop: 'delayInfo',

      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'delayList',
          span: 24,
        },
      ],
    },
  ],
});
const props = defineProps({
  delBtn:{
    type:Boolean,
    default:false
  }
})
watchEffect(() => {
  if(!dialogVisible.value){
    currentObjectId.value = ''
  }
})
let currentObjectId = ref(null);
function viewDetail(row, tabActive = 1) {
  detailOption.value.tabsActive = tabActive;
  currentObjectId.value = row.id;

  axios.get('/api/vt-admin/sealContractObject/detail?id=' + row.id).then(res => {
    dialogVisible.value = true;
    detailForm.value = {
      ...res.data.data,
      // distributionMethod :res.data.data.distributionMethod.toString(),
    };
  });
}
defineExpose({
  viewDetail,
});
</script>

<style lang="scss" scoped></style>
