<template>
  <div v-loading="loading">
    <div v-for='item in detailForm.supplierVOList ' >
      <resultItem :info="item"></resultItem>
      <el-divider></el-divider>
    </div>
  </div>
</template>

<script setup>
import { CaretRight } from '@element-plus/icons-vue';
import resultItem from './resultItem.vue';
import { onMounted, watch } from 'vue';

const props = defineProps(['inquiryId']);
let loading = ref(false);
watch(
  () => props.inquiryId,
  () => {
    getDetail();
  },{
    immediate: true
  }
);

let detailForm = ref({});
function getDetail() {

  loading.value = true;

  axios
    .get('/api/vt-admin/purchaseInquiry/detail', {
      params: {
        id: props.inquiryId,
      },
    })
    .then(res => {
      loading.value = false;
      detailForm.value = res.data.data;
      
      getStatus()
    });
}
let inquiryStatus =  ref(0)
function getStatus() {
  inquiryStatus.value = detailForm.value.supplierVOList.inquiryStatus > 1;
  
}

defineExpose({
  inquiryStatus,
  detailForm
})
</script>

<style lang="scss" scoped></style>
