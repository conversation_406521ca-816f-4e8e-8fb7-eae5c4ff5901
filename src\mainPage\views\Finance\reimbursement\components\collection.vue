<template>
 
    <avue-crud
      :option="option"
      :data="tableData"
     
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @selection-change="selectionChange"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
        :summary-method="getSummaries"
    >
    
   
   
     
      <template #planCollectionDate="{ row }">
        <div v-if="row.collectionStatus != 2">
          <span
            :style="{
              color:
                row.planCollectionDays > 7
                  ? 'var(--el-color-success)'
                  : row.planCollectionDays <= 7 && row.planCollectionDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
            }"
            class="planCollectionDays"
            >{{ row.planCollectionDays }}</span
          ><span>{{ row.planCollectionDays && row.planCollectionDays == 0 ? '天' : '' }}</span>
        </div>
        <div v-else style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
      </template>
      <template #collectionStatus="{ row }">
        <div v-if="row.collectionStatus == 2" style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
        <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 1"
          >部分收款</el-tag
        >
        <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 0"
          >未收款</el-tag
        >
      </template>
      <template #invoiceStatus="{ row }">
        <el-tag
          effect="plain"
          :type="
            row.invoiceStatus == 0
              ? 'primary'
              : row.invoiceStatus == 10 || row.invoiceStatus == 4
              ? 'info'
              : row.invoiceStatus == 1 || row.invoiceStatus == 2
              ? 'success'
              : 'danger'
          "
          >{{ row.$invoiceStatus }}</el-tag
        >
      </template>
    </avue-crud>
 

</template>

<script setup lang="jsx">

import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
//   import wfCustomerInvoiceDrop from '../compoents/wf-customerInvoice-drop.vue';

let option = ref({
  
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 100,
  menu: false,
    header:false,
     showSummary: true,
  border: true,
  selectable: (row, index) => {
    return row.collectionStatus == 0;
  },
  column: [
    // {
    //   type: 'input',
    //   label: '客户名称',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080949446133484',
    // },
    // {
    //   type: 'input',
    //   label: '项目名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
   
    {
      type: 'input',
      label: '关联发票',
      span: 12,
      hide: true,
      display: true,
      prop: 'invoiceId',
    },
  
    {
      type: 'input',
      label: '计划名称',
      span: 12,
      display: true,
     
      overHidden: true,
      prop: 'planName',
      required: true,
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '离回款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      disabled: false,
      readonly: false,
      width: 110,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      label: '回款时间',
      prop: 'actualTime',
      overHidden: true,
      width: 110,
      order: 0,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      type: 'date',
      component: 'wf-daterange-search',
      // search: true,
      //   component: 'wf-contract-select',
    },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 24,
      width: 110,
      display: true,
      prop: 'planCollectionPrice',
    },
    // {
    //   type: 'number',
    //   label: '收款比例',
    //   controls: true,
    //   span: 24,
    //   width: 90,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'collectionRate',
    //   formatter: row => {
    //     return row.collectionRate + '%';
    //   },
    // },
    {
      type: 'number',
      label: '实际收款金额',
      controls: true,
      span: 24,
      width: 110,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollection',
    },
    // {
    //   type: 'number',
    //   label: '实际回款比例',
    //   controls: true,
    //   span: 24,
    //   width: 110,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'actualCollectionRate',
    //   formatter: row => {
    //     return row.actualCollectionRate + '%';
    //   },
    // },
    {
      type: 'input',
      label: '逾期',
      controls: true,
      span: 24,
      width: 110,
      addDisplay: false,
      editDisplay: false,
      prop: 'overDays',
      html: true,
      formatter: row => {
        if (row.overDays) {
          return `<span style="color:var(--el-color-danger)">${row.overDays}天</span>`;
        } else {
          return `---`;
        }
      },
    },
    // {
    //   label: '收款状态',
    //   prop: 'collectionStatus',
    // },
    {
      label: '收款状态',
      prop: 'collectionStatuss',
      slot: true,
      addDisplay: false,
      editDisplay: false,
      hide: true,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '未收款',
        },
        {
          value: 1,
          label: '部分收款',
        },
        {
          value: 2,
          label: '已收款',
        },
      ],
     
      multiple: true,
      searchSpan: 8,
      dataType: 'string',
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      width: 100,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
        {
          label: '无需开票',
          value: 10,
        },
        {
          label: '未关联',
          value: null,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    // {
    //   label: '业务员',
    //   prop: 'businessName',
    //   search: true,
    //   component: 'wf-user-drop',
    // },
   
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps(['sealContractName','sealContractId']);
const addUrl = '/api/vt-admin/sealContractPlanCollection/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractPlanCollection/page';
let route = useRoute()
let params = ref({
});

let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
let noCollectPrice = ref(0);
let actualPrice = ref(0);
watch(() => props.sealContractName,() => {
    onLoad()
})
function onLoad() {
    if(!props.sealContractName) return 
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
     selectType:1,
      
        sealContractId:props.sealContractId,
         year:props.year
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractPlanCollection/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        customerId: props.customerId,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noCollectPrice.value = res.data.data.noCollectPrice;
      actualPrice.value = res.data.data.actualPrice;
    });
}
let router = useRouter();

function getSummaries({ columns, data }) {
  let arr = [];
  let valueArr = [
    
   'planCollectionPrice',
   'actualCollection'
  ];
  columns.forEach((item, index) => {
    if (index === 0) {
      arr.push(
        <div>
          本页小计：
          {/* <br />
          合计： */}
        </div>
      );
    } else {
      if (valueArr.includes(item.property)) {
        const value = data.reduce((sum, i) => {
          sum = sum + i[item.property] * 1;
          return sum;
        }, 0);
        arr.push(
          <div>
            {isNaN(value) ? '' : value.toFixed(2)}
            {/* <br />
            {
              statisticData.value[
                item.property == 'purchaseCost'
                  ? 'purchasePrice'
                  : item.property == 'profit'
                  ? 'profitPrice'
                  : item.property
              ]
            } */}
          </div>
        );
      } else {
        arr.push('');
      }
    }
  });
  return arr;
}

</script>

<style lang="scss" scoped>
:deep(.planCollectionDays) {
  font-size: 25px;
  font-weight: bolder;
}
.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);
  color: var(--el-color-success);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}
</style>
