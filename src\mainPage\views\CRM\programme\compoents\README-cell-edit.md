# 单元格双击编辑功能

## 功能概述

为表格单元格添加了双击编辑功能，用户可以双击任意可编辑的单元格，弹出一个专门的编辑窗口进行数据修改。

## 实现的功能

### 1. 双击触发编辑
- 双击任意可编辑的单元格
- 弹出对应字段的编辑窗口
- 根据字段类型显示不同的输入控件

### 2. 支持的字段类型

#### 文本字段
- **产品名称** (`customProductName`) - 必填，最多200字符
- **品牌** (`productBrand`) - 最多100字符
- **规格型号** (`customProductSpecification`) - 最多200字符
- **单位** (`customUnit`) - 最多20字符

#### 多行文本字段
- **产品描述** (`customProductDescription`) - 最多500字符，4行文本域
- **备注** (`remark`) - 最多500字符，3行文本域

#### 数字字段
- **数量** (`number`) - 必填，数字输入框，最小值0，精度2位小数
- **设备单价** (`sealPrice`) - 数字输入框，最小值0，精度2位小数
- **人工单价** (`laborCost`) - 数字输入框，最小值0，精度2位小数
- **其他单价** (`qthsdj`) - 数字输入框，最小值0，精度2位小数
- **延保单价** (`ybhsdj`) - 数字输入框，最小值0，精度2位小数

#### 成本字段
- **设备成本单价** (`costPrice`) - 数字输入框，最小值0，精度2位小数
- **人工成本单价** (`rgcbdj`) - 数字输入框，最小值0，精度2位小数
- **其他成本单价** (`qtcbdj`) - 数字输入框，最小值0，精度2位小数
- **延保成本单价** (`ybcbdj`) - 数字输入框，最小值0，精度2位小数
- **专项成本** (`specialCostPrice`) - 数字输入框，最小值0，精度2位小数

### 3. 编辑窗口特性
- **可拖拽** - 窗口标题栏可拖拽移动
- **自动聚焦** - 打开时自动聚焦到输入框
- **字符限制** - 文本字段显示字符计数
- **数据验证** - 必填字段验证
- **键盘支持** - 支持ESC键关闭

### 4. 数据同步
- 编辑确认后自动更新原始数据
- 同步更新浮动编辑面板（如果正在编辑同一产品）
- 触发汇总数据重新计算
- 保持数据的响应式更新

## 使用方法

### 基本使用
1. 双击任意可编辑的单元格
2. 在弹出的编辑窗口中修改数据
3. 点击"确定"保存更改，或点击"取消"放弃更改

### 快捷操作
- **ESC键** - 关闭编辑窗口
- **Enter键** - 在单行输入框中确认编辑（多行文本框除外）

## 技术实现

### 组件结构
```
edit.vue (主组件)
├── ProductRow.vue (产品行组件)
│   └── @cell-double-click 事件
└── CellEditDialog.vue (编辑弹窗组件)
    ├── 动态表单字段
    └── 数据验证
```

### 事件流程
1. `ProductRow` 组件监听双击事件
2. 触发 `cell-double-click` 事件，传递字段信息
3. 主组件接收事件，设置弹窗状态
4. `CellEditDialog` 显示对应的编辑界面
5. 用户确认后触发 `confirm` 事件
6. 主组件更新数据并触发重新计算

### 数据处理
- 数字字段自动转换为数字类型
- 文本字段保持字符串类型
- 空值处理和默认值设置
- 响应式数据更新和缓存优化

## 注意事项

1. **只读模式** - 在详情查看模式下不允许编辑
2. **数据验证** - 必填字段和格式验证
3. **性能优化** - 使用防抖和缓存机制
4. **兼容性** - 与现有的内联编辑功能共存

## 扩展说明

如需添加新的字段类型支持：

1. 在 `ProductRow.vue` 中为对应单元格添加双击事件
2. 在 `CellEditDialog.vue` 中添加字段标签映射
3. 在 `CellEditDialog.vue` 模板中添加对应的表单控件
4. 在主组件的 `handleCellEditConfirm` 方法中添加数据类型处理逻辑
