<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu="{ row, size }">
      <el-button
        text
        type="primary"
        icon="el-icon-edit"
        :size="size"
        v-if="row.isCheck == 0"
        @click="$refs.crud.rowEdit(row, index)"
        >编辑</el-button
      >
      <el-button
        text
        type="primary"
        v-if="row.isCheck == 0"
        @click="$refs.crud.rowDel(row)"
        icon="el-icon-delete"
        :size="size"
        >删除</el-button
      >
    </template>
    <template #collectionFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
  planId: String,
  type: Number,
  humanId:String,
  projectId:String,
  menu:{
    type:Boolean,
    default:true
  }
});

watchEffect(() => {
  if (props.planId) {
    onLoad();
  }
});
watch(() => props.type ,() => {
  onLoad();
})
watch(() => props.humanId ,() => {
  onLoad();
})
let option = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  menu: props.menu,
  editBtn: false,
  delBtn: false,
  //   calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 150,
  menuWidth: 180,
  border: true,
  index: true,
  column: {
    userName: {
      label: '工人/团队姓名',
      disabled: true,
      width: 120,
      overHidden: true,
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 150,
      //   addDisplay: false,
      cell: true,
      type: 'number',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'select',
      width: 150,
      cell: true,
      dicData: [],
      allowCreate: true,
      filterable: true,
      // hide: true,

      props: {
        value: 'salary',
        label: 'salary',
      },
    },
    type: {
      label: '工人类型',
      width: 100,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
    },
    startTime: {
      label: '工作开始时间',

      type: 'datetime',
      width: 150,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 150,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },

    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      row: 2,
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/projectHours/remove?ids=';
const updateUrl = '/api/vt-admin/projectHours/update';
const tableUrl = '/api/vt-admin/projectHours/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        type: props.type,
        planId: props.planId,
        humanId:props.humanId,
        projectId:props.projectId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
