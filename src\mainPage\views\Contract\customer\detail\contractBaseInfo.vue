<template>
  <!-- 基本信息 -->
  <div v-if="form.id">
    <avue-form :option="props.contractType == 2 || props.contractType == 3 ? detailOption1 : detailOption"
      @submit="handleSubmit" :modelValue="props.form">
      <template #managementFeePoints>{{ parseFloat(props.form.managementFeePoints) }}%</template>
      <template #managementFeePointsPrice>{{ (props.form.contractTotalPrice * (props.form.managementFeePoints /
        100)).toFixed(2) }}</template>
      <template #contractFiles>
        <File :fileList="form.attachList || []"></File>
      </template>
      <template #customerInvoiceId>
        <span v-if="!form.customerInvoiceInfoVO">暂无</span>
        <el-form v-else inline label-position="left">
          <el-form-item label="客户公司名称：">
            <el-tag effect="plain">{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.invoiceCompanyName
            }}</el-tag>
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            <el-tag effect="plain">{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.ratepayerIdentifyNumber
            }}</el-tag>
          </el-form-item>
          <el-form-item label="开户银行：">
            <el-tag effect="plain">{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.bankName
            }}</el-tag>
          </el-form-item>
          <el-form-item label="初期末开票余额：">
            <el-text type="primary">{{
              form.customerInvoiceInfoVO &&
              parseFloat(form.customerInvoiceInfoVO.endTermNoInvoiceAmount).toLocaleString()
            }}</el-text>
          </el-form-item>

        </el-form>
      </template>

      <template #deepenStatus>
        <el-tag effect="plain" type="info" v-if="form.deepenStatus == 0">无需设计</el-tag>
        <el-tag effect="plain" type="info" v-if="form.deepenStatus == 1">未开始</el-tag>
        <el-tag effect="plain" type="warning" v-if="form.deepenStatus == 2">进行中</el-tag>
        <el-tag effect="plain" type="success" v-if="form.deepenStatus == 3">已完成</el-tag>
      </template>
      <template #contractTotalPrice>
        <el-text style="padding: 0px">
          {{ form.contractTotalPrice }}
          <span v-if="form.discountsPrice * 1 > 0">：(产品总额<span style="color: var(--el-color-danger);">{{ (
            form.contractTotalPrice * 1 + form.discountsPrice * 1).toFixed(2) }}</span> -
            折减金额<span style="color: var(--el-color-danger);">{{ form.discountsPrice }}</span> )</span>
        </el-text>
      </template>
      <template #cooperationCompanyName>
        <div style="display: flex;align-items: center;">
          <!-- 如果有合作伙伴ID，显示可点击的链接 -->
          <el-popover
            v-if="props.form.cooperationCompanyId"
            placement="right"
            :width="500"
            trigger="click"
            @before-enter="loadPartnerDetail(props.form.cooperationCompanyId)"
          >
            <template #reference>
              <el-link type="primary" style="margin-right: 8px;">
                {{ props.form.cooperationCompanyName }}
              </el-link>
            </template>
            <template #default>
              <div v-loading="partnerLoading">
                <h4 style="margin: 0 0 16px 0; padding-bottom: 8px; border-bottom: 1px solid #eee;">
                  合作伙伴详情
                </h4>
                <el-descriptions :column="1" border size="small">
                  <el-descriptions-item label="伙伴名称">
                    {{ partnerDetail.partnerName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系人">
                    {{ partnerDetail.contactPerson || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话">
                    {{ partnerDetail.contactPhone || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item v-if="partnerDetail.type == 0" label="业务员">
                    {{ partnerDetail.businessPersonName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开票伙伴">
                    {{ partnerDetail.invoiceCompanyName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="纳税人识别号">
                    {{ partnerDetail.ratepayerIdentifyNumber || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开户行名称">
                    {{ partnerDetail.bankName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="账号名称">
                    {{ partnerDetail.accountName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开户行账号">
                    {{ partnerDetail.bankAccount || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="备注">
                    {{ partnerDetail.remark || '-' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </el-popover>
          <span v-else>{{ props.form.cooperationCompanyName }}</span>

          <img title="伙伴挂靠"
            v-if="props.form.contractType == 5 && props.form.cooperationType == 1"
            src="../../../../assets/imgIcon/伙伴挂靠.png" style="height: 20px;width: 20px; margin-left: 8px;" alt="">
          <img title="挂靠伙伴" v-if="props.form.contractType == 5 && props.form.cooperationType == 0"
            src="../../../../assets/imgIcon/挂靠伙伴.png" style="height: 20px;width: 20px; margin-left: 8px;" alt="">
        </div>
      </template>
    </avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch, computed } from 'vue';
import { useRoute } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: {
    type: Object,
    default: () => {
      return { customerInvoiceInfoVO: {} };
    },
  },

  isEdit: Boolean,
  sealContractId: {
    type: String,
  },
  contractType: {
    type: String,
    default: () => '0'
  }
});
watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
  if (props.sealContractId) {
    getDetail();
    console.log('查询详情');
  }
});
// watch(() => props.sealContractId,() => {

//   if(props.sealContractId){
//     getDetail();
//   }
// },{
//   immediate: true
// })
const route = useRoute();
let detailOption = computed(() => {
  return {
    labelWidth: 150,
    submitBtn: false,
    detail: true,
    emptyBtn: false,
    group: [
      {
        label: '基本信息',
        prop: 'baseInfo',
        arrow: true,
        collapse: true,
        display: true,
        column: [
          {
            type: 'input',
            label: '关联报价',
            display: true,
            component: 'wf-quotation-select',
            prop: 'offerId',
          },
          {
            type: 'input',
            label: '关联商机',
            display: true,
            disabled: true,
            prop: 'businessOpportunityName',
          },
          {
            label: '客户名称',
            span: 12,
            display: true,
            prop: 'customerName',
          },
          {
            type: 'input',
            label: '客户地址',
            span: 12,
            display: true,
            prop: 'customerAddress',
          },
          {
            label: '关联联系人',
            type: 'input',
            prop: 'customerContact',
            component: 'wf-contact-select',
            placeholder: '请先选择报价',
            // disabled: true,
          },
          {
            type: 'input',
            label: '电话',
            span: 12,
            display: true,
            prop: 'customerPhone',
          },
          {
            label: '最终用户',
            prop: 'finalCustomer',
            display: props.form.contractType != 5,
            span: 24
          }
        ],
      },
      {
        label: '合同信息',
        prop: 'contractInfo',
        arrow: true,
        collapse: true,
        display: true,
        column: [
          {
            type: 'input',
            label: '合同编号',
            span: 12,
            display: true,
            readonly: true,
            placeholder: '自动生成',
            prop: 'contractCode',
          },
          {
            type: 'input',
            label: '合同名称',
            span: 12,
            display: true,
            prop: 'contractName',
            required: true,
            rules: [
              {
                required: true,
                message: '项目名称必须填写',
              },
            ],
          },
          {
            type: 'input',
            label: '对方订单编号',
            span: 12,
            display: true,
            prop: 'customerOrderNumber',
          },
          {
            type: 'input',
            label: '合同金额',
            span: 12,
            display: true,
            prop: 'contractTotalPrice',
            required: true,
            rules: [
              {
                required: true,
                message: '合同金额必须填写',
              },
            ],
          },
          {
            label: '管理费点数',
            prop: 'managementFeePoints',
            span: 12,
            display: props.form.contractType == 5
          },
          {
            label: '管理费金额',
            prop: 'managementFeePointsPrice',
            span: 12,
            display: props.form.contractType == 5
          },
          {
            label: '合作伙伴',
            prop: 'cooperationCompanyName',

            span: 24,
            display: props.form.contractType == 5,


          },
          {
            label: '是否开票',
            type: 'switch', display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
            prop: 'isNeedInvoice',
            value: 1,
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            control: val => {
              return {
                invoiceType: {
                  display: val == 1 && !(props.form.contractType == 5 && props.form.cooperationType == 0),
                },
                billingCompany: {
                  display: val == 1 && !(props.form.contractType == 5 && props.form.cooperationType == 0),
                },
                taxRate: {
                  display: val == 1 && !(props.form.contractType == 5 && props.form.cooperationType == 0),
                },
              };
            },
          },
          {
            label: '是否预订单',
            type: 'switch',
            prop: 'isPreOrder',
            value: 0,
            display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
          },
          {
            label: '付款期限',
            type: 'radio',
            span: 24, display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
            prop: 'paymentDeadline',
            dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
            props: {
              value: 'id',
              label: 'dictValue',
            },
          },
          {
            label: '结算方式',
            type: 'radio',
            prop: 'settlementMethod', display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
            span: 24,
            dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
            props: {
              value: 'id',
              label: 'dictValue',
            },
          },
          
          {
            type: 'select',
            label: '发票类型',
            dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
            cascader: [],
            span: 12,
            search: true,
           
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            prop: 'invoiceType', display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
          },
          {
            label: '开票公司',
            type: 'select',

            prop: 'billingCompany',

            props: {
              label: 'companyName',
              value: 'id',
            },
            dicFormatter: res => {
              return res.data.records;
            },

            overHidden: true,
            cell: false,
            dicUrl: '/api/vt-admin/company/page?size=100', display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
          },
          {
            label: '税率',
            type: 'select',
            width: 80,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },

            cell: false,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            rules: [
              {
                required: true,
                message: '请选择税率',
                trigger: 'blur',
              },
            ], display: !(props.form.contractType == 5 && props.form.cooperationType == 0),
          },
          {
            type: 'date',
            label: '签订日期',
            span: 12,
            display: true,
            prop: 'signDate',
            required: true,

            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            rules: [
              {
                required: true,
                message: '签订日期必须填写',
              },
            ],
          },
          {
            type: 'date',
            label: '客户要求交付日期',
            span: 12,
            display: true,
            prop: 'contractDeliveryDate',
            required: true,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            rules: [
              {
                required: true,
                message: '签订日期必须填写',
              },
            ],
          },
          {
            label: '确认附件',
            prop: 'contractFiles',
            type: 'upload',
            value: [],
            dataType: 'object',
            loadText: '附件上传中，请稍等',
            span: 12,
            slot: true,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'id',
              name: 'originalName',
              // home: 'https://www.w3school.com.cn',
            },
            action: '/blade-resource/attach/upload',
          },

        ],
      },
      {
        label: '送货信息',
        prop: 'distributionInfo',
        arrow: true,
        collapse: true,
        display: props.form.cooperationType != 1,
        column: [
          {
            label: '送货方式',
            type: 'radio',
            prop: 'distributionMethod',
            span: 12,
            dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
            props: {
              value: 'dictKey',
              label: 'dictValue',
            },
            control: val => {
              console.log(val);
              return {
                distributionUser: {
                  label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
                },
                deliveryDate: {
                  label:
                    val == 3
                      ? '送货日期'
                      : val == 1
                        ? '发货日期'
                        : val == 4
                          ? '交付日期'
                          : '自提日期',
                },
                deliveryAddress: {
                  label: val == 3 ? '收货地址' : val == 1 ? '收货地址' : val == 4 ? '交付地址' : '',
                  display: val != 2 ? true : false,
                },
              };
            },
          },
          {
            type: 'date',
            label: '交付日期',
            span: 12,
            display: true,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            prop: 'deliveryDate',
            required: true,
            rules: [
              {
                required: true,
                message: '交付日期必须填写',
              },
            ],
          },
          {
            type: 'input',
            label: '交付地址',
            span: 24,
            display: true,
            prop: 'deliveryAddress',
          },
          {
            label: '关联联系人',
            type: 'input',
            prop: 'contact',
            component: 'wf-contact-select',
            placeholder: '请先选择报价',
            // disabled: true,
          },
          {
            type: 'input',
            label: '电话',
            span: 12,
            display: true,
            prop: 'contactPhone',
          },
          {
            type: 'input',
            label: '开票信息',
            span: 24,
            display: true,
            prop: 'customerInvoiceId',
          },
          {
            type: 'textarea',
            label: '备注',
            span: 24,
            display: true,
            prop: 'remark',
          },
        ],
      },
    ],
  }
});
let detailOption1 = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [

        {
          label: '客户名称',
          span: 12,
          display: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },

      ],
    },
    {
      label: '合同信息',
      prop: 'contractInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '合同编号',
          span: 12,
          display: true,
          readonly: true,
          placeholder: '自动生成',
          prop: 'contractCode',
        },
        {
          type: 'input',
          label: '项目名称',
          span: 12,
          display: true,
          prop: 'contractName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },

        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '合同金额必须填写',
            },
          ],
        },
        {
          label: '是否开票',
          type: 'switch',
          prop: 'isNeedInvoice',
          value: 1,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },

        {
          label: '付款期限',
          type: 'radio',
          span: 24,
          prop: 'paymentDeadline',
          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },


        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          type: 'date',
          label: '客户要求交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '确认附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },

      ],
    },
    {
      label: '服务信息',
      prop: 'wokerOrderInfo',
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },
        {
          label: '服务联系人',
          prop: 'finalCustomerConcat',
        },
        {
          label: '服务联系电话',
          prop: 'finalCustomerPhone',
        },
        {
          label: '服务地址',
          prop: 'deliveryAddress',
          span: 24
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();

// 合作伙伴详情数据
let partnerDetail = ref({});
let partnerLoading = ref(false);

// 加载合作伙伴详情
function loadPartnerDetail(partnerId) {
  if (!partnerId) return;

  partnerLoading.value = true;
  axios
    .get('/api/vt-admin/partner/detail', {
      params: {
        id: partnerId,
      },
    })
    .then(res => {
      if (res.data.code === 200 && res.data.data) {
        partnerDetail.value = res.data.data;
      }
    })
    .catch(() => {
      partnerDetail.value = {};
    })
    .finally(() => {
      partnerLoading.value = false;
    });
}

function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: route.query.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}

function getDetail() {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: props.sealContractId,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    });
}
</script>

<style lang="scss" scoped></style>
