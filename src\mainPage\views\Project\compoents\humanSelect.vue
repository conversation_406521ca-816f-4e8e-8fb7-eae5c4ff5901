<template>
  <el-input @click="dialogVisible = true" :value="currentName" readonly placeholder="请选择人员">
    <template #append
      ><el-button
        style="cursor: pointer"
        type="primary"
        icon="plus"
        size="default"
        @click="openAddDialog"
      ></el-button>
    </template>
  </el-input>
  <el-dialog
    title="选择工人"
    top="2vh"
    append-to-body
    width="80%"
    class="avue-dialog avue-dialog--top"
    v-model="dialogVisible"
  >
    <!-- 提示   输入工人或公司名字搜索数据 -->
     <el-alert
      title="提示"
      type="info"
      description="请输入工人或公司名字搜索数据"
      style="margin-bottom: 10px"
      :closable="false"
    ></el-alert>
    <el-radio-group v-model="form.type" @change="onLoad" style="margin-bottom: 10px">
      <el-radio-button label="0">本地个人</el-radio-button>
      <el-radio-button label="1">本地公司</el-radio-button>
      <el-radio-button label="2">外地个人</el-radio-button>
      <el-radio-button label="3">外地公司</el-radio-button>
    </el-radio-group>

    <avue-crud
      :option="form.type == 0 || form.type == 2 ? selfOption : companyOption"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      @row-click="rowClick"
    >
      <template #specialty-form="{ type }">
        <el-autocomplete
          style="width: 100%"
          v-model="form.specialty"
          value-key="value"
          v-if="type == 'add'"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入强项"
        />
        <span v-else>{{ form.specialty }}</span>
      </template>
      <!-- <template #name="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.name }}</el-link>
      </template> -->
      <template #radio="{ row }">
        <el-radio v-model="selectRowId" :label="row.id">{{}}</el-radio>
      </template>
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleClick" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  <el-drawer
    title="添加工人"
    top="2vh"
    append-to-body
    size="60%"
    class="avue-dialog avue-dialog--top"
    v-model="addDialogVisible"
  >
    <el-radio-group v-model="form.type" @change="onLoad" style="margin-bottom: 10px">
      <el-radio-button label="0">本地个人</el-radio-button>
      <el-radio-button label="1">本地公司</el-radio-button>
      <el-radio-button label="2">外地个人</el-radio-button>
      <el-radio-button label="3">外地公司</el-radio-button>
    </el-radio-group>
    <avue-form :option="addFormOption" ref="addFormRef" v-model="form" @submit="addSubmit">
      <template #specialty="{ type }">
        <el-autocomplete
          :disabled="type == 'view'"
          style="width: 100%"
          v-model="form.specialty"
          value-key="value"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入技能标签"
        />
      </template>
    </avue-form>

    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="$refs.addFormRef?.submit" type="primary">确 定</el-button>
    </div>
  </el-drawer>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const emits = defineEmits(['select']);
let baseUrl = '/api/blade-system/region/lazy-tree';
let selfOption = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  menu: false,
  column: [
    // {
    //   label: '姓名',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label: '',
      prop: 'radio',
    },
    {
      label: '姓名',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      search: true,
    },
    {
      label: '年龄',
      prop: 'age',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '性别',
      prop: 'sex1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      dicData: [
        {
          label: '男',
          value: 1,
        },
        {
          label: '女',
          value: 0,
        },
      ],
      props: {
        label: 'label',
        value: 'value',
      },
      formatter: (row, value) => {
        return row.sex == 1 ? '男' : '女';
      },
    },

    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },

    {
      label: '技能标签',
      prop: 'specialty',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      overHidden: true,
    },

    {
      label: '态度标签',
      prop: 'attitudeLabel',
      type: 'input',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },

    {
      label: '所在区域',
      prop: 'address',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return `${row.provinceName}/${row.cityName}/${row.areaName}`;
      },
      overHidden: true,
    },
    {
      label: '是否保险',
      prop: 'isInsurance',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '是否开票',
      prop: 'isInvoice1',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],

      formatter: (row, value) => {
        return row.$isInsurance;
      },
    },
    {
      label: '工种',
      prop: 'workType',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      formatter: row => {
        return row.salaryVOS?.map(item => item.workTypeName).join('/');
      },
      type: 'select',
    },
    {
      label: '工价(元/天)',
      prop: 'salary',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      placeholder: '请输入工价',
      formatter: row => {
        console.log(row);
        return row.salaryVOS?.map(item => item.salary).join('/');
      },
    },
    // {
    //   label: '开户行',
    //   prop: 'openBank1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.openBank;
    //   },
    // },
    // {
    //   label: '银行账号',
    //   prop: 'bankNumber1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.bankNumber;
    //   },
    // },
    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
          searchSpan: 6,
        },
        {
          label: '性别',
          prop: 'sex',
          type: 'radio',
          dicData: [
            {
              label: '男',
              value: 1,
            },
            {
              label: '女',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '年龄',
          prop: 'age',
          width: 250,
          overHidden: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },
        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },

        {
          label: '是否开票',
          prop: 'isInvoice',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
          control: val => {
            return {
              invoiceType: {
                display: val == 1,
              },
              taxRate: {
                display: val == 1,
              },
            };
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '所在区域',
          prop: 'province_city_area',
          search: true,
          hide: true,
          searchSpan: 5,
          type: 'cascader',
          props: {
            label: 'title',
            value: 'id',
          },

          lazy: true,
          lazyLoad(node, resolve) {
            let stop_level = 2;
            let level = node.level;
            let data = node.data || {};
            let id = data.id;
            let list = [];
            let callback = () => {
              resolve(
                (list || []).map(ele => {
                  return Object.assign(ele, {
                    leaf: level >= stop_level || !ele.hasChildren,
                  });
                })
              );
            };
            axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
              list = res.data.data;
              callback();
            });
          },
        },
        {
          label: '详细地址',
          prop: 'address',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
      ],
    },
    {
      label: '工种信息',
      column: [
        {
          type: 'dynamic',
          hide: true,
          span: 24,
          prop: 'salaryDTOList',
          children: {
            column: [
              {
                label: '工种',
                prop: 'workType',
                dicUrl: '/blade-system/dict-biz/dictionary?code=workType',
                props: {
                  label: 'dictValue',
                  value: 'id',
                },
                type: 'select',
              },
              {
                label: '工价(元/天)',
                prop: 'salary',
                placeholder: '请输入工价',
              },
            ],
          },
        },
      ],
    },
    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let companyOption = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 100,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  column: [
    // {
    //   label: '公司名称',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label: '',
      prop: 'radio',
    },
    {
      label: '公司名称',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      overHidden: true,
      editDisplay: false,
      viewDisplay: false,
      searchSpan: 6,
      search: true,
    },
    // {
    //   label: '性别',
    //   prop: 'sex1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   dicData: [
    //     {
    //       label: '男',
    //       value: 1,
    //     },
    //     {
    //       label: '女',
    //       value: 0,
    //     },
    //   ],
    //   props: {
    //     label: 'label',
    //     value: 'value',
    //   },
    //   formatter: (row, value) => {
    //     return row.sex == 1 ? '男' : '女';
    //   },
    // },
    {
      label: '公司地址',
      prop: 'address',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      formatter: (row, value) => {
        return row.address;
      },
    },
    {
      label: '联系人',
      prop: 'contact',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },
    {
      label: '团队管理标签',
      prop: 'teamManageLabels',
      type: 'input',
      addDisplay: false,
      overHidden: true,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '交付能力标签',
      prop: 'teamPaymentLabels',
      type: 'input',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
    },
    // {
    //   label: '技能标签',
    //   prop: 'specialty',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   overHidden:true,
    // },
    // {
    //   label: '是否保险',
    //   prop: 'isInsurance',
    //   type: 'select',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   type: 'radio',
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },

    // {
    //   label: '态度标签',
    //   prop: 'attitudeLabel',
    //   type: 'input',

    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    // },
    {
      type: 'select',
      label: '发票资料',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      // search: true,

      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      prop: 'invoiceType',
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      type: 'dynamic',
      hide: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      children: {
        column: [
          {
            label: '工种',
            prop: 'workType1',
            formatter: (row, value) => {
              return row.$workType;
            },
          },
          {
            label: '工价',
            prop: 'salary1',
            formatter: (row, value) => {
              return row.salary;
            },
          },
        ],
      },
    },

    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '公司名称',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '联系人',
          prop: 'contact',
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },
        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },
        {
          label: '团队管理标签',
          prop: 'teamManageLabels',
          type: 'input',
        },
        {
          label: '交付能力标签',
          prop: 'teamPaymentLabels',
          type: 'input',
        },
        {
          label: '所在区域',
          prop: 'province_city_area',
          search: true,
          hide: true,
          searchSpan: 5,
          type: 'cascader',
          props: {
            label: 'title',
            value: 'id',
          },
          lazy: true,
          lazyLoad(node, resolve) {
            let stop_level = 2;
            let level = node.level;
            let data = node.data || {};
            let id = data.id;
            let list = [];
            let callback = () => {
              resolve(
                (list || []).map(ele => {
                  return Object.assign(ele, {
                    leaf: level >= stop_level || !ele.hasChildren,
                  });
                })
              );
            };
            axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
              list = res.data.data;
              callback();
            });
          },
        },
        {
          label: '公司地址',
          prop: 'address',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
      ],
    },

    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let form = ref({
  type: '0',
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/humanResource/save';
const delUrl = '/api/vt-admin/humanResource/remove?ids=';
const updateUrl = '/api/vt-admin/humanResource/update';
const tableUrl = '/api/vt-admin/humanResource/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  if (!params.value.name) return;
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  const typeData = {
    userType: form.value.type == 0 || form.value.type == 1 ? null : form.value.type == 2 ? 0 : 1,
    type: form.value.type == 0 ? 0 : form.value.type == 1 ? 1 : 2,
  };
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        ...typeData,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function querySearchAsync(value, cb) {
  axios.get('/api/vt-admin/humanResource/getSpecialtyList').then(res => {
    console.log(res.data.data);
    cb(
      res.data.data.map(item => {
        return {
          value: item,
        };
      })
    );
  });
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.salaryDTOList = form.value.salaryVOS;
  }
  done();
}
let selectRowId = ref('');
let selectRow = ref({});
function rowClick(row) {
  selectRowId.value = row.id;
  selectRow.value = row;
}
let dialogVisible = ref(false);
function open() {
  dialogVisible.value = true;
}
let currentName = ref(null);
function handleClick() {
  emits('select', selectRow.value);
  currentName.value = selectRow.value.name;
  dialogVisible.value = false;
}
function validateName(rule, value, callback) {
  axios
    .get('/api/vt-admin/humanResource/existName?', {
      params: {
        name: value,
        type: 0,
        id: form.value.id || null,
      },
    })
    .then(res => {
      if (res.data.data == 1) {
        callback(new Error('资源库已经存在此人，请重新输入'));
      } else {
        callback();
      }
    });
}
let addDialogVisible = ref(false);
let addFormOption = computed(() => {
  return {
    submitBtn: false,
    emptyBtn: false,
    labelWidth: 120,
    group:
      form.value.type == 0 || form.value.type == 2
        ? [
            {
              label: '基本信息',
              column: [
                {
                  label: '姓名',
                  prop: 'name',
                  width: 250,
                  overHidden: true,
                  search: true,
                  rules: [
                    {
                      validator: validateName,
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '性别',
                  prop: 'sex',
                  type: 'radio',
                  dicData: [
                    {
                      label: '男',
                      value: 1,
                    },
                    {
                      label: '女',
                      value: 0,
                    },
                  ],
                  props: {
                    label: 'label',
                    value: 'value',
                  },
                },
                {
                  label: '年龄',
                  prop: 'age',
                  width: 250,
                  overHidden: true,
                },
                {
                  label: '联系电话',
                  prop: 'phone',
                },

                {
                  label: '技能标签',
                  prop: 'specialty',
                  type: 'input',
                },

                {
                  label: '态度标签',
                  prop: 'attitudeLabel',
                  type: 'input',
                },
                {
                  label: '是否开票',
                  prop: 'isInvoice',
                  type: 'select',
                  type: 'radio',
                  dicData: [
                    {
                      label: '是',
                      value: 1,
                    },
                    {
                      label: '否',
                      value: 0,
                    },
                  ],
                  props: {
                    label: 'label',
                    value: 'value',
                  },
                  control: val => {
                    return {
                      invoiceType: {
                        display: val == 1,
                      },
                      taxRate: {
                        display: val == 1,
                      },
                    };
                  },
                },
                {
                  type: 'select',
                  label: '发票类型',
                  dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
                  cascader: [],
                  span: 12,
                  // search: true,
                  display: true,
                  props: {
                    label: 'dictValue',
                    value: 'id',
                    desc: 'desc',
                  },
                  prop: 'invoiceType',
                },
                {
                  label: '税率',
                  type: 'select',
                  width: 80,
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  cell: false,
                  prop: 'taxRate',
                  dicUrl: '/blade-system/dict/dictionary?code=tax',
                },
                {
                  label: '是否保险',
                  prop: 'isInsurance',
                  type: 'select',
                  type: 'radio',
                  dicData: [
                    {
                      label: '是',
                      value: 1,
                    },
                    {
                      label: '否',
                      value: 0,
                    },
                  ],
                  props: {
                    label: 'label',
                    value: 'value',
                  },
                },
                {
                  label: '所在区域',
                  prop: 'province_city_area',
                  search: true,
                  display: form.value.type == 2,
                  searchSpan: 5,
                  type: 'cascader',
                  props: {
                    label: 'title',
                    value: 'id',
                  },
                  lazy: true,
                  lazyLoad(node, resolve) {
                    let stop_level = 2;
                    let level = node.level;
                    let data = node.data || {};
                    let id = data.id;
                    let list = [];
                    let callback = () => {
                      resolve(
                        (list || []).map(ele => {
                          return Object.assign(ele, {
                            leaf: level >= stop_level || !ele.hasChildren,
                          });
                        })
                      );
                    };
                    axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
                      list = res.data.data;
                      callback();
                    });
                  },
                },
                {
                  label: '详细地址',
                  prop: 'address',
                },
                {
                  label: '引进人',
                  prop: 'createUser',
                  component: 'wf-user-select',
                  disabled: true,
                  value: proxy.$store.getters.userInfo.user_id,
                },
                {
                  label: '工种',
                  prop: 'workType',
                  dicUrl: '/blade-system/dict-biz/dictionary?code=workType',
                  props: {
                    label: 'dictValue',
                    value: 'id',
                  },
                  type: 'select',
                },
                {
                  label: '工价(元/天)',
                  prop: 'salary',
                  placeholder: '请输入工价',
                },
                {
                  label: '备注',
                  prop: 'remark',
                  type: 'textarea',
                  overHidden: true,
                  span: 24,
                },
                {
                  label: '附件',
                  prop: 'fileIds',
                  type: 'upload',
                  dataType: 'object',
                  loadText: '附件上传中，请稍等',
                  span: 24,
                  viewDisplay: false,
                  slot: true,
                  // align: 'center',
                  propsHttp: {
                    res: 'data',
                    url: 'id',
                    name: 'originalName',
                    // home: 'https://www.w3school.com.cn',
                  },
                  action: '/blade-resource/attach/upload',
                },
              ],
            },

            {
              label: '银行信息',
              column: [
                {
                  label: '开户行',
                  prop: 'openBank',
                },
                {
                  label: '银行账号',
                  prop: 'bankNumber',
                },
              ],
            },
          ]
        : [
            {
              label: '基本信息',
              column: [
                {
                  label: '公司名称',
                  prop: 'name',
                  width: 250,
                  overHidden: true,
                  search: true,
                  rules: [
                    {
                      validator: validateName,
                      trigger: 'blur',
                    },
                  ],
                },
                {
                  label: '联系人',
                  prop: 'contact',
                },
                {
                  label: '联系电话',
                  prop: 'phone',
                },

                {
                  type: 'select',
                  label: '发票类型',
                  dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
                  cascader: [],
                  span: 12,
                  // search: true,
                  display: true,
                  props: {
                    label: 'dictValue',
                    value: 'id',
                    desc: 'desc',
                  },
                  prop: 'invoiceType',
                },
                {
                  label: '税率',
                  type: 'select',
                  width: 80,
                  props: {
                    label: 'dictValue',
                    value: 'dictKey',
                  },
                  cell: false,
                  prop: 'taxRate',
                  dicUrl: '/blade-system/dict/dictionary?code=tax',
                },
                {
                  label: '是否保险',
                  prop: 'isInsurance',
                  type: 'select',
                  type: 'radio',
                  dicData: [
                    {
                      label: '是',
                      value: 1,
                    },
                    {
                      label: '否',
                      value: 0,
                    },
                  ],
                  props: {
                    label: 'label',
                    value: 'value',
                  },
                },

                {
                  label: '团队管理标签',
                  prop: 'teamManageLabels',
                  type: 'input',
                },
                {
                  label: '交付能力标签',
                  prop: 'teamPaymentLabels',
                  type: 'input',
                },
                {
                  label: '所在区域',
                  prop: 'province_city_area',
                  search: true,
                  display: form.value.type == 3,
                  searchSpan: 5,
                  type: 'cascader',
                  props: {
                    label: 'title',
                    value: 'id',
                  },
                  lazy: true,
                  lazyLoad(node, resolve) {
                    let stop_level = 2;
                    let level = node.level;
                    let data = node.data || {};
                    let id = data.id;
                    let list = [];
                    let callback = () => {
                      resolve(
                        (list || []).map(ele => {
                          return Object.assign(ele, {
                            leaf: level >= stop_level || !ele.hasChildren,
                          });
                        })
                      );
                    };
                    axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
                      list = res.data.data;
                      callback();
                    });
                  },
                },
                {
                  label: '公司地址',
                  prop: 'address',
                  span: 12,
                },
                {
                  label: '备注',
                  prop: 'remark',
                  type: 'textarea',
                  overHidden: true,
                  span: 24,
                },
                {
                  label: '附件',
                  prop: 'fileIds',
                  type: 'upload',
                  dataType: 'object',
                  loadText: '附件上传中，请稍等',
                  span: 24,
                  viewDisplay: false,
                  slot: true,
                  // align: 'center',
                  propsHttp: {
                    res: 'data',
                    url: 'id',
                    name: 'originalName',
                    // home: 'https://www.w3school.com.cn',
                  },
                  action: '/blade-resource/attach/upload',
                },
              ],
            },

            {
              label: '银行信息',
              column: [
                {
                  label: '开户行',
                  prop: 'openBank',
                },
                {
                  label: '银行账号',
                  prop: 'bankNumber',
                },
              ],
            },
          ],
  };
});
let addFormRef = ref(null);
function addSubmit(form, done, loading) {
  const data = {
    ...form,
    type: form.type == 0 ? 0 : form.type == 1 ? 1 : 2,
    userType: form.type == 2 ? 0 : form.type == 3 ? 1 : null,
    files: form.fileIds?.map(item => item.value).join(','),
    salaryDTOList: [
      {
        workType: form.workType,
        salary: form.salary,
      },
    ],
    province: form.province_city_area && form.province_city_area[0],
    city: form.province_city_area && form.province_city_area[1],
    area: form.province_city_area && form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/humanResource/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        currentName.value = res.data.data.name;
        emits('select', res.data.data);
        done();
        addFormRef.value.resetForm();
        addDialogVisible.value = false;
      }
    })
    .catch(err => {
      done();
    });
}
function openAddDialog() {
  addDialogVisible.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
