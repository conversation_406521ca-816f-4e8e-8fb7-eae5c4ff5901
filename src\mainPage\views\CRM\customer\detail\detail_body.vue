<template>
  <basic-container shadow="never" style="height: 100%">
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.customerName }}</h3>
        <div class="right"></div>
        <el-form inline>
          <el-form-item label="业务员">
            <el-tag>{{ form.businessPersonName || '---' }}</el-tag>
            <el-icon
              style="cursor: pointer; margin-left: 5px"
              @click="edit"
              v-if="
                (props.type == 0 &&
                  form.businessPerson.split(',').includes($store.getters.userInfo.user_id)) ||
                props.type == 5
              "
            >
              <Edit></Edit>
            </el-icon>
          </el-form-item>
          <el-form-item label="商务协作人">
            <el-tag>{{ form.assistantName || '---' }}</el-tag>
            <el-icon
              style="cursor: pointer; margin-left: 5px"
              @click="edit"
              v-if="
                (props.type == 0 &&
                  form.businessPerson.split(',').includes($store.getters.userInfo.user_id)) ||
                props.type == 5
              "
            >
              <Edit></Edit>
            </el-icon>
          </el-form-item>
          <el-form-item label="专项业务员">
            <el-tag>{{ form.specialBusinessPersonName || '---' }}</el-tag>
            <el-icon
              style="cursor: pointer; margin-left: 5px"
              @click="edit"
              v-if="
                (props.type == 0 &&
                  form.businessPerson.split(',').includes($store.getters.userInfo.user_id)) ||
                props.type == 5
              "
            >
              <Edit></Edit>
            </el-icon>
          </el-form-item>
          <el-form-item label="推荐人">
            <el-tag>{{ form.referrer || '---' }}</el-tag>
            <el-icon
              style="cursor: pointer; margin-left: 5px"
              @click="edit"
              v-if="
                (props.type == 0 &&
                  form.businessPerson.split(',').includes($store.getters.userInfo.user_id)) ||
                props.type == 5
              "
            >
              <Edit></Edit>
            </el-icon>
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type=""
          icon="el-icon-right"
          @click="transformCustomer(1)"
          v-if="
            props.type == 3 &&
            $store.getters.permission.customer_receive &&
            form.businessPerson.split(',').includes($store.getters.userInfo.user_id)
          "
          >领取客户</el-button
        >
        <el-button
          type=""
          icon="el-icon-right"
          @click="translateOther"
          v-if="
            (props.type == 3 && $store.getters.permission.customer_transferHim) ||
            (props.type == 0 &&
              form.businessPerson.split(',').includes($store.getters.userInfo.user_id))
          "
          >转移他人</el-button
        >
        <el-button
          type=""
          icon="el-icon-right"
          @click="transformCustomer(3)"
          v-if="
            props.type == 0 &&
            form.businessPerson.split(',').includes($store.getters.userInfo.user_id)
          "
          >转移公共池</el-button
        >
        <el-button
          type=""
          icon="el-icon-right"
          @click="transformCustomer(2)"
          v-if="
            (props.type == 3 && $store.getters.permission.customer_transfer_invalid) ||
            (props.type == 0 &&
              form.businessPerson.split(',').includes($store.getters.userInfo.user_id))
          "
          >转移失效池</el-button
        >
        <el-button
          type="primary"
          icon="Edit"
          v-if="
            !isEdit &&
            currentIndex.index == 0 &&
            props.type == 0 &&
            form.businessPerson.split(',').includes($store.getters.userInfo.user_id)
          "
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button icon="close" v-else-if="isEdit" @click="isEdit = false">取消</el-button>
      </div>
    </div>
    <!-- <div style="display: flex">
      <div class="left_content">
        <div class="main_box">
          <div
            class="item"
            v-for="(item, index) in tabArr"
            :class="{ active: currentIndex == index }"
            @click="handleClick(index)"
          >
            <div class="arrow"></div>
            {{ item }}
          </div>
        </div>
      </div>
      <div style="width: calc(100% - 100px)">
        <component
          :is="currentCompoent"
          :form="form"
          :isEdit="isEdit"
          @getDetail="getDetail"
        ></component>
      </div>
    </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <BaseInfo
            :form="form"
            v-loading="loading"
            :customer-id="props.id"
            :isEdit="isEdit"
            :key="form.id"
            @getDetail="getDetail"
          ></BaseInfo>
        </div>
        <el-tabs v-model="baseName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="IT现状" name="ItCurrent">
            <ItCurrent
              :type="props.type"
              :id="form.id"
              :networkTopologyList="form.networkTopologyList"
            ></ItCurrent>
          </el-tab-pane>
          <el-tab-pane label="开票信息" name="InvoiceRecord"
            ><InvoiceRecord :type="props.type" :id="form.id"></InvoiceRecord
          ></el-tab-pane>
          <el-tab-pane label="平台信息" :type="props.type" name="Platform"
            ><Platform :type="props.type" :id="form.id"></Platform
          ></el-tab-pane>
          <el-tab-pane label="附件" name="file"
            ><File :type="props.type" :id="form.id"></File
          ></el-tab-pane>
          <el-tab-pane label="联系人" name="Contact"
            ><Contact :type="props.type" :id="form.id"></Contact
          ></el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :is="tabArray.find(item => item.name == activeName).component"
      :info="{
        customerId: form.id,
        type: props.type,
      }"
      :customerId="props.id"
      :url="'/vt-admin/customerFollow/pageForDetail'"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import ItCurrent from './itCurrent.vue';
import InvoiceRecord from './invoiceRecord.vue';
import Platform from './platformInfo.vue';
import File from './file.vue';
import Contact from './contactPerson.vue';
import BaseInfo from './baseInfo.vue';
// 商机
import Business from './business.vue';
// 跟进
import Follow from '../../follow/allFollow.vue';
// 报价
import Quatation from './quatation.vue';
// 我的合同
import Contract from './contract.vue';

// 开票
import Invioce from './invoice.vue';
// 费用
import Cost from './cost.vue';
// 回款
import Collection from './projectCollection.vue';
// 产品
import Product from './product.vue';
// 日志
import OperationLog from './OperationLog.vue';
// 续费产品
import productRenew from './productRenew.vue';
// 维保服务
import sealContractQualityGuarantee from './sealContractQualityGuarantee.vue';
// 维修单
import repair from './repair.vue';
// 工单
import wokerOrder from './wokerOrder.vue';

import { ElMessage } from 'element-plus';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
const props = defineProps(['type', 'id']);
onMounted(() => {
  getDetail();
});

watch(
  () => props.id,
  () => {
    activeName.value = 'baseInfo';
    getDetail();
  }
);
const conpletArr = [
  'Business',
  'Follow',
  'Quatation',
  'Contract',
  'Purchase',
  'Invioce',
  'Cost',
  'Collection',
  'Product',
  'OperationLog',
  'productRenew',
  'sealContractQualityGuarantee',
  'repair',
  'wokerOrder'
];
const tabArray = [
  {
    label: '商机',
    name: 'Business',
    component: Business,
  },
  // {
  //   label: '项目信息',
  //   name: 'ProjectInfo',
  //   component: Business,
  // },
  {
    label: '报价',
    name: 'Quatation',
    component: Quatation,
  },
  // {
  //   label: '投标',
  //   name: 'Bid',
  //   component: Business,
  // },
  {
    label: '合同',
    name: 'Contract',
    component: Contract,
  },
  {
    label: '对账单',
    name: 'Product',
    component: Product,
  },
  // {
  //   label: '采购记录',
  //   name: 'Purchase',
  //   component: Purchase,
  // },
  // {
  //   label: '跟单记录',
  //   name: 'Documentary',
  //   component: Business,
  // },
  {
    label: '开票管理',
    name: 'Invioce',
    component: Invioce,
  },
  {
    label: '回款管理',
    name: 'Collection',
    component: Collection,
  },
  {
    label: '费用管理',
    name: 'Cost',
    component: Cost,
  },

  {
    label: '续费产品',
    name: 'productRenew',
    component: productRenew,
  },
  // {
  //   label: '维保服务',
  //   name: 'sealContractQualityGuarantee',
  //   component: sealContractQualityGuarantee,
  // },
  {
    label: '维修单',
    name: 'repair',
    component: repair,
  },
  {
    label: '工单',
    name: 'wokerOrder',
    component: wokerOrder,
  },
  // {
  //   label: '提醒',
  //   name: 'Remind',
  //   component: Business,
  // },
  // {
  //   label: '投诉记录',
  //   name: 'Complaint',
  //   component: Business,
  // },
  // {
  //   label: '跟进记录',
  //   name: 'Follow',
  //   component: Follow,
  // },
  {
    label: '操作日志',
    name: 'OperationLog',
    component: OperationLog,
  },
];
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      const { provinceCode, cityCode, areaCode, networkTopologyList } = res.data.data;
      nextTick(() => {
       form.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        registeredCapital: Number(res.data.data.registeredCapital),
      };
      });
     
    });
}
let moreInfoDetail = ref(false);
function loadMore() {
  moreInfoDetail.value = true;
}
let tabArr = ['基本信息', 'it现状', '开票信息', '平台信息', '相关附件', '联系人'];
let compoentArr = [BaseInfo, ItCurrent, InvoiceRecord, Platform, File, Contact];
let currentIndex = ref({
  index: 0,
});
let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  isEdit.value = false;
  currentCompoent.value = compoentArr[value];
}
let { proxy } = getCurrentInstance();
function edit() {
  if(form.value.customerStatus != 1){
    const text = ['公共客户','跟进中','失效客户'][form.value.customerStatus];
    ElMessage.warning(`当前客户状态为${text}，无法编辑，请去${text}列表中编辑`);
    return
  }
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '业务员',
          component: 'wf-user-select',
          prop: 'businessPerson',
          params: {
            userUrl:'/api/blade-system/search/user?functionKeys=bussinessUser'
          },
          // params: {
          //   checkType: 'checkbox',
          // },
          value: form.value.businessPerson,
        },
        {
          label: '专项业务员',
          component: 'wf-user-select',
          params: {
            userUrl:'/api/blade-system/search/user?functionKeys=specialSalesman'
          },
          prop: 'specialBusinessPerson',
          value: form.value.specialBusinessPerson,
        },
        // {
        //   label: '业务配合人',
        //   component: 'wf-user-select',
        //   prop: 'businessCoordinationPerson',
        //   value: form.value.businessCoordinationPerson,
        //     params: {
        //     userUrl:'/api/blade-system/search/user?functionKeys=businessCooperator'
        //   },
        // },

        {
          label: '商务协作人',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistant,
            params: {
            userUrl:'/api/blade-system/search/user?functionKeys=businessAssistant'
          },
        },
        {
          label: '推荐人',
          prop: 'referrer',
          value: form.value.referrer,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          ...res.data,
          id: props.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const store = useStore();
const tag = computed(() => store.getters.tag);
function translateOther() {
  proxy.$refs.dialogForm.show({
    title: '转给他人',
    option: {
      column: [
        {
          label: '转移人',
          component: 'wf-user-select',
          prop: 'targetPerson',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customerTransferRecord/save', {
          ...res.data,
          customerId: props.id,
          transferType: 0,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          store.commit('DEL_TAG', tag.value);
          router.back();
        });
    },
  });
}
function transformSubmit(data) {
  axios.post('/api/vt-admin/customerTransferRecord/save', data).then(res => {
    proxy.$message.success(res.data.msg);
    onLoad();
  });
}
function transformCustomer(transferType) {
  const data = {
    customerId: props.id,
    transferType,
  };
  if (transferType == 1) {
    proxy.$refs.dialogForm.show({
      title: '领取客户',
      option: {
        column: [
          {
            label: '客户名称',
            component: 'wf-customer-select',
            prop: 'customerId',
            value: props.id,
            disabled: true,
            span: 24,
          },
          {
            label: '认领理由',
            prop: 'applyReason',
            type: 'textarea',
            span: 24,
          },
        ],
      },
      callback(res) {
        const data = {
          customerId: props.id,
          ...res.data,
          transferType,
        };
        res.close();
        transformSubmit(data);
        onLoad();
      },
    });
  } else {
    proxy
      .$confirm('确定将该客户转移?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        axios.post('/api/vt-admin/customerTransferRecord/save', data).then(res => {
          proxy.$message.success(res.data.msg);
        });
      });
  }
}
const baseName = ref('ItCurrent');
const activeName = ref('baseInfo');
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
