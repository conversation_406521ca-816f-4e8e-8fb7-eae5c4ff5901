<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      新增客户
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form">
      <template #customerName>
        <el-autocomplete
          style="width: 100%"
          v-model="form.customerName"
          :fetch-suggestions="querySearch"
          :trigger-on-focus="false"
          value-key="customerName"
          placeholder="请输入客户全称（全部名称）"
        ></el-autocomplete>
      </template>
      <template #businessPerson>
        <el-select
          filterable
          style="width: 100%"
          v-model="form.businessPerson"
          placeholder="请选择业务员"
          ref="businessPersonRef"
        >
          <el-option
            v-for="item in businessUserList"
            :key="item.id"
            :label="item.realName"
            :value="item.id"
          >
          </el-option>
          
        </el-select>
      </template>
    </avue-form>
    <el-drawer title="新增外部业务员" v-model="drawer" size="50%">
      <avue-form :option="userOption" v-model="userForm" @submit="handleSubmitUser"></avue-form>
    </el-drawer>
  </basic-container>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed, nextTick, onMounted } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';

import { add } from '@/api/system/user';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

const type = route.query.type; // 0 自己添加客户 1 为外部业务员添加客户
const validatePass = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请输入密码'));
  } else {
    callback();
  }
};
const validatePass2 = (rule, value, callback) => {
  if (value === '') {
    callback(new Error('请再次输入密码'));
  } else if (value !== userForm.value.password) {
    callback(new Error('两次输入密码不一致!'));
  } else {
    callback();
  }
};
function validateName(rule, value, callback) {
  axios.get('/api/vt-admin/customer/existCustomerName?customerName=' + value).then(res => {
    if (res.data.data == 1) {
      callback(new Error('系统已经存在此客户'));
    } else {
      callback();
    }
  });
}

let userInfo = computed(() => store.getters.userInfo);
let isPaymentPeriodData = ref([]);
const option = ref({
  labelWidth: 120,
  emptyBtn: false,
  column: [
    {
      type: 'input',
      label: '客户全称',
      span: 12,
      display: true,
      prop: 'customerName',
      required: true,
      rules: [
        {
          required: true,
          message: '客户全称必须填写',
        },
        {
          validator: validateName,
          trigger: 'blur',
        },
      ],
    },
    {
      label: '业务员',
      prop: 'businessPerson',
      type: 'input',
      span: 12,
      display: type == 1,
      rules: [
        {
          required: true,
          message: '外部业务员必须选择',
        },
      ],
    },
    {
      label:'商务协作人',
      prop:'assistant',
      component:'wf-user-select',
      
     
      display:type != 1
      // value:proxy.$store.getters.userInfo.user_id,
    },
    // {
    //   type: 'input',
    //   label: '传真',
    //   span: 12,
    //   display: true,
    //   prop: 'fax',
    // },
    {
      type: 'input',
      label: '客户联系人',
      span: 12,
      display: true,
      prop: 'contactPerson',
      rules: [
        {
          required: true,
          message: '联系人必须填写',
        },
      ],
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },

      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      type: 'input',
      label: '客户联系电话',
      span: 12,
      display: true,
      prop: 'contactPhone',
      rules: [
        {
          required: true,
          message: '联系电话',
        },
      ],
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
      rules: [
        {
          required: true,
          message: '请填写详细地址',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      label: '客户类型',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择客户类型',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerType',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customer_type',
      remote: false,
    },
    {
      type: 'radio',
      label: '是否账期',
      cascader: [],
      // rules: [
      //   {
      //     required: true,
      //     message: '请选择账期',
      //   },
      // ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'remark',
      },
      dicFormatter: res => {
        isPaymentPeriodData.value = res.data;
        return res.data;
      },
      control: (val, form, b, c) => {
        return {
          fixedBillingDate: {
            display: form.$isPaymentPeriod == '固定账期',
          },
        };
      },
      prop: 'isPaymentPeriod',
      dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
      remote: false,
    },
    {
      type: 'number',
      label: '固定账期时间',
      span: 12,
      display: true,
      min: 1,
      max: 31,
      tip: '输入1到31之间的数字,账期则为每月这个时间',
      prop: 'fixedBillingDate',
      rules: [
        {
          required: true,
          message: '请输入固定账期时间',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      label: '客户级别',
      cascader: [],

      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerLevel',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customerLevel',
      remote: false,
    },
    {
      type: 'input',
      label: '推荐人',
      span: 12,
      display: true,
      prop: 'referrer',
    },
    // {
    //   type: 'select',
    //   label: '客户阶段',
    //   cascader: [],
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择客户阶段',
    //     },
    //   ],
    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },
    //   prop: 'customerStage',
    //   dicUrl: '/blade-system/dict/dictionary?code=customer_stage',
    //   remote: false,
    // },
    {
      type: 'select',
      label: '客户来源',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'customerSource',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customer_source',
      remote: false,
    },
    {
      type: 'number',
      label: '注册资金(万元)',
      span: 12,
      display: true,
      prop: 'registeredCapital',
      controls: false,

      precision: 2,
    },
    {
      type: 'select',
      label: '跟进状态',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'label',
        value: 'value',
      },
      prop: 'followStatus',
      dicData: followData,
      remote: false,
    },
    {
      type: 'select',
      label: '人员规模',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'staffSize',
      dicUrl: '/blade-system/dict-biz/dictionary?code=staffSize',
      remote: false,
    },
    {
      label: '跟进人',
      component: 'wf-user-select',
      span: 12,
      display: true,
      value: route.query.type == 3 ? null : userInfo.value.user_id,
      prop: 'follower',
    },
    {
      type: 'select',
      label: '所属行业',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'industry',
      dicUrl: '/blade-system/dict-biz/dictionary?code=industry',
      remote: false,
      rules: [
        {
          required: true,
          message: '请选择所属行业',
        },
      ],
    },
    {
      type: 'date',
      label: '下次跟进日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD hh:mm:ss',
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
      prop: 'nextFollowTime',
    },
    {
      type: 'input',
      label: '法人代表',
      span: 12,

      display: true,
      prop: 'legalRepresentative',
    },
    // {
    //   type: 'select',
    //   label: '企业性质',
    //   cascader: [],
    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },

    //   prop: 'companyNature',
    //   dicUrl: '/blade-system/dict/dictionary?code=companyNature',
    //   remote: false,
    // },
    // {
    //   type: 'input',
    //   label: '年度IT投入',
    //   span: 12,

    //   display: true,
    //   prop: 'annualInputIt',
    // },
    {
      type: 'input',
      label: '分公司',
      span: 12,

      display: true,
      prop: 'branchOffice',
    },
    {
      type: 'select',
      label: '采购方式',
      dicUrl: '/blade-system/dict-biz/dictionary?code=procurementMethod',
      span: 12,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      display: true,
      prop: 'procurementMethod',
    },

    {
      type: 'textarea',
      label: '主营业务',
      span: 24,
      display: true,

      prop: 'mainBusiness',
    },
  ],
});
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    type: route.query.type || null,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
    assistant:proxy.$store.getters.userInfo.user_id,
  };
  const url = type == 0? '/api/vt-admin/customer/save' : '/api/vt-admin/customer/saveExternalCustomer';
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        router.$avueRouter.closeTag();
        router.go(-1);
      }
    })
    .catch(() => {
      done();
    });
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/customer/list', {
      params: {
        size: 1000,
        customerName: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
onMounted(() => {
  getBusinessUserList();
});
let businessUserList = ref([]);

function getBusinessUserList(params) {
   Promise.all([getBusinessInUserList(), getBusinessOutUserList()]).then(res => {
    businessUserList.value = res[0].data.data.records.concat(res[1].data.data.records);
  })
}
function getBusinessOutUserList() {
 return  axios.get('/api/blade-system/search/externalUser',{params:{
    size: 5000,
    status:1
  }})
}
function getBusinessInUserList(params) {
  return  axios.get('/api/blade-system/search/user',{params:{
    size: 5000,
    functionKeys:'bussinessUser'
  }})
}
let drawer = ref(false);
let userForm = ref({});
let userOption = ref({
  height: 'auto',
  calcHeight: 140,
  tip: false,
  searchShow: true,
  searchMenuSpan: 6,
  border: true,
  index: true,
  selection: true,
  viewBtn: true,
  dialogType: 'drawer',
  dialogClickModal: false,
  column: [
    {
      label: '登录账号',
      prop: 'account',
      search: true,
      display: false,
    },
    {
      label: '所属租户',
      prop: 'tenantName',
      slot: true,
      display: false,
    },
    {
      label: '用户姓名',
      prop: 'realName',
      search: true,
      display: false,
    },
    {
      label: '所属角色',
      prop: 'roleName',
      slot: true,
      display: false,
    },
    {
      label: '所属部门',
      prop: 'deptName',
      slot: true,
      display: false,
    },
    {
      label: '账号状态',
      prop: 'status',
      slot: true,
      display: false,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 1,
          label: '正常',
        },
        {
          value: 2,
          label: '冻结',
        },
      ],
    },
    {
      label: '是否绑定微信号',
      prop: 'isBindWxmini',
      slot: true,
      display: false,
      search: false,
      type: 'select',
      align: 'center',
      dicData: [
        {
          value: 1,
          label: '是',
        },
        {
          value: 0,
          label: '否',
        },
      ],
    },
    // {
    //   label: '用户平台',
    //   prop: 'userTypeName',
    //   slot: true,
    //   display: false,
    // },
    {
      label: '用户平台',
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=user_type',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      dataType: 'number',
      search: true,
      hide: true,
      display: false,
      prop: 'userType',
      rules: [
        {
          required: true,
          message: '请选择用户平台',
          trigger: 'blur',
        },
      ],
    },
  ],
  group: [
    {
      label: '基础信息',
      prop: 'baseInfo',
      icon: 'el-icon-user-solid',
      column: [
        {
          label: '登录账号',
          prop: 'account',
          rules: [
            {
              required: true,
              message: '请输入登录账号',
              trigger: 'blur',
            },
          ],
        },
        // {
        //   label: '用户平台',
        //   type: 'select',
        //   dicUrl: '/blade-system/dict/dictionary?code=user_type',
        //   props: {
        //     label: 'dictValue',
        //     value: 'dictKey',
        //   },
        //   dataType: 'number',
        //   slot: true,
        //   prop: 'userType',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择用户平台',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        {
          label: '密码',
          prop: 'password',
          hide: true,
          editDisplay: false,
          viewDisplay: false,
          rules: [{ required: true, validator: validatePass, trigger: 'blur' }],
        },
        {
          label: '确认密码',
          prop: 'password2',
          hide: true,
          editDisplay: false,
          viewDisplay: false,
          rules: [{ required: true, validator: validatePass2, trigger: 'blur' }],
        },
      ],
    },
    {
      label: '详细信息',
      prop: 'detailInfo',
      icon: 'el-icon-s-order',
      column: [
        {
          label: '用户昵称',
          prop: 'name',
          hide: true,
          rules: [
            {
              required: true,
              message: '请输入用户昵称',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '用户姓名',
          prop: 'realName',
          rules: [
            {
              required: true,
              message: '请输入用户姓名',
              trigger: 'blur',
            },
            {
              min: 2,
              max: 5,
              message: '姓名长度在2到5个字符',
            },
          ],
        },
        {
          label: '手机号码',
          prop: 'phone',
          overHidden: true,
        },
        {
          label: '电子邮箱',
          prop: 'email',
          hide: true,
          overHidden: true,
        },
        {
          label: '用户性别',
          prop: 'sex',
          type: 'select',
          dicData: [
            {
              label: '男',
              value: 1,
            },
            {
              label: '女',
              value: 2,
            },
            {
              label: '未知',
              value: 3,
            },
          ],
          hide: true,
        },
        {
          label: '用户生日',
          type: 'date',
          prop: 'birthday',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          hide: true,
        },
        {
          label: '账号状态',
          prop: 'statusName',
          hide: true,
          display: false,
        },
      ],
    },
  ],
});
function handleSubmitUser(form, done, loading) {
  const data = {
    ...form,
    userType: 3,
    tenantId: proxy.$store.getters.userInfo.tenant_id,
  };
  axios
    .post('/api/blade-system/user/submitExternalBusiness', data)
    .then(res => {
      done(),
        proxy.$message({
          type: 'success',
          message: '操作成功!',
        });
      drawer.value = false;
    })
    .catch(err => {
      done();
    });
}

function handleAdd() {
  drawer.value = true;
  nextTick(() => {
    setTimeout(() => {
      proxy.$refs.businessPersonRef.blur()
    }, 0);
  });
}
</script>

<style lang="scss" scoped></style>
