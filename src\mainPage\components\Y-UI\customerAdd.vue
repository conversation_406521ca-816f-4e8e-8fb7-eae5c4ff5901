<template>
 
  <avue-form :option="option" @submit="handleSubmit" v-model="form">
    <template #customerName>
      <el-autocomplete
        style="width: 100%"
        v-model="form.customerName"
        :fetch-suggestions="querySearch"
        :trigger-on-focus="false"
        value-key="customerName"
        placeholder="请输入客户全称"
      ></el-autocomplete>
    </template>
  </avue-form>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

function validateName(rule, value, callback) {
  axios.get('/api/vt-admin/customer/existCustomerName?customerName=' + value).then(res => {
    if (res.data.data == 1) {
      callback(new Error('系统已经存在此客户'));
    } else {
      callback();
    }
  });
}

let userInfo = computed(() => store.getters.userInfo);
let isPaymentPeriodData = ref([]);
let emits =  defineEmits(['onConfirm'])
const option = ref({
  labelWidth: 120,
  emptyBtn: false,
  column: [
    {
      type: 'input',
      label: '客户全称',
      span: 12,
      display: true,
      prop: 'customerName',
      required: true,
      rules: [
        {
          required: true,
          message: '客户名称必须填写',
        },
        {
          validator: validateName,
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'input',
      label: '传真',
      span: 12,
      display: true,
      prop: 'fax',
    },
    {
      type: 'input',
      label: '联系人',
      span: 12,
      display: true,
      prop: 'contactPerson',
      rules: [
        {
          required: true,
          message: '联系人必须填写',
        },
      ],
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },

      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      type: 'input',
      label: '联系方式',
      span: 12,
      display: true,
      prop: 'contactPhone',
      rules: [
        {
          required: true,
          message: '联系方式必须填写',
        },
      ],
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
      rules: [
        {
          required: true,
          message: '请填写详细地址',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      label: '客户类型',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择客户类型',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerType',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customer_type',
      remote: false,
    },
    {
      type: 'radio',
      label: '是否账期',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择账期',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'remark',
      },
      dicFormatter: res => {
        isPaymentPeriodData.value = res.data;
        return res.data;
      },
      control: (val, form, b, c) => {
        return {
          fixedBillingDate: {
            display: form.$isPaymentPeriod == '固定账期',
          },
        };
      },
      prop: 'isPaymentPeriod',
      dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
      remote: false,
    },
    {
      type: 'number',
      label: '固定账期时间',
      span: 12,
      display: true,
      min: 1,
      max: 31,
      tip: '输入1到31之间的数字,账期则为每月这个时间',
      prop: 'fixedBillingDate',
      rules: [
        {
          required: true,
          message: '请输入固定账期时间',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      label: '客户级别',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择客户级别',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerLevel',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customerLevel',
      remote: false,
    },
    {
      type: 'input',
      label: '推荐人',
      span: 12,
      display: true,
      prop: 'referrer',
    },
    // {
    //   type: 'select',
    //   label: '客户阶段',
    //   cascader: [],
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择客户阶段',
    //     },
    //   ],
    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },
    //   prop: 'customerStage',
    //   dicUrl: '/blade-system/dict/dictionary?code=customer_stage',
    //   remote: false,
    // },
    {
      type: 'select',
      label: '客户来源',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'customerSource',
      dicUrl: '/blade-system/dict-biz/dictionary?code=customer_source',
      remote: false,
    },
    {
      type: 'number',
      label: '注册资金(万元)',
      span: 12,
      display: true,
      prop: 'registeredCapital',
      controls: false,

      precision: 2,
    },
    {
      type: 'select',
      label: '跟进状态',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'label',
        value: 'value',
      },
      prop: 'followStatus',
      dicData: followData,
      remote: false,
    },
    {
      type: 'select',
      label: '人员规模',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'staffSize',
      dicUrl: '/blade-system/dict-biz/dictionary?code=staffSize',
      remote: false,
    },
    {
      label: '跟进人',
      component: 'wf-user-select',
      span: 12,
      display: true,
      value: route.query.type == 3 ? null : userInfo.value.user_id,
      prop: 'follower',
    },
    {
      type: 'select',
      label: '所属行业',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'industry',
      dicUrl: '/blade-system/dict-biz/dictionary?code=industry',
      remote: false,
      rules: [
        {
          required: true,
          message: '请选择所属行业',
        },
      ],
    },
    {
      type: 'date',
      label: '下次跟进日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD hh:mm:ss',
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
      prop: 'nextFollowTime',
    },
    {
      type: 'input',
      label: '法人代表',
      span: 12,

      display: true,
      prop: 'legalRepresentative',
    },
    // {
    //   type: 'select',
    //   label: '企业性质',
    //   cascader: [],
    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },

    //   prop: 'companyNature',
    //   dicUrl: '/blade-system/dict/dictionary?code=companyNature',
    //   remote: false,
    // },
    {
      type: 'input',
      label: '年度IT投入',
      span: 12,

      display: true,
      prop: 'annualInputIt',
    },
    {
      type: 'input',
      label: '分公司',
      span: 12,

      display: true,
      prop: 'branchOffice',
    },
    {
      type: 'select',
      label: '采购方式',
      dicUrl: '/blade-system/dict-biz/dictionary?code=procurementMethod',
      span: 12,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      display: true,
      prop: 'procurementMethod',
    },

    {
      type: 'textarea',
      label: '主营业务',
      span: 24,
      display: true,

      prop: 'mainBusiness',
    },
  ],
});
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    type: route.query.type || null,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/customer/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success('新增成功');
        emits('onConfirm', res.data.data);
        done();
      }
    })
    .catch(() => {
      done();
    });
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/customer/list', {
      params: {
        size: 1000,
        customerName: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
</script>

<style lang="scss" scoped></style>
