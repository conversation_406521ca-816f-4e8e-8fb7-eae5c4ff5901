<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #stage="{ row, a, b, c }">
        <el-tag effect='plain'>{{ (a, b, c) }}{{ row.$stage }}</el-tag>
      </template>
      <template #menu="{ row, index }">
        <el-button type="primary" text @click="viewBusiness(row)" icon="Edit">{{
          row.technicalPersonnelName ? '重新分配' : '分配方案'
        }}</el-button>
        <el-button
          type="primary"
         
          text
          @click="returnBussiness(row)"
          icon="Edit"
          >退回</el-button
        >
        <!-- <el-button
          type="primary"
          text
          @click="toSelf(row)"
          icon="Edit"
          >分配给自己</el-button
        > -->
        <!-- <el-button type="primary" text @click="handleAdd(row)" v-else icon="plus"
          >新增方案</el-button
        > -->
        <!-- <el-button type="primary" text @click="submit(row)" icon="check" v-if="row.optionStatus != 2 && $store.getters.userInfo.user_id == row.technicalPersonnel">提交</el-button> -->
        <!-- <el-button type="primary" text @click="handleView(row)" icon="View">详情</el-button> -->
        <!-- <el-button type="primary" text @click="handleHistory(row)" icon="Clock">历史记录</el-button> -->
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      title="分配方案"
      top="2vh"
      style="width: 60%"
      v-model="dialogVisible"
      class="avue-dialog avue-dialog--top"
    >
      <!-- <slot ></slot> -->
      <wfUserSelect
        v-model="technicalPerson"
        placeholder="请选择要分配的技术人员"
        style="margin-bottom: 10px"
        :userUrl="'/api/blade-system/search/user?functionKeys=technician'"
        size="normal"
      ></wfUserSelect>
      <el-input
        type="textarea"
        v-model="distributionOptionRemark"
        style="margin-bottom: 10px"
        rows="4"
        placeholder="请输入备注"
      ></el-input>
      <BusinessDetail :form="businessForm" :isEdit="false"></BusinessDetail>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { programmeStatus, businessOpportunityData } from '@/const/const.js';
import BusinessDetail from '@/views/CRM/businessOpportunity/detail/baseInfo.vue';
import wfUserSelect from '@/views/plugin/workflow/components/custom-fields/wf-user-select/index.vue';
import { ElForm, ElFormItem, ElInput, ElMessageBox } from 'element-plus';
import moment from 'moment';
let store = useStore();
let permission = computed(() => store.getters.permission);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 180,
  menu: true,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,component: 'wf-bussiness-drop',
      search: true,
    },
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,

    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    // },
    {
      type: 'select',
      label: '业务板块',
      span: 12,
      // search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择商机关联',
        },
      ],
      display: true,
      prop: 'type',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机描述',
      prop: 'description',
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      // search: true,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机阶段',
      prop: 'stage',
      slot: true,
      type: 'select',
      search: true,
      dicData: businessOpportunityData,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
    },
    {
      prop: 'businessPersonName',
      label: '业务员',
    },
    {
      label: '要求完成时间',
      prop: 'optionRequireTime',
      html: true,
      overHidden: true,
      width: 110,
      formatter: row => {
        if (!row.optionRequireTime) return '--';
        console.log(moment(new Date()).format('YYYY-MM-DD'));
        const day = moment(moment(row.optionRequireTime).format('YYYY-MM-DD')).diff(
          moment(new Date()).format('YYYY-MM-DD'),
          'days'
        );
        console.log(day);
        return `<div style='color:${
          day <= 1 && row.auditStatus != 2 ? 'var(--el-color-danger)' : ''
        }'>${moment(row.optionRequireTime).format('YYYY-MM-DD')}</div>`;
      },
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
    {
      prop: 'technicalPersonnelName',
      label: '技术人员',
    },
    {
      label: '介绍人',
      prop: 'introducer',
      type: 'input',
    },
    // {
    //   label: '方案状态',
    //   type: 'select',
    //   dicData: programmeStatus,
    //   prop: 'optionStatus',
    //   search: true,
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/needTechnicalPersonPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}

function handleView(row) {
  router.push({
    path: '/CRM/programme/compoents/detail',
    query: {
      id: row.id,
      type: 'detail',
    },
  });
}
function handleEdit(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '技术员',
          value: proxy.$store.getters.userInfo.user_id,
          component: 'wf-user-select',
          prop: 'technicalPerson',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/transferOption', null, {
          params: {
            id: row.id,
            ...res.data,
          },
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function toSelf(row) {
  proxy.$confirm('确定分配给自己?', '提示').then(() => {
    axios
      .post('/api/vt-admin/businessOpportunity/transferOption', null, {
        params: {
          id: row.id,
          technicalPerson: proxy.$store.getters.userInfo.user_id,
        },
      })
      .then(e => {
        proxy.$message.success('操作成功');
        onLoad();
      });
  });
}
function handleAdd(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'add',
    },
  });
}
let currentId = ref(0);
function handleHistory(row) {
  currentId.value = row.id;
  proxy.$refs.history.open();
}
// function submit(row) {
//   proxy
//     .$confirm('提交之后将会不可修改，你可以在历史记录查看?', '提示', {
//       confirmButtonText: '确定',
//       cancelButtonText: '取消',
//       type: 'warning',
//     })
//     .then(() => {
//       axios
//         .post('/api/vt-admin/businessOpportunityOption/confirm', {
//           id: row.optionId,
//         })
//         .then(res => {
//           proxy.$message.success(res.data.msg);
//         });
//     });
// }
let dialogVisible = ref(false);
let businessForm = ref({});
function viewBusiness(row) {
  dialogVisible.value = true;
  technicalPerson.value = row.technicalPersonnel;
  getBusinessDetail(row);
  currentBusnessId.value = row.id;
}
function getBusinessDetail(row) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      businessForm.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}
let technicalPerson = ref(proxy.$store.getters.userInfo.user_id);
let distributionOptionRemark = ref('');
let currentBusnessId = ref(null);
function submit() {
  axios
    .post('/api/vt-admin/businessOpportunity/transferOption', {
      id: currentBusnessId.value,
      technicalPersonnel: technicalPerson.value,
      distributionOptionRemark: distributionOptionRemark.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
      dialogVisible.value = false;
      onLoad();
    });
}
function returnBussiness(row) {
  const sendBackReason = ref('');

  ElMessageBox({
    title: '提示',
    cancelButtonText: 'Cancel',
    message: () => [
      h(
        ElForm,
        {
          labelPosition: 'top',
        },

        h(
          ElFormItem,
          { label: '退回原因：' },
          h(ElInput, {
            modelValue: sendBackReason.value,
            type: 'textarea',
            placeholder: '请输入退回原因',
            'onUpdate:modelValue': val => {
              sendBackReason.value = val;
            },
          })
        )
      ),
    ],
  }).then(res => {
    axios
      .post('/api/vt-admin/businessOpportunity/sendBackOption', {
        id: row.id,
        sendBackReason: sendBackReason.value,
      })
      .then(res => {
        proxy.$message.success('操作成功');

        onLoad();
      });
  });
}
</script>

<style lang="scss" scoped></style>
