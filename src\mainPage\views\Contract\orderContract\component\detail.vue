<template>
  <div class="contract-detail">
    <!-- 头部信息 -->
    <el-card
      class="header-card"
      style="border: none"
      body-style="padding: 5px 0;border: none"
      shadow="never"
    >
      <div class="header-info">
        <div class="project-number">
          <!-- <span class="number">4214345</span> -->
          <el-tabs type="card" v-model="activeTab" class="header-tabs">
            <el-tab-pane label="详情" name="detail"></el-tab-pane>
            <el-tab-pane
              label="干系人"
              v-if="contractForm.projectId"
              name="stakeholder"
            ></el-tab-pane>
            <el-tab-pane
              label="项目计划"
              v-if="contractForm.projectId"
              name="projectPlan"
            ></el-tab-pane>
            <el-tab-pane
              label="项目文档"
              v-if="contractForm.projectId"
              name="projectFile"
            ></el-tab-pane>
            <el-tab-pane label="产品信息" name="productInfo"></el-tab-pane>
            <el-tab-pane label="请购单" name="purchaseOrder"></el-tab-pane>
            <el-tab-pane label="工单" name="workOrder"></el-tab-pane>
            <el-tab-pane label="开票信息" name="invoice"></el-tab-pane>
            <el-tab-pane label="收款计划" name="collection"></el-tab-pane>
            <el-tab-pane label="实际收款" name="actual"></el-tab-pane>
          </el-tabs>
        </div>
        <div class="actions">
          <el-button type="primary" :icon="Download" @click="downloadReport">
            下载运维服务报告
          </el-button>
          <el-button
            :icon="Close"
            @click="
              $router.$avueRouter.closeTag();
              $router.back();
            "
          >
            关 闭
          </el-button>
        </div>
      </div>
    </el-card>
    <!-- 动态组件渲染 -->
    <div class="content-wrapper1">
      <component
        :is="currentComponent"
        v-loading="loading"
        :offerId="contractForm.offerId"
        :sealContractId="contractForm.id"
        :projectId="contractForm.projectId"
        :isNeedInvoice="contractForm.isNeedInvoice"
        :customerId="contractForm.customerId"
        :sealContractInvoiceId="contractForm.customerInvoiceId"
        :contractType="contractForm.contractType"
        @success="getContractDetail"
        :form="contractForm"
        :selectType="route.query.selectType == 0 ? 0 : 1"
        :contractTotalPrice="contractForm.contractTotalPrice"
      ></component>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch, getCurrentInstance, computed } from 'vue';
import { useRoute } from 'vue-router';
import {
  Download,
  Edit,
  InfoFilled,
  Tools,
  TrendCharts,
  Star,
  Document,
  Delete,
  Close,
} from '@element-plus/icons-vue';
import baseInfo from '../detail/baseInfo.vue';

// 干系人
import Contact from '@/views/Project/detail/contact.vue';
// 项目计划
import ProjectPlan from '@/views/Project/detail/projectPlan.vue';
// 项目文档
import ProjectFile from '@/views/Project/detail/projectFile.vue';
// 产品信息
import productInfo from '@/views/Contract/customer/detail/contractProduct.vue';
// 采购订单
import purchaseOrder from '@/views/Contract/customer/detail/purchaseOrder.vue';
// 发票
import invoice from '@/views/Contract/customer/detail/invoice.vue';
// 计划收款
import plan from '@/views/Contract/customer/detail/plannedCollection.vue';
// 实际收款
import actual from '@/views/Contract/customer/detail/actual.vue';
// 工单
import wokerOrder from '@/views/Contract/wokerOrder/myMissonForWokerOrder.vue';
// 费用
import cost from '@/views/Contract/customer/detail/cost.vue';
// 响应式数据
const activeTab = ref('detail');
const route = useRoute();
const { proxy } = getCurrentInstance();

// 组件映射表
const componentMap = {
  detail: baseInfo,
  stakeholder: Contact,
  projectPlan: ProjectPlan,
  projectFile: ProjectFile,
  productInfo: productInfo,
  purchaseOrder: purchaseOrder,
  invoice: invoice,
  collection: plan,
  actual: actual,
  workOrder: wokerOrder,
  cost: cost,
};

// 计算当前组件
const currentComponent = computed(() => {
  const component = componentMap[activeTab.value];

  // 对于需要projectId的组件，检查条件
  if (['stakeholder', 'projectPlan', 'projectFile'].includes(activeTab.value)) {
    return contractForm.value.projectId ? component : null;
  }

  return component;
});
// 方法
const downloadReport = () => {
  console.log('下载运维服务报告');
};

const modifyContract = () => {
  console.log('修改合同');
};
let loading = ref(false);
let contractForm = ref({});
watch(() => route.query.id, getContractDetail, {
  immediate: true,
});
function getContractDetail() {
  if (!route.query.id) {
    return;
  }
  loading.value = true;
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      if (!res.data.data && route.path == '/Contract/customer/compoents/detail') {
        ElMessage.warning('该合同不存在，自动关闭页面');
        proxy.$router.$avueRouter.closeTag();
        proxy.$router.back();
      }
      loading.value = false;
      // form.value = formatData(res.data.data)
      contractForm.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    });
}
</script>

<style lang="scss" scoped>
.contract-detail {
  padding: 10px;
  background: var(--el-bg-color-page);
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .header-card {
    // margin-bottom: 10px;

    .header-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      .project-number {
        display: flex;
        align-items: center;
        gap: 24px;

        .number {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .header-tabs {
          :deep(.el-tabs__header) {
            margin: 0;
          }

          :deep(.el-tabs__nav-wrap) {
            &::after {
              display: none;
            }
          }
        }
      }

      .actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .content-wrapper1 {
    // display: flex;
    height: 90%;
    padding: 10px;
    background-color: #fff;
    // gap: 16px;
    flex: 1;
    // align-items: center;
    justify-content: center;
    box-sizing: border-box;
    // overflow: hidden;

    .left-content,
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow: hidden;
    }

    .basic-info-card,
    .service-task-card,
    .task-overview-card,
    .satisfaction-card {
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .el-icon {
          color: var(--el-color-primary);
        }

        .view-more {
          margin-left: auto;
        }

        .satisfaction-actions {
          margin-left: auto;
          display: flex;
          gap: 8px;
        }
      }
    }

    .basic-info-card {
      flex: 0 0 auto;

      .info-sections {
        display: flex;
        flex-direction: row;
        gap: 32px;

        .info-section {
          flex: 1;

          .section-title {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            font-weight: 500;
            color: var(--el-text-color-primary);
            font-size: 14px;

            .el-icon {
              color: var(--el-color-primary);
              font-size: 16px;
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;

            .info-item {
              display: flex;
              align-items: center;

              .label {
                color: var(--el-text-color-regular);
                min-width: 90px;
                font-size: 13px;
              }

              .value {
                color: var(--el-text-color-primary);
                font-size: 13px;
                flex: 1;
              }
            }
          }
        }
      }
    }

    .service-task-card {
      flex: 1;

      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        .task-date {
          color: var(--el-text-color-regular);
          font-size: 14px;
        }

        .task-code {
          color: var(--el-text-color-primary);
          font-weight: 500;
          flex: 1;
          margin: 0 16px;
        }
      }
    }

    .task-overview-card {
      .overview-stats {
        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: var(--el-fill-color-light);
          border-radius: var(--el-border-radius-base);

          .stat-label {
            color: var(--el-text-color-regular);
            font-size: 14px;
          }

          .stat-value {
            color: var(--el-text-color-primary);
            font-size: 18px;
            font-weight: 600;
          }
        }
      }
    }

    .satisfaction-card {
      flex: 1;

      .satisfaction-charts {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;

        .satisfaction-item {
          flex: 1;

          h4 {
            margin: 0 0 16px 0;
            color: var(--el-text-color-primary);
            font-size: 14px;
            text-align: center;
            font-weight: 500;
          }

          .pie-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 16px;
            background: conic-gradient(#f39c12 0% 60%, #e74c3c 60% 100%);
          }

          .legend {
            .legend-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 6px;
              font-size: 12px;
              color: var(--el-text-color-regular);

              .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
              }
            }
          }

          .score-section {
            margin-top: 16px;
            text-align: center;

            .score-title {
              font-size: 14px;
              color: var(--el-text-color-primary);
              margin-bottom: 8px;
              font-weight: 500;
            }

            .score-numbers {
              display: flex;
              justify-content: center;
              gap: 8px;

              div {
                width: 28px;
                height: 28px;
                background: var(--el-fill-color-light);
                border: 1px solid var(--el-border-color-light);
                border-radius: var(--el-border-radius-base);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: var(--el-text-color-regular);
                transition: all 0.3s;

                &:hover {
                  background: var(--el-color-primary-light-9);
                  border-color: var(--el-color-primary);
                  color: var(--el-color-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .contract-detail {
    .content-wrapper {
      flex-direction: column;
    }

    .header-info {
      flex-direction: column;
      gap: 5px;
      align-items: flex-start;
    }

    .satisfaction-charts {
      flex-direction: column !important;
    }

    .basic-info-card .info-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
