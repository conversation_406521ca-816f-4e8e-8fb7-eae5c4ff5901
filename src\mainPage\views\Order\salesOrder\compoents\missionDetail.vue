<template>
  <el-drawer
    size="70%"
    title="任务详情"
    v-model="dialogVisible"
    class="avue-dialog avue-dialog--top"
  >
    <avue-form ref="dialogForm" :option="detailOption" v-model="detailForm">
      <template #productList>
        <missionProductList :objectId="currentObjectId" :delBtn="props.delBtn"></missionProductList>
      </template>
      <template #objectName1>
        {{ detailForm.$objectName }}
      </template>
      <template #delayList>
        <delayList :objectId="currentObjectId"></delayList>
      </template>
      <template #completeFiles>
        <File :fileList="detailForm.completeFileList || []"></File>
      </template>
      <template #serviceTime>
        {{ detailForm.serviceStartTime }} - {{ detailForm.serviceEndTime }} <el-tag effect='plain' type="success">{{ detailForm.totalTime }}小时</el-tag>
      </template>
    </avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </div>
  </el-drawer>
</template>

<script setup>

import missionProductList from './missionProductList.vue';
import delayList from './delayList.vue';
import { objectType } from '@/const/const';
import moment from 'moment'
let dialogVisible = ref(false);
let detailForm = ref({});
let detailOption = ref({
  detail: true,
  tabs: true,
  submitBtn: false,
  emptyBtn: false,
  labelWidth:120,
  group: [
    {
      label: '基本信息',
      prop: 'applyInfo',
      labelWidth: 140,
      column: [
        {
          type: 'input',
          label: '任务名称',
          span: 24,
          display: true,
          prop: 'objectName1',
        },
        {
          type: 'textarea',
          label: '任务描述',
          span: 24,
          display: true,
          prop: 'durationNode',
        },
        {
          type: 'datetime',
          label: '时间要求',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
        },

        {
          label: '处理人',
          span: 12,
          component: 'wf-user-select',
          display: true,
          span: 12,
          overHidden: true,
          prop: 'handleUser',
        },
        {
          type: 'select',
          label: '状态',
          span: 24,
          editDisplay: false,
          addDisplay: false,
          value: '0',
          dicData: [
            {
              label: '未开始',
              value: 0,
            },
            {
              label: '进行中',
              value: 1,
            },
            {
              label: '已完成',
              value: 2,
            },
          ],
          prop: 'objectStatus',
        },

        // {
        //   type: 'textarea',
        //   label: '延期说明',
        //   span: 24,
        //   editDisplay: false,
        //   addDisplay: false,
        //   prop: 'a170072237641194426',
        // },

        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },

    {
      label: '配送信息',
      prop: 'invoiceInfo',
      column: [
        {
          label: '交付方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 24,
          dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
        },

        // {
        //   label: '交付日期',
        //   prop: 'distributionDate',
        //   span: 12,
        //   type: 'datetime',

        //   format: 'YYYY-MM-DD HH:mm:ss',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',

        // },
        {
          label: '交付地址',
          prop: 'distributionAddress',
          span: 24,

          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入送货地址',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系人',
          prop: 'contact',
          span: 12,
          type: 'input',

          rules: [
            {
              required: true,
              message: '请输入联系人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          span: 12,

          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'change',
            },
          ],
        },
      ],
    },

    {
      label: '关联产品',
      prop: 'productInfo',

      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'productList',
          span: 24,
        },
      ],
    },

    {
      label: '完成信息',
      prop: 'completeInfo',
      column: [
        {
          type: 'checkbox',
          dicData: objectType,
          label: '任务名称',
          span: 24,
          display: true,
         
          overHidden: true,
          prop: 'objectName',
          // disabled: true,
          control: val => {
            return {
              deliveryChannel: {
                display: val.indexOf('0') > -1,
              },
              courierCompany: {
                display: val.indexOf('0') > -1,
              },
              courierNumber: {
                display: val.indexOf('0') > -1,
              },
              useCarType: {
                display: val.indexOf('1') > -1,
              },
              feeRegistration: {
                display:val.indexOf('1') > -1,
              },
              serviceTime: {
                display: val.indexOf('0') == -1 && val.indexOf('1') == -1,
              },
              serviceReorder: {
                display: val.indexOf('2') > -1 || val == 3,
              },
              questionFeedback: {
                display: val.indexOf('4') > -1,
              },
              pickAddress: {
                display: val.indexOf('5') > -1,
              },
              distributionAddress: {
                display: val.indexOf('5') > -1,
              },
              isOutStorage: {
                display:val.indexOf('0') > -1 || val.indexOf('1') > -1,
              },
            };
          },
        },
        {
          label: '发货渠道',
          prop: 'deliveryChannel',
          type: 'radio',
          dicData: [
            { label: '快递', value: 0 },
            { label: '物流', value: 1 },
          ],
          span: 12,
          display: true,
          rules: [
            {
              required: true,
              message: '请选择发货渠道',
              trigger: 'change',
            },
          ],
          control: val => {
          
            return {
              logisticsCompany: {
                display: val == 1 && detailForm.value.objectName.indexOf('0') > -1,
              },
              courierCompany: {
                display:  val == 0 && detailForm.value.objectName.indexOf('0') > -1,
              },

              courierNumber: {
                label:  val == 0 ? '快递单号' : '物流单号',
                display: ( val == 0 ||  val == 1)&& detailForm.value.objectName.indexOf('0') > -1,
              },
            };
          },
        },

        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict-biz/dictionary?code=courierCompany',
          cascader: [],
          span: 12,
          // search: true,
          display: false,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },

          prop: 'courierCompany',
        },
        {
          type: 'select',
          label: '物流公司',
          dicUrl: '/blade-system/dict/dictionary?code=logisticsCompany',
          cascader: [],
          span: 12,
          // search: true,
          display: false,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },

          prop: 'logisticsCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          display: false,
        },
        {
          label: '用车类型',
          prop: 'useCarType',
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=carType',
          cascader: [],
          display: false,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          span: 12,
          display: false,
        },
        {
          label: '费用登记',
          prop: 'feeRegistration',
          type: 'switch',
          dicData: [
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ],
          span: 12,
          display: false,
        },
        {
          label: '完成日期',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
          span: 12,
          display: true,
        },
        {
          label: '所用时间',
          prop: 'serviceTime',
          component: 'wf-daterange-search',
          timeType: 'time',
          display: false,
        },
        {
          label: '服务复盘',
          prop: 'serviceReorder',
          type: 'textarea',
          display: false,
          span: 24,
          value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
        },
        {
          label: '问题反馈',
          prop: 'questionFeedback',
          type: 'textarea',
          display: false,
          span: 24,
        },
        {
          label: '拿货地址',
          prop: 'pickAddress',
          display: false,
          span: 24,
        },
        {
          label: '货拿目的地',
          prop: 'distributionAddress',
          display: false,
          span: 24,
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          dataType: 'object',
          // listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          drag: true,
          multiple: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            coverId = res.id;

            done();
          },
        },
        {
          label: '同步出库',
          prop: 'isOutStorage',
          type: 'radio',
          display: false,
          dicData: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
          span: 24,
          value: 0,
        },

        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '延期信息',
      prop: 'delayInfo',

      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'delayList',
          span: 24,
        },
      ],
    },
  ],
});
const props = defineProps({
  delBtn: {
    type: Boolean,
    default: false,
  },
});
watchEffect(() => {
  if (!dialogVisible.value) {
    currentObjectId.value = '';
  }
});
let currentObjectId = ref(null);
function viewDetail(row, tabActive = 1) {
  detailOption.value.tabsActive = tabActive;
  currentObjectId.value = row.id;

  axios.get('/api/vt-admin/sealContractObject/detail?id=' + row.id).then(res => {
    dialogVisible.value = true;

    detailForm.value = {
      ...res.data.data,
      distributionMethod:
        res.data.data.distributionMethod && res.data.data.distributionMethod.toString(),
        serviceTime:res.data.data.serviceStartTime && [res.data.data.serviceStartTime,res.data.data.serviceEndTime],
        logisticsCompany: res.data.data.courierCompany,
        totalTime: res.data.data.serviceStartTime && moment.duration(moment(res.data.data.serviceEndTime).diff(moment(res.data.data.serviceStartTime))).hours()
    };
    console.log(detailForm.value);
  });
}

defineExpose({
  viewDetail,
});
</script>

<style lang="scss" scoped></style>
