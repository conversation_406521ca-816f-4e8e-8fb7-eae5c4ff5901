<template>
  <div style="height: 100%">
    <el-row style="height: 100%" :gutter="20">
      <!-- <el-col :span="5" style="height: 100%">
        <el-divider>采集目录</el-divider>

        <el-tree
          style="max-width: 600px"
          :data="treeData"
          ref="tree"
          node-key="id"
          :default-expanded-keys="expandArr"
          check-strictly
          @check="handleChange"
          :props="{
            label: 'categoryName',
            children: 'children',
            disabled: 'disabled',
          }"
          show-checkbox
        />
      </el-col> -->

      <el-col :span="24" style="height: 100%">
        <!-- <Title>{{ categoryName || '----' }}</Title> -->

        <div style="margin: 20px 0">
          <el-form>
            <el-divider>采集配置</el-divider>

            <div v-for="(item, index) in allData">
              <el-divider content-position="left"
                >{{ index + 1 }}. {{ item.categoryName }}</el-divider
              >
              <el-table class="avue-crud" :row-key="row => row.id" :data="item.tableData" border>
                <el-table-column type="index" width="50" />
                <el-table-column label="#" width="50" type="expand">
                  <template #default="scope">
                    <div style="margin-left: 100px; display: flex">
                      <el-tag
                        effect="plain"
                        style="margin-right: 5px; margin-bottom: 5px"
                        v-for="element in scope.row.valuesVOList"
                        :key="element.id"
                      >
                        {{ element.value }}
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="所属分类" prop="categoryName" width="100" /> -->
                <el-table-column label="采集内容" prop="collectionContent" width="500" />
                <el-table-column label="备注" prop="remark" width=""> </el-table-column>
                <!-- <el-table-column label="选择项" >
                    <template #default="scope">
                      <div >
                        <draggable
                          v-model="scope.row.valuesVOList"
                          :animation="100"
                          @sort="
                            a => {
                              onMoveCallback(a, scope.row);
                            }
                          "
                        >
                          <transition-group>
                           <div   v-for="element in scope.row.valuesVOList">
                            <el-tag effect='plain'
                              style="margin-right: 5px;margin-bottom: 5px"
                             
                              :key="element.id"
                              closable
                              @close="handleClose(element)"
                            >
                              {{ element.value }}
                            </el-tag>
                           </div>
                          </transition-group>
                        </draggable>
  
                        <el-input
                          class="input-new-tag"
                          v-if="scope.row.inputVisible"
                          style="width: 100%"
                          v-model="scope.row.inputValue"
                          :ref="'saveTagInput' + scope.$index"
                          size="small"
                          @keyup.enter.native="addTag(scope.row)"
                          @blur="addTag(scope.row)"
                        >
                        </el-input>
  
                        <el-button
                          v-else-if="scope.row.type != 2"
                          class="button-new-tag"
                          size="small"
                          @click="showInput(scope.row, scope.$index)"
                          >+ 添加可选项</el-button
                        >
                      </div>
                    </template>
                  </el-table-column> -->
                <el-table-column label="回答方式" prop="type" width="100">
                  <template #default="scope">
                    <el-tag effect="plain" type="success" v-if="scope.row.type == 0">单选</el-tag>
                    <el-tag effect="plain" type="success" v-else-if="scope.row.type == 1"
                      >多选</el-tag
                    >
                    <el-tag effect="plain" type="success" v-else>文本输入</el-tag>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="是否使用" prop="isUse" width="100">
                    <template #default="scope">
                      <el-switch
                        :active-value="1"
                        :inactive-value="0"
                        disabled
                        v-model="scope.row.isUse"
                      ></el-switch
                    ></template>
                  </el-table-column> -->

                <el-table-column label="操作" align="center" width="150">
                  <template #default="scope">
                    <!-- <el-button
                    text
                    icon="edit"
                    size="small"
                    type="primary"
                    @click="editParams(scope.row)"
                    >编辑</el-button
                  > -->
                    <el-button
                      text
                      icon="delete"
                      size="small"
                      type="danger"
                      @click="delParams(scope.row, index)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-form>
        </div>
      </el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let form = ref({
  hasChildren: true,
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: true,
  menu: false,
  multiple: true,
  checkStrictly: true,
  filter: true,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    children: 'children',
    value: 'id',
  },
  formOption: {
    column: {
      categoryName: {
        label: '分类名称',
        rules: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
          },
        ],
      },
    },
  },
});

const updateUrl = '/api/vt-admin/product/update';
const props = defineProps(['data']);
let { proxy } = getCurrentInstance();
let tableData = ref([]);
let allData = ref([]);
watch(
  () => props.data,
  val => {
    getTreeData();

    allData.value = val.propertyVOList.reduce((pre, cur) => {
      const data = pre.find(iten => iten.id == cur.categoryId);
      if (data) {
        data.tableData.push({ ...cur, id: cur.requirementPropertyId });
      } else {
        pre.push({
          id: cur.categoryId,
          categoryName: cur.categoryName,
          tableData: [{ ...cur, id: cur.requirementPropertyId }],
        });
      }
      return pre;
    }, []);
    
  },
  {
    deep: true,
    immediate: true,
  }
);
let params = ref({});

let route = useRoute();

let loading = ref(false);

let router = useRouter();
let treeData = ref([]);
let isLock = ref(null);
let expandArr = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/requirementCategory/tree', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          disabled: true,
          leaf: !item.hasChildren,
        };
      });
      console.log(props.data.keys, proxy.$refs.tree);
      expandArr.value = props.data.keys;
      proxy.$nextTick(() => {
        proxy.$refs.tree.setCheckedKeys(props.data.keys);
      });
    });
}

let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);

function getTableData(id, categoryName) {
  const { current, size } = page.value;
  axios
    .get('/api/vt-admin/requirementProperty/page', {
      params: {
        categoryId: id,
        current,
        size: 5000,
      },
    })
    .then(res => {
      allData.value.push({
        id,
        categoryName,
        tableData: res.data.data.records,
      });
    });
}
function delParams(row, index) {
  proxy
    .$confirm('确定删除此参数吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      // axios.post('/api/vt-admin/requirementProperty/remove?ids=' + row.id).then(res => {
      //   proxy.$message.success('删除成功');
      //   getTableData();
      // });
      allData.value[index].tableData = allData.value[index].tableData.filter(
        item => item.id != row.id
      );
    });
}
let drag = ref(false);
let topList = ref([]);

function handleChange(a, b, c) {
  const bol = b.checkedKeys.includes(a.id);
  if (bol) {
    getTableData(a.id, a.categoryName);
  } else {
    allData.value = allData.value.filter(item => item.id != a.id);
  }
}
function getData() {
  console.log(
    allData.value.reduce((pre, cur) => {
      pre.concat(cur.tableData);
      return pre;
    }, [])
  );

  return allData.value.reduce((pre, cur) => {
    return pre.concat(cur.tableData);
  }, []);
}
defineExpose({
  getData,
});
</script>

<style lang="scss" scoped></style>
