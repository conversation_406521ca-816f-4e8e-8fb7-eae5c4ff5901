const iframeId = "dify-chatbot-bubble-window";
const closeButtonId = "dify-chatbot-close-button";
const minimizeButtonId = "dify-chatbot-minimize-button";
const agentListId = "dify-agent-list-panel";
import store from '@/store';

// 公共方法：压缩和编码
async function compressAndEncodeBase64(input) {
    const uint8Array = (new TextEncoder).encode(input);
    const compressedStream = new Response(new Blob([uint8Array]).stream().pipeThrough(new CompressionStream("gzip"))).arrayBuffer();
    const compressedUint8Array = new Uint8Array(await compressedStream);
    return btoa(String.fromCharCode(...compressedUint8Array))
}

// 公共方法：获取压缩后的 inputs
async function getCompressedInputs(inputs, mode) {
    const compressedInputs = {};
    await Promise.all(Object.entries(inputs || {}).map(async ([key, value]) => {
        compressedInputs[key] = mode == 'chat' ? value : await compressAndEncodeBase64(value)
    }));
    return compressedInputs
}

// 公共方法：获取压缩后的 systemVariables
async function getCompressedSystemVariables(systemVariables) {
    const compressedSystemVariables = {};
    await Promise.all(Object.entries(systemVariables || {}).map(async ([key, value]) => {
        compressedSystemVariables[`sys.${key}`] = await compressAndEncodeBase64(value)
    }));
    return compressedSystemVariables
}

// 公共方法：获取压缩后的 userVariables
async function getCompressedUserVariables(userVariables, mode) {
    const compressedUserVariables = {};
    await Promise.all(Object.entries(userVariables || {}).map(async ([key, value]) => {
        compressedUserVariables[`user.${key}`] = mode == 'chat' ? value : await compressAndEncodeBase64(value)
    }));
    return compressedUserVariables
}

// 公共方法：根据配置生成 iframe URL
async function buildIframeUrl(config) {
    const params = new URLSearchParams({
        ...await getCompressedInputs(config?.inputs, config?.mode),
        ...await getCompressedSystemVariables(config?.systemVariables),
        ...await getCompressedUserVariables(config?.userVariables, config?.mode)
    });

    const baseUrl = config.baseUrl || `https://${config.isDev ? "dev." : ""}udify.app`;
    const iframeUrl = `${baseUrl}/${config.mode || 'chatbot'}/${config.token}?${params}`;

    if (iframeUrl.length > 2048) {
        console.error("The URL is too long, please reduce the number of inputs to prevent the bot from failing to load")
    }

    return { iframeUrl, baseUrl };
}

export const initBot = function(config) {


    let isExpanded = false;
    let isMinimized = false;
    let isAgentListCollapsed = false; // 智能体列表折叠状态

    // 用于更新智能体列表选中状态的函数
    function updateAgentListSelection(selectedToken) {
        const agentPanel = document.getElementById(agentListId);
        if (!agentPanel) return;

        // 清除所有选中状态
        agentPanel.querySelectorAll('[data-agent-item]').forEach(item => {
            item.style.background = '#f5f5f5';
            item.style.border = '1px solid transparent';
            item.dataset.selected = 'false';
        });

        // 设置当前选中状态
        agentPanel.querySelectorAll('[data-agent-item]').forEach(item => {
            if (item.dataset.agentToken === selectedToken) {
                item.style.background = '#f0f7ff';
                item.style.border = '1px solid #409eff';
                item.dataset.selected = 'true';
            }
        });
    }

    // 折叠/展开智能体列表的函数
    function toggleAgentListCollapse(agentPanel, toggleButton) {
        isAgentListCollapsed = !isAgentListCollapsed;

        if (isAgentListCollapsed) {
            // 折叠：隐藏列表内容，只显示标题
            const items = agentPanel.querySelectorAll('[data-agent-item], .empty-state');
            items.forEach(item => {
                item.style.display = 'none';
            });

            // 旋转按钮
            toggleButton.style.transform = 'rotate(-90deg)';

            // 调整面板宽度
            agentPanel.style.width = '50px';
            agentPanel.style.overflow = 'hidden';
        } else {
            // 展开：显示列表内容
            const items = agentPanel.querySelectorAll('[data-agent-item], .empty-state');
            items.forEach(item => {
                item.style.display = '';
            });

            // 恢复按钮旋转
            toggleButton.style.transform = 'rotate(0deg)';

            // 恢复面板宽度
            agentPanel.style.width = '250px';
            agentPanel.style.overflow = 'auto';
        }
    }
    const originalContainerStyleText = `
    position: fixed;
    display: flex;
    flex-direction: row;
    top: 1rem;
    right: 1rem;
    width: calc(250px + 35rem);
    max-width: calc(100vw - 2rem);
    height: 43.75rem;
    max-height: calc(100vh - 2rem);
    border-radius: 1rem;
    border: 1px solid var(--el-color-primary);
    z-index: 2147483640;
    user-select: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    background: white;
    transition: opacity 0.2s ease;
  `;
    const expandedContainerStyleText = `
    position: fixed;
    display: flex;
    flex-direction: row;
    top: 1rem;
    right: 1rem;
    width: calc(250px + 48%);
    max-width: calc(100vw - 2rem);
    min-height: 43.75rem;
    height: 88%;
    max-height: calc(100vh - 2rem);
    border-radius: 1rem;
    z-index: 2147483640;
    user-select: none;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
    background: white;
    transition: opacity 0.2s ease;
  `;
    async function embedChatbot() {
        if (!config || !config.token) {
            console.error('Config is empty or token is not provided');
            return
        }

        // 使用公共方法生成 iframe URL
        const { iframeUrl, baseUrl } = await buildIframeUrl(config);
        const targetOrigin = new URL(baseUrl).origin;

        function createChatbotContainer() {
            const agentTypeList = store.state.agent.agentTypeList || [];

            // 创建主容器
            const mainContainer = document.createElement("div");
            mainContainer.id = iframeId + "-container";
            mainContainer.style.cssText = originalContainerStyleText;



            // 创建 agent 列表面板
            const agentPanel = document.createElement("div");
            agentPanel.id = agentListId;
            agentPanel.style.cssText = `
                width: 250px;
                height: 100%;
                background: #f8f9fa;
                overflow-y: auto;
                padding: 1rem;
                flex-shrink: 0;
                border-right: 1px solid #e5e7eb;
            `;

            // 创建标题
            const header = document.createElement("div");
            header.style.cssText = `
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 1rem;
                color: #333;
                display: flex;
                justify-content: space-between;
                align-items: center;
            `;

            // 创建标题容器（左侧）
            const titleContainer = document.createElement("div");
            titleContainer.style.cssText = `
                display: flex;
                align-items: center;
                gap: 0.5rem;
            `;

            // 创建折叠/展开按钮
            const toggleButton = document.createElement("button");
            toggleButton.id = "dify-agent-list-toggle";
            toggleButton.style.cssText = `
                background: none;
                border: none;
                cursor: pointer;
                padding: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                color: #333;
                transition: transform 0.2s ease;
            `;
            toggleButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `;

            // 添加折叠按钮点击事件
            toggleButton.addEventListener('click', () => {
                toggleAgentListCollapse(agentPanel, toggleButton);
            });

            titleContainer.appendChild(toggleButton);

            const titleText = document.createElement("span");
            titleText.textContent = "智能体列表";
            titleContainer.appendChild(titleText);

            header.appendChild(titleContainer);

            const countSpan = document.createElement("span");
            countSpan.style.cssText = `font-size: 12px; color: #999; font-weight: normal;`;
            countSpan.textContent = agentTypeList.length;
            header.appendChild(countSpan);

            agentPanel.appendChild(header);

            // 创建列表
            if (agentTypeList.length === 0) {
                const emptyState = document.createElement("div");
                emptyState.className = 'empty-state';
                emptyState.style.cssText = `
                    text-align: center;
                    padding: 2rem 0;
                    color: #999;
                `;
                emptyState.innerHTML = `
                    <div style="font-size: 48px; margin-bottom: 0.5rem;">📦</div>
                    <p>暂无可用智能体</p>
                `;
                agentPanel.appendChild(emptyState);
            } else {
                agentTypeList.forEach((agent, index) => {
                    const agentItem = document.createElement("div");
                    agentItem.style.cssText = `
                        padding: 0.75rem;
                        margin-bottom: 0.5rem;
                        border-radius: 0.5rem;
                        cursor: pointer;
                        transition: all 0.2s;
                        background: ${index === 0 ? '#f0f7ff' : '#f5f5f5'};
                        border: ${index === 0 ? '1px solid #409eff' : '1px solid transparent'};
                    `;

                    agentItem.innerHTML = `
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <div style="width: 32px; height: 32px; background: #fff; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; font-size: 18px;">
                                 <img style="width: 60px; height: 60px; border-radius: 50%;"  src=${agent.logo} />
                            </div>
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-weight: 500; color: #333; font-size: 14px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${agent.dictValue}</div>
                                ${agent.remark ? `<div style="font-size: 12px; color: #999; margin-top: 2px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${agent.remark}</div>` : ''}
                            </div>
                        </div>
                    `;

                    // 添加悬停效果
                    agentItem.addEventListener('mouseenter', () => {
                        if (agentItem.dataset.selected !== 'true') {
                            agentItem.style.background = '#e6f7ff';
                        }
                    });

                    agentItem.addEventListener('mouseleave', () => {
                        if (agentItem.dataset.selected !== 'true') {
                            agentItem.style.background = '#f5f5f5';
                        }
                    });

                    // 添加点击事件
                    agentItem.addEventListener('click', () => {
                        // 清除其他选中状态
                        agentPanel.querySelectorAll('[data-agent-item]').forEach(item => {
                            item.style.background = '#f5f5f5';
                            item.style.border = '1px solid transparent';
                            item.dataset.selected = 'false';
                        });

                        // 设置当前选中状态
                        agentItem.style.background = '#f0f7ff';
                        agentItem.style.border = '1px solid #409eff';
                        agentItem.dataset.selected = 'true';

                        // 切换智能体
                        switchAgent(agent);
                    });

                    agentItem.dataset.agentItem = 'true';
                    agentItem.dataset.agentToken = agent.dictKey; // 添加 token 标识
                    if (index === 0) {
                        agentItem.dataset.selected = 'true';
                    }

                    agentPanel.appendChild(agentItem);
                });
            }

            // 创建 iframe 容器
            const iframeWrapper = document.createElement("div");
            iframeWrapper.style.cssText = `
                flex: 1;
                height: 100%;
                position: relative;
                background: white;
                overflow: hidden;
            `;

            // 创建 iframe
            const iframe = document.createElement("iframe");
            // iframe.allow = "fullscreen;microphone";
            iframe.title = "dify chatbot bubble window";
            iframe.id = iframeId;
            iframe.src = iframeUrl;
            iframe.style.cssText = `
                width: 100%;
                height: 100%;
                border: none;
            `;

            iframeWrapper.appendChild(iframe);

            // 组装容器
            mainContainer.appendChild(agentPanel);
            mainContainer.appendChild(iframeWrapper);

            return mainContainer;
        }

        async function switchAgent(agent) {
            console.log('切换智能体:', agent);

            // 获取 iframe 元素
            const iframe = document.getElementById(iframeId);
            if (!iframe) {
                console.error('未找到 iframe 元素');
                return;
            }

            // 显示加载状态
            const iframeWrapper = iframe.parentElement;
            let loadingOverlay = iframeWrapper.querySelector('.loading-overlay');

            if (!loadingOverlay) {
                loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'loading-overlay';
                loadingOverlay.style.cssText = `
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba(255, 255, 255, 0.9);
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    z-index: 1000;
                `;
                loadingOverlay.innerHTML = `
                    <div style="text-align: center;">
                        <div style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #409eff; border-radius: 50%; animation: spin 1s linear infinite; margin: 0 auto 1rem;"></div>
                        <div style="color: #666; font-size: 14px;">正在切换智能体...</div>
                    </div>
                    <style>
                        @keyframes spin {
                            0% { transform: rotate(0deg); }
                            100% { transform: rotate(360deg); }
                        }
                    </style>
                `;
                iframeWrapper.appendChild(loadingOverlay);
            } else {
                loadingOverlay.style.display = 'flex';
            }

            // 使用新的 agent token 重新构建配置
            const newConfig = {
                ...config,
                token: agent.dictKey
            };

            // 使用公共方法生成新的 iframe URL
            const { iframeUrl: newIframeUrl } = await buildIframeUrl(newConfig);

            // 监听 iframe 加载完成
            const onIframeLoad = () => {
                if (loadingOverlay) {
                    loadingOverlay.style.display = 'none';
                }
                iframe.removeEventListener('load', onIframeLoad);
                console.log('智能体切换完成:', agent.dictValue);
            };

            iframe.addEventListener('load', onIframeLoad);

            // 更新 iframe src 来切换智能体
            iframe.src = newIframeUrl;

            console.log('正在切换智能体:', agent.dictValue, '新URL:', newIframeUrl);
        }

        function createCloseButton(container) {
            const closeButton = document.createElement("div");
            closeButton.id = closeButtonId;
            closeButton.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `;

            // 创建最小化按钮（显示在关闭按钮左边）
            const minimizeButton = document.createElement("div");
            minimizeButton.id = minimizeButtonId;
            minimizeButton.innerHTML = `
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5 12H19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            `;

            minimizeButton.style.cssText = `
                position: fixed;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 2147483643;
                transition: all 0.2s ease;
                pointer-events: auto;
            `;

            // 创建拖动图标按钮（显示在最小化按钮左边）
            const dragIconButton = document.createElement("div");
            dragIconButton.id = "dify-drag-icon-button";
            dragIconButton.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="7" cy="5" r="2" fill="currentColor"/>
                    <circle cx="17" cy="5" r="2" fill="currentColor"/>
                    <circle cx="7" cy="12" r="2" fill="currentColor"/>
                    <circle cx="17" cy="12" r="2" fill="currentColor"/>
                    <circle cx="7" cy="19" r="2" fill="currentColor"/>
                    <circle cx="17" cy="19" r="2" fill="currentColor"/>
                </svg>
            `;

            dragIconButton.style.cssText = `
                position: fixed;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: move;
                z-index: 2147483643;
                transition: all 0.2s ease;
                pointer-events: auto;
            `;

            // 更新关闭按钮、最小化按钮和拖动图标位置的函数
            function updateButtonPositions() {
                // 使用 requestAnimationFrame 确保获取最新的布局信息
                requestAnimationFrame(() => {
                    const rect = container.getBoundingClientRect();
                    closeButton.style.top = `${rect.top - 12}px`;
                    closeButton.style.left = `${rect.right - 20}px`;
                    minimizeButton.style.top = `${rect.top - 12}px`;
                    minimizeButton.style.left = `${rect.right - 60}px`; // 关闭按钮左边 40px
                    dragIconButton.style.top = `${rect.top - 12}px`;
                    dragIconButton.style.left = `${rect.right - 100}px`; // 最小化按钮左边 40px
                });
            }

            closeButton.style.cssText = `
                position: fixed;
                width: 32px;
                height: 32px;
                border-radius: 50%;
                background-color: rgba(0, 0, 0, 0.6);
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 2147483643;
                transition: background-color 0.2s;
                pointer-events: auto;
            `;

            // 初始化位置
            updateButtonPositions();

            closeButton.addEventListener("mouseenter", () => {
                closeButton.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
            });

            closeButton.addEventListener("mouseleave", () => {
                closeButton.style.backgroundColor = "rgba(0, 0, 0, 0.6)";
            });

            minimizeButton.addEventListener("mouseenter", () => {
                minimizeButton.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
            });

            minimizeButton.addEventListener("mouseleave", () => {
                minimizeButton.style.backgroundColor = "rgba(0, 0, 0, 0.6)";
            });

            dragIconButton.addEventListener("mouseenter", () => {
                dragIconButton.style.backgroundColor = "rgba(0, 0, 0, 0.8)";
            });

            dragIconButton.addEventListener("mouseleave", () => {
                dragIconButton.style.backgroundColor = "rgba(0, 0, 0, 0.6)";
            });

            closeButton.addEventListener("click", (e) => {
                console.log('关闭按钮被点击');
                e.preventDefault();
                e.stopPropagation();
                const targetContainer = document.getElementById(iframeId + "-container");
                const targetCloseButton = document.getElementById(closeButtonId);
                const targetMinimizeButton = document.getElementById(minimizeButtonId);
                const targetDragIcon = document.getElementById("dify-drag-icon-button");
                const targetMinimizedIcon = document.getElementById("dify-minimized-icon");
                if (targetContainer) {
                    targetContainer.remove();
                    console.log('iframe 容器已删除');
                }
                if (targetCloseButton) {
                    targetCloseButton.remove();
                    console.log('关闭按钮已删除');
                }
                if (targetMinimizeButton) {
                    targetMinimizeButton.remove();
                    console.log('最小化按钮已删除');
                }
                if (targetDragIcon) {
                    targetDragIcon.remove();
                    console.log('拖动图标已删除');
                }
                if (targetMinimizedIcon) {
                    targetMinimizedIcon.remove();
                    console.log('最小化图标已删除');
                }
            });

            // 最小化按钮点击事件
            minimizeButton.addEventListener("click", (e) => {
                console.log('最小化按钮被点击');
                e.preventDefault();
                e.stopPropagation();
                toggleMinimize();
            });

            // 保存更新函数供拖动时使用
            closeButton.updatePosition = updateButtonPositions;
            minimizeButton.updatePosition = updateButtonPositions;
            dragIconButton.updatePosition = updateButtonPositions;

            // 监听窗口大小变化，实时更新按钮位置
            const resizeObserver = new ResizeObserver(() => {
                updateButtonPositions();
            });
            resizeObserver.observe(container);

            // 监听窗口滚动，更新按钮位置
            window.addEventListener('scroll', updateButtonPositions, { passive: true });
            window.addEventListener('resize', updateButtonPositions, { passive: true });

            // 保存清理函数
            container.cleanupButtonPositionListeners = () => {
                resizeObserver.disconnect();
                window.removeEventListener('scroll', updateButtonPositions);
                window.removeEventListener('resize', updateButtonPositions);
            };

            return { closeButton, minimizeButton, dragIconButton };
        }

        function toggleExpand() {
            isExpanded = !isExpanded;
            const targetContainer = document.getElementById(iframeId + "-container");
            const targetCloseButton = document.getElementById(closeButtonId);
            const targetMinimizeButton = document.getElementById(minimizeButtonId);
            const targetDragIcon = document.getElementById("dify-drag-icon-button");

            if (!targetContainer)
                return;

            // 保存当前位置信息（如果容器被拖动过）
            const currentLeft = targetContainer.style.left;
            const currentTop = targetContainer.style.top;
            const hasBeenDragged = currentLeft && currentTop && currentLeft !== '' && currentTop !== '';

            // 定义按钮位置更新函数
            const updateButtons = () => {
                // 强制浏览器重新计算布局
                targetContainer.offsetHeight;

                requestAnimationFrame(() => {
                    // 再次强制重绘
                    targetContainer.offsetHeight;

                    const rect = targetContainer.getBoundingClientRect();
                    console.log('更新按钮位置 - 容器位置:', {
                        top: rect.top,
                        right: rect.right,
                        width: rect.width,
                        height: rect.height
                    });

                    if (targetCloseButton) {
                        targetCloseButton.style.top = `${rect.top - 12}px`;
                        targetCloseButton.style.left = `${rect.right - 20}px`;
                    }
                    if (targetMinimizeButton) {
                        targetMinimizeButton.style.top = `${rect.top - 12}px`;
                        targetMinimizeButton.style.left = `${rect.right - 60}px`;
                    }
                    if (targetDragIcon) {
                        targetDragIcon.style.top = `${rect.top - 12}px`;
                        targetDragIcon.style.left = `${rect.right - 100}px`;
                    }
                });
            };

            // 添加过渡效果
            targetContainer.style.transition = 'width 0.3s ease, height 0.3s ease, opacity 0.2s ease';

            if (isExpanded) {
                // 最大化：只修改尺寸相关的样式
                targetContainer.style.width = 'calc(250px + 48%)';
                targetContainer.style.minHeight = '43.75rem';
                targetContainer.style.height = '88%';

                // 如果没有被拖动过，使用默认位置
                if (!hasBeenDragged) {
                    targetContainer.style.top = '1rem';
                    targetContainer.style.right = '1rem';
                    targetContainer.style.left = 'auto';
                    targetContainer.style.bottom = 'auto';
                }
            } else {
                // 还原：恢复原始尺寸
                targetContainer.style.width = 'calc(250px + 50rem)';
                targetContainer.style.minHeight = 'auto';
                targetContainer.style.height = '43.75rem';

                // 如果没有被拖动过，使用默认位置
                if (!hasBeenDragged) {
                    targetContainer.style.top = '1rem';
                    targetContainer.style.right = '1rem';
                    targetContainer.style.left = 'auto';
                    targetContainer.style.bottom = 'auto';
                }
            }

            // 监听过渡结束事件
            const handleTransitionEnd = (e) => {
                // 只处理容器自身的过渡事件
                if (e.target === targetContainer && (e.propertyName === 'width' || e.propertyName === 'height')) {
                    targetContainer.removeEventListener('transitionend', handleTransitionEnd);
                    console.log('过渡完成，更新按钮位置');
                    // 过渡完成后更新按钮位置
                    updateButtons();
                }
            };
            targetContainer.addEventListener('transitionend', handleTransitionEnd);

            // 多次延迟更新确保容器尺寸已完全更新
            setTimeout(updateButtons, 0);
            setTimeout(updateButtons, 100);
            setTimeout(updateButtons, 200);
            setTimeout(updateButtons, 350);
            setTimeout(updateButtons, 500);
        }

        function toggleMinimize() {
            isMinimized = !isMinimized;
            const targetContainer = document.getElementById(iframeId + "-container");
            const targetCloseButton = document.getElementById(closeButtonId);
            const targetMinimizeButton = document.getElementById(minimizeButtonId);
            const targetDragIcon = document.getElementById("dify-drag-icon-button");

            if (isMinimized) {
                // 最小化动画
                if (targetContainer) {
                    // 获取容器中心点位置
                    const rect = targetContainer.getBoundingClientRect();
                    const centerX = rect.left + rect.width / 2;
                    const centerY = rect.top + rect.height / 2;

                    // 添加缩小动画
                    targetContainer.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                    targetContainer.style.transform = 'scale(0.3)';
                    targetContainer.style.opacity = '0';

                    // 隐藏按钮
                    if (targetCloseButton) {
                        targetCloseButton.style.transition = 'opacity 0.2s ease';
                        targetCloseButton.style.opacity = '0';
                    }
                    if (targetMinimizeButton) {
                        targetMinimizeButton.style.transition = 'opacity 0.2s ease';
                        targetMinimizeButton.style.opacity = '0';
                    }
                    if (targetDragIcon) {
                        targetDragIcon.style.transition = 'opacity 0.2s ease';
                        targetDragIcon.style.opacity = '0';
                    }

                    // 创建最小化图标（从中心点开始）
                    setTimeout(() => {
                        createMinimizedIcon(centerX, centerY);

                        // 动画完成后隐藏容器
                        setTimeout(() => {
                            if (targetContainer) targetContainer.style.display = 'none';
                            if (targetCloseButton) targetCloseButton.style.display = 'none';
                            if (targetMinimizeButton) targetMinimizeButton.style.display = 'none';
                            if (targetDragIcon) targetDragIcon.style.display = 'none';
                        }, 100);
                    }, 200);
                }
            } else {
                // 还原动画
                const minimizedIcon = document.getElementById("dify-minimized-icon");

                if (minimizedIcon) {
                    // 最小化图标缩小消失
                    minimizedIcon.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
                    minimizedIcon.style.transform = 'scale(0)';
                    minimizedIcon.style.opacity = '0';

                    setTimeout(() => {
                        minimizedIcon.remove();
                    }, 300);
                }

                // 定义按钮位置更新函数
                const updateButtonPositions = () => {
                    requestAnimationFrame(() => {
                        const rect = targetContainer.getBoundingClientRect();
                        if (targetCloseButton) {
                            targetCloseButton.style.top = `${rect.top - 12}px`;
                            targetCloseButton.style.left = `${rect.right - 20}px`;
                        }
                        if (targetMinimizeButton) {
                            targetMinimizeButton.style.top = `${rect.top - 12}px`;
                            targetMinimizeButton.style.left = `${rect.right - 60}px`;
                        }
                        if (targetDragIcon) {
                            targetDragIcon.style.top = `${rect.top - 12}px`;
                            targetDragIcon.style.left = `${rect.right - 100}px`;
                        }
                    });
                };

                // 显示容器并添加展开动画
                if (targetContainer) {
                    targetContainer.style.display = 'flex';
                    targetContainer.style.transform = 'scale(0.3)';
                    targetContainer.style.opacity = '0';

                    // 强制重绘
                    targetContainer.offsetHeight;

                    // 展开动画
                    setTimeout(() => {
                        targetContainer.style.transition = 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)';
                        targetContainer.style.transform = 'scale(1)';
                        targetContainer.style.opacity = '1';

                        // 监听动画结束事件
                        const handleTransitionEnd = (e) => {
                            if (e.target === targetContainer && e.propertyName === 'transform') {
                                targetContainer.removeEventListener('transitionend', handleTransitionEnd);
                                // 动画完成后更新按钮位置
                                updateButtonPositions();
                                setTimeout(updateButtonPositions, 50);
                                setTimeout(updateButtonPositions, 100);
                            }
                        };
                        targetContainer.addEventListener('transitionend', handleTransitionEnd);
                    }, 50);
                }

                // 显示按钮（在动画完成后）
                setTimeout(() => {
                    if (targetCloseButton) {
                        targetCloseButton.style.display = 'flex';
                        targetCloseButton.style.opacity = '0';
                        // 先更新位置
                        updateButtonPositions();
                        setTimeout(() => {
                            targetCloseButton.style.transition = 'opacity 0.3s ease';
                            targetCloseButton.style.opacity = '1';
                        }, 100);
                    }
                    if (targetMinimizeButton) {
                        targetMinimizeButton.style.display = 'flex';
                        targetMinimizeButton.style.opacity = '0';
                        setTimeout(() => {
                            targetMinimizeButton.style.transition = 'opacity 0.3s ease';
                            targetMinimizeButton.style.opacity = '1';
                        }, 100);
                    }
                    if (targetDragIcon) {
                        targetDragIcon.style.display = 'flex';
                        targetDragIcon.style.opacity = '0';
                        setTimeout(() => {
                            targetDragIcon.style.transition = 'opacity 0.3s ease';
                            targetDragIcon.style.opacity = '1';
                        }, 100);
                    }

                    // 按钮显示后再次更新位置
                    setTimeout(updateButtonPositions, 150);
                    setTimeout(updateButtonPositions, 300);
                    setTimeout(updateButtonPositions, 500);
                }, 500); // 等待容器动画完成（50ms + 400ms = 450ms，这里用500ms确保完成）
            }
        }

        function createMinimizedIcon(startX, startY) {
            // 先删除已存在的最小化图标
            const existingIcon = document.getElementById("dify-minimized-icon");
            if (existingIcon) existingIcon.remove();

            const minimizedIcon = document.createElement("div");
            minimizedIcon.id = "dify-minimized-icon";
            minimizedIcon.innerHTML = `
               <svg t="1763964480055" class="icon" width="30" height="30" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2617" width="200" height="200"><path d="M938.6496 469.3504A42.5984 42.5984 0 0 0 896 512v85.3504c0 70.5024-57.4464 128-128 128h-149.3504a42.6496 42.6496 0 0 0-30.1568 12.4928L512 814.3872 435.5072 737.792a42.6496 42.6496 0 0 0-30.208-12.4928H256a128.2048 128.2048 0 0 1-128-128V298.6496c0-70.5024 57.4464-128 128-128h341.3504a42.5984 42.5984 0 1 0 0-85.2992H256A213.4016 213.4016 0 0 0 42.6496 298.6496v298.7008c0 117.76 95.5904 213.2992 213.3504 213.2992h131.6864l94.208 94.208a42.9056 42.9056 0 0 0 30.1056 12.4928 42.9056 42.9056 0 0 0 30.1568-12.544l94.1568-94.1568H768c117.76 0 213.3504-95.5392 213.3504-213.2992V512a42.5984 42.5984 0 0 0-42.7008-42.6496z" fill="#0099F2" p-id="2618"></path><path d="M967.1168 213.3504H896v-71.168a42.5984 42.5984 0 1 0-85.3504 0v71.168h-71.1168a42.5984 42.5984 0 1 0 0 85.2992h71.168v71.168a42.5984 42.5984 0 1 0 85.2992 0v-71.168h71.1168a42.5984 42.5984 0 1 0 0-85.2992zM305.7664 420.352a42.5984 42.5984 0 1 0 0 85.3504h42.7008a42.5984 42.5984 0 1 0 0-85.3504h-42.7008z m291.584 0a42.5984 42.5984 0 1 0 0 85.3504h42.6496a42.5984 42.5984 0 1 0 0-85.3504h-42.6496z" fill="#0099F2" p-id="2619"></path></svg>
            `;

            // 计算最终位置（右上角）
            const finalTop = 16; // 1rem = 16px
            const finalRight = 16;
            const finalLeft = window.innerWidth - finalRight - 48; // 48px 是图标宽度

            // 设置初始位置（从窗口中心点开始）
            const initialLeft = startX - 24; // 24px 是图标宽度的一半
            const initialTop = startY - 24;

            minimizedIcon.style.cssText = `
                position: fixed;
                left: ${initialLeft}px;
                top: ${initialTop}px;
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background: #fff;
                color: white;
                display: flex;
                border: 1px solid #1296db;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 2147483643;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                opacity: 0;
                transform: scale(0.5);
            `;

            document.body.appendChild(minimizedIcon);

            // 强制重绘
            minimizedIcon.offsetHeight;

            // 飞行动画到右上角
            setTimeout(() => {
                minimizedIcon.style.transition = 'all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1)';
                minimizedIcon.style.left = `${finalLeft}px`;
                minimizedIcon.style.top = `${finalTop}px`;
                minimizedIcon.style.opacity = '1';
                minimizedIcon.style.transform = 'scale(1)';
            }, 50);

            // 悬停效果
            let isHovering = false;

            minimizedIcon.addEventListener("mouseenter", () => {
                if (!isHovering) {
                    isHovering = true;
                    minimizedIcon.style.transition = 'all 0.2s ease';
                    minimizedIcon.style.transform = "scale(1.1)";
                    minimizedIcon.style.boxShadow = "0 6px 16px rgba(0, 0, 0, 0.2)";
                }
            });

            minimizedIcon.addEventListener("mouseleave", () => {
                if (isHovering) {
                    isHovering = false;
                    minimizedIcon.style.transition = 'all 0.2s ease';
                    minimizedIcon.style.transform = "scale(1)";
                    minimizedIcon.style.boxShadow = "0 4px 12px rgba(0, 0, 0, 0.15)";
                }
            });

            minimizedIcon.addEventListener("click", (e) => {
                e.preventDefault();
                e.stopPropagation();
                toggleMinimize();
            });
        }
        window.addEventListener("message", event => {
            if (event.origin !== targetOrigin)
                return;
            const targetIframe = document.getElementById(iframeId);
            if (!targetIframe || event.source !== targetIframe.contentWindow)
                return;
            if (event.data.type === "dify-chatbot-iframe-ready") {
                targetIframe.contentWindow?.postMessage({
                    type: "dify-chatbot-config",
                    payload: {
                        isToggledByButton: false,
                        isDraggable: false
                    }
                }, targetOrigin)
            }
            if (event.data.type === "dify-chatbot-expand-change") {
                toggleExpand()
            }
        });

        // 先清除已存在的元素
        const existingContainer = document.getElementById(iframeId + "-container");
        if (existingContainer) {
            existingContainer.remove();
        }
        const existingCloseButton = document.getElementById(closeButtonId);
        if (existingCloseButton) {
            existingCloseButton.remove();
        }
        const existingMinimizeButton = document.getElementById(minimizeButtonId);
        if (existingMinimizeButton) {
            existingMinimizeButton.remove();
        }
        const existingDragIcon = document.getElementById("dify-drag-icon-button");
        if (existingDragIcon) {
            existingDragIcon.remove();
        }
        const existingMinimizedIcon = document.getElementById("dify-minimized-icon");
        if (existingMinimizedIcon) {
            existingMinimizedIcon.remove();
        }

        // 创建并显示整体容器（包含 agent 列表和 iframe）
        const container = createChatbotContainer();
        document.body.appendChild(container);

        // 创建并显示关闭按钮、最小化按钮和拖动图标
        const { closeButton, minimizeButton, dragIconButton } = createCloseButton(container);
        document.body.appendChild(closeButton);
        document.body.appendChild(minimizeButton);
        document.body.appendChild(dragIconButton);

        // 添加拖动功能
        enableDragging(container, closeButton, minimizeButton, dragIconButton);

        // 添加边缘拖动改变大小功能
        enableResizing(container, closeButton, minimizeButton, dragIconButton);

        // 添加点击窗口外部最小化功能
        enableClickOutsideToMinimize(container, closeButton, minimizeButton, dragIconButton);

        // 初始化时更新智能体列表选中状态
        updateAgentListSelection(config.token);

        console.log('聊天机器人容器已创建');

        // 点击窗口外部最小化功能
        function enableClickOutsideToMinimize(container, closeButton, minimizeButton, dragIconButton) {
            function handleClickOutside(e) {
                // 如果已经最小化，不处理
                if (isMinimized) return;

                // 检查点击是否在容器内
                const containerRect = container.getBoundingClientRect();
                const clickX = e.clientX;
                const clickY = e.clientY;

                const isInsideContainer = (
                    clickX >= containerRect.left &&
                    clickX <= containerRect.right &&
                    clickY >= containerRect.top &&
                    clickY <= containerRect.bottom
                );

                // 检查是否点击了按钮
                const isClickOnButton = (
                    e.target === closeButton ||
                    e.target === minimizeButton ||
                    e.target === dragIconButton ||
                    closeButton.contains(e.target) ||
                    minimizeButton.contains(e.target) ||
                    dragIconButton.contains(e.target)
                );

                // 如果点击在容器外且不是按钮，则最小化
                if (!isInsideContainer && !isClickOnButton) {
                    toggleMinimize();
                }
            }

            // 添加点击事件监听
            document.addEventListener('click', handleClickOutside);

            // 保存清理函数（如果需要移除监听器）
            container.removeClickOutsideListener = () => {
                document.removeEventListener('click', handleClickOutside);
            };
        }

        // 拖动功能实现
        function enableDragging(container, closeButton, minimizeButton, dragIconButton) {
            let isDragging = false;
            let startX, startY, startLeft, startTop;

            // 移除原来的拖动手柄（如果存在）
            const dragHandle = document.getElementById('dify-drag-handle');
            if (dragHandle) {
                dragHandle.remove();
            }

            // 使用拖动图标按钮作为拖动手柄
            if (!dragIconButton) return;

            dragIconButton.addEventListener('mousedown', startDrag);

            function startDrag(e) {
                // 只响应左键点击
                if (e.button !== 0) return;

                isDragging = true;
                const containerRect = container.getBoundingClientRect();
                startX = e.clientX;
                startY = e.clientY;
                startLeft = containerRect.left;
                startTop = containerRect.top;

                // 添加拖动时的视觉反馈
                document.body.style.cursor = 'move';
                document.body.style.userSelect = 'none';
                container.style.opacity = '0.9';
                dragIconButton.style.transform = 'scale(1.1)';

                // 防止 iframe 捕获鼠标事件
                const iframe = container.querySelector('iframe');
                if (iframe) {
                    iframe.style.pointerEvents = 'none';
                }

                document.addEventListener('mousemove', drag, { passive: false });
                document.addEventListener('mouseup', stopDrag);

                // 防止文本选择和默认拖动行为
                e.preventDefault();
                e.stopPropagation();
            }

            function drag(e) {
                if (!isDragging) return;

                // 防止默认行为
                e.preventDefault();
                e.stopPropagation();

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                let newLeft = startLeft + deltaX;
                let newTop = startTop + deltaY;

                // 边界限制
                const rect = container.getBoundingClientRect();
                const maxLeft = window.innerWidth - rect.width;
                const maxTop = window.innerHeight - rect.height;

                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                newTop = Math.max(0, Math.min(newTop, maxTop));

                container.style.left = `${newLeft}px`;
                container.style.top = `${newTop}px`;
                container.style.right = 'auto';
                container.style.bottom = 'auto';

                // 实时更新所有按钮位置
                updateAllButtonPositions();
            }

            function updateAllButtonPositions() {
                // 使用 requestAnimationFrame 确保在下一帧更新，避免布局抖动
                requestAnimationFrame(() => {
                    const rect = container.getBoundingClientRect();

                    // 更新关闭按钮位置
                    closeButton.style.top = `${rect.top - 12}px`;
                    closeButton.style.left = `${rect.right - 20}px`;

                    // 更新最小化按钮位置
                    minimizeButton.style.top = `${rect.top - 12}px`;
                    minimizeButton.style.left = `${rect.right - 60}px`;

                    // 更新拖动图标位置
                    dragIconButton.style.top = `${rect.top - 12}px`;
                    dragIconButton.style.left = `${rect.right - 100}px`;
                });
            }

            function stopDrag(e) {
                if (!isDragging) return;

                isDragging = false;

                // 恢复样式
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
                container.style.opacity = '1';
                dragIconButton.style.transform = 'scale(1)';

                // 恢复 iframe 的鼠标事件
                const iframe = container.querySelector('iframe');
                if (iframe) {
                    iframe.style.pointerEvents = 'auto';
                }

                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', stopDrag);

                // 拖动结束后强制更新所有按钮位置，确保精确定位
                setTimeout(() => {
                    updateAllButtonPositions();
                }, 0);

                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        }

        // 边缘拖动改变大小功能
        function enableResizing(container, closeButton, minimizeButton, dragIconButton) {
            const resizeHandleSize = 10; // 可拖动的边缘宽度
            let isResizing = false;
            let resizeDirection = '';
            let startX, startY, startWidth, startHeight, startLeft, startTop;

            // 创建调整大小的光标样式
            function updateCursor(e) {
                const rect = container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                let cursor = 'default';

                // 检查是否在边缘
                const isLeft = x < resizeHandleSize;
                const isRight = x > rect.width - resizeHandleSize;
                const isTop = y < resizeHandleSize;
                const isBottom = y > rect.height - resizeHandleSize;

                if ((isLeft && isTop) || (isRight && isBottom)) {
                    cursor = 'nwse-resize';
                } else if ((isRight && isTop) || (isLeft && isBottom)) {
                    cursor = 'nesw-resize';
                } else if (isLeft || isRight) {
                    cursor = 'ew-resize';
                } else if (isTop || isBottom) {
                    cursor = 'ns-resize';
                }

                container.style.cursor = cursor;
            }

            // 监听容器上的鼠标移动
            container.addEventListener('mousemove', updateCursor);

            // 监听容器上的鼠标按下
            container.addEventListener('mousedown', (e) => {
                const rect = container.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                // 检查是否在边缘
                const isLeft = x < resizeHandleSize;
                const isRight = x > rect.width - resizeHandleSize;
                const isTop = y < resizeHandleSize;
                const isBottom = y > rect.height - resizeHandleSize;

                if (!isLeft && !isRight && !isTop && !isBottom) {
                    return; // 不在边缘，不处理
                }

                isResizing = true;
                startX = e.clientX;
                startY = e.clientY;
                startWidth = rect.width;
                startHeight = rect.height;
                startLeft = rect.left;
                startTop = rect.top;

                // 确定拖动方向
                resizeDirection = '';
                if (isLeft) resizeDirection += 'left';
                if (isRight) resizeDirection += 'right';
                if (isTop) resizeDirection += 'top';
                if (isBottom) resizeDirection += 'bottom';

                // 防止 iframe 捕获鼠标事件
                const iframe = container.querySelector('iframe');
                if (iframe) {
                    iframe.style.pointerEvents = 'none';
                }

                document.addEventListener('mousemove', resize, { passive: false });
                document.addEventListener('mouseup', stopResize);

                e.preventDefault();
                e.stopPropagation();
            });

            function resize(e) {
                if (!isResizing) return;

                e.preventDefault();
                e.stopPropagation();

                const deltaX = e.clientX - startX;
                const deltaY = e.clientY - startY;

                let newWidth = startWidth;
                let newHeight = startHeight;
                let newLeft = startLeft;
                let newTop = startTop;

                // 根据拖动方向调整大小
                if (resizeDirection.includes('left')) {
                    newWidth = startWidth - deltaX;
                    newLeft = startLeft + deltaX;
                } else if (resizeDirection.includes('right')) {
                    newWidth = startWidth + deltaX;
                }

                if (resizeDirection.includes('top')) {
                    newHeight = startHeight - deltaY;
                    newTop = startTop + deltaY;
                } else if (resizeDirection.includes('bottom')) {
                    newHeight = startHeight + deltaY;
                }

                // 最小宽度和高度限制
                const minWidth = 300;
                const minHeight = 300;
                newWidth = Math.max(minWidth, newWidth);
                newHeight = Math.max(minHeight, newHeight);

                // 最大宽度和高度限制
                const maxWidth = window.innerWidth - 20;
                const maxHeight = window.innerHeight - 20;
                newWidth = Math.min(maxWidth, newWidth);
                newHeight = Math.min(maxHeight, newHeight);

                // 应用新的尺寸和位置
                container.style.width = `${newWidth}px`;
                container.style.height = `${newHeight}px`;
                container.style.left = `${newLeft}px`;
                container.style.top = `${newTop}px`;
                container.style.right = 'auto';
                container.style.bottom = 'auto';

                // 检查宽度是否小于600px，如果是则自动折叠智能体列表
                const agentPanel = document.getElementById(agentListId);
                if (agentPanel) {
                    if (newWidth < 600 && !isAgentListCollapsed) {
                        // 自动折叠
                        const toggleButton = document.getElementById("dify-agent-list-toggle");
                        if (toggleButton) {
                            toggleAgentListCollapse(agentPanel, toggleButton);
                        }
                    } else if (newWidth >= 400 && isAgentListCollapsed) {
                        // 自动展开
                        const toggleButton = document.getElementById("dify-agent-list-toggle");
                        if (toggleButton) {
                            toggleAgentListCollapse(agentPanel, toggleButton);
                        }
                    }
                }

                // 实时更新所有按钮位置
                updateAllButtonPositions();
            }

            function updateAllButtonPositions() {
                requestAnimationFrame(() => {
                    const rect = container.getBoundingClientRect();

                    closeButton.style.top = `${rect.top - 12}px`;
                    closeButton.style.left = `${rect.right - 20}px`;

                    minimizeButton.style.top = `${rect.top - 12}px`;
                    minimizeButton.style.left = `${rect.right - 60}px`;

                    dragIconButton.style.top = `${rect.top - 12}px`;
                    dragIconButton.style.left = `${rect.right - 100}px`;
                });
            }

            function stopResize(e) {
                if (!isResizing) return;

                isResizing = false;

                // 恢复 iframe 的鼠标事件
                const iframe = container.querySelector('iframe');
                if (iframe) {
                    iframe.style.pointerEvents = 'auto';
                }

                document.removeEventListener('mousemove', resize);
                document.removeEventListener('mouseup', stopResize);

                // 调整大小结束后强制更新所有按钮位置
                setTimeout(() => {
                    updateAllButtonPositions();
                }, 0);

                if (e) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            }
        }
    }

    // 直接执行
    embedChatbot();
}

export const closeChatbot = () => {
  // 删除聊天机器人容器（包含 agent 列表和 iframe）
  const targetContainer = document.getElementById(iframeId + "-container");
  if (targetContainer) {
    // 移除点击外部监听器
    if (targetContainer.removeClickOutsideListener) {
      targetContainer.removeClickOutsideListener();
    }
    // 移除按钮位置监听器
    if (targetContainer.cleanupButtonPositionListeners) {
      targetContainer.cleanupButtonPositionListeners();
    }
    targetContainer.remove();
  }

  // 删除关闭按钮
  const targetCloseButton = document.getElementById(closeButtonId);
  if (targetCloseButton) {
    targetCloseButton.remove();
  }

  // 删除最小化按钮
  const targetMinimizeButton = document.getElementById(minimizeButtonId);
  if (targetMinimizeButton) {
    targetMinimizeButton.remove();
  }

  // 删除拖动图标
  const targetDragIcon = document.getElementById("dify-drag-icon-button");
  if (targetDragIcon) {
    targetDragIcon.remove();
  }

  // 删除最小化图标
  const targetMinimizedIcon = document.getElementById("dify-minimized-icon");
  if (targetMinimizedIcon) {
    targetMinimizedIcon.remove();
  }
}