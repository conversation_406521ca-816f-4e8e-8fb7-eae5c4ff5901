<template>
    <el-popover ref="popover1" placement="top" width="400" trigger="hover">
        <template #reference>
            <div>
                <i class="element-icons el-icon-ait_aizhushou"
                    style="color: var(--el-color-primary);font-size: 26px;"></i>
            </div>
        </template>
        <div class="agent-list-container">
            <div class="agent-list-header">
                <span class="header-title">可用智能体</span>
                <span class="agent-count">{{ store.state.agent.agentTypeList.length }}</span>
            </div>
            <div class="agent-list-content">
                <div v-for="agent in store.state.agent.agentTypeList" :key="agent.id" class="agent-item"
                    @click="handleAgentClick(agent)">
                    <div class="agent-icon">
                         <img style="width: 60px; height: 60px; border-radius: 50%;"  :src="agent.logo" />
                    </div>
                    <div class="agent-info">
                        <div class="agent-name">{{ agent.dictValue }}</div>
                        <div class="agent-desc" v-if="agent.remark">{{ agent.remark }}</div>
                    </div>
                </div>
                <div v-if="store.state.agent.agentTypeList === 0" class="empty-state">
                    <i class="el-icon-box"></i>
                    <p>暂无可用智能体</p>
                </div>
            </div>
        </div>
    </el-popover>
</template>

<script setup>
import axios from 'axios';
import { ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { initBot, closeChatbot } from './js';
import website from '@/config/website.js';
import { useStore } from 'vuex';
const store = useStore()

let agentTypeList = ref([]);


function handleAgentClick(agent) {
    ElMessage.success(`已选择智能体: ${agent.dictValue}`);
    // TODO: 在这里添加智能体点击后的逻辑
    console.log('选中的智能体:', agent);
    const config = {
        token: agent.dictKey,
        baseUrl: website.aiChatUrl,
        inputs: {
            // You can define the inputs from the Start node here
            // key is the variable name
            // e.g.
            // name: "NAME"
            user_id: store.getters.userInfo.user_id,
            tenant_id: store.getters.userInfo.tenant_id,
        },
        systemVariables: {
            user_id: store.getters.userInfo.user_id,
            // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',

        },
        userVariables: {
            avatar_url: store.getters.userInfo.avatar,
            name: store.getters.userInfo.real_name,
            user_id: store.getters.userInfo.user_id,
        },
        dynamicScript: true,
        mode: 'chat'
    }
    initBot(config);
}
</script>

<style lang="scss" scoped>
.agent-list-container {
    padding: 8px 0;
}

.agent-list-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 16px 12px;
    border-bottom: 1px solid #f0f0f0;

    .header-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
    }

    .agent-count {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 20px;
        height: 20px;
        padding: 0 6px;
        font-size: 12px;
        color: #fff;
        background: var(--el-color-primary);
        border-radius: 10px;
    }
}

.agent-list-content {
    max-height: 400px;
    overflow-y: auto;

    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-thumb {
        background: #dcdfe6;
        border-radius: 3px;

        &:hover {
            background: #c0c4cc;
        }
    }
}

.agent-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        background: #f5f7fa;

        .agent-icon {
            background: var(--el-color-primary);
            color: #fff;
            transform: scale(1.05);
        }
    }

    &:active {
        transform: scale(0.98);
    }
}

.agent-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: #ecf5ff;
    color: var(--el-color-primary);
    border-radius: 8px;
    font-size: 20px;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.agent-info {
    flex: 1;
    min-width: 0;
}

.agent-name {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.agent-desc {
    font-size: 12px;
    color: #909399;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #909399;

    i {
        font-size: 48px;
        margin-bottom: 12px;
        opacity: 0.5;
    }

    p {
        margin: 0;
        font-size: 14px;
    }
}
</style>