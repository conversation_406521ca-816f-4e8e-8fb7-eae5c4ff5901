<template>
  
  <basic-container :shadow="props.sealContractId || props.customerId? 'never': 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; justify-content: flex-start; align-items: center">
          <el-button type="primary" @click="handleAdd" icon="plus">新增</el-button>
          <div class="stats-container" style="display: flex; margin-left: 50px">
            <div class="stat-item">
              <div class="stat-label">
                <el-icon><document /></el-icon>
                工单总数
              </div>
              <el-text class="stat-value" type="primary">{{ totalNumber }}</el-text>
            </div>
          </div>
        </div>
      </template>
      <template #externalHandleUser-form="{ type }">
        <div v-if="type !== 'view'" style="display: flex; align-items: center">
          <div>
            <el-avatar
              shape="circle"
              :size="60"
              style="cursor: pointer; position: relative; overflow: visible; margin-right: 15px"
              v-for="item in form.externalHandleUserList"
            >
              <el-text style="font-weight: bolder; color: #fff">{{ item.realName }}</el-text>
              <!-- <el-icon class="closeIcon" @click="handleDelete(item)" :size="20"
          ><CircleCloseFilled
        /></el-icon> -->
            </el-avatar>
          </div>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;

              justify-content: center;
            "
          >
            <div>
              <!-- <el-button type="primary" icon="plus" @click="handleAddIn" text
                >添加内部工程师</el-button
              > -->
            </div>
            <div>
              <el-button type="primary" icon="plus" @click="handleAddOut" text
                >添加外部工程师</el-button
              >
            </div>
          </div>
        </div>
        <el-text style="padding-left: 15px" disabled v-else>
          {{ form.handleUserName }}
        </el-text>
      </template>
      <template #objectStatus="{ row }">
        <el-tag :type="['info', 'warning', 'success'][row.objectStatus]" effect="plain">{{
          row.$objectStatus
        }}</el-tag>
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)" target="_blank">{{
          row.objectName
        }}</el-link>
      </template>
      <template #filesDetail-form>
        <File :fileList="form.fileList"></File>
      </template>
      <template #completeFiles-form>
        <File :fileList="form.completeFilesList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect ref="userSelectRef" @onConfirm="handleConfirm"></wfUserSelect>
    <userSelect ref="userSelectRefOut" @onConfirm="handleConfirmOut"></userSelect>
    <el-drawer title="详情" v-model="drawer" size="50%">
      <avue-form :option="detailOption" v-model="detailForm">
        <template #taskName>
          <el-descriptions :title="detailForm.objectName">
            <el-descriptions-item label="预约时间">{{
              (detailForm.reservationTime && detailForm.reservationTime.split(' ')[0]) || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务客户">{{
              detailForm.finalCustomer || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务联系人">{{
              detailForm.contact || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              detailForm.contactPhone || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务地址" :span="2">
              {{ detailForm.distributionAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="工单描述" :span="3">
              <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #planTime>
          {{ detailForm.serviceStartTime  }} - {{  detailForm.serviceEndTime }}
        </template>
        <template #files>
          <File :fileList="detailForm.fileList"></File>
        </template>
        <template #completeFiles>
          <File :fileList="detailForm.completeFilesList"></File>
        </template>
        <template #engineerServiceInfo>
          <div
            v-if="
              detailForm.sealContractObjectResultVOList &&
              detailForm.sealContractObjectResultVOList.length > 0
            "
            class="engineer-list"
          >
            <el-card
              v-for="(engineer, index) in detailForm.sealContractObjectResultVOList"
              :key="index"
              class="engineer-card"
              shadow="hover"
            >
              <template #header>
                <div class="engineer-header">
                  <h3>{{ engineer.handleName || '工程师' + (index + 1) }}</h3>
                </div>
              </template>
              <div class="engineer-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">服务开始时间：</span>
                    <span>{{ engineer.serviceStartTime || '--' }}</span>
                  </div>
                  <!-- <div class="info-item">
                    <span class="label">实际完成时间：</span>
                    <span>{{ engineer.completeTime.split(' ')[0] || '--' }}</span>
                  </div> -->
                  <div class="info-item">
                    <span class="label">服务结束时间：</span>
                    <span>{{ engineer.serviceEndTime || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">所用工时：</span>
                    <span>{{ engineer.useTimes || '--' }}小时</span>
                  </div>
                </div>
                <div
                  v-if="engineer.workOrderPhotoList && engineer.workOrderPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">现场图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.workOrderPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.workOrderPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="engineer.handleResultPhotoList && engineer.handleResultPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">处理结果图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.handleResultPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.handleResultPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>

                <div v-if="engineer.serviceReorder" class="info-item">
                  <span class="label">服务复盘：</span>
                  <div class="service-reorder">
                    <pre>{{ engineer.serviceReorder }}</pre>
                  </div>
                </div>

                <div v-if="engineer.completeRemark" class="info-item">
                  <span class="label">备注：</span>
                  <div>{{ engineer.completeRemark }}</div>
                </div>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无工程师服务信息"></el-empty>
        </template>
      </avue-form>
    </el-drawer>
    <el-dialog
      v-model="addDialogVisible"
      width="400"
      :title="'选择类型'"
      style="border-radius: 10px"
      :show-close="false"
      align-center
    >
      <div style="display: flex; align-items: center; justify-content: center">
        <el-button type="primary" @click="handleToAdd(0)" size="large" round>关联合同</el-button>

        <!-- <el-button type="primary" size="large" @click="handleToAdd(1)" round>新增合同</el-button> -->
        <el-button type="primary" @click="handleToAdd(2)" size="large" round>公共事务</el-button>
      </div>
    </el-dialog>
    <wokerOrderDetail ref="wokerOrderDetailRef"></wokerOrderDetail>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import userSelect from './component/userSelect.vue';
import { dateFormat } from '@/utils/date.js';
import wokerOrderDetail from './component/wokerOrderDetail.vue';
const props = defineProps({
  
  sealContractId: String,
  customerId: String,
});
let contactList = ref([]);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 180,
  labelWidth: 120,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    // {
    //   label: '工单编号',
    //   prop: 'objectNo',
    //   display: false,
    // },
    // {
    //   label: '工单名称',
    //   prop: 'objectName',
    //   search: true,
    //   display: false,
    // },
    {
      label: '服务类型',
      type: 'tree',
      display: false,
      props: {
        value: 'id',
        label: 'dictValue',
      },
      rules: [
        {
          required: true,
          message: '请选择工单类型',
          trigger: 'blur',
        },
      ],
      dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
      prop: 'serverType',
    },
    {
      label: '工单名称',
      prop: 'objectName',
      placeholder: '请输入工单类型',
      display: false,
    },
    {
      label: '工单描述',
      prop: 'taskDescription',
      overHidden: true,
      display: false,
    },

    {
      label: '关联合同',
      prop: 'sealContractId',
      placeholder: '请选择关联合同',
      addDisplay: true,
      disabled: props.sealContractId,
      width: 250,
      // search:!props.sealContractId,
      hide:props.sealContractId?true:false,
      editDisplay: true,
      component: 'wf-contract-select',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5',
      },
      change: val => {
        setBaseInfo(val.value);
      },

      formatter: row => {
        return row.contractName;
      },
      rules: [
        {
          required: true,
          message: '请选择关联合同',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '合同名称',
      prop: 'contractName',
      hide: true,
      search: !props.sealContractId,
      display: false,
    },
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '服务时间',
      prop: 'planTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
      overHidden: true,
       formatter: row => {
        return `${dateFormat(new Date(row.serviceStartTime), 'yyyy-MM-dd hh:mm')} - ${dateFormat(new Date(row.serviceEndTime), 'yyyy-MM-dd hh:mm')}`
      },
    },
    {
      label: '发布时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      span: 12,
      component: 'wf-daterange-search',
      searchSpan: 6,
      search: true,
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '派单人',
      prop: 'projectLeaderName',
      width: 110,
      display: false,
      //   search: true,
    },
     {
      label: '工程师',
      prop: 'handleUserName',
      width: 180,
      display: false,
      search: true,
    },
    {
      label: '工单状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      width: 110,
      dicData: [
        {
          label: '待派单',

          value: 0,
        },
        {
          label: '待接单',
          value: 3,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        }, {
          label: '已关单',
          value: 4,
        },
      ],
    },
  ],
  group: [
    {
      label: '合同信息',
      prop: 'contractInfo',
      display: true,

      column: [
        {
          label: '客户类型',
          prop: 'customerType',
          type: 'radio',
          value: 0,
          span: 24,
          dicData: [
            {
              value: 0,
              label: '直接客户',
            },
            {
              value: 1,
              label: '居间客户',
            },
          ],
        },
        {
          label: '关联客户',
          prop: 'customerId',
          placeholder: '请选择关联客户',
          change: val => {
            setCustomerInfo(val.value);
          },
          params: {
            isHasAdd: true,
          },
          component: 'wf-customer-select',
        },
        {
          label: '合同名称',
          prop: 'contractName',
          rules: [
            {
              required: true,
              message: '请输入合同名称',
            },
          ],
        },

        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请选择联系人',
          // disabled: true,
          params: {},
          change: val => {
            setContactInfo(val.value);
          },
        },
        {
          label: '合同金额',
          prop: 'contractTotalPrice',
          placeholder: '请输入合同金额',
          type: 'number',
          rules: [
            {
              required: true,
              message: '请输入合同金额',
            },
          ],
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          type: 'date',
          label: '合同要求交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          change: val => {
            form.value.reservationTime = val.value;
          },
          valueFormat: 'YYYY-MM-DD',
        },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务板块',
          // multiple: true,
          span: 12,
          parent: true,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          display: true,
          filterable: true,
          prop: 'businessType',
          checkStrictly: true,
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 24,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            return res.data;
          },

          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '是否开票',
          prop: 'isNeedInvoice',
          type: 'radio',
          value: 1,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          label: '技术人员',
          prop: 'operationTechnology',
          component: 'wf-user-select',
          checkType: 'checkbox',
          params: {
            userUrl: '/api/blade-system/search/user?functionKeys=engineer',
          },
          span: 12,
        },
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '备注',
          prop: 'taskDescription',
          placeholder: '请输入备注',
          span: 24,
          type: 'textarea',

          showWordLimit: true,
          autosize: {
            minRows: 3,
            maxRows: 4,
          },
        },
      ],
    },
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',

          span: 24,
        },

         {
          label: '服务联系人',
          prop: 'contact',
          type: 'select',
          allowCreate: true,
          filterable: true,
          placeholder: '请选择联系人',
          props: {
            label: 'label',
            value: 'value',
          },

          dicFormatter: res => {
            contactList.value = res.data.records;
            return res.data.records
              .map(item => {
                return {
                  label: item.name,
                  value: item.name,
                  customerId: item.customerId,
                };
              })
              .filter(item => item.customerId == form.value.customerId);
          },
          // disabled: true,
          dicUrl: '/api/vt-admin/customerContact/page',
          params: {},
          change: val => {
            setContactInfoForWoker(contactList.value.find(item => item.name == val) || {});
          },
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        // {
        //   type: 'date',
        //   label: '预约时间',
        //   span: 12,

        //   width: 140,
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //   prop: 'reservationTime',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择预约时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        // {
        //   label: '故障类型',
        //   prop: 'lables',
        //   type: 'select',
        //   dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
        //   span: 12,
        //   props: {
        //     value: 'id',
        //     label: 'dictValue',
        //   },
        // },
        // // {
        // //   label: '现场图',
        // //   type: 'upload',
        // //   value: [],
        // //   dataType: 'string',
        // //   loadText: '附件上传中，请稍等',
        // //   span: 24,
        // //   slot: true,
        // //   prop: 'workOrderPhotos',
        // //   addDisplay: true,
        // //   editDisplay: true,
        // //   viewDisplay: true,
        // //   listType: 'picture-card',
        // //   // align: 'center',
        // //   propsHttp: {
        // //     res: 'data',
        // //     url: 'id',
        // //     name: 'originalName',
        // //     // home: 'https://www.w3school.com.cn',
        // //   },
        // //   action: '/blade-resource/attach/upload',
        // // },
        // {
        //   label: '服务要求',
        //   prop: 'taskDescription',
        //   type: 'textarea',
        //   span: 24,
        // },
      ],
    },

    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          type: 'input',
        },

        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
         {
          type: 'datetime',
          label: '服务开始时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'serviceStartTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'datetime',
          label: '服务结束时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'serviceEndTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '派单人',
          prop: 'projectLeader',
          placeholder: '请选择派单人',

          component: 'wf-user-select',
          params: {
            userUrl: '/api/blade-system/search/user?functionKeys=dispatcher',
          },
          rules: [
            {
              required: true,
              message: '请选择派单人',
            },
          ],
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 是否需要签到
        // {
        //   label: '是否需要签到',
        //   prop: 'isNeedSign',
        //   type: 'radio',
         
          
        //   props: {
        //     value: 'value',
        //     label: 'label',
        //   },
        //   dicData: [
        //     {
        //       label: '需要',
        //       value: 1,
        //     },
        //     {
        //       label: '不需要',
        //       value: 0,
        //     },
        //   ],
        // },
         {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          span: 24,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',

          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '工单描述',
          prop: 'taskDescription',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
      ],
    },

    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '完成时间',
          prop: 'completeTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          type: 'datetime',
          span: 24,
        },
        {
          label: '完成附件',
          span: 24,
          prop: 'completeFiles',
        },
        {
          label: '完成备注',
          prop: 'completeRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});
/*
设置基础信息
*/
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contact = res.data.data.finalCustomerConcat || res.data.data.customerContactName;
      form.value.finalCustomer = res.data.data.finalCustomer || res.data.data.customerName;
      form.value.contactPhone = res.data.data.finalCustomerPhone || res.data.data.customerPhone;
      form.value.distributionAddress = res.data.data.deliveryAddress;
      // form.value.objectName = res.data.data.contractName;
      option.value.group[0].display = false;
    });
}
function setCustomerInfo(id) {
  if (!id) return;
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerAddress = res.data.data.address;

      const customerContact1 = proxy.findObject(option.value.group[0].column, 'customerContact');

      customerContact1.params.Url = '/vt-admin/customerContact/page?customerId=' + id;

      const { id: cotactId, phone, name } = res.data.data.customerContactVO || {};
      const { customerName, address } = res.data.data;

      if (form.value.customerType == 0) {
        form.value.finalCustomer = customerName;
        form.value.contact = name;
        form.value.contactPhone = phone;
        form.value.distributionAddress = res.data.data.address;
      }
      form.value.customerContact = cotactId;
      form.value.customerPhone = phone;
    });
}
function permission(key, row) {
  console.log(row);
  if (['editBtn', 'delBtn'].includes(key) && row.objectStatus != 0) {
    return false;
  } else {
    return true;
  }
}
function setContactInfo(id, type) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { phone } = res.data.data;
      form.value[type] = phone;
    });
}
function setContactInfoForWoker(info) {
  if (!info.phone) return;
  form.value.contactPhone = info.phone;
}
// 添加内部工程师
function handleAddIn() {
  proxy.$refs.userSelectRef.visible = true;
}
function handleConfirm(id) {
  axios
    .get('/api/blade-system/user/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      if (!form.value.externalHandleUserList) {
        form.value.externalHandleUserList = [];
      } else {
        form.value.externalHandleUserList.push(res.data.data);
        form.value.externalHandleUser = form.value.externalHandleUserList
          .map(item => item.id)
          .join(',');
      }
    });
}
function handleConfirmOut(data) {
  if (!form.value.externalHandleUserList) {
    form.value.externalHandleUserList = [data];
  } else {
    form.value.externalHandleUserList.push(...data);
    form.value.externalHandleUser = form.value.externalHandleUserList
      .map(item => item.id)
      .join(',');
  }
}
function handleAddOut() {
  if (form.value.externalHandleUserList && form.value.externalHandleUserList.length >= 1)
    return proxy.$message.warning('只能选择一个外部处理人');
  proxy.$refs.userSelectRefOut.dialogVisible = true;
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNumber = ref(0);
let totalTimes = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        sealContractId:props.sealContractId?props.sealContractId:0,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        selectType: 0,
        ...params.value,
        createTime: null,
        customerId:props.customerId
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/externalPcPageStatistics', {
      params: {
        size,
        current,
          sealContractId:props.sealContractId?props.sealContractId:0,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        selectType:props.sealContractId?null:0,
        ...params.value,
        createTime: null,
         customerId:props.customerId
      },
    })
    .then(res => {
      totalNumber.value = res.data.data.totalNumber;
      totalTimes.value = res.data.data.totalTimes;
    });
}
let router = useRouter();
function beforeOpen(done, type) {
  if (type == 'add') {
    return done();
  }
  // 获取详情
  axios
    .get('/api/vt-admin/sealContractObject/detail', {
      params: {
        id: form.value.id,
      },
    })
    .then(res => {
      // form.value.objectRemark = res.data.data.taskDescription;
      if (['add', 'view'].includes(type)) {
        if (type == 'add') {
        } else {
          option.value.group[0].display = false;
        }
      } else {
        option.value.group[0].display = false;
        form.value.files =
          res.data.data.fileList &&
          res.data.data.fileList.map(item => {
            // 修正拼写错误，将 reuturn 改为 return
            return {
              label: item.originalName,
              value: item.id,
            };
          });

        // form.value.objectRemark = res.data.data.taskDescription;
        form.value.isNeedSign = res.data.data.isNeedSign;
      }
      done();
    });
}
function rowSave(form, done, loading) {
  
  if (type.value != 1) {
    const data = {
      ...form,

      files: form.files && form.files.map(item => item.value).join(','),
      // taskDescription: form.objectRemark,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } else {
    addContract(form, done, loading);
  }
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    deliveryAddress: form.distributionAddress,
    finalCustomer: form.finalCustomer,
    contractType: 2,

    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        form.sealContractId = res.data.data;

        const data = {
          ...form,

          files: form.files && form.files.map(item => item.url).join(','),
          // taskDescription: form.objectRemark,
        };
        axios
          .post(addUrl, data)
          .then(res => {
            if (res.data.code == 200) {
              proxy.$message.success(res.data.msg);
              onLoad();
              done();
            }
          })
          .catch(err => {});
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
    // taskDescription: row.objectRemark,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let drawer = ref(false);
let detailForm = ref({});
let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        // {
        //   type: 'date',
        //   label: '预约时间',
        //   span: 12,

        //   width: 140,
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //   prop: 'reservationTime',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择预约时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        // {
        //   label: '故障类型',
        //   prop: 'lables',
        //   type: 'select',
        //   props: {
        //     value: 'id',
        //     label: 'dictValue',
        //   },
        //   dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
        //   span: 12,
        // },
        // // {
        // //   label: '现场图',
        // //   type: 'upload',
        // //   value: [],
        // //   dataType: 'string',
        // //   loadText: '附件上传中，请稍等',
        // //   span: 24,
        // //   slot: true,
        // //   prop: 'workOrderPhotos',
        // //   addDisplay: true,
        // //   editDisplay: true,
        // //   viewDisplay: false,
        // //   listType: 'picture-card',
        // //   // align: 'center',
        // //   propsHttp: {
        // //     res: 'data',
        // //     url: 'id',
        // //     name: 'originalName',
        // //     // home: 'https://www.w3school.com.cn',
        // //   },
        // //   action: '/blade-resource/attach/upload',
        // // },
        // {
        //   label: '服务要求',
        //   prop: 'taskDescription',
        //   type: 'textarea',
        //   span: 24,
        // },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          label: '服务时间',
          prop: 'planTime',
          type: 'date',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
     
         
          overHidden: true,
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 发布人
        {
          label: '发布人',
          prop: 'createName',
          span: 12,
        },

        // 派单人
        {
          label: '派单人',
          prop: 'projectLeaderName',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 24,
        },
        {
          label: '工单描述',
          prop: 'taskDescription',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '工程师服务信息',
          prop: 'engineerServiceInfo',
          labelWidth: 0,
          span: 24,
          formslot: true,
        },
      ],
    },
  ],
});
let wokerOrderDetailRef = ref(null);
function viewDetail(row) {
   wokerOrderDetailRef.value.open(row.id)
   
}

let addDialogVisible = ref(false);
let type = ref(0);
function handleAdd() {
  if(props.sealContractId){
    type.value = 0;
    handleToAdd(type.value);
    form.value.sealContractId = props.sealContractId
      setBaseInfo(form.value.sealContractId);
    return 
  }
  addDialogVisible.value = true;
  // router.push({
  //   path: '/Order/salesOrder/compoents/addOrder',
  // });
}

function handleToAdd(value) {
  type.value = value;
  const sealContractIdRef = proxy.findObject(option.value.column, 'sealContractId');
  sealContractIdRef.display = value == 0;
  option.value.group[0].display = value == 1;
  proxy.$refs.crud.rowAdd();
  addDialogVisible.value = false;
}
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translaTypeteY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}

/* 工程师服务信息样式 */
.engineer-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.engineer-card {
  margin-bottom: 0;
  border-radius: 6px;
  overflow: hidden;
}

.engineer-card :deep(.el-card__header) {
  padding: 10px 15px;
  background-color: #f5f7fa;
}

.engineer-card :deep(.el-card__body) {
  padding: 12px 15px;
}

.engineer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.engineer-header h3 {
  margin: 0;
  font-size: 15px;
  font-weight: 600;
  color: #303133;
}

.engineer-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 8px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.label {
  font-weight: 600;
  color: #606266;
  margin-right: 4px;
  font-size: 13px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 5px;
}

.result-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 3px;
  cursor: pointer;
  transition: transform 0.2s;
}

.result-image:hover {
  transform: scale(1.05);
}

.service-reorder {
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 3px;
  margin-top: 3px;
  font-size: 13px;
}

.service-reorder pre {
  white-space: pre-wrap;
  font-family: inherit;
  margin: 0;
  font-size: 13px;
}
/* 工程师服务信息样式 */
.engineer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.engineer-card {
  margin-bottom: 0;
  border-radius: 4px;
  overflow: hidden;
}

.engineer-card :deep(.el-card__header) {
  padding: 8px 12px;
  background-color: #f5f7fa;
}

.engineer-card :deep(.el-card__body) {
  padding: 10px 12px;
}

.engineer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.engineer-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.engineer-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 6px;
  margin-bottom: 6px;
}

.info-item {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

.label {
  font-weight: 600;
  color: #606266;
  margin-right: 3px;
  font-size: 12px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.result-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 2px;
  cursor: pointer;
  transition: transform 0.2s;
}

.result-image:hover {
  transform: scale(1.05);
}

.service-reorder {
  background-color: #f5f7fa;
  padding: 6px;
  border-radius: 2px;
  margin-top: 2px;
  font-size: 12px;
}

.service-reorder pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  font-size: 12px;
  line-height: 1.3;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.info-card {
  margin-bottom: 3px;
  background: white;
  border-radius: 0px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  // background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  padding: 16px 20px;
  color: #000;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 20px;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.description-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .file-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
<style>
.avue-crud__left {
  flex: 1;
}
</style>
