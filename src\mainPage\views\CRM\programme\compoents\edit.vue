<template>
  <div class="wrap">
    <div :style="{ height: isFold ? 'auto' : 'auto' }" style="transition: all 0.3s">
      <el-row :gutter="10" v-if="!isFold">
        <el-col :span="5">
          <el-card :body-style="{ padding: 0 }" shadow="never" class="modern-summary-card" style="
              height: 100%;
              overflow-y: auto;
              font-size: 14px;
              padding: 16px;
              box-sizing: border-box;

              border: 1px solid #e2e8f0;
              box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            ">
            <div class="summary-title">
              <span class="title-text">所有汇总</span>
              <div class="title-decoration"></div>
            </div>
            <div style="display: flex; gap: 20px">
              <div style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                ">
                <div style="color: #666">
                  成本金额：
                  <span style="color: #000">{{ (allModuleTotal.costTotal || 0).toFixed(2) }}</span>
                </div>
                <div style="color: #666">
                  总金额：
                  <span style="color: #000">{{
                    ((allModuleTotal.total || 0) * 1).toFixed(2)
                  }}</span>
                </div>

                <div style="color: #666">
                  客户报价：
                  <span>{{
                    (
                      (allModuleTotal.total || 0) - ((form.discountsPrice * 1).toFixed(2) || 0)
                    )?.toFixed(2)
                  }}</span>
                </div>
              </div>
              <div style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                ">
                <div style="color: #666">
                  毛利润 ：<span>{{
                    ((allModuleTotal.total || 0) - (allModuleTotal.costTotal || 0) - (form.discountsPrice ||
                      0)).toFixed(2)
                    }}</span>
                </div>
                <!-- <div style="color: #666">
                  折减：
                  <span style="color: var(--el-color-danger)">
                    {{ (form.discountsPrice * 1).toFixed(2) || `0.00` }}
                  </span>
                  <el-button
                    @click="editDiscountsPrice"
                    type="primary"
                    icon="edit"
                    v-if="$route.query.type != 'detail' && !props.dialogView"
                    text
                  ></el-button>
                </div> -->
                <div style="color: #666">
                  毛利润率：
                  <span>{{
                    (
                      ((allModuleTotal.total || 0) -
                        ((form.discountsPrice * 1).toFixed(2) || 0) -
                        (allModuleTotal.costTotal || 0)) /
                      ((allModuleTotal.total || 0) - ((form.discountsPrice * 1).toFixed(2) || 0) ||
                        1)
                    ).toFixed(2)
                  }}</span>
                </div>
              </div>
            </div>
          </el-card></el-col>
        <el-col :span="6">
          <el-card :body-style="{ padding: 0 }" shadow="never" style="
              height: 100%;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
              overflow-y: auto;
            ">
            <div v-if="form.currentModule > 0">
              <div style="font-size: 14px; font-weight: bolder">
                当前模块汇总:<el-text type="primary">{{
                  moduleDTOList.find((item, index) => index == form.currentModule).moduleName
                }}</el-text>
                <el-icon v-if="$route.query.type != 'detail' && !props.dialogView"
                  @click="addModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer" title="编辑">
                  <edit />
                </el-icon>
                <el-icon v-if="$route.query.type != 'detail' && !props.dialogView"
                  @click="copyModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer" title="复制模块">
                  <CopyDocument />
                </el-icon>
                <el-icon @click="delModule" title="删除模块" v-if="$route.query.type != 'detail' && !props.dialogView"
                  style="margin-left: 5px; cursor: pointer; color: var(--el-color-danger)">
                  <delete />
                </el-icon>
                <el-icon @click="saveModuleTemplate" title="保存模板"
                  style="margin-left: 5px; cursor: pointer; color: var(--el-color-primary)">
                  <Document />
                </el-icon>
              </div>
              <div style="display: flex; gap: 20px">
                <div style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                  ">
                  <div style="color: #666">
                    设备金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalProductPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    人工金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalLaborCost || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    其他金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalQtPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    延保金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalYbPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666" v-if="!form.isHasTax">
                    税金：
                    <span style="color: #000">{{
                      (currentModuleTotal.taxPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    报价金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.total || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润 ：<span style="color: black">{{
                      (
                        (currentModuleTotal.total || 0) - (currentModuleTotal.costTotal || 0)
                      ).toFixed(2)
                    }}</span>
                  </div>
                </div>
                <div style="display: flex; flex-direction: column; justify-content: space-between">
                  <div style="color: #666">
                    设备成本金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalCostPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    人工成本金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalRgcbPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666" v-if="!form.isHasTax">
                    成本税金：
                    <span style="color: #000">{{
                      (currentModuleTotal.costTaxPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    其他成本金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalQtcbPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    延保成本金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.totalYbcbPrice || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    报价成本金额：
                    <span style="color: #000">{{
                      (currentModuleTotal.costTotal || 0).toFixed(2)
                    }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润率：
                    <span style="color: black">{{
                      isNaN(
                        (
                          ((currentModuleTotal.total || 0) - (currentModuleTotal.costTotal || 0)) /
                          (currentModuleTotal.total || 1)
                        ).toFixed(2)
                      )
                        ? '--'
                        : (
                          (((currentModuleTotal.total || 0) -
                            (currentModuleTotal.costTotal || 0)) /
                            (currentModuleTotal.total || 1)) *
                          100
                        ).toFixed(2) + '%'
                    }}</span>
                  </div>
                </div>
                <div style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 10px;
                  "></div>
              </div>
            </div>
          </el-card></el-col>
        <el-col :span="5">
          <el-card :body-style="{ padding: 0 }" shadow="never"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box; font-size: 14px">
            <div style="font-size: 14px; font-weight: bolder">
              产品参考信息:
              <el-text type="primary" style="overflow: hidden; white-space: nowrap">{{
                referInfo.productName
              }}</el-text>
            </div>
            <div style="display: flex; gap: 10px">
              <div style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                ">
                <div style="color: #666">
                  最低销售价： <span style="color: #000">{{ referInfo.minSealPrice }}</span>
                </div>
                <div style="color: #666">
                  最近设备销售价： <span style="color: #000">{{ referInfo.preSealPrice }}</span>
                </div>
                <!-- <div style="color: #666">
                最近人工销售价： <span style="color: #000">{{ `--` }}</span>
              </div>
              <div style="color: #666">
                最近其他销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->

                <div style="color: #666">
                  参考销售价： <span style="color: #000">{{ referInfo.referSealPrice }}</span>
                </div>
              </div>
              <div style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                ">
                <!-- <div style="color: #666">
                最近延保销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->
              </div>
            </div>
          </el-card></el-col>
        <el-col :span="4">
          <el-card shadow="never" :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box">
            <div style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
              快捷配置:
              <el-switch v-model="form.isLock" size="small" v-if="$route.query.type != 'detail' && !props.dialogView"
                active-text="锁定" inactive-text="解锁" />
              <el-button type="primary" text size="small" @click="colmunDrawer = !colmunDrawer">显示设置</el-button>
            </div>
            <el-row>
              <el-form>
                <el-form-item style="margin: 0" label="设备利润比:">
                  <el-col :span="12">
                    <el-popconfirm hide-icon @confirm="setPrice(form.productTax)" title="修改将会重新计算所有产品设备单价，是否确认?">
                      <template #reference>
                        <el-input size="small" :disabled="form.isLock || $route.query.type == 'detail' || props.dialogView
                          " v-model="form.productTax" placeholder="请输入"></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
                <el-form-item style="margin: 0" label="人工利润比:">
                  <el-col :span="12">
                    <el-popconfirm hide-icon @confirm="setLaborPrice(form.laborTax)" title="修改将会重新计算所有产品人工单价，是否确认?">
                      <template #reference>
                        <el-input size="small" :disabled="form.isLock || $route.query.type == 'detail' || props.dialogView
                          " v-model="form.laborTax" placeholder="请输入"></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
              </el-form>
            </el-row>
          </el-card></el-col>
        <el-col :span="4">
          <el-card shadow="never" :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box">
            <div style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              ">
              导出配置:
              <el-switch style="margin-right: 10px" v-model="form.isHasTax" size="small"
                v-if="$route.query.type != 'detail' && !props.dialogView" active-text="含税" inactive-text="未税"
                :active-value="1" :inactive-value="0" />
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              设备税率：
              <el-input-number size="small" style="width: 60%" step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.productRate" placeholder="请输入"></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              人工税率：
              <el-input-number size="small" style="width: 60%" step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView" v-model="form.labourRate"
                placeholder="请输入"></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              延保税率：
              <el-input-number size="small" step="0.01" style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView"
                v-model="form.warrantyRate" placeholder="请输入"></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              其他税率：
              <el-input-number size="small" step="0.01" style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail' || props.dialogView" v-model="form.otherRate"
                placeholder="请输入"></el-input-number>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24" style="margin-bottom: 0px">
          <el-card shadow="never" class="modern-module-tabs"
            :body-style="{ padding: '0px', width: '100%', overflowX: 'auto' }" :key="randomKey">
            <el-radio-group @change="handleModuleChange()" style="margin-right: 10px; flex-wrap: nowrap"
              v-model="form.currentModule" size="default" ref="sort-buttons" :disabled="moduleChangeLoading">
              <el-radio-button v-for="(item, index) in moduleDTOList" :key="item.uuid" :label="index"
                :class="{ move1: true }" :disabled="moduleChangeLoading">{{ item.moduleName }}</el-radio-button>
              <el-button type="" size="default" title="添加模块" v-if="$route.query.type != 'detail' && !props.dialogView"
                @click="addModule" style="width: 50px; margin-left: 5px; border-radius: 1px" icon="plus"
                :disabled="moduleChangeLoading"></el-button>

              <!-- 模块切换加载状态指示器 -->
              <div v-if="moduleChangeLoading" class="module-loading-indicator"
                style="display: inline-flex; align-items: center; margin-left: 15px">
                <el-icon class="is-loading" style="margin-right: 5px; color: #409eff">
                  <Loading />
                </el-icon>
                <span style="color: #409eff; font-size: 12px">正在切换模块...</span>
              </div>
              <el-button type="" size="default" title="从模板库选择" v-if="$route.query.type != 'detail' && !props.dialogView"
                @click="addTemplate(1)" style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="CopyDocument"></el-button>
            </el-radio-group>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-row id="table_box">
      <div style="
          display: flex;
          cursor: pointer;
          justify-content: center;
          background-color: #ccc;
          width: 100%;
        " ref="heightAutoRef" @click="handleResetHeight">
        <el-icon :style="{ transform: `rotate(${isFold ? '-90deg' : '90deg'})` }">
          <DArrowLeft />
        </el-icon>
      </div>
    </el-row>
    <mySplit v-model="splitValue" min="0">
      <template v-slot:left>
        <div style="height: 100%; position: relative; padding: 5px; box-sizing: border-box;background-color: #fff;"
          shadow="never">
          <div v-show="moduleDTOList[form.currentModule].isHasClassify != 1 && form.currentModule != 0"
            ref="category_box">
            <el-tag effect="plain" plain size="large" class="modern-category-tag" @click="handleScroll(index)"
              v-for="(item, index) in categoryList" :title="item" :key="item">
              <el-icon v-if="$route.query.type != 'detail' && !props.dialogView" class="sort">
                <sort></sort>
              </el-icon>
              {{ item }}</el-tag>
          </div>
          <div v-show="moduleDTOList[form.currentModule].isHasClassify != 1 && form.currentModule != 0"
            style="display: flex; gap: 0; justify-content: space-between">
            <el-button type="" title="添加分类" v-if="$route.query.type != 'detail' && !props.dialogView" style="width: 50%"
              @click="addCategory" icon="plus"></el-button>
            <el-button type="" title="从模板库选择" v-if="$route.query.type != 'detail' && !props.dialogView"
              style="width: 50%" @click="addTemplate(2)" icon="CopyDocument"></el-button>
          </div>
          <!-- <div
            style="
              height: 50%;
              width: auto;
              box-shadow: var(--el-box-shadow-light);
              position: absolute;
              bottom: 0;
              left: 5px;
              box-sizing: border-box;
              right: 5px;
              padding: 5px;
            "
          >
            <div style="height: 20px; overflow: hidden">
              <el-text type="primary">{{ currentProduct.customProductName }}</el-text>
            </div>
            <textarea
              style="
                height: calc(100% - 20px);
                overflow: auto;
                font-size: 14px;
                border: none;
                resize: none;
              "
              :disabled="$route.query.type == 'detail'"
              v-model="currentProduct.customProductDescription"
            >
            </textarea>
          </div> -->
        </div>
      </template>
      <template v-slot:right>
        <div ref="tableCard" class="myCard table-container" shadow="never" :body-style="{ padding: 0 }"
          style="box-sizing: border-box; position: relative" v-loading="moduleChangeLoading"
          element-loading-text="正在切换模块..." element-loading-background="rgba(255, 255, 255, 0.8)">
          <div v-if="form.currentModule == 0">
            <el-table class="avue_table avue-crud" style="margin-top: 0" :data="allData" ref="allTableRef"
              @select="handleSelect" @select-all="handleSelectAll" row-key="uuid"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" border>
              <!-- <el-table-column type="expand" width="50">
                <template #default="{ row }">
                  <div style="margin-left: 50px">
                    <el-table class="avue_table" :show-header="false" :data="row.children" border>
                      <el-table-column
                        label="序号"
                        width="80"
                        type="index"
                        align="center"
                      ></el-table-column>
                      <el-table-column
                        label="子系统名称"
                        prop="classify"
                        align="center"
                      ></el-table-column>
                      <el-table-column label="设备金额" prop="total" align="center">
                        <template #default="{ row }">
                          {{ row.totalProductPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="人工金额" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalLaborCost.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="延保金额" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalYbPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="其它金额" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalQtPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="报价金额" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.total.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="设备成本" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalCostPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="人工成本" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalRgcbPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="延保成本" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalYbcbPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="其它成本" prop="total" align="center">
    <template #default="{ row }">
                          {{ row.totalQtcbPrice.toFixed(2) }}
                        </template>
  </el-table-column>
  <el-table-column label="成本金额" prop="costTotal" align="center">
    <template #default="{ row }">
                          {{ row.costTotal.toFixed(2) }}
                        </template></el-table-column>
  <el-table-column label="毛利润" prop="costTotal" align="center">
    <template #default="{ row }">
                          {{ (row.total - row.costTotal).toFixed(2) }}
                        </template>
  </el-table-column>

  <el-table-column label="毛利润率" prop="costTotal" align="center">
    <template #default="{ row }">
                          {{
                            isNaN(((row.total - row.costTotal) / row.total).toFixed(2))
                              ? '--'
                              : ((row.total - row.costTotal) / row.total).toFixed(2)
                          }}
                        </template>
  </el-table-column>
  </el-table>
  </div>
  </template>
  </el-table-column> -->
              <el-table-column type="selection" v-if="$route.query.type != 'detail' && !props.dialogView" width="55">
                <template #header> 111 </template>
              </el-table-column>
              <el-table-column label="序号" width="80" type="index" align="center"></el-table-column>
              <el-table-column label="模块名称" width="200" prop="moduleName" align="center">
              </el-table-column>
              <el-table-column label="设备金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalProductPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="人工金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalLaborCost.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="延保金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalYbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="其它金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalQtPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="报价金额" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.total.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="设备成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalCostPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="人工成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalRgcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="延保成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalYbcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="其它成本" prop="total" align="center">
                <template #default="{ row }">
                  {{ row.totalQtcbPrice.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="成本金额" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{ row.costTotal.toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column label="毛利润" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{ (row.total - row.costTotal).toFixed(2) }}
                </template>
              </el-table-column>

              <el-table-column label="毛利润率" prop="costTotal" align="center">
                <template #default="{ row }">
                  {{
                    isNaN(((row.total - row.costTotal) / row.total).toFixed(2))
                      ? '--'
                      : ((row.total - row.costTotal) / row.total).toFixed(2)
                  }}
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div style="width: 100%; height: 100%" v-else>
            <!-- 使用虚拟滚动表格优化性能 -->
            <VirtualTable :table-data="tableData" :column-hide-data="columnHideData"
              :is-current-editing-product="isCurrentEditingProduct"
              :enable-drag="$route.query.type != 'detail' && !props.dialogView" @product-click="setReferPrice"
              @drag-end="handleVirtualTableDragEnd" ref="virtualTableRef">
              <!-- 分类标题行的操作按钮插槽 -->
              <template #category-title-actions="{ category }">
                <el-icon v-if="
                  $route.query.type != 'detail' &&
                  moduleDTOList[form.currentModule].isHasClassify != 1 &&
                  !props.dialogView
                " @click="editCategory(category.classify)" style="margin-left: 5px; cursor: pointer">
                  <edit />
                </el-icon>
                <el-button v-if="currentIndex == category.classify" @click="currentIndex = null" type="primary"
                  text>退出编辑模式</el-button>
                <productSelectDrop v-if="$route.query.type != 'detail' && !props.dialogView" @select="
                  id => {
                    form.currentclassify = category.classify;
                    handleProductSelectConfirm(id);
                  }
                " style="margin-left: 5px"></productSelectDrop>

                <el-button class="delete_category" type="danger" size="small" v-if="
                  $route.query.type != 'detail' &&
                  moduleDTOList[form.currentModule].isHasClassify != 1 &&
                  !props.dialogView
                " @click="delCategory(category.classify)" style="margin-left: 10px" circle title="删除"
                  icon="delete"></el-button>

                <el-button class="delete_category" type="primary" size="small" v-if="
                  $route.query.type != 'detail' &&
                  moduleDTOList[form.currentModule].isHasClassify != 1 &&
                  !props.dialogView
                " @click="copyCategory(category.classify)" style="margin-left: 10px" circle title="复制"
                  icon="CopyDocument"></el-button>
                <el-button class="delete_category" type="primary"
                  v-if="moduleDTOList[form.currentModule].isHasClassify != 1" size="small"
                  @click="saveCategoryTemplate(category.classify)" style="margin-left: 10px" circle title="存为分类模板"
                  icon="Document"></el-button>
                <el-button class="delete_category" type="primary" v-if="
                  moduleDTOList[form.currentModule].isHasClassify != 1 &&
                  $route.query.isFromOther == 1
                " size="small" @click="exportProductByFromOhter(category.productList)" style="margin-left: 10px"
                  circle title="导入至分类" icon="Position"></el-button>
              </template>

              <!-- 分类底部的添加产品按钮插槽 -->
              <template #category-actions="{ category }">
                <el-button type="primary" size="small" icon="plus" title="从产品库选择产品"
                  v-if="$route.query.type != 'detail' && !props.dialogView" @click="
                    $refs['product-select'].visible = true;
                  form.currentclassify = category.classify;
                  " style="margin: 2px 0; margin-left: 3px; color: #fff" plain >库</el-button>
                <el-button type="primary" icon="plus" title="添加一个空产品"
                  v-if="$route.query.type != 'detail' && !props.dialogView" @click="addEmptyProduct(category)" plain
                  style="color: #fff" size="small">空</el-button>
                <el-button type="primary" icon="plus" title="从询价单导入"
                  v-if="$route.query.type != 'detail' && !props.dialogView" @click="addProductFromSheet(category)" plain
                  style="color: #fff" size="small">询</el-button>
                <el-button type="primary" icon="plus" title="从之前的方案导入"
                  v-if="$route.query.type != 'detail' && !props.dialogView" @click="exportProductByHistory(category)"
                  style="color: #fff" plain size="small">导</el-button>
              </template>

              <!-- 产品行插槽 -->
              <template #product-row="{ product, index }">
                <ProductRow :product="product" :display-index="customIndex(product.productIndex, product.categoryIndex)"
                  :is-active="currentIndex === product.classify"
                  :is-readOnly="$route.query.type == 'detail' || props.dialogView" :brand-list="brandList"
                  :product-specification-list="productSpecificationList"
                  :visible-columns="columnHideData?.filter(item => item.isShow)"
                  @row-click="() => setReferPrice(product)" @field-focus="currentModelKey = $event" @blur="handleBlur"
                  @delete-product="deleteProduct" @export-product="exportProductByFromOhter"
                  @brand-click="handleProductBrandClick" @get-brand="getBrand"
                  @specification-click="handleSpecificationClick" @get-specification="getProductSpecificationList"
                  @field-change="handleFieldChange" @cell-double-click="handleCellDoubleClick" />
              </template>
            </VirtualTable>
          </div>
        </div>
      </template>
    </mySplit>

    <!-- 可拖动的产品编辑面板 -->
    <div v-if="showEditPanel && currentProduct.uuid && false" class="floating-edit-panel" :style="panelStyle">
      <div class="panel-header" @mousedown="startDrag">
        <div class="header-left">
          <h4>编辑产品信息</h4>
          <div class="product-navigation" v-if="allProducts.length > 0">
            <span class="nav-info"> {{ currentProductIndex + 1 }} / {{ allProducts.length }} </span>
            <el-button type="text" icon="ArrowLeft" @click="switchToPreviousProduct"
              :disabled="currentProductIndex <= 0" style="color: #666; margin-left: 8px" title="上一个产品 (←)"
              size="small"></el-button>
            <el-button type="text" icon="ArrowRight" @click="switchToNextProduct"
              :disabled="currentProductIndex >= allProducts.length - 1" style="color: #666" title="下一个产品 (→)"
              size="small"></el-button>
          </div>
        </div>
        <div class="header-controls">
          <el-button type="text" icon="Plus" @click="insertEmptyProduct" style="color: #409eff; margin-right: 8px"
            title="在当前产品下方插入空行" size="small" :disabled="$route.query.type == 'detail' || props.dialogView"></el-button>
          <el-button type="text" :icon="isCollapsed ? 'ArrowDown' : 'ArrowUp'" @click="toggleCollapse"
            style="color: #666; margin-right: 8px" title="折叠/展开"></el-button>
          <el-button type="text" icon="close" @click="closeEditPanel" style="color: #666" title="关闭"></el-button>
        </div>
      </div>
      <div class="panel-content" style="background-color: #eee" v-show="!isCollapsed">
        <div class="operation-hints">
          <div class="keyboard-hint" v-if="allProducts.length > 1">
            <el-text size="small" type="info">
              <el-icon>
                <ArrowLeft />
              </el-icon>
              <el-icon>
                <ArrowRight />
              </el-icon>
              使用左右箭头键快速切换产品，输入框内容会自动全选
            </el-text>
          </div>
          <div class="insert-hint" v-if="$route.query.type != 'detail' && !props.dialogView">
            <el-text size="small" type="success">
              <el-icon>
                <Plus />
              </el-icon>
              点击标题栏的加号按钮在当前产品下方插入空行
            </el-text>
          </div>
        </div>
        <el-form :model="currentProduct" label-width="100px" size="small" :key="formKey">
          <el-form-item label="产品名称">
            <el-input v-model="currentProduct.customProductName" placeholder="请输入产品名称"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="customProductName"
              @input="value => handlePanelFieldChange('customProductName', value)"></el-input>
          </el-form-item>

          <el-form-item label="品牌">
            <el-input v-model="currentProduct.productBrand" placeholder="请输入品牌"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="productBrand"
              @input="value => handlePanelFieldChange('productBrand', value)"></el-input>
          </el-form-item>

          <el-form-item label="规格型号">
            <el-input v-model="currentProduct.customProductSpecification" placeholder="请输入规格型号"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="customProductSpecification"
              @input="value => handlePanelFieldChange('customProductSpecification', value)"></el-input>
          </el-form-item>

          <el-form-item label="产品描述">
            <el-input v-model="currentProduct.customProductDescription" type="textarea" :rows="3" placeholder="请输入产品描述"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="customProductDescription"
              @input="value => handlePanelFieldChange('customProductDescription', value)"></el-input>
          </el-form-item>

          <el-form-item label="单位">
            <el-input v-model="currentProduct.customUnit" :placeholder="currentProduct.unitName || '请输入单位'"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="customUnit"
              @input="value => handlePanelFieldChange('customUnit', value)"></el-input>
          </el-form-item>

          <el-form-item label="数量">
            <el-input-number v-model="currentProduct.number" :min="0" :precision="2" placeholder="请输入数量"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%" data-field="number"
              @change="value => handlePanelFieldChange('number', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="设备单价">
            <el-input-number v-model="currentProduct.sealPrice" :min="0" :precision="2" placeholder="请输入设备单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%" data-field="sealPrice"
              @change="value => handlePanelFieldChange('sealPrice', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="人工单价">
            <el-input-number v-model="currentProduct.laborCost" :min="0" :precision="2" placeholder="请输入人工单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%" data-field="laborCost"
              @change="value => handlePanelFieldChange('laborCost', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="设备成本单价">
            <el-input-number v-model="currentProduct.costPrice" :min="0" :precision="2" placeholder="请输入设备成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%" data-field="costPrice"
              @change="value => handlePanelFieldChange('costPrice', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="人工成本单价">
            <el-input-number v-model="currentProduct.rgcbdj" :min="0" :precision="2" placeholder="请输入人工成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%"
              @change="value => handlePanelFieldChange('rgcbdj', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="延保单价">
            <el-input-number v-model="currentProduct.ybhsdj" :min="0" :precision="2" placeholder="请输入延保单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%"
              @change="value => handlePanelFieldChange('ybhsdj', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="延保成本单价">
            <el-input-number v-model="currentProduct.ybcbdj" :min="0" :precision="2" placeholder="请输入延保成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%"
              @change="value => handlePanelFieldChange('ybcbdj', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="其他单价">
            <el-input-number v-model="currentProduct.qthsdj" :min="0" :precision="2" placeholder="请输入其他单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%"
              @change="value => handlePanelFieldChange('qthsdj', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="其他成本单价">
            <el-input-number v-model="currentProduct.qtcbdj" :min="0" :precision="2" placeholder="请输入其他成本单价"
              :disabled="$route.query.type == 'detail' || props.dialogView" style="width: 100%"
              @change="value => handlePanelFieldChange('qtcbdj', value)"></el-input-number>
          </el-form-item>

          <el-form-item label="备注">
            <el-input v-model="currentProduct.remark" type="textarea" :rows="2" placeholder="请输入备注"
              :disabled="$route.query.type == 'detail' || props.dialogView" data-field="remark"
              @input="value => handlePanelFieldChange('remark', value)"></el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 调整大小控制点 -->
      <div class="resize-handle resize-right" @mousedown="startResize('right', $event)" v-show="!isCollapsed"></div>
      <div class="resize-handle resize-bottom" @mousedown="startResize('bottom', $event)" v-show="!isCollapsed"></div>
      <div class="resize-handle resize-corner" @mousedown="startResize('corner', $event)" v-show="!isCollapsed"></div>
    </div>

    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select ref="product-select" check-type="box" @onConfirm="handleProductSelectConfirm"
      @openIframe="handleOpenIframe">
    </wf-product-select>
    <el-drawer title="列显隐" size="20%" v-model="colmunDrawer">
      <el-table border :data="columnHideData">
        <el-table-column label="列名称" prop="value"></el-table-column>
        <el-table-column label="隐藏/显示">
          <template #default="scope">
            <el-switch v-model="scope.row.isShow" />
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>

    <templateSelect ref="templateDialog" :templateType="templateType" :level="templateType"
      :businessType="$route.query.businessType" @change="handleTemplateChange"></templateSelect>

    <!-- 单元格编辑弹窗 -->
    <CellEditDialog v-model="cellEditDialog.visible" :field-name="cellEditDialog.fieldName"
      :field-value="cellEditDialog.fieldValue" :product="cellEditDialog.product" :read-only="cellEditDialog.readOnly"
      @confirm="handleCellEditConfirm" />
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import productSelectDrop from '../../quotation/compoents/productSelectDrop.vue';
import { deepClone } from '@/utils/util.js';
const { proxy } = getCurrentInstance();
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
import templateSelect from './templateSelect.vue';
import mySplit from '@/components/my-split/my-split.vue';
import VirtualTable from './VirtualTable.vue';
import ProductRow from './ProductRow.vue';
import CellEditDialog from './CellEditDialog.vue';
let currentIndex = ref(null);
const route = useRoute();
let splitValue = ref('0');
let isFold = ref(false);
let columnHideData = ref([
  // {
  //   value :'产品名称',
  //   isShow:true,
  // },
  // {
  //   value :'品牌',
  //   isShow:true,
  // },
  // {
  //   value :'规格型号',
  //   isShow:true,
  // },
  // {
  //   value :'详细描述',
  //   isShow:true,
  // },
  // {
  //   value :'产品图片',
  //   isShow:true,
  // },
  // {
  //   value :'单位',
  //   isShow:true,
  // },
  // {
  //   value :'数量',
  //   isShow:true,
  // },
  {
    value: '设备单价',
    isShow: true,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '设备金额',
    isShow: true,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '人工单价',
    isShow: true,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '人工金额',
    isShow: true,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '其他单价',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '其他金额',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '延保单价',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '延保金额',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '综合单价',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '综合金额',
    isShow: false,
    width: 120, // 增加宽度以显示完整金额如300000.00
  },

  {
    value: '备注',
    isShow: true,
    width: 150,
    align: 'left',
  },

  {
    value: '设备成本单价',
    isShow: true,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '设备成本金额',
    isShow: true,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '人工成本单价',
    isShow: true,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '人工成本金额',
    isShow: true,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '其他成本单价',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '其他成本金额',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '延保成本单价',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '延保成本金额',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '综合成本单价',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '综合成本金额',
    isShow: false,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '专项成本',
    isShow: true,
    width: 100, // 增加宽度以显示完整金额如300000.00
  },
  {
    value: '专项供应商',
    isShow: true,
    width: 100,
  },
]);
let form = ref({
  currentModule: 0,
  currentclassify: null,
  isHasTax: 1,
});

// 单元格编辑弹窗状态
const cellEditDialog = ref({
  visible: false,
  fieldName: '',
  fieldValue: '',
  product: null,
  readOnly: false,
});
let oldSplitValue = ref('200px');
watch(
  () => form.value.currentModule,
  val => {
    if (val == 0 || moduleDTOList.value[val].isHasClassify == 1) {
      oldSplitValue.value = splitValue.value;
      splitValue.value = '0';
    } else {
      splitValue.value = oldSplitValue.value;
    }
  },
  {}
);
let moduleDTOList = ref([
  { moduleName: '汇总', uuid: randomLenNum(10) },
  //   { moduleName: '默认子项', id: null, detailDTOList: [] },
]); // 所有数据的操作都会存在这里
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  openSheetProductSelect: {
    type: Function,
    default: () => { },
  },
  dialogView: Boolean,
});
watch(
  () => props.data,
  () => {
    if (props.data) {
     
      form.value = props.data;
      form.value.currentModule =
        form.value.currentModule == 0 || !form.value.currentModule ? 0 : form.value.currentModule;
      moduleDTOList.value = [
        { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
        ...(props.data.moduleVOList?.map(item => {
          return {
            ...item,
            uuid: item.id || randomLenNum(10),
            detailDTOList: item.detailVOList.map(item => {
              return {
                ...item,
                uuid: item.id || randomLenNum(10),
                minSealPrice: item.product?.minSealPrice,
                preSealPrice: item.product?.sealPrice,
                referSealPrice: item.product?.referSealPrice,
              };
            }),
          };
        }) || []),
      ];
      if (props.data.columnHideData) {
        columnHideData.value = props.data.columnHideData;
      }
      if (props.data.isHasTax == null) {
        form.value.isHasTax = 1;
      }
      isFold.value = props.data.isFold;
      proxy.$nextTick(() => {
        form.value.currentModule = 0;
        setTableData();
        setCateGoryList();
        setAllData();
      });

      //    setTableData()
    }
  },
  { immediate: true }
);
// 模块切换加载状态
const moduleChangeLoading = ref(false);

// 添加模块切换防抖
let moduleChangeTimer = null;

function handleModuleChange() {
  console.log(
    '模块切换到:',
    form.value.currentModule,
    moduleDTOList.value[form.value.currentModule]?.moduleName
  );

  // 设置加载状态
  moduleChangeLoading.value = true;

  // 防抖处理，避免快速切换时的重复计算
  if (moduleChangeTimer) {
    clearTimeout(moduleChangeTimer);
  }

  moduleChangeTimer = setTimeout(() => {
    try {
      // 保存当前编辑面板状态
      const wasEditPanelOpen = showEditPanel.value;
      const previousProduct = currentProduct.value ? { ...currentProduct.value } : null;

      // 只在必要时更新数据
      const currentModuleIndex = form.value.currentModule;

      // 清除相关缓存
      clearModuleCache(currentModuleIndex);

      setCateGoryList();

      // 使用防抖的表格数据设置
      debouncedSetTableData();

      if (currentModuleIndex == 0) {
        setAllData();
      }

      // 处理编辑面板的显示逻辑
      if (currentModuleIndex === 0) {
        // 切换到汇总模块时，关闭编辑面板
        if (showEditPanel.value) {
          closeEditPanel();
        }
      } else if (wasEditPanelOpen) {
        // 在非汇总模块之间切换时，保持编辑面板打开
        // 延迟更新产品列表，确保 tableData 已经更新
        nextTick(() => {
          updateProductList();

          // 如果有产品可以显示，则显示第一个产品
          if (allProducts.value.length > 0) {
            // 设置当前产品为新模块的第一个产品
            currentProduct.value = allProducts.value[0];
            currentProductIndex.value = 0;
            showEditPanel.value = true;

            // 强制重新渲染表单
            formKey.value++;
          } else {
            // 如果新模块没有产品，关闭编辑面板
            closeEditPanel();
          }
        });
      }

      // 延迟更新组件，避免阻塞UI
      nextTick(() => {
        randomKey.value = randomLenNum(10);

        // 强制刷新虚拟表格并重新初始化拖拽
        if (virtualTableRef.value) {
          if (virtualTableRef.value.forceRefresh) {
            virtualTableRef.value.forceRefresh();
          }
          // 确保拖拽功能在模块切换后正确初始化
          setTimeout(() => {
            if (virtualTableRef.value.initDrag) {
              console.log('模块切换后重新初始化拖拽');
              virtualTableRef.value.initDrag();
            }

            // 模块切换完成，关闭加载状态并重新初始化拖拽
            setTimeout(() => {
              moduleChangeLoading.value = false;

              // 重新初始化模块拖拽排序
              if (proxy.$route.query.type != 'detail' && !props.dialogView) {
                console.log('模块切换完成，重新初始化模块拖拽排序');
                nextTick(() => {
                  setSort();
                });
              }
            }, 100);
          }, 150);
        } else {
          // 如果没有虚拟表格，直接关闭加载状态并重新初始化拖拽
          setTimeout(() => {
            moduleChangeLoading.value = false;

            // 重新初始化模块拖拽排序
            if (proxy.$route.query.type != 'detail' && !props.dialogView) {
              console.log('模块切换完成（无虚拟表格），重新初始化模块拖拽排序');
              nextTick(() => {
                setSort();
              });
            }
          }, 200);
        }
      });
    } catch (error) {
      console.error('模块切换出错:', error);
      moduleChangeLoading.value = false;
    }
  }, 50); // 50ms防抖延迟
}

// 清除特定模块的缓存
function clearModuleCache(moduleIndex) {
  const keysToDelete = [];
  for (const [key] of cachedTotals.value) {
    if (key.includes(`module-${moduleIndex}`)) {
      keysToDelete.push(key);
    }
  }
  keysToDelete.forEach(key => cachedTotals.value.delete(key));
}

// 测试拖拽功能
function testDragFunction() {
  console.log('=== 主组件拖拽测试 ===');
  console.log('virtualTableRef:', virtualTableRef.value);

  if (virtualTableRef.value && virtualTableRef.value.testDrag) {
    virtualTableRef.value.testDrag();
  } else {
    console.log('虚拟表格引用或测试方法不存在');
  }

  // 尝试重新初始化拖拽
  if (virtualTableRef.value && virtualTableRef.value.initDrag) {
    console.log('手动重新初始化拖拽');
    virtualTableRef.value.initDrag();
  }
}

// 测试虚拟滚动功能
function testVirtualScrollFunction() {
  console.log('=== 主组件虚拟滚动测试 ===');
  console.log('virtualTableRef:', virtualTableRef.value);
  console.log('tableData长度:', tableData.value.length);

  // 显示当前数据统计
  let totalProducts = 0;
  let totalCategories = tableData.value.length;

  tableData.value.forEach(category => {
    if (category.productList) {
      totalProducts += category.productList.filter(p => p.detailType === 0).length;
    }
  });

  console.log('=== 数据统计 ===');
  console.log('分类数量:', totalCategories);
  console.log('产品总数:', totalProducts);
  console.log('预计总行数:', totalCategories * 2 + totalProducts); // 分类行 + 产品行 + 操作按钮行

  // 调用虚拟表格的测试方法
  if (virtualTableRef.value && virtualTableRef.value.testVirtualScroll) {
    virtualTableRef.value.testVirtualScroll();
  } else {
    console.log('虚拟表格引用或测试方法不存在');
  }

  // 当前虚拟滚动状态说明
  console.log('=== 当前状态 ===');
  console.log('虚拟滚动: 已禁用（确保数据完整显示）');
  console.log('原因: 修复数据显示不全和混乱的问题');
  console.log('建议: 等待虚拟滚动重新设计完成后再启用');

  // 性能建议
  if (totalProducts > 100) {
    console.log('⚠️ 警告: 产品数量较多，可能影响性能');
    console.log('建议: 考虑分页加载或其他优化方案');
  } else if (totalProducts > 50) {
    console.log('💡 提示: 产品数量适中，当前性能可接受');
  } else {
    console.log('✅ 良好: 产品数量较少，性能表现良好');
  }
}

// 测试模块切换加载状态
function testModuleChangeLoading() {
  console.log('=== 测试模块切换加载状态 ===');
  console.log('当前模块:', form.value.currentModule);
  console.log('加载状态:', moduleChangeLoading.value);

  // 模拟加载状态
  moduleChangeLoading.value = true;
  console.log('开启加载状态...');

  setTimeout(() => {
    moduleChangeLoading.value = false;
    console.log('关闭加载状态');
    console.log('=== 测试完成 ===');
  }, 2000);
}

// 测试模块拖拽功能
function testModuleDrag() {
  console.log('=== 测试模块拖拽功能 ===');
  console.log('当前模块数量:', moduleDTOList.value.length);
  console.log('模块拖拽实例:', moduleSortableInstance);

  const sortButtonsRef = proxy.$refs['sort-buttons'];
  console.log('模块按钮组引用:', sortButtonsRef);

  if (sortButtonsRef) {
    const el = sortButtonsRef.$el;
    console.log('模块按钮组DOM元素:', el);

    if (el) {
      const moveHandles = el.querySelectorAll('.move1');
      console.log('拖拽句柄数量:', moveHandles.length);

      if (moveHandles.length > 0) {
        console.log('✅ 拖拽句柄存在，模块拖拽应该可以正常工作');
        console.log('提示: 尝试拖拽模块按钮进行排序');
      } else {
        console.log('❌ 未找到拖拽句柄(.move1)');
      }
    } else {
      console.log('❌ 未找到模块按钮组DOM元素');
    }
  } else {
    console.log('❌ 未找到模块按钮组引用');
  }

  // 检查是否需要重新初始化
  if (!moduleSortableInstance) {
    console.log('⚠️ 模块拖拽实例不存在，尝试重新初始化...');
    nextTick(() => {
      setSort();
    });
  }

  console.log('=== 测试完成 ===');
}
let categoryList = ref([]);
function setCateGoryList() {
  let arr = [];

  if (form.value.currentModule == 0 || !form.value.currentModule) return [];
  moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
    if (arr.includes(item.classify)) return;
    arr.push(item.classify);
  });
  categoryList.value = arr;
}
// 当前模块汇总 - 优化版本，使用缓存减少重复计算
const currentModuleTotal = computed(() => {
  if (form.value.currentModule == 0 || !form.value.currentModule) return {};

  const currentModule = moduleDTOList.value[form.value.currentModule];
  if (!currentModule || !currentModule.detailDTOList) return {};

  // 创建缓存键
  const cacheKey = `module-${currentModule.moduleId || form.value.currentModule}-${currentModule.detailDTOList.length
    }`;

  // 检查缓存
  if (cachedTotals.value.has(cacheKey)) {
    return cachedTotals.value.get(cacheKey);
  }

  const list = currentModule.detailDTOList.filter(item => item.detailType == 0);

  // 使用单次遍历计算所有总计，避免多次reduce
  const totals = list.reduce(
    (acc, cur) => {
      const number = cur.number || 0;
      acc.totalProductPrice += number * (cur.sealPrice || 0);
      acc.totalRgcbPrice += number * (cur.rgcbdj || 0);
      acc.totalCostPrice += number * (cur.specialCostPrice || cur.costPrice || 0);
      acc.totalLaborCost += number * (cur.laborCost || 0);
      acc.totalYbPrice += number * (cur.ybhsdj || 0);
      acc.totalQtPrice += number * (cur.qthsdj || 0);
      acc.totalYbcbPrice += number * (cur.ybcbdj || 0);
      acc.totalQtcbPrice += number * (cur.qtcbdj || 0);
      return acc;
    },
    {
      totalProductPrice: 0,
      totalLaborCost: 0,
      totalYbPrice: 0,
      totalQtPrice: 0,
      totalRgcbPrice: 0,
      totalCostPrice: 0,
      totalQtcbPrice: 0,
      totalYbcbPrice: 0,
    }
  );

  const {
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalQtcbPrice,
    totalYbcbPrice,
  } = totals;
  const { taxPrice } = {
    // 税金
    taxPrice:
      totalProductPrice * 1 * form.value.productRate +
      totalLaborCost * 1 * form.value.labourRate +
      totalYbPrice * 1 * form.value.warrantyRate +
      totalQtPrice * 1 * form.value.otherRate,
  };
  const { costTaxPrice } = {
    // 成本税金
    costTaxPrice:
      totalCostPrice * 1 * form.value.productRate +
      totalRgcbPrice * 1 * form.value.labourRate +
      totalYbcbPrice * 1 * form.value.warrantyRate +
      totalQtcbPrice * 1 * form.value.otherRate,
  };
  return {
    // 设备金额
    totalProductPrice,
    // 人工金额
    totalLaborCost,
    // 延保金额
    totalYbPrice,
    // 其他金额
    totalQtPrice,
    // 人工成本
    totalRgcbPrice,
    // 设备成本
    totalCostPrice,
    // 延保成本
    totalYbcbPrice,
    // 其他成本
    totalQtcbPrice,
    taxPrice,
    costTaxPrice,
    // 报价合计
    total:
      totalProductPrice * 1 +
      totalLaborCost * 1 +
      totalYbPrice * 1 +
      totalQtPrice * 1 +
      (form.value.isHasTax ? 0 : taxPrice * 1),
    //成本合计
    costTotal:
      totalRgcbPrice * 1 +
      totalCostPrice * 1 +
      totalYbcbPrice * 1 +
      totalQtcbPrice * 1 +
      (form.value.isHasTax ? 0 : costTaxPrice * 1),
  };
});
// 所有模块汇总 - 优化版本，减少数组操作和重复计算
const allModuleTotal = computed(() => {
  // 直接在遍历模块时计算总计，避免先concat再reduce
  const totals = {
    totalProductPrice: 0,
    totalLaborCost: 0,
    totalYbPrice: 0,
    totalQtPrice: 0,
    totalRgcbPrice: 0,
    totalCostPrice: 0,
    totalQtcbPrice: 0,
    totalYbcbPrice: 0,
  };

  moduleDTOList.value.forEach((module, index) => {
    if (index !== 0 && module.isCheck != 0) {
      module.detailDTOList.forEach(item => {
        if (item.detailType == 0 && item.isCheck != 0) {
          const number = item.number || 0;
          totals.totalProductPrice += number * (item.sealPrice || 0);
          totals.totalRgcbPrice += number * (item.rgcbdj || 0);
          totals.totalCostPrice += number * (item.specialCostPrice || item.costPrice || 0);
          totals.totalLaborCost += number * (item.laborCost || 0);
          totals.totalYbPrice += number * (item.ybhsdj || 0);
          totals.totalQtPrice += number * (item.qthsdj || 0);
          totals.totalYbcbPrice += number * (item.ybcbdj || 0);
          totals.totalQtcbPrice += number * (item.qtcbdj || 0);
        }
      });
    }
  });

  const {
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalQtcbPrice,
    totalYbcbPrice,
  } = totals;
  const { taxTotal } = {
    taxTotal:
      totalProductPrice * form.value.productRate +
      totalLaborCost * form.value.labourRate +
      totalYbPrice * form.value.warrantyRate +
      totalQtPrice * form.value.otherRate,
  };
  const { costTaxTotal } = {
    costTaxTotal:
      totalCostPrice * form.value.productRate +
      totalRgcbPrice * form.value.labourRate +
      totalYbcbPrice * form.value.warrantyRate +
      totalQtcbPrice * form.value.otherRate,
  };
  const result = {
    // 报价合计
    total:
      totalProductPrice * 1 +
      totalLaborCost * 1 +
      totalYbPrice * 1 +
      totalQtPrice * 1 +
      (form.value.isHasTax ? 0 : taxTotal * 1),
    //成本合计
    costTotal:
      totalRgcbPrice * 1 +
      totalCostPrice * 1 +
      totalYbcbPrice * 1 +
      totalQtcbPrice * 1 +
      (form.value.isHasTax ? 0 : costTaxTotal * 1),
    // 添加其他汇总字段
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalYbcbPrice,
    totalQtcbPrice,
    taxPrice: taxTotal,
    costTaxPrice: costTaxTotal,
  };

  // 缓存结果
  if (typeof cacheKey !== 'undefined') {
    cachedTotals.value.set(cacheKey, result);
  }

  return result;
});
function handleBlur(value, item) {
  //   currentIndex.value = null;
}

// 防抖函数，用于优化频繁的数据更新
let updateDebounceTimer = null;

// 处理字段变更事件，用于实时更新汇总数据
function handleFieldChange({ field, value, product }) {
  // 确保产品数据在 moduleDTOList 中也得到更新
  const currentModule = moduleDTOList.value[form.value.currentModule];
  if (currentModule && currentModule.detailDTOList) {
    const targetProduct = currentModule.detailDTOList.find(item => item.uuid === product.uuid);
    if (targetProduct) {
      targetProduct[field] = value;
    }
  }

  // 如果当前编辑面板显示的是这个产品，需要立即同步更新编辑面板的数据
  if (showEditPanel.value && currentProduct.value && currentProduct.value.uuid === product.uuid) {
    // 直接更新 currentProduct 的字段值，确保编辑面板立即同步
    currentProduct.value[field] = value;

    // 移除强制重新渲染，避免输入框失去焦点
    // Vue 的响应式系统会自动处理数据更新
  }

  // 使用防抖优化频繁的数据更新，但对于编辑面板同步不使用防抖
  if (updateDebounceTimer) {
    clearTimeout(updateDebounceTimer);
  }

  updateDebounceTimer = setTimeout(() => {
    // 对于影响计算的字段，需要更新汇总数据
    if (['number', 'sealPrice', 'laborCost', 'qthsdj', 'ybhsdj'].includes(field)) {
      nextTick(() => {
        // 使用更轻量的方式触发响应式更新
        const currentTableData = tableData.value;
        tableData.value = [...currentTableData];
      });
    }

    // 对于所有字段变更，都需要触发表格的重新渲染以确保数据同步
    nextTick(() => {
      // 强制触发表格数据的响应式更新
      const currentModuleList = moduleDTOList.value;
      moduleDTOList.value = [...currentModuleList];
    });
  }, 100); // 100ms防抖延迟
}

// 处理浮动面板字段变更
function handlePanelFieldChange(field, value) {
  if (currentProduct.value) {
    // 更新当前产品的字段值
    currentProduct.value[field] = value;

    // 调用通用的字段变更处理函数
    handleFieldChange({
      field,
      value,
      product: currentProduct.value,
    });
  }
}

// 处理单元格双击事件
function handleCellDoubleClick({ field, value, product }) {


  // 设置弹窗数据
  cellEditDialog.value = {
    visible: true,
    fieldName: field,
    fieldValue: value,
    product: product,
    readOnly: route.query.type == 'detail' || props.dialogView,
  };
}

// 处理单元格编辑确认
function handleCellEditConfirm({ field, value, product }) {
  // 更新产品数据
  if (product && field) {
    // 对于数字字段，确保转换为数字类型
    if (
      [
        'number',
        'sealPrice',
        'laborCost',
        'qthsdj',
        'ybhsdj',
        'costPrice',
        'rgcbdj',
        'qtcbdj',
        'ybcbdj',
        'specialCostPrice',
      ].includes(field)
    ) {
      product[field] = Number(value) || '';
    } else {
      product[field] = value;
    }

    // 调用通用的字段变更处理函数
    handleFieldChange({
      field,
      value: product[field],
      product: product,
    });

    // 如果当前编辑面板显示的是同一个产品，也需要更新
    if (currentProduct.value && currentProduct.value.uuid === product.uuid) {
      currentProduct.value[field] = product[field];
    }
  }
}

// 处理虚拟表格拖拽结束事件
function handleVirtualTableDragEnd(dragInfo) {
  const {
    oldIndex,
    newIndex,
    categoryIndex,
    productIndex,
    uuid,
    totalProductRows,
    originalOldIndex,
    originalNewIndex,
  } = dragInfo;

  console.log('=== 拖拽开始 ===');
  console.log('拖拽事件:', {
    oldIndex,
    newIndex,
    categoryIndex,
    productIndex,
    uuid,
    totalProductRows,
    originalOldIndex,
    originalNewIndex,
  });

  if (oldIndex === newIndex) {
    console.log('位置未变化，取消拖拽');
    return;
  }

  // 简化逻辑：直接在当前分类内处理拖拽
  const sourceCategory = tableData.value[categoryIndex];
  if (!sourceCategory || !sourceCategory.productList) {
    console.log('找不到源分类');
    return;
  }

  // 获取当前分类的所有产品（只包含detailType === 0的产品）
  const products = sourceCategory.productList.filter(p => p.detailType === 0);
  console.log(
    '当前分类产品:',
    products.map((p, i) => `${i}: ${p.customProductName}`)
  );

  // 验证产品索引是否有效
  if (productIndex < 0 || productIndex >= products.length) {
    console.log('产品索引无效:', productIndex, '范围:', 0, '-', products.length - 1);
    return;
  }

  const draggedProduct = products[productIndex];
  if (!draggedProduct || draggedProduct.uuid !== uuid) {
    console.log('产品验证失败');
    return;
  }

  console.log('拖拽产品:', draggedProduct.customProductName, '从位置', productIndex);

  // 现在 oldIndex 和 newIndex 已经是正确的产品行索引，无需转换
  console.log('产品行索引:', 'oldIndex:', oldIndex, 'newIndex:', newIndex);

  // 验证索引是否在有效范围内
  if (oldIndex < 0 || oldIndex >= products.length) {
    console.log('oldIndex超出范围:', oldIndex, '有效范围:', 0, '-', products.length - 1);
    return;
  }

  if (newIndex < 0 || newIndex >= products.length) {
    console.log('newIndex超出范围:', newIndex, '有效范围:', 0, '-', products.length - 1);
    return;
  }

  console.log('分类内拖拽确认:', oldIndex, '->', newIndex);

  // 执行数组重排
  const productsCopy = [...products];
  console.log(
    '重排前:',
    productsCopy.map((p, i) => `${i}: ${p.customProductName}`)
  );

  // 移除源位置的产品
  const [movedProduct] = productsCopy.splice(oldIndex, 1);
  console.log('移除的产品:', movedProduct.customProductName);

  // 插入到新位置
  productsCopy.splice(newIndex, 0, movedProduct);
  console.log(
    '重排后:',
    productsCopy.map((p, i) => `${i}: ${p.customProductName}`)
  );

  // 更新分类的产品列表
  console.log(
    '更新前分类产品列表:',
    sourceCategory.productList.map(p => p.customProductName)
  );

  // 保留非产品项（detailType !== 0），然后添加重排后的产品
  const nonProductItems = sourceCategory.productList.filter(p => p.detailType !== 0);
  sourceCategory.productList = [...nonProductItems, ...productsCopy];

  console.log(
    '更新后分类产品列表:',
    sourceCategory.productList.map(p => p.customProductName)
  );

  // 强制触发 tableData 的响应式更新
  // 通过重新赋值整个 tableData 数组来确保 Vue 检测到变化
  const newTableData = [...tableData.value];
  newTableData[categoryIndex] = {
    ...sourceCategory,
    productList: [...sourceCategory.productList],
  };
  tableData.value = newTableData;

  console.log('强制更新 tableData 完成');

  // 直接更新 moduleDTOList 中对应分类的数据
  const currentModule = moduleDTOList.value[form.value.currentModule];
  if (currentModule && currentModule.detailDTOList) {
    console.log('更新 moduleDTOList 开始');
    console.log('当前分类名称:', sourceCategory.classify);

    // 重新构建整个 detailDTOList，保持正确的顺序
    const newDetailDTOList = [];

    // 按照 tableData 的顺序重新构建
    tableData.value.forEach((category, catIndex) => {
      // 添加分类行
      const categoryItem = currentModule.detailDTOList.find(
        item => item.detailType === 1 && item.classify === category.classify
      );

      if (categoryItem) {
        newDetailDTOList.push({
          ...categoryItem,
          classifySort: catIndex,
        });

        // 添加该分类的所有产品（按照新的顺序）
        const categoryProducts = category.productList.filter(p => p.detailType === 0);
        categoryProducts.forEach((product, prodIndex) => {
          newDetailDTOList.push({
            ...product,
            classifySort: catIndex,
            productSort: prodIndex,
          });
        });
      }
    });

    // 替换整个 detailDTOList
    currentModule.detailDTOList = newDetailDTOList;
    console.log(
      'moduleDTOList 重新构建完成，产品数量:',
      newDetailDTOList.filter(item => item.detailType === 0).length
    );
  }

  // 强制触发响应式更新
  console.log('触发响应式更新');

  // 不调用 setTableData，直接强制刷新虚拟表格
  console.log('跳过 setTableData，直接强制刷新');

  // 强制刷新虚拟表格
  nextTick(() => {
    const virtualTableRef = proxy.$refs.virtualTableRef;
    if (virtualTableRef && virtualTableRef.forceRefresh) {
      console.log('调用虚拟表格强制刷新');
      virtualTableRef.forceRefresh();
    } else {
      console.log('虚拟表格引用不存在');
    }
  });

  console.log('=== 拖拽完成，数据更新完毕 ===');
}

// 从 tableData 更新 moduleDTOList
function updateModuleDTOListFromTableData() {
  const currentModule = moduleDTOList.value[form.value.currentModule];
  if (!currentModule) return;

  // 重新构建 detailDTOList
  currentModule.detailDTOList = tableData.value.reduce((acc, category, categoryIndex) => {
    // 添加分类行
    acc.push({
      ...category,
      productList: null,
      classifySort: categoryIndex,
      detailType: 1,
    });

    // 添加产品行
    category.productList?.forEach((product, productIndex) => {
      if (product.detailType === 0) {
        acc.push({
          ...product,
          classifySort: categoryIndex,
          productSort: productIndex,
        });
      }
    });

    return acc;
  }, []);
}
let tableData = ref([]); //表格临时数据

// 增强的缓存机制
const tableDataCache = new Map();
const moduleDataCache = new Map();
let lastModuleId = null;
let lastDetailListLength = 0;

function setTableData(c) {
  if (form.value.currentModule == 0) {
    tableData.value = [];
    return;
  }

  const currentData = moduleDTOList.value[form.value.currentModule];
  if (!currentData) {
    tableData.value = [];
    return;
  }

  // 创建更精确的缓存键
  const currentModuleId = currentData?.moduleId || form.value.currentModule;
  const currentDetailListLength = currentData?.detailDTOList?.length || 0;
  const dataHash = generateDataHash(currentData.detailDTOList);
  const cacheKey = `table-${currentModuleId}-${currentDetailListLength}-${dataHash}`;

  // 检查缓存
  if (tableDataCache.has(cacheKey)) {
    console.log('使用缓存的表格数据');
    tableData.value = tableDataCache.get(cacheKey);
    if (typeof c == 'function') {
      c();
    }
    return;
  }

  // 检查是否需要重新构建表格数据
  if (
    lastModuleId === currentModuleId &&
    lastDetailListLength === currentDetailListLength &&
    tableData.value.length > 0
  ) {
    // 数据没有变化，不需要重新构建
    if (typeof c == 'function') {
      c();
    }
    return;
  }

  setCategotyAndProductDrag();

  // 优化：使用Map来提高查找性能，避免每次findIndex
  const classifyMap = new Map();

  tableData.value = currentData.detailDTOList.reduce((pre, cur) => {
    const classify = cur.classify;

    if (!classifyMap.has(classify)) {
      const newCategory = {
        ...cur,
        classify: classify,
        productList: [cur],
      };
      classifyMap.set(classify, newCategory);
      pre.push(newCategory);
    } else {
      classifyMap.get(classify).productList.push(cur);
    }
    return pre;
  }, []);

  // 更新缓存标识
  lastModuleId = currentModuleId;
  lastDetailListLength = currentDetailListLength;

  proxy.$nextTick(() => {
    for (let i = 0; i < tableData.value.length; i++) {
      tableData.value[i].productList = tableData.value[i].productList.filter(
        item => item.detailType == 0
      );
      // setProductDrag(i); // 虚拟表格已经处理了拖拽功能
    }

    // 如果编辑面板打开，更新产品列表和当前产品
    if (showEditPanel.value) {
      updateProductList();

      // 重新定位当前产品索引
      if (currentProduct.value) {
        const newIndex = allProducts.value.findIndex(p => p.uuid === currentProduct.value.uuid);

        if (newIndex >= 0) {
          // 如果找到了当前产品，更新索引
          currentProductIndex.value = newIndex;
          // 确保使用最新的产品数据
          currentProduct.value = allProducts.value[newIndex];
        } else if (allProducts.value.length > 0) {
          // 如果找不到当前产品但有其他产品，显示第一个产品
          currentProduct.value = allProducts.value[0];
          currentProductIndex.value = 0;
          // 强制重新渲染表单
          formKey.value++;
        } else {
          // 如果没有产品，关闭编辑面板
          closeEditPanel();
        }
      }
    }
  });

  // 缓存新构建的表格数据
  if (typeof cacheKey !== 'undefined') {
    tableDataCache.set(cacheKey, [...tableData.value]);
    console.log('缓存表格数据:', cacheKey);
  }

  // 更新缓存标记
  lastModuleId = currentModuleId;
  lastDetailListLength = currentDetailListLength;

  // 确保拖拽功能在数据更新后正确初始化
  nextTick(() => {
    if (virtualTableRef.value && virtualTableRef.value.initDrag) {
      console.log('数据更新后重新初始化拖拽');
      // 延迟一点时间确保DOM完全渲染
      setTimeout(() => {
        virtualTableRef.value.initDrag();
      }, 100);
    }
  });

  if (typeof c == 'function') {
    c();
  }
}

// 生成数据哈希，用于检测数据变化
function generateDataHash(data) {
  if (!data || !Array.isArray(data)) return '0';

  // 简单的哈希算法，基于数据长度和关键字段
  let hash = data.length.toString();

  // 取前几个和后几个项目的关键字段生成哈希
  const sampleSize = Math.min(5, data.length);
  for (let i = 0; i < sampleSize; i++) {
    const item = data[i];
    if (item) {
      hash += (item.uuid || '') + (item.classify || '') + (item.detailType || '');
    }
  }

  // 如果数据量大，也取末尾几个
  if (data.length > sampleSize) {
    for (let i = data.length - sampleSize; i < data.length; i++) {
      const item = data[i];
      if (item) {
        hash += (item.uuid || '') + (item.classify || '') + (item.detailType || '');
      }
    }
  }

  return hash;
}

// 清理过期缓存
function cleanupCache() {
  // 限制缓存大小，避免内存泄漏
  const maxCacheSize = 20;

  if (tableDataCache.size > maxCacheSize) {
    const keysToDelete = Array.from(tableDataCache.keys()).slice(
      0,
      tableDataCache.size - maxCacheSize
    );
    keysToDelete.forEach(key => tableDataCache.delete(key));
  }

  if (cachedTotals.value.size > maxCacheSize) {
    const keysToDelete = Array.from(cachedTotals.value.keys()).slice(
      0,
      cachedTotals.value.size - maxCacheSize
    );
    keysToDelete.forEach(key => cachedTotals.value.delete(key));
  }
}

let allData = ref([]);
function setAllData() {
  allData.value = moduleDTOList.value
    .filter((item, index) => index != 0)
    .map((item, index1) => {
      const list = item.detailDTOList.filter(item => item.detailType == 0);
      const {
        totalProductPrice,
        totalLaborCost,
        totalYbPrice,
        totalQtPrice,
        totalRgcbPrice,
        totalCostPrice,
        totalQtcbPrice,
        totalYbcbPrice,
      } = {
        totalProductPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.sealPrice;
        }, 0),
        totalRgcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.rgcbdj;
        }, 0),
        totalCostPrice: list.reduce((pre, cur) => {
          return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
        }, 0),
        totalLaborCost: list.reduce((pre, cur) => {
          return pre + cur.number * cur.laborCost;
        }, 0),
        totalYbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.ybhsdj;
        }, 0),
        totalQtPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.qthsdj;
        }, 0),
        totalYbcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.ybcbdj;
        }, 0),
        totalQtcbPrice: list.reduce((pre, cur) => {
          return pre + cur.number * cur.qtcbdj;
        }, 0),
      };
      const { taxPrice } = {
        // 税金
        taxPrice:
          totalProductPrice * 1 * form.value.productRate +
          totalLaborCost * 1 * form.value.labourRate +
          totalYbPrice * 1 * form.value.warrantyRate +
          totalQtPrice * 1 * form.value.otherRate,
      };
      const { costTaxTotal } = {
        costTaxTotal:
          totalCostPrice * form.value.productRate +
          totalRgcbPrice * form.value.labourRate +
          totalYbcbPrice * form.value.warrantyRate +
          totalQtcbPrice * form.value.otherRate,
      };
      const uuid = randomLenNum(10);
      return {
        // 设备金额
        totalProductPrice,
        // 人工金额
        totalLaborCost,
        // 延保金额
        totalYbPrice,
        // 其他金额
        totalQtPrice,
        // 人工成本
        totalRgcbPrice,
        // 设备成本
        totalCostPrice,
        // 延保成本
        totalYbcbPrice,
        // 其他成本
        totalQtcbPrice,
        taxPrice,
        // 报价合计
        total:
          totalProductPrice * 1 +
          totalLaborCost * 1 +
          totalYbPrice * 1 +
          totalQtPrice * 1 +
          (form.value.isHasTax ? 0 : taxPrice * 1),

        //成本合计
        costTotal:
          totalRgcbPrice * 1 +
          totalCostPrice * 1 +
          totalYbcbPrice * 1 +
          totalQtcbPrice * 1 +
          (form.value.isHasTax ? 0 : costTaxTotal * 1),
        moduleName: item.moduleName,
        isHasClassify: item.isHasClassify,
        index: index1 + 1,
        isCheck: item.isCheck,
        uuid,
        children:
          item.isHasClassify == 1 ? [] : childrenAllData(item.detailDTOList, uuid, index1 + 1),
      };
    });
  setToggle();
}
function setToggle() {
  allData.value.forEach(item => {
    nextTick(() => {
      allTableRef.value.toggleRowSelection(item, !!item.isCheck);
      item.children.forEach(item1 => {
        allTableRef.value.toggleRowSelection(item1, !!item1.isCheck);
      });
    });
  });
}
function childrenAllData(data, parentUuid, parentIndex) {
  const list = data.reduce((pre, cur) => {
    if (pre.findIndex(item => item.classify === cur.classify) === -1) {
      pre.push({
        ...cur,
        classify: cur.classify,
        productList: [cur],
      });
    } else {
      pre.find(item => item.classify === cur.classify).productList.push(cur);
    }
    return pre;
  }, []);

  return list.map(item => {
    const list = item.productList.filter(item => item.detailType == 0);
    const {
      totalProductPrice,
      totalLaborCost,
      totalYbPrice,
      totalQtPrice,
      totalRgcbPrice,
      totalCostPrice,
      totalQtcbPrice,
      totalYbcbPrice,
    } = {
      totalProductPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
      totalRgcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.rgcbdj;
      }, 0),
      totalCostPrice: list.reduce((pre, cur) => {
        return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
      }, 0),
      totalLaborCost: list.reduce((pre, cur) => {
        return pre + cur.number * cur.laborCost;
      }, 0),
      totalYbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.ybhsdj;
      }, 0),
      totalQtPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.qthsdj;
      }, 0),
      totalYbcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.ybcbdj;
      }, 0),
      totalQtcbPrice: list.reduce((pre, cur) => {
        return pre + cur.number * cur.qtcbdj;
      }, 0),
    };
    const { taxPrice } = {
      // 税金
      taxPrice:
        totalProductPrice * 1 * form.value.productRate +
        totalLaborCost * 1 * form.value.labourRate +
        totalYbPrice * 1 * form.value.warrantyRate +
        totalQtPrice * 1 * form.value.otherRate,
    };
    const { costTaxTotal } = {
      costTaxTotal:
        totalCostPrice * form.value.productRate +
        totalRgcbPrice * form.value.labourRate +
        totalYbcbPrice * form.value.warrantyRate +
        totalQtcbPrice * form.value.otherRate,
    };
    return {
      // 设备金额
      totalProductPrice,
      // 人工金额
      totalLaborCost,
      // 延保金额
      totalYbPrice,
      // 其他金额
      totalQtPrice,
      // 人工成本
      totalRgcbPrice,
      // 设备成本
      totalCostPrice,
      // 延保成本
      totalYbcbPrice,
      // 其他成本
      totalQtcbPrice,
      taxPrice,
      // 报价合计
      total:
        totalProductPrice * 1 +
        totalLaborCost * 1 +
        totalYbPrice * 1 +
        totalQtPrice * 1 +
        (form.value.isHasTax ? 0 : taxPrice * 1),

      //成本合计
      costTotal:
        totalRgcbPrice * 1 +
        totalCostPrice * 1 +
        totalYbcbPrice * 1 +
        totalQtcbPrice * 1 +
        (form.value.isHasTax ? 0 : costTaxTotal * 1),
      moduleName: item.classify,

      parentIndex,
      isCheck: item.isCheck,
      uuid: randomLenNum(10),
      parentUuid,
    };
  });
}

function handleSelectionChange(list) {
  console.log(list);
}


// 虚拟表格相关
const virtualTableRef = ref(null);

// 添加防抖函数来优化频繁的计算
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// 防抖的表格数据设置
const debouncedSetTableData = debounce(setTableData, 100);

// 缓存计算结果以提高性能
const cachedTotals = ref(new Map());

// 清除缓存
function clearTotalsCache() {
  cachedTotals.value.clear();
}

// 监听数据变化，清除缓存
watch(() => moduleDTOList.value, clearTotalsCache, { deep: true });

// 监听模块列表变化，重新初始化拖拽
watch(
  () => moduleDTOList.value.length,
  (newLength, oldLength) => {
    if (newLength !== oldLength && proxy.$route.query.type != 'detail' && !props.dialogView) {
      console.log('模块数量变化，重新初始化拖拽排序');
      proxy.$nextTick(() => {
        setSort();
      });
    }
  },
  { flush: 'post' }
);

function addModule(row = {}, type) {
  proxy.$refs.dialogForm.show({
    title: type ? '编辑' : '新增',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'input',
          label: '子系统名称',
          span: 24,
          prop: 'moduleName',
          value: row.moduleName,
          rules: [
            {
              required: true,
              message: '请输入子系统名称',
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 去掉 - 字符的限制
                const reg = /^[^/\\?？\[\]]*$/;
                console.log(value, rule, reg);
                if (!reg.test(value)) {
                  callback('不能包含特殊字符"/\?？[]"');
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          type: 'radio',
          label: '分类',
          value: row.isHasClassify,
          prop: 'isHasClassify',
          disabled: type == 'edit',

          labelTip: '无分类可以直接添加产品，不需要先新建分类',

          dicData: [
            {
              label: '有分类',
              value: 0,
            },
            {
              label: '无分类',
              value: 1,
            },
          ],
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          value: row.remark,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      if (type) {
        row.remark = res.data.remark;
        row.moduleName = res.data.moduleName;
        row.isHasClassify = res.data.isHasClassify;
      } else {
        if (res.data.isHasClassify == 1) {
          // 无分类模块添加
          moduleDTOList.value.push({
            moduleName: res.data.moduleName,
            remark: res.data.remark,
            isHasClassify: res.data.isHasClassify,
            uuid: randomLenNum(10),
            detailDTOList: [
              {
                classify: '',
                detailType: 1,
                classifySort: 0,
              },
            ],
          });
        } else {
          moduleDTOList.value.push({
            moduleName: res.data.moduleName,
            remark: res.data.remark,
            isHasClassify: res.data.isHasClassify,
            uuid: randomLenNum(10),
            detailDTOList: [],
          });
        }
      }
      res.close();
    },
  });
}
// 添加分类
function addCategory() {
  const currentData = moduleDTOList.value[form.value.currentModule];
  proxy.$refs.dialogForm.show({
    title: '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          rules: [
            {
              required: true,
              message: '请输入分类名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      currentData.detailDTOList.push({
        classify: res.data.classify,
        detailType: 1,
        isCheck: 1,
        classifySort: categoryList.value.length,
      });
      setTableData();
      setCateGoryList();
      res.close();
      sendMessage();
    },
  });
}
async function handleProductSelectConfirm(ids) {
  console.log(ids);
  const res = await axios.get('/api/vt-admin/product/detailByIds', {
    params: {
      idList: ids,
    },
  });

  const data = res.data.data
    .map(item => {
      return {
        customProductName: item.productName,
        customProductSpecification: item.productSpecification,
        customProductDescription: item.description,
        customUnit: item.unitName,
        productId: item.id,
        source: 0,
        classify: form.value.currentclassify,
        ...item,
        id: null,
        detailType: 0,
        number: '',
        rgcbdj: '',
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        isCheck: 1,
        uuid: randomLenNum(10),
        classifySort: categoryList.value.findIndex(item => item == form.value.currentclassify),
      };
    })
    .reverse();

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
  setTableData();
  setCateGoryList();
}
// 删除分类
function delCategory(classify) {
  proxy
    .$confirm('是否删除该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value[form.value.currentModule].detailDTOList = moduleDTOList.value[
        form.value.currentModule
      ].detailDTOList.filter(item => item.classify != classify);
      setTableData();
      setCateGoryList();
    })
    .catch(() => { });
}
// 复制分类
function copyCategory(classify) {
  proxy
    .$confirm('是否复制该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const randomLenNumValue = randomLenNum(5);
      const data = [];
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          const itemCopy = {
            ...item,
            classify: item.classify + `--${randomLenNumValue}`,
            classifySort: categoryList.value.length,
            uuid: randomLenNum(10),
            id: null,
          };
          data.push(itemCopy);
        }
      });
      moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
      setTableData();
      setCateGoryList();
    })
    .catch(() => { });
}

function editCategory(classify) {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          value: classify,
          rules: [
            {
              required: true,
              message: '请输入模块名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          item.classify = res.data.classify;
        }
      });
      setTableData();
      setCateGoryList();
      res.close();
    },
  });
}
// 删除模块
function delModule() {
  proxy
    .$confirm('是否删除该模块?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value.splice(form.value.currentModule, 1);
      form.value.currentModule = 1;
      setTableData();
    })
    .catch(() => { });
}

function handleScroll(index) {
  // 虚拟表格中的滚动处理
  console.log('滚动到分类:', index, '分类名称:', categoryList.value[index]);

  // 尝试通过虚拟表格的引用来滚动
  const virtualTableRef = proxy.$refs.virtualTableRef;
  if (!virtualTableRef) {
    console.log('虚拟表格引用不存在');
    return;
  }

  const categoryName = categoryList.value[index];
  if (!categoryName) {
    console.log('分类名称不存在，索引:', index);
    return;
  }

  // 方法1：使用新的 scrollToCategoryByName 方法
  if (virtualTableRef.scrollToCategoryByName) {
    console.log('使用 scrollToCategoryByName 方法');
    virtualTableRef.scrollToCategoryByName(categoryName);
    return;
  }

  // 方法2：使用计算索引的方式
  if (virtualTableRef.scrollToItem) {
    // 计算目标分类在扁平化数据中的索引
    let targetIndex = 0;
    for (let i = 0; i < index; i++) {
      // 每个分类包含：分类行 + 产品行 + 操作按钮行
      const categoryData = tableData.value[i];
      const productCount = categoryData.productList?.filter(p => p.detailType === 0).length || 0;
      targetIndex += 1 + productCount + 1; // 分类行 + 产品行 + 操作按钮行
    }

    console.log('计算的目标索引:', targetIndex);
    virtualTableRef.scrollToItem(targetIndex);
    return;
  }

  // 方法3：直接查找DOM元素（备选方案）
  const containerElement = virtualTableRef.$el || virtualTableRef;
  if (containerElement) {
    // 等待DOM更新后再查找
    proxy.$nextTick(() => {
      const categoryElements = containerElement.querySelectorAll('tr.category .category-name');
      console.log('找到的分类名称元素数量:', categoryElements.length);

      const targetElement = Array.from(categoryElements).find(
        el => el.textContent.trim() === categoryName
      );

      if (targetElement) {
        console.log('找到目标分类元素:', targetElement);
        const categoryRow = targetElement.closest('tr.category');
        if (categoryRow) {
          categoryRow.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest',
          });
        }
      } else {
        console.log('未找到分类元素，分类名:', categoryName);
        console.log(
          '可用分类:',
          Array.from(categoryElements).map(el => el.textContent.trim())
        );
      }
    });
  } else {
    console.log('虚拟表格容器元素不存在');
  }
}


const tableWidth = computed(() => {
  const value = columnHideData.value
    .filter(item => item.isShow)
    .reduce((pre, cur) => {
      return pre + cur.width;
    }, 0);
  return `${value}px`;
});

function isTrue(value) {
  return columnHideData.value.find(item => item.value == value)?.isShow;
}

// 设置参考信息
let referInfo = ref({});
let currentProduct = ref({});
let preItem = ref({});
let currentHeight = ref(20);
let currentModelKey = ref('');

// 浮动面板相关变量
let showEditPanel = ref(false);
let panelPosition = ref({ x: window.innerWidth - 480, y: 80 });
let panelSize = ref({ width: 460, height: 720 });
let isDragging = ref(false);
let isResizing = ref(false);
let isCollapsed = ref(false);
let dragOffset = ref({ x: 0, y: 0 });
let resizeType = ref(''); // 'right', 'bottom', 'corner'
let resizeStartPos = ref({ x: 0, y: 0 });
let resizeStartSize = ref({ width: 0, height: 0 });

// 产品切换相关变量
let currentProductIndex = ref(-1);
let allProducts = ref([]); // 当前模块的所有产品列表
let formKey = ref(0); // 用于强制重新渲染表单

// 监听currentProduct的uuid变化，当切换产品时强制重新渲染表单
watch(
  () => currentProduct.value?.uuid,
  (newUuid, oldUuid) => {
    if (newUuid && oldUuid && newUuid !== oldUuid && showEditPanel.value) {
      // 当切换到不同产品时，更新formKey强制重新渲染表单
      formKey.value++;
    }
  }
);

// 面板样式计算
const panelStyle = computed(() => ({
  position: 'fixed',
  left: panelPosition.value.x + 'px',
  top: panelPosition.value.y + 'px',
  width: panelSize.value.width + 'px',
  height: isCollapsed.value ? '70px' : panelSize.value.height + 'px',
  zIndex: 9999,
  cursor: isDragging.value ? 'grabbing' : 'default',
  transition: isCollapsed.value ? 'height 0.3s ease' : 'none',
}));

// 判断产品是否为当前编辑的产品
function isCurrentEditingProduct(product) {
  return showEditPanel.value && currentProduct.value && currentProduct.value.uuid === product.uuid;
}

function setReferPrice(item) {
  // 重置之前产品的高度
  if (preItem.value && preItem.value.height) {
    preItem.value.height = 20;
  }

  // 设置当前产品信息，但不改变高度
  preItem.value = item;
  referInfo.value = {
    productName: item.customProductName,
    preSealPrice: item.preSealPrice,
    minSealPrice: item.minSealPrice,
    referSealPrice: item.referSealPrice,
    customProductDescription: item.customProductDescription,
  };

  // 更新产品列表和当前索引
  updateProductList();
  currentProductIndex.value = allProducts.value.findIndex(p => p.uuid === item.uuid);

  // 直接引用原始对象，保持双向绑定
  currentProduct.value = item;

  // 显示编辑面板
  showEditPanel.value = true;

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyDown);
}

// 拖拽相关方法
function startDrag(event) {
  // 如果点击的是控制按钮，不触发拖拽
  if (event.target.closest('.header-controls')) {
    return;
  }

  if (event.target.closest('.panel-header') && !isResizing.value) {
    isDragging.value = true;
    dragOffset.value = {
      x: event.clientX - panelPosition.value.x,
      y: event.clientY - panelPosition.value.y,
    };

    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopDrag);
    event.preventDefault();
  }
}

function onDrag(event) {
  if (isDragging.value && !isResizing.value) {
    const newX = event.clientX - dragOffset.value.x;
    const newY = event.clientY - dragOffset.value.y;

    // 限制在屏幕范围内
    const maxX = window.innerWidth - panelSize.value.width;
    const maxY = window.innerHeight - (isCollapsed.value ? 50 : panelSize.value.height);

    panelPosition.value = {
      x: Math.max(0, Math.min(maxX, newX)),
      y: Math.max(0, Math.min(maxY, newY)),
    };
  }
}

function stopDrag() {
  isDragging.value = false;
  document.removeEventListener('mousemove', onDrag);
  document.removeEventListener('mouseup', stopDrag);
}

function closeEditPanel() {
  showEditPanel.value = false;
  isCollapsed.value = false;
  currentProductIndex.value = -1;
  // 重置当前产品的高度
  if (currentProduct.value && currentProduct.value.height) {
    currentProduct.value.height = 20;
  }
  if (preItem.value && preItem.value.height) {
    preItem.value.height = 20;
  }
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeyDown);
}

// 折叠/展开面板
function toggleCollapse() {
  isCollapsed.value = !isCollapsed.value;
}

// 开始调整大小
function startResize(type, event) {
  isResizing.value = true;
  resizeType.value = type;
  resizeStartPos.value = {
    x: event.clientX,
    y: event.clientY,
  };
  resizeStartSize.value = {
    width: panelSize.value.width,
    height: panelSize.value.height,
  };

  document.addEventListener('mousemove', onResize);
  document.addEventListener('mouseup', stopResize);
  event.preventDefault();
  event.stopPropagation();
}

// 调整大小过程中
function onResize(event) {
  if (!isResizing.value) return;

  const deltaX = event.clientX - resizeStartPos.value.x;
  const deltaY = event.clientY - resizeStartPos.value.y;

  let newWidth = resizeStartSize.value.width;
  let newHeight = resizeStartSize.value.height;

  if (resizeType.value === 'right' || resizeType.value === 'corner') {
    newWidth = Math.max(380, Math.min(900, resizeStartSize.value.width + deltaX));
  }

  if (resizeType.value === 'bottom' || resizeType.value === 'corner') {
    newHeight = Math.max(
      500,
      Math.min(window.innerHeight - 80, resizeStartSize.value.height + deltaY)
    );
  }

  panelSize.value = {
    width: newWidth,
    height: newHeight,
  };

  // 确保面板不会超出屏幕边界
  const maxX = window.innerWidth - panelSize.value.width;
  const maxY = window.innerHeight - (isCollapsed.value ? 50 : panelSize.value.height);

  if (panelPosition.value.x > maxX) {
    panelPosition.value.x = maxX;
  }
  if (panelPosition.value.y > maxY) {
    panelPosition.value.y = maxY;
  }
}

// 停止调整大小
function stopResize() {
  isResizing.value = false;
  resizeType.value = '';
  document.removeEventListener('mousemove', onResize);
  document.removeEventListener('mouseup', stopResize);
}

// 更新产品列表
function updateProductList() {
  allProducts.value = [];
  if (form.value.currentModule === 0 || !tableData.value.length) {
    return;
  }

  // 收集当前模块的所有产品
  tableData.value.forEach(category => {
    if (category.productList && category.productList.length > 0) {
      allProducts.value.push(...category.productList.filter(item => item.detailType === 0));
    }
  });
}

// 键盘事件处理
function handleKeyDown(event) {
  if (!showEditPanel.value || allProducts.value.length === 0) {
    return;
  }

  // 左箭头键 - 上一个产品
  if (event.key === 'ArrowLeft') {
    event.preventDefault();
    switchToPreviousProduct();
  }
  // 右箭头键 - 下一个产品
  else if (event.key === 'ArrowRight') {
    event.preventDefault();
    switchToNextProduct();
  }
}

// 切换到上一个产品
function switchToPreviousProduct() {
  if (currentProductIndex.value > 0) {
    currentProductIndex.value--;
    switchToProduct(allProducts.value[currentProductIndex.value]);
  }
}

// 切换到下一个产品
function switchToNextProduct() {
  if (currentProductIndex.value < allProducts.value.length - 1) {
    currentProductIndex.value++;
    switchToProduct(allProducts.value[currentProductIndex.value]);
  }
}

// 缓存DOM查找结果
const focusElementCache = new Map();

// 切换到指定产品 - 优化版本
function switchToProduct(product) {
  // 重置之前产品的高度
  if (currentProduct.value && currentProduct.value.height) {
    currentProduct.value.height = 20;
  }

  // 记录当前焦点元素
  const activeElement = document.activeElement;
  let focusedFieldName = null;

  if (
    activeElement &&
    (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')
  ) {
    // 通过data-field属性或者向上查找父元素的data-field属性
    focusedFieldName = activeElement.getAttribute('data-field');
    if (!focusedFieldName) {
      // 如果当前元素没有data-field，向上查找父元素
      let parent = activeElement.parentElement;
      while (parent && !focusedFieldName) {
        focusedFieldName = parent.getAttribute('data-field');
        parent = parent.parentElement;
      }
    }
  }

  // 设置新产品，但不改变高度
  preItem.value = product;

  // 只更新必要的引用信息字段
  if (
    !referInfo.value ||
    referInfo.value.productName !== product.customProductName ||
    referInfo.value.preSealPrice !== product.preSealPrice
  ) {
    referInfo.value = {
      productName: product.customProductName,
      preSealPrice: product.preSealPrice,
      minSealPrice: product.minSealPrice,
      referSealPrice: product.referSealPrice,
      customProductDescription: product.customProductDescription,
    };
  }

  // 直接引用原始对象，保持双向绑定
  currentProduct.value = product;

  // 优化焦点恢复逻辑
  if (focusedFieldName) {
    // 使用缓存的DOM查找结果
    let newActiveElement = focusElementCache.get(focusedFieldName);
    if (!newActiveElement || !document.contains(newActiveElement)) {
      newActiveElement = document.querySelector(`[data-field="${focusedFieldName}"]`);
      if (newActiveElement) {
        focusElementCache.set(focusedFieldName, newActiveElement);
      }
    }

    if (newActiveElement) {
      // 使用requestAnimationFrame优化DOM操作时机
      requestAnimationFrame(() => {
        // 对于Element Plus的输入框，需要找到实际的input元素
        const inputElement =
          newActiveElement.querySelector('input') ||
          newActiveElement.querySelector('textarea') ||
          newActiveElement;

        if (inputElement && typeof inputElement.focus === 'function') {
          inputElement.focus();
          // 全选文本内容，像Tab键切换一样
          requestAnimationFrame(() => {
            // 确保输入框有内容才进行全选
            if (inputElement.value && inputElement.value.length > 0) {
              if (typeof inputElement.select === 'function') {
                inputElement.select();
              } else if (typeof inputElement.setSelectionRange === 'function') {
                // 如果select方法不可用，使用setSelectionRange全选
                inputElement.setSelectionRange(0, inputElement.value.length);
              }
            }
          });
        }
      });
    }
  }

  // 滚动到产品位置
  scrollToProduct(product);
}

// 滚动到产品位置
function scrollToProduct(product) {
  proxy.$nextTick(() => {
    const productElement = document.getElementById(`tr_${product.uuid}`);
    if (productElement) {
      productElement.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  });
}

// 在当前产品下方插入空产品
function insertEmptyProduct() {
  if (!currentProduct.value || !currentProduct.value.uuid) {
    return;
  }

  // 创建空产品对象
  const newProduct = {
    detailType: 0,
    source: 3,
    classify: currentProduct.value.classify,
    id: null,
    costPrice: 0,
    sealPrice: 0,
    rgcbdj: 0,
    ybcbdj: 0,
    qtcbdj: 0,
    ybhsdj: 0,
    qthsdj: 0,
    laborCost: 0,
    remark: '',
    uuid: randomLenNum(10),
    isCheck: 1,
    classifySort: currentProduct.value.classifySort,
    customProductName: '',
    customProductSpecification: '',
    customProductDescription: '',
    customUnit: '',
    productBrand: '',
    number: 1,
    height: 20,
  };

  // 找到当前产品在detailDTOList中的位置
  const currentData = moduleDTOList.value[form.value.currentModule];
  const currentProductIndex = currentData.detailDTOList.findIndex(
    item => item.uuid === currentProduct.value.uuid
  );

  if (currentProductIndex !== -1) {
    // 在当前产品后面插入新产品
    currentData.detailDTOList.splice(currentProductIndex + 1, 0, newProduct);

    // 重新设置表格数据
    setTableData();

    // 更新产品列表
    updateProductList();

    // 切换到新插入的产品
    proxy.$nextTick(() => {
      const newIndex = allProducts.value.findIndex(p => p.uuid === newProduct.uuid);
      if (newIndex !== -1) {
        currentProductIndex.value = newIndex;
        switchToProduct(allProducts.value[newIndex]);
      }
    });
  }
}

// 根据利润比计算价格
function setPrice(value) {
  moduleDTOList.value.forEach(cur => {
    if (!cur.detailDTOList) return;

    cur.detailDTOList.forEach(cur => {
      if (cur.detailType == 0) {
        cur.sealPrice = (cur.costPrice / (1 - value)).toFixed(2);
      }
    });
  });
  form.value.isLock = true;
}
// 根据利润比计算价格
function setLaborPrice(value) {
  moduleDTOList.value.forEach(cur => {
    if (!cur.detailDTOList) return;

    cur.detailDTOList.forEach(cur => {
      if (cur.detailType == 0) {
        cur.laborCost = (cur.rgcbdj / (1 - value)).toFixed(2);
      }
    });
  });
  form.value.isLock = true;
}
onMounted(() => {
  if (proxy.$route.query.type != 'detail' && !props.dialogView) {
    // 使用nextTick确保DOM已经渲染完成
    proxy.$nextTick(() => {
      setSort();
    });
  }

  form.value.currentModule = 0;
});
// 模块拖拽排序实例
let moduleSortableInstance = null;

// 拖拽排序
const setSort = () => {
  // 设置 子系统拖拽
  console.log('初始化子模块拖拽排序');
  console.log('proxy.$refs:', proxy.$refs);

  const sortButtonsRef = proxy.$refs['sort-buttons'];
  if (!sortButtonsRef) {
    console.error('找不到sort-buttons引用');
    return;
  }

  const el = sortButtonsRef.$el;
  if (!el) {
    console.error('找不到sort-buttons DOM元素');
    return;
  }

  console.log('找到DOM元素，初始化Sortable:', el);

  // 销毁现有的拖拽实例
  if (moduleSortableInstance) {
    console.log('销毁现有的模块拖拽实例');
    moduleSortableInstance.destroy();
    moduleSortableInstance = null;
  }

  // 创建新的拖拽实例
  moduleSortableInstance = new Sortable(el, {
    handle: '.move1',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log('子模块拖拽结束:', evt);

      const targetRow = moduleDTOList.value.splice(evt.oldIndex, 1);
      console.log(
        '当前模块:',
        form.value.currentModule,
        '新位置:',
        evt.newIndex,
        '旧位置:',
        evt.oldIndex
      );

      moduleDTOList.value.splice(evt.newIndex, 0, targetRow[0]);

      // 更新当前选中的模块索引
      if (form.value.currentModule == evt.oldIndex) {
        form.value.currentModule = evt.newIndex;
      } else if (form.value.currentModule == evt.newIndex) {
        form.value.currentModule = evt.oldIndex;
      }

      setTableData();
      console.log('子模块拖拽排序完成');
    },
  });

  console.log('子模块拖拽排序初始化完成');
};
function setCategotyAndProductDrag(params) {
  // 设置分类拖拽
  const el = proxy.$refs.category_box;

  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const targetRow = tableData.value.splice(evt.oldIndex, 1);
      tableData.value.splice(evt.newIndex, 0, targetRow[0]);
      moduleDTOList.value[form.value.currentModule].detailDTOList = tableData.value.reduce(
        (acc, cur, index) => {
          cur.productList.forEach(item => {
            item.classifySort = index;
          });
          acc.push({
            ...cur,
            productList: null,
            classifySort: index,
          });
          acc.push(...cur.productList);
          return acc;
        },
        []
      );

      setTableData();
      setCateGoryList();
    },
  });
}
function setProductDrag(ins) {
  // 虚拟表格已经处理了拖拽功能，这个函数不再需要
  console.log('setProductDrag 已被虚拟表格的拖拽功能替代，分类索引:', ins);

  // 检查虚拟表格的拖拽是否已经初始化
  const virtualTableRef = proxy.$refs.virtualTableRef;
  if (virtualTableRef) {
    console.log('虚拟表格拖拽已启用');
  } else {
    console.log('虚拟表格引用不存在');
  }

  return;
}
// 自定义序号
function customIndex(index, parentIndex) {
  let i = 0;
  if (parentIndex == 0) {
    i = index + 1;
  } else {
    let sum = 0;
    for (let i = 0; i < parentIndex; i++) {
      sum += tableData.value[i].productList.filter(item => item.detailType == 0).length;
    }
    // 返回序号
    i = sum + index + 1;
  }

  return i;
}

function deleteProduct(i) {
  proxy
    .$confirm('此操作将删除该产品, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      if (i.id) {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.id == i.id
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      } else {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.uuid == i.uuid
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      }
      setTableData();
    })
    .catch(() => { });
}

function getData() {
  console.log(moduleDTOList.value);
  return {
    ...form.value,
    moduleDTOList: moduleDTOList.value
      .filter((item, index) => index != 0)
      .map(item => {
        return {
          ...item,
        };
      }),
    columnHideData: columnHideData.value,
    isFold: isFold.value,
  };
}
function getTree() {
  const treeData = formatTreeData(moduleDTOList.value.filter((item, index) => index != 0));
  return treeData;
}
// 将有分类的数据分发到各个分类
function setData(list) {
  list.forEach(item => {
    const moduleName = item.classifyName.split('?')[0];
    const classify = item.classifyName.split('?')[1];
    const index = moduleDTOList.value.findIndex(item => item.moduleName == moduleName);
    const {
      customProductName,
      customProductSpecification,
      customProductDescription,
      customUnit,
      productBrand,
      number,
      sealPrice,
      laborCost,
      qthsdj,
      ybhsdj,
      costPrice,
      source,
      rgcbdj,
      ybcbdj,
      qtcbdj,
      remark,
    } = item;
    const moduleDTOS = moduleDTOList.value.find(item => item.moduleName == moduleName);
    const classifySort = moduleDTOS.detailDTOList.find(
      item => item.classify == classify
    ).classifySort;
    const data = {
      customProductName,
      customProductSpecification,
      customProductDescription,
      customUnit,
      classify,
      source,
      productBrand,
      id: null,
      detailType: 0,
      isCheck: 1,
      number,
      rgcbdj,
      ybcbdj,
      qtcbdj,
      costPrice,
      sealPrice,
      laborCost,
      qthsdj,
      ybhsdj,
      remark: remark,
      uuid: randomLenNum(10),
      classifySort,
    };

    moduleDTOS.detailDTOList.push(data);
  });
  console.log(moduleDTOList.value);
  setTableData();
}
function formatTreeData(data) {
  return data.map(item => {
    item.label = item.moduleName;
    item.value = item.moduleName;
    item.disabled = true;
    item.children = item.detailDTOList
      .map(i => {
        return {
          value: `${item.value}-${i.classify}`,
          label: i.classify || '---',
          parentId: item.uuid,
        };
      })
      .reduce((acc, cur) => {
        if (!acc.find(item => item.value === cur.value)) {
          acc.push({
            ...cur,
          });
        }
        return acc;
      }, []);
    return item;
  });
}

let targetWindow = null;
function handleOpenIframe() {
  targetWindow = window.open('/productSelect', '_productSelect', 'width=1980,height=1500');
}
function sendMessage() {
  // 将分类处理好发送过去

  const treeData = moduleDTOList.value
    .filter((item, index) => index != 0)
    .map((item, index) => {
      item.label = item.moduleName;
      item.children = item.detailDTOList
        .map(i => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: index,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push(cur);
          }
          return acc;
        }, []);
      return item;
    });
  console.log(treeData);
  targetWindow.postMessage(
    JSON.stringify({
      projectName: proxy.$route.query.name,
      treeData: treeData,
      selectClassify: form.value.currentclassify,
      currentModule: form.value.currentModule - 1,
    }),
    '*'
  );
}
// 添加事件监听
function addEvent() {
  window.addEventListener('message', function (event) {
    // 接收子窗口发送的消息
    const data = event.data;
    console.log(data);
    // type :   0 页面加载完成  1 选择产品
    if (data.type == 0) {
      console.log(sendMessage);
      sendMessage();
    } else if (data.type == 1) {
      (form.value.currentModule = data.data.parentId + 1),
        (form.value.currentclassify = data.data.classify);

      handleProductSelectConfirm(data.data.ids);
    }
  });
}

let randomKey = ref(null);
function setRandomKey() {
  randomKey.value = randomLenNum(10);
}

function isNumber(str) {
  return /^[0-9]*(\.[0-9]+)?$/.test(str);
}
function copyModule() {
  const { moduleName, detailDTOList } = moduleDTOList.value[form.value.currentModule];
  moduleDTOList.value.push({
    moduleName: moduleName + '复制',
    detailDTOList: deepClone(
      detailDTOList.map(item => {
        return {
          uuid: randomLenNum(10),
          ...item,
          id: null,
        };
      })
    ),
  });
}
let templateType = ref('');
// 添加模板
async function handleTemplateChange(id, done) {
  const res = await axios.get('/api/vt-admin/optionTemplate/detailForAdd', {
    params: {
      id,
    },
  });

  const currentData = moduleDTOList.value[form.value.currentModule];
  if (templateType.value == 1) {
    // 子系统模块
    const { moduleName, remark, optionModuleDetailTemplateVOS } =
      res.data.data.moduleTemplateVOS[0];
    if (moduleDTOList.value.some(item => item.moduleName == moduleName))
      return proxy.$message.warning(`已存在模块：${moduleName},请先修改`);
    moduleDTOList.value.push({
      moduleName: moduleName,
      remark: remark,
      uuid: randomLenNum(10),
      detailDTOList: optionModuleDetailTemplateVOS.map(item => {
        return {
          ...item,
          ...item.productVO,
          productBrand: item.productBrand,
          sealPrice: '',
          rgcbdj: '',
          ybcbdj: '',
          qtcbdj: '',
          ybhsdj: '',
          qthsdj: '',
          source: 1,
          sealPrice: '',
          laborCost: '',
          id: null,
          uuid: randomLenNum(10),
          number: parseFloat(item.number),
        };
      }),
    });
    done();
  } else if (templateType.value == 2) {
    //子分类模块
    const { classify, optionModuleDetailTemplateVOS } = res.data.data
      .optionModuleClassifyTemplateVO || {
      classify: '',
      optionModuleDetailTemplateVOS: [],
    };

    if (categoryList.value.some(item => item == classify))
      return proxy.$message.warning(`已存在分类：${classify},请先修改`);
    console.log(classify, optionModuleDetailTemplateVOS);
    // 1。添加分类
    currentData.detailDTOList.push({
      classify: classify,
      detailType: 1,
      classifySort: categoryList.value.length,
    });
    // 2.添加产品
    const data = optionModuleDetailTemplateVOS.map(item => {
      return {
        ...item.productVO,
        productId: item.id,
        classify: classify,
        ...item,
        id: null,
        detailType: 0,
        sealPrice: '',
        rgcbdj: '',
        source: 1,
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        uuid: randomLenNum(10),
        classifySort: categoryList.value.length,
        number: parseFloat(item.number),
      };
    });
    done();
    currentData.detailDTOList.push(...data);
    setTableData();
    setCateGoryList();
  }
}
function addTemplate(val) {
  templateType.value = val;
  proxy.$nextTick(() => {
    proxy.$refs.templateDialog.open();
  });
}

function addEmptyProduct(item) {
  form.value.currentclassify = item.classify;
  const data = {
    detailType: 0,
    source: 3,
    classify: form.value.currentclassify,
    id: null,
    detailType: 0,
    costPrice: 0,
    sealPrice: '',
    rgcbdj: 0,
    ybcbdj: 0,
    qtcbdj: 0,
    ybhsdj: 0,
    qthsdj: 0,
    sealPrice: 0,
    laborCost: 0,
    remark: '',
    uuid: randomLenNum(10),
    isCheck: 1,
    classifySort: categoryList.value.findIndex(item => item == form.value.currentclassify),
  };

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(data);
  setTableData();
  setCateGoryList();
}

function addProductFromSheet(item) {
  const moduleName = moduleDTOList.value[form.value.currentModule].moduleName;
  const classify = item.classify;
  props.openSheetProductSelect({ moduleName, classify });
}
function saveCategoryTemplate(classify) {
  proxy.$refs.dialogForm.show({
    title: '保存为模板',
    option: {
      column: [
        {
          label: '模板名称',
          prop: 'templateName',
          type: 'input',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入模板名称',
            },
          ],
        },
        // {
        //   label: '模块名称',
        //   prop: 'moduleName',
        //   value: moduleDTOList.value[form.value.currentModule].moduleName,
        //   type: 'input',
        //   span: 24,
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入模块名称',
        //     },
        //   ],
        // },
        {
          type: 'tree',
          label: '模板分类',
          rules: [
            {
              required: true,
              message: '请选择模板分类',
            },
          ],
          width: 220,
          span: 12,
          // parent: false,

          span: 24,

          search: true,
          dicUrl: '/api/vt-admin/templateCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.map(item => {
              return {
                ...item,
                disabled: true,
                children:
                  item.children &&
                  item.children.map(item => {
                    return {
                      ...item,
                      disabled: true,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            disabled: false,
                          };
                        }),
                    };
                  }),
              };
            });
          },
          dataType: 'string',
          display: true,
          filterable: true,
          prop: 'templateCategory',
          checkStrictly: true,
        },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务类型',
          // multiple: true,
          span: 12,
          width: 250,
          overHidden: true,
          parent: true,
          span: 24,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          value: proxy.$route.query.businessType,
          search: true,
          display: true,
          filterable: true,

          prop: 'businessType',
          checkStrictly: true,
        },
        {
          label: '模板描述',
          prop: 'remark',
          type: 'textarea',
          search: true,
          span: 24,
        },
      ],
    },
    callback(res) {
      const moduleDTOS = [moduleDTOList.value[form.value.currentModule]].map((item, index) => {
        return {
          ...item,
          moduleName: moduleDTOList.value[form.value.currentModule].moduleName,
          classifyDTO: {
            ...item.detailDTOList.find(item => item.detailType == 1),
            detailDTOS: item.detailDTOList
              .filter(item => item.detailType != 1)
              .filter(item => item.classify == classify)
              .map((item, index) => {
                return {
                  ...item,
                  sortNumber: item.index,
                };
              }),
          },
          sortNumber: index,
        };
      });
      const value = {
        ...moduleDTOS[0],
        ...res.data,
        templateType: 2,
      };
      axios.post('/vt-admin/optionTemplate/save', value).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
      });
    },
  });
}
function saveModuleTemplate() {
  proxy.$refs.dialogForm.show({
    title: '保存为模板',
    option: {
      column: [
        {
          label: '模板名称',
          prop: 'templateName',
          type: 'input',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入模板名称',
            },
          ],
        },
        // {
        //   label: '子系统名称',
        //   prop: 'moduleName',
        //   value: moduleDTOList.value[form.value.currentModule].moduleName,
        //   type: 'input',
        //   span: 24,
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入模块名称',
        //     },
        //   ],
        // },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务类型',
          // multiple: true,
          span: 12,
          width: 250,
          overHidden: true,
          parent: true,
          span: 24,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          value: proxy.$route.query.businessType,
          search: true,
          display: true,
          filterable: true,

          prop: 'businessType',
          checkStrictly: true,
        },
        {
          type: 'tree',
          label: '模板分类',
          rules: [
            {
              required: true,
              message: '请选择模板分类',
            },
          ],
          width: 220,
          span: 12,
          // parent: false,

          span: 24,

          search: true,
          dicUrl: '/api/vt-admin/templateCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.map(item => {
              return {
                ...item,
                disabled: true,
                children:
                  item.children &&
                  item.children.map(item => {
                    return {
                      ...item,
                      disabled: false,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            disabled: true,
                          };
                        }),
                    };
                  }),
              };
            });
          },
          dataType: 'string',
          display: true,
          filterable: true,
          prop: 'templateCategory',
          checkStrictly: true,
        },
        {
          label: '模板描述',
          prop: 'remark',
          type: 'textarea',
          search: true,
          span: 24,
        },
      ],
    },
    callback(res) {
      const moduleDTOS = [moduleDTOList.value[form.value.currentModule]].map((item, index) => {
        return {
          ...item,
          moduleName: moduleDTOList.value[form.value.currentModule].moduleName,
          classifyDTOS: item.detailDTOList.reduce((pre, cur, index) => {
            if (cur.detailType == 1) {
              const detailDTOS = item.detailDTOList
                .filter(item => item.classify == cur.classify && item.detailType != 1)
                .map(item => {
                  return {
                    ...item,
                    sortNumber: index,
                  };
                });
              pre.push({
                ...cur,
                classifySort: index,
                detailDTOS,
              });
            }
            return pre;
          }, []),
          sortNumber: index,
        };
      });
      const value = {
        moduleDTOS,
        ...res.data,
        templateType: 1,
      };
      axios.post('/vt-admin/optionTemplate/save', value).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
      });
    },
  });
}

onMounted(() => {
  addEvent();
});
let brandList = ref([]);

function getBrand(i) {
  if (!(!i.productId && i.source == 1 && i.categoryId)) {
    return;
  }
  axios.get('/api/vt-admin/product/getBrandListByCategory?categoryId=' + i.categoryId).then(res => {
    brandList.value = res.data.data;
  });
}
let productSpecificationList = ref([]);
function getProductSpecificationList(i) {
  if (!(!i.productId && i.source == 1 && i.categoryId)) {
    return;
  }
  axios
    .get('/api/vt-admin/product/getSpecificationListByCategory', {
      params: {
        categoryId: i.categoryId,
        productBrand: i.productBrand,
      },
    })
    .then(res => {
      productSpecificationList.value = res.data.data;
    });
}
function handleSpecificationClick(i, item) {
  i.customProductName = item.productName;
  i.customProductSpecification = item.productSpecification;
  i.customProductDescription = item.description;
  // i.productId = item.id;
  i.customUnit = item.unitName;
  i.productBrand = item.productBrand;
  i.rgcbdj = item.laborCost || '';
  i.ybcbdj = item.ybcbdj || '';
  i.qtcbdj = item.qtcbdj || '';
  i.costPrice = item.costPrice || '';
}
function handleProductBrandClick(i, item) {
  console.log(i, item);
  i.customProductName = '';
  i.customProductSpecification = '';
  i.customProductDescription = '';
  // i.productId = item.id;
  i.customUnit = '';
  i.productBrand = item;
}
function editDiscountsPrice() {
  proxy.$refs.dialogForm.show({
    title: '报价折减',
    width: '30%',
    option: {
      column: [
        {
          label: '折减金额',
          prop: 'discountsPrice',
          type: 'number',
          span: 24,
          value: form.value.discountsPrice || 0,
          rules: [
            {
              required: true,
              message: '请输入折减金额',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      form.value.discountsPrice = res.data.discountsPrice;
      res.close();
    },
  });
}
const emits = defineEmits(['exportProductByHistory']);
function exportProductByHistory(item) {
  const moduleName = moduleDTOList.value[form.value.currentModule].moduleName;
  const classify = item.classify;
  emits('exportProductByHistory', { moduleName, classify });
  window.setData = setData;
}
function exportProductByFromOhter(list) {
  const data = list.map(item => {
    return {
      ...item,
      source: 4,
      classifyName: `${route.query.moduleName}?${route.query.classify}`,
    };
  });
  window.opener.setData(data);
  proxy.$message.success('导入成功');
}

function handleResetHeight() {
  isFold.value = !isFold.value;

  // 延迟更新表格高度，等待DOM更新完成
  nextTick(() => {
    updateTableHeight();
  });
}

// 计算并更新表格高度
function updateTableHeight() {
  const virtualTableRef = proxy.$refs.virtualTableRef;
  if (virtualTableRef && virtualTableRef.updateHeight) {
    virtualTableRef.updateHeight();
  }
}

// 监听窗口大小变化
function handleWindowResize() {
  updateTableHeight();
}

// 组件挂载时添加事件监听
onMounted(() => {
  window.addEventListener('resize', handleWindowResize);

  // 初始化表格高度
  nextTick(() => {
    updateTableHeight();
  });
});

// 组件卸载时移除事件监听和清理缓存
onUnmounted(() => {
  window.removeEventListener('resize', handleWindowResize);
  document.removeEventListener('keydown', handleKeyDown);

  // 清理所有缓存
  focusElementCache.clear();
  tableDataCache.clear();
  cachedTotals.value.clear();

  if (updateDebounceTimer) {
    clearTimeout(updateDebounceTimer);
    updateDebounceTimer = null;
  }

  if (moduleChangeTimer) {
    clearTimeout(moduleChangeTimer);
    moduleChangeTimer = null;
  }

  // 重置加载状态
  moduleChangeLoading.value = false;

  // 清理模块拖拽实例
  if (moduleSortableInstance) {
    moduleSortableInstance.destroy();
    moduleSortableInstance = null;
  }

  // 重置缓存变量
  lastModuleId = null;
  lastDetailListLength = 0;
});
let allTableRef = ref(null);
function handleSelectAll(val) {
  const isCheck = val.length > 0;
  moduleDTOList.value.forEach(item => {
    item.checked = isCheck;
    item.detailDTOList.forEach(i => {
      i.isCheck = isCheck;
    });
  });
}

function handleSelect(val, row) {
  console.log(val, row);

  if (row.parentUuid) {
    //选中子级
    const parent = allData.value.find(item => item.uuid == row.parentUuid);
    if (val.findIndex(item => item.uuid == row.uuid) > -1) {
      // 选中

      allTableRef.value.toggleRowSelection(parent, true);

      const children = parent.children;
      children.forEach(item => {
        const bol = val.findIndex(i => i.uuid == item.uuid) > -1;
        allTableRef.value.toggleRowSelection(item, bol);

        if (item.uuid == row.uuid) {
          allTableRef.value.toggleRowSelection(item, true);
        }
      });

      moduleDTOList.value[row.parentIndex].isCheck = 1;
      moduleDTOList.value[row.parentIndex].detailDTOList.forEach(item => {
        if (item.classify == row.moduleName) {
          item.isCheck = 1;
        }
      });
    } else {
      let bol = false;
      const children = parent.children;
      val.forEach(item => {
        children.forEach(i => {
          if (item.uuid == i.uuid) {
            bol = true;
          }
        });
      });
      moduleDTOList.value[row.parentIndex].detailDTOList.forEach(item => {
        if (item.classify == row.moduleName) {
          item.isCheck = 0;
        }
      });
      if (!bol) {
        allTableRef.value.toggleRowSelection(parent, false);
        moduleDTOList.value[row.parentIndex].isCheck = 0;
      }
    }
  } else {
    //选中父级
    const isCheck = val.findIndex(item => item.uuid == row.uuid) > -1 ? 1 : 0;
    moduleDTOList.value[row.index].isCheck = isCheck;
    moduleDTOList.value[row.index].detailDTOList.forEach(item => {
      item.isCheck = isCheck;
    });
  }
}
// 询价单引入产品
function setDataByInquiry({ id, key, value, data }) {
  if (data) {
    return ElMessage("请自行添加")
  } else {
    let data
    moduleDTOList.value.forEach(item => {
      data = item.detailDTOList.find(item => item.id == id);
    })
    data[key] = value;
  }
}
defineExpose({
  getData,
  getTree,
  setData,
  showEditPanel,
  closeEditPanel,
  toggleCollapse,
  switchToPreviousProduct,
  switchToNextProduct,
  isCurrentEditingProduct,
  insertEmptyProduct,
  setDataByInquiry
});
</script>

<style lang="scss" scoped>
.wrap {
  height: 100%;
  width: calc(100%);
  // background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  border-radius: 12px;
  // box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);

  .barbox {
    display: flex;
    align-items: center;
    padding-left: 12px;
  }

  .box {
    height: calc(100% - 80px);
    width: 100%;
  }
}

table {
  width: 100%;
  border-collapse: collapse;
  //   margin: 25px 0;
  //   height: 100%;
  font-size: 0.9em;
  min-width: 400px;
  color: #111;
  border-radius: 5px 5px 0 0;
  border-color: #ccc;
  table-layout: fixed;
  // overflow: hidden;
  // box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}

th,
td {
  //   padding: 5px;
  text-align: left;
  height: 25px;
  white-space: nowrap;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #dddddd;
  // border-bottom: 1px solid #dddddd;
}

.active {
  white-space: wrap;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
  border-bottom: 1px solid #dddddd;
}

thead {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;

  // border-bottom: 1px solid #dddddd;
  td {
    // word-break: unset;
    white-space: unset;
  }
}

th {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
}

tr {
  // background-color: #f3f3f3;
  height: 25px;
}

.category {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  border-left: 4px solid #0288d1 !important;

  .delete_category {
    display: none;
  }

  &:hover {
    background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
  }

  &:hover .delete_category {
    display: inline-block;
  }
}

.cell_hover:hover {
  background-color: #fef3c7 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.index_product {
  &:hover .delete_product {
    display: inline-block;
  }

  &:hover .index_product_span {
    display: none;
  }

  .delete_product {
    display: none;
    cursor: pointer;
  }

  .source {
    display: inline-block;
    height: 20px;
    width: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 15px;
    border: 1px solid var(--el-color-success);
    color: var(--el-color-success);
  }
}

.left {
  text-align: left;
}

.center {
  text-align: center;
}

.right {
  text-align: right;
}

.error {
  background-color: var(--el-color-danger-light-8);
}

/* 当前编辑产品的背景色 - 淡蓝色背景，黑色字体 */
.current-editing {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  position: relative;
}

/* 当前编辑行内的所有单元格 */
.current-editing td {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-color: #d4edda !important;
}

.current-editing:hover {
  background-color: #cce7ff !important;
  color: #000 !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

/* 当前编辑行悬停时的所有单元格 */
.current-editing:hover td {
  background-color: #cce7ff !important;
  color: #000 !important;
}

/* 选中的数据行背景颜色 - 淡蓝色背景，黑色字体 */
.row-selected {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  position: relative;
}

/* 选中行内的所有单元格 */
.row-selected td {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-color: #d4edda !important;
}

/* 选中行的悬停效果 - 稍深一点的淡蓝色 */
.row-selected:hover {
  background-color: #cce7ff !important;
  color: #000 !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

/* 选中行悬停时的所有单元格 */
.row-selected:hover td {
  background-color: #cce7ff !important;
  color: #000 !important;
}

/* 现代化浮动编辑面板样式 */
.floating-edit-panel {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15), 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  user-select: none;
  position: relative;
  min-width: 420px;
  min-height: 60px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.floating-edit-panel:hover {
  box-shadow: 0 25px 80px rgba(0, 0, 0, 0.2), 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.panel-header {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  padding: 16px 20px;
  border-bottom: 1px solid #cbd5e1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: grab;
  position: relative;
  backdrop-filter: blur(5px);
}

.panel-header::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border-radius: 0 2px 2px 0;
}

.panel-header:active {
  cursor: grabbing;
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
}

.header-left {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.panel-header h4 {
  margin: 0;
  font-size: 18px;
  color: #1f2937;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.025em;
}

.product-navigation {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.nav-info {
  color: #666;
  font-weight: 500;
}

.header-controls {
  display: flex;
  align-items: center;
}

.panel-content {
  padding: 20px 24px;
  padding-bottom: 40px;
  overflow-y: auto;
  height: calc(100% - 70px);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(5px);
}

.operation-hints {
  margin-bottom: 16px;
}

.keyboard-hint {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 4px;
  padding: 8px 12px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.keyboard-hint .el-icon {
  font-size: 14px;
  color: #0ea5e9;
}

.insert-hint {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.insert-hint .el-icon {
  font-size: 14px;
  color: #16a34a;
}

.panel-content :deep(.el-form-item) {
  margin-bottom: 16px;
}

.panel-content :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.panel-content :deep(.el-input__wrapper) {
  border-radius: 4px;
}

.panel-content :deep(.el-input-number) {
  width: 100%;
}

.panel-content :deep(.el-textarea__inner) {
  border-radius: 4px;
}

/* 调整大小控制点样式 */
.resize-handle {
  position: absolute;
  background: transparent;
}

.resize-right {
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  cursor: ew-resize;
}

.resize-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  cursor: ns-resize;
}

.resize-corner {
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  cursor: nw-resize;
  background: linear-gradient(-45deg,
      transparent 0%,
      transparent 30%,
      #dcdfe6 30%,
      #dcdfe6 70%,
      transparent 70%);
}

.resize-corner::before {
  content: '';
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: linear-gradient(-45deg,
      transparent 0%,
      transparent 30%,
      #909399 30%,
      #909399 70%,
      transparent 70%);
}

/* 滚动条样式 */
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.panel-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.panel-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 表格容器样式 - 现代化表格编辑器 */
.table-container {
  height: 100%;
  overflow: visible;
  /* 改为visible以支持sticky定位 */
  padding: 0;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  position: relative;
  /* 确保定位上下文正确 */
}

.myCard {
  padding: 0px;
  margin-bottom: 0;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
  background: #fff;
}

.myCard .barbox {
  display: flex;
  align-items: center;
  padding-left: 10px;
}

.myCard .box {
  height: calc(100% - 60px);
  width: 100%;
  position: relative;
  /* 确保sticky定位的父容器设置正确 */
  overflow: visible;
  /* 允许sticky元素显示 */
}

/* 现代化汇总卡片样式 */
.modern-summary-card {
  transition: all 0.3s ease;
}

// .modern-summary-card:hover {
//   transform: translateY(-2px);
//   box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1) !important;
// }

.summary-title {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  position: relative;
}

.title-text {
  font-size: 16px;
  font-weight: 700;
  color: #1f2937;
  letter-spacing: 0.025em;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.title-decoration {
  flex: 1;
  height: 2px;
  background: linear-gradient(90deg, #409eff 0%, transparent 100%);
  margin-left: 12px;
  border-radius: 1px;
}

/* 汇总数据项样式 */
.modern-summary-card .el-col div[style*='color: #666'] {
  color: #6b7280 !important;
  font-weight: 500;
  margin-bottom: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 1);
  border-radius: 6px;
  border-left: 3px solid #e5e7eb;
  transition: all 0.2s ease;
}

.modern-summary-card .el-col div[style*='color: #666']:hover {
  background: rgba(255, 255, 255, 0.9);
  border-left-color: #409eff;
  transform: translateX(2px);
}

.modern-summary-card .el-col div[style*='color: #666'] span {
  font-weight: 600;
  color: #1f2937 !important;
}

/* 现代化模块标签栏样式 */
.modern-module-tabs {
  // border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.modern-module-tabs:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* 优化单选按钮组样式 */
.modern-module-tabs :deep(.el-radio-group) {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  overflow-x: auto;
  padding: 4px;
}

.modern-module-tabs :deep(.el-radio-button) {
  margin: 0;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
}

.modern-module-tabs :deep(.el-radio-button__inner) {
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  background: #ffffff;
  color: #6b7280;
  font-weight: 500;
  padding: 8px 16px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-module-tabs :deep(.el-radio-button__inner:hover) {
  border-color: #409eff;
  color: #409eff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.modern-module-tabs :deep(.el-radio-button.is-active .el-radio-button__inner) {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border-color: #409eff;
  color: #ffffff;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 添加和模板按钮样式 */
.modern-module-tabs :deep(.el-button) {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.modern-module-tabs :deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 现代化分类标签样式 */
.modern-category-tag {
  width: 100% !important;
  margin-bottom: 8px !important;
  cursor: pointer !important;
  overflow: hidden !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%) !important;
  border: 2px solid #e5e7eb !important;
  border-radius: 10px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  color: #374151 !important;
  transition: all 0.2s ease !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
  position: relative !important;
}

.modern-category-tag::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border-radius: 2px 0 0 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.modern-category-tag:hover {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  border-color: #409eff !important;
  color: #337ecc !important;
  transform: translateX(4px) !important;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15) !important;
}

.modern-category-tag:hover::before {
  opacity: 1;
}

.modern-category-tag:active {
  transform: translateX(2px) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2) !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .wrap {
    padding: 16px;
  }

  .modern-summary-card {
    margin-bottom: 12px;
  }

  .floating-edit-panel {
    min-width: 360px;
  }
}

@media (max-width: 768px) {
  .wrap {
    padding: 12px;
  }

  .modern-module-tabs :deep(.el-radio-button__inner) {
    padding: 6px 12px;
    font-size: 13px;
  }

  .floating-edit-panel {
    min-width: 320px;
  }

  .panel-header {
    padding: 12px 16px;
  }

  .panel-content {
    padding: 16px 20px;
  }
}

/* 加载动画 */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// .modern-summary-card {
//   animation: slideInUp 0.6s ease-out;
// }

// .modern-category-tag {
//   animation: slideInLeft 0.4s ease-out;
// }

// .modern-module-tabs {
//   animation: slideInUp 0.5s ease-out;
// }

/* 表格行动画 */
.virtual-table-container tbody tr {
  animation: slideInUp 0.3s ease-out;
}

/* 优化滚动条 */
.wrap::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.wrap::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.5);
  border-radius: 4px;
}

.wrap::-webkit-scrollbar-thumb {
  background: rgba(148, 163, 184, 0.6);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.wrap::-webkit-scrollbar-thumb:hover {
  background: rgba(100, 116, 139, 0.8);
}

/* 模块切换加载状态样式 */
.module-loading-indicator {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #f0f9ff;
  border: 1px solid #409eff;
  border-radius: 4px;
  font-size: 12px;
  color: #409eff;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.7;
  }

  100% {
    opacity: 1;
  }
}

/* 禁用状态下的模块按钮样式 */
.el-radio-group[disabled] .el-radio-button {
  opacity: 0.6;
  cursor: not-allowed;
}

.el-radio-group[disabled] .el-radio-button:hover {
  opacity: 0.6;
}

.el-col {
  margin-bottom: 0;
}
</style>
