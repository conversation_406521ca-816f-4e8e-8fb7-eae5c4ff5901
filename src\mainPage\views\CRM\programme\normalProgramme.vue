<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      :table-loading="loading"
      ref="crud"
      @search-reset="searchReset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      @expand-change="expandChanges"
      v-model="form"
      :row-class-name="getClass"
    >
      <template #menu-left="{}">
        <div style="display: flex">
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">总设计额：</span>
            <el-text type="primary" size="large"
              >￥{{ (totalPrice * 1).toLocaleString() || 0 }}</el-text
            >
          </div>
        </div>
      </template>
      <template #stage="{ row, a, b, c }">
        <el-tag effect='plain'>{{ (a, b, c) }}{{ row.$stage }}</el-tag>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="primary"
          text
          @click="handleEdit(row)"
          v-if="row.optionStatus == 1"
          icon="Edit"
          >编辑</el-button
        >
        <!-- <el-button type="primary" text @click="handleAdd(row)" v-else icon="plus"
            >新增方案</el-button
          > -->
        <!-- <el-button type="primary" text @click="submit(row)" icon="check" v-if="row.optionStatus != 2 && $store.getters.userInfo.user_id == row.technicalPersonnel">提交</el-button> -->
        <el-button type="primary" text @click="handleView(row)" icon="View">详情</el-button>
        <el-button type="primary" text @click="handleHistory(row)" icon="Clock">历史</el-button>
        <el-button
          text
          :type="row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'"
          icon="el-icon-download"
          v-if="row.optionId"
          @click="download(row)"
          >下载</el-button
        >
        <el-button
          text
          type="primary"
          icon="CopyDocument"
          v-if="row.optionId && row.stage != 3"
          @click="addSubedition(row)"
          >复制</el-button
        >
        <el-button
          type="primary"
          text
          @click="back(row.optionId)"
          v-if="row.optionStatus == 2 && row.auditStatus == 1"
          icon="CaretLeft"
          title="退回"
          >退回</el-button
        >
      </template>
      <template #auditStatus="{ row }">
        <span v-if="!row.auditStatus">---</span>
        <el-tag effect='plain' v-if="row.auditStatus == 1" size="small" type="info">待主管审核</el-tag>
        <el-tag effect='plain' v-if="row.auditStatus == 2" size="small" type="success">审核成功</el-tag>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.auditReason"
          placement="top-start"
          v-if="row.auditStatus == 3"
        >
          <el-tag effect='plain' size="small" type="danger">审核失败</el-tag>
        </el-tooltip>
      </template>
      <template #expand="{ row }">
        <div style="padding: 0 10px">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col :span="4" v-for="item in row.businessOpportunityOptionVOS || []">
              <el-card
                class="maCard"
                :shadow="item.optionVersionType == 0 ? 'never' : 'always'"
                style="height: 200px; overflow-y: auto; border: 1px solid var(--el-color-primary)"
              >
                <template #header>
                  <el-row>
                    <el-col
                      :span="24"
                      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                    >
                      <el-tooltip :content="item.optionName">
                        <el-text
                          type="primary"
                          size="default"
                          style="
                            font-weight: bolder;
                            font-family: Inter, Helvetica Neue, Helvetica, PingFang SC,
                              Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial,
                              sans-serif;
                          "
                          >{{ item.optionName }}</el-text
                        >
                      </el-tooltip>
                    </el-col>
                    <el-col :span="24">
                      <div
                        v-if="item.optionVersionType == 1"
                        style="display: flex; justify-content: flex-end"
                      >
                        <el-button
                          type="info"
                          v-if="item.optionStatus == 0"
                          @click="handleEditSub(item)"
                          circle
                          title="编辑"
                          icon="edit"
                          text
                        ></el-button>
                        <el-button
                          type="info"
                          @click="handleViewSub(item)"
                          circle
                          title="预览"
                          icon="view"
                          text
                        ></el-button>
                        <el-button
                          text
                          icon="el-icon-download"
                          v-if="row.optionId"
                          type="info"
                          title="下载"
                          @click="download(item)"
                        ></el-button>
                        <el-button
                          text
                          @click="back(item.id)"
                          v-if="item.optionStatus ==2 && row.auditStatus != 2"
                          icon="CaretLeft"
                          title="退回"
                        ></el-button>
                        <el-button
                          text
                          icon="delete"
                          v-if="row.optionId"
                          type="danger"
                          title="删除"
                          @click="deleteOption(item)"
                        ></el-button>
                      </div>

                      <div v-else style="display: flex; justify-content: flex-end">
                        <el-button
                          type="primary"
                          text
                          @click="handleEdit(row)"
                          v-if="row.optionStatus == 1"
                          icon="Edit"
                          title="编辑"
                        ></el-button>

                        <el-button
                          type="primary"
                          text
                          @click="handleView(row)"
                          icon="View"
                          title="预览"
                        ></el-button>
                        <el-button
                          type="primary"
                          text
                          @click="handleHistory(row)"
                          icon="Clock"
                          title="历史"
                        ></el-button>
                        <el-button
                          text
                          :type="
                            row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'
                          "
                          title="下载"
                          icon="el-icon-download"
                          v-if="row.optionId"
                          @click="download(row)"
                        ></el-button>
                        <el-button
                          text
                          type="primary"
                          icon="CopyDocument"
                          title="复制"
                          v-if="row.optionId && row.stage != 3"
                          @click="addSubedition(row)"
                        ></el-button>
                        <el-button
                          type="primary"
                          text
                          @click="back(row.optionId)"
                          v-if="item.optionStatus == 2 && row.auditStatus == 1"
                          icon="CaretLeft"
                          title="退回"
                        ></el-button>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <el-row style="margin-top: 10px; margin-left: 5px">
                  <el-col :span="16">
                    <el-text
                      style="font-size: 30px"
                      :type="item.optionStatus == 0 ? 'info' : 'success'"
                      >￥{{ item.sealTotalPrice }}</el-text
                    >
                  </el-col>
                  <el-col :span="8">
                    <el-tag effect='plain' :type="item.optionStatus == 0 ? 'info' : 'success'">
                      {{
                        [
                          {
                            value: 0,
                            label: '草稿',
                          },
                          // {
                          //   value: 1,
                          //   label: '--',
                          // },
                          {
                            value: 2,
                            label: '已做',
                          },
                        ].find(i => item.optionStatus == i.value).label
                      }}
                    </el-tag>
                  </el-col>
                  <el-descriptions size="default" column="1">
                    <el-descriptions-item label="备注:">{{ item.remark }}</el-descriptions-item>
                    <el-descriptions-item label="撤回原因:">{{
                      item.auditReason
                    }}</el-descriptions-item>
                  </el-descriptions>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
      <template #name="{row}">
        {{row.name}}-{{ row.$stage }}
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="历史记录" v-model="dialogVisible">
      <History ref="history" :id="currentId" :currentStatus="currentStatusTxt"></History>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { programmeStatus, businessOpportunityData, auditStatus } from '@/const/const.js';
import History from './compoents/history.vue';
import moment from 'moment';
import { download as downloadOffer } from '@/utils/download.js';

let store = useStore();
let permission = computed(() => store.getters.permission);
let currentStatusTxt = computed(() => {
  console.log(currentStatus.value);
  return !currentStatus.value || currentStatus.value == 3;
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  searchLabelWidth: 110,
  menuWidth: 300,
  menu: true,
  border: true,
  expand: true,
  rowKey: 'id',
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,component: 'wf-bussiness-drop',
      search: true,
    },
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,

    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    // },
    {
      type: 'select',
      label: '业务板块',
      span: 12, hide: true,
      // search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择商机关联',
        },
      ],
      display: true,
      prop: 'type',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机描述',
      prop: 'description',
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      // search: true,
      hide: true,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      width: 90,
    },
    {
      label: '商机阶段',
      prop: 'stage',
      slot: true,
      type: 'select',
      search: true,
      dicData: businessOpportunityData,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
    },

    {
      prop: 'businessPersonName',
      label: '业务员',
    },
    {
      label: '总金额',
      width: 130,
      prop: 'sealTotalPrice',
      overHidden: true,
    },
    {
      label: '要求完成时间',
      prop: 'optionRequireTime',
      html: true,
      width: 110,
      overHidden: true,
      formatter: row => {
        if (!row.optionRequireTime) return '--';

        const day = moment(moment(row.optionRequireTime).format('YYYY-MM-DD')).diff(
          moment(new Date()).format('YYYY-MM-DD'),
          'days'
        );

        return `<div style='color:${
          day <= 1 && row.auditStatus != 2 ? 'var(--el-color-danger)' : ''
        }'>${moment(row.optionRequireTime).format('YYYY-MM-DD')}</div>`;
      },
    },

    {
      label: '方案状态',
      type: 'select',
      dicData: programmeStatus,
      prop: 'optionStatus',
      search: true,
    },
    {
      label: '审核状态',
      type: 'select',
      width: 100,
      dicData: auditStatus,
      prop: 'auditStatus',
      search: true,
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
  
    {
      label: '审核时间',
      width: 135,
      hide: true,
      prop: 'auditTime',
      type: 'dateTime',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      type: 'datetime',
      format: 'YYYY-MM-DD',

      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/needOptionPage';
let route = useRoute();
let params = ref({
  ids:route.query.ids
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        optionStartTime: params.value.auditTime ? params.value.auditTime[0] : null,
        optionEndTime: params.value.auditTime ? params.value.auditTime[1] : null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });

  axios
    .get('/api/vt-admin/businessOpportunity/needOptionStatistics', {
      params: {
        ...params.value,
        selectType: 0,
        optionStartTime: params.value.auditTime ? params.value.auditTime[0] : null,
        optionEndTime: params.value.auditTime ? params.value.auditTime[1] : null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.sealTotalPrice;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function searchReset() {
  params.value.ids = null
  onLoad();
}
function handleView(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        name: row.name,
        type: 'detail',
      },
    });
  } else if (row.isHasDataJson == 0) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        name: row.name,
        type: 'detail',
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
function handleViewSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else if (row.isHasDataJson == 0 || !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
function handleEdit(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
      },
    });
    // window.open(`/CRM/programme/compoents/update?id=${row.id}&type=edit&name=${row.name}&businessOpportunityId=${row.id}`)
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
        businessType: row.type,
      },
    });
  }
}
function addSubedition(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
        isAddSubedition: 1,
      },
    });
    // window.open(`/CRM/programme/compoents/update?id=${row.id}&type=edit&name=${row.name}&businessOpportunityId=${row.id}`)
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.optionId,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
        isAddSubedition: 1,
        businessType: row.type,
      },
    });
  }
}
function handleEditSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.businessOpportunityId,
        isEditSubedition: 1,
        businessType: row.type,
        isAddSubedition: 1,
      },
    });
    // window.open(`/CRM/programme/compoents/update?id=${row.id}&type=edit&name=${row.name}&businessOpportunityId=${row.id}`)
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.businessOpportunityId,
        isEditSubedition: 1,
        isAddSubedition: 1,
        businessType: row.type,
      },
    });
  }
}

function handleAdd(row) {
  router.push({
    path: '/CRM/programme/compoents/updateVersion3',
    query: {
      id: row.id,
      type: 'add',
    },
  });
}
let currentId = ref(0);
let currentStatus = ref(null);
let dialogVisible = ref(false);
function handleHistory(row) {
  currentId.value = row.optionId;
  currentStatus.value = row.auditStatus;
  dialogVisible.value = true;
}
function submit(row) {
  proxy
    .$confirm('提交之后将会不可修改，你可以在历史记录查看?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunityOption/confirm', {
          id: row.optionId,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
        });
    });
}
function download(row) {
  downloadOffer('/api/vt-admin/businessOpportunityOption/downloadOption', {
    id: row.optionId || row.id,
  });
}

function expandChanges(row, expendList) {
  if (expendList.length) {
    option.value.expandRowKeys = [];
    if (row) {
      option.value.expandRowKeys.push(row.id);
    }
  } else {
    option.value.expandRowKeys = [];
  }
}
function getClass({ row }) {
  return row.isHasManyOption == 0 ? 'hide_icon' : '';
}
function deleteOption(item) {
  proxy
    .$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunityOption/deletedById?id=' + item.id, {})
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}
function back(id) {
  proxy
    .$confirm('确定退回吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/businessOpportunityOption/returnBack?id=' + id, {}).then(res => {
        proxy.$message.success(res.data.msg);
        onLoad();
      });
    });
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 5px;
}
:deep(.el-col) {
  margin-bottom: 0px;
}
:deep(.hide_icon td:first-child .cell) {
  visibility: hidden;
}
</style>
