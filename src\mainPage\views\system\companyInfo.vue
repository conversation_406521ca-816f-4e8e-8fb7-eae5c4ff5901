<template>
  <basic-container>
    <avue-form :option="option" v-model="detailForm" @submit="handleSubmit"></avue-form>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { getCurrentInstance, nextTick, onMounted } from 'vue';
let option = ref({
  column: [
    // {
    //   label: '公司名称',
    //   prop: 'tenantName',
    //   search: true,
    //   width: 180,
    //   span: 24,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入公司名称',
    //       trigger: 'blur',
    //     },
    //   ],
    // },
    // {
    //   label: '联系人',
    //   prop: 'linkman',
    //   width: 100,
    //   search: true,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入联系人',
    //       trigger: 'blur',
    //     },
    //   ],
    // },
    // {
    //   label: '联系电话',
    //   prop: 'contactNumber',
    //   width: 150,
    // },
    // {
    //   label: '联系地址',
    //   prop: 'address',
    //   span: 24,
    //   minRows: 2,
    //   type: 'textarea',
    //   hide: true,
    // },
    {
      label: '系统登录页背景',
      prop: 'backgroundUrl',
      type: 'upload',
      listType: 'picture-card',
      dataType: 'string',
      labelWidth: 150,
      action: '/blade-resource/attach/upload',
      propsHttp: {
        res: 'data',
        url: 'link',
      },
      accept: '.jpeg,.png,.jpg',
      tip: '1920px * 1080px',
      hide: true,
      span: 24,
    },
    {
      label: '系统logo',
      prop: 'logoUrl',
      type: 'upload',
      labelWidth: 150,
      listType: 'picture-card',
      dataType: 'string',
      accept: '.jpeg,.png,.jpg',
      limit: 1,
      uploadExceed: () => {
        ElMessage.warning('限制1张');
      },
      action: '/blade-resource/attach/upload',
      propsHttp: {
        res: 'data',
        url: 'link',
      },
      hide: true,
      span: 24,
    },
    {
      label: '系统编号',
      prop: 'systemCode',
      labelWidth: 150,
      span: 12,
      placeholder: '请输入系统编号，大写字母，系统编号生成将以这个开头，列如VT',
      rules: [
        {
          // required: true,

          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (!value) {
              callback();
              return;
            }
            // 验证必须是大写字母
            const upperCaseRegex = /^[A-Z]+$/;
            if (!upperCaseRegex.test(value)) {
              callback(new Error('系统编号必须是大写字母'));
            } else {
              callback();
            }
          },
        },
      ],
    },
    // {
    //   label: '公司简介',
    //   prop: 'description',
    //   value: '',
    //   component: 'AvueUeditor',
    //   action: '/api/blade-resource/attach/upload',
    //   options: {
    //     action: '/api/blade-resource/attach/upload',
    //     accept: 'image/png, image/jpeg, image/jpg,.mp4',
    //   },
    //   propsHttp: {
    //     res: 'data',
    //     url: 'link',
    //   },

    //   hide: true,
    //   minRows: 6,
    //   span: 24,
    // },
  ],
});
const { proxy } = getCurrentInstance();
const tenantId = proxy.$store.getters.userInfo.tenant_id;
let detailForm = ref({});
onMounted(() => {
  setTimeout(() => {
    getDetail();
  }, 100);
});
function getDetail(params) {
  axios
    .get('/api/blade-system/tenant/detail', {
      params: {
        tenantId,
      },
    })
    .then(res => {
      nextTick(() => {
        detailForm.value = res.data.data;
      });
    });
}
function handleSubmit(form, done, loading) {
  axios.post('/api/blade-system/tenant/submit', form).then(res => {
    proxy.$message.success(res.data.msg);
    done();
  });
}
</script>

<style lang="scss" scoped></style>
