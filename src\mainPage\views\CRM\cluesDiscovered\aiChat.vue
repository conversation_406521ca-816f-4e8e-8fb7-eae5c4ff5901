<template></template>
<script setup>
import { onBeforeMount,onBeforeUnmount } from 'vue'
import { useStore } from 'vuex';
import {initBot,closeChatbot } from './index.js';
import website from '@/config/website.js';

const store = useStore()

// window.difyChatbotConfig = {
//     token: '5d1WTr2PRTQq1uQx',
//     baseUrl: website.aiChatUrl,
//     inputs: {
//         // You can define the inputs from the Start node here
//         // key is the variable name
//         // e.g.
//         // name: "NAME"
//         blade_auth: store.getters.userInfo.access_token,
//     },
//     systemVariables: {
//         user_id: store.getters.userInfo.user_id,
//         // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',

//     },
//     userVariables: {
//         avatar_url: store.getters.userInfo.avatar,
//         name: store.getters.userInfo.real_name,
//         user_id: store.getters.userInfo.user_id,
//     },
//     dynamicScript: true,
//     draggable:true,
//     dragAxis:'both',
//     mode:'chat'
// }
// console.log(window.difyChatbotConfig,import.meta.env.VITE_APP_ENV, 'initBot');
// initBot();
// let script = document.createElement('script');

// script.src = 'http://localhost/embed.min.js';
// script.id = '5d1WTr2PRTQq1uQx';
// script.defer = true;
// document.head.appendChild(script);
// onBeforeUnmount(() => {
//     let value = import.meta.env.VITE_APP_ENV;
//     if(value == 'production') return 
//   closeChatbot();
  
// })
</script>
<style>

</style>