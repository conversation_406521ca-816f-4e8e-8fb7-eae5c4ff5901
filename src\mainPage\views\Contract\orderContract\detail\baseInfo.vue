<template>
 
   

    <div class="content-wrapper">
      <!-- 左侧内容 - 基本信息 -->
      <div class="left-content">
        <el-card class="basic-info-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><InfoFilled /></el-icon>
              <span>基本信息</span>
            </div>
          </template>
          <div class="info-sections">
            <!-- 合同信息部分 -->
            <div class="info-section">
              <!-- <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>合同信息</span>
              </div> -->
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">合同名称：</span>
                  <span class="value">{{props.contractForm.contractName}}</span>
                </div>
                <div class="info-item">
                  <span class="label">客户名称：</span>
                  <span class="value">{{props.contractForm.customerName}}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系人：</span>
                  <span class="value">{{props.contractForm.customerContactName}}</span>
                </div>
                <div class="info-item">
                  <span class="label">联系电话：</span>
                  <span class="value">{{ props.contractForm.customerPhone }}</span>
                </div>
                <div class="info-item">
                  <span class="label">合同金额：</span>
                  <span class="value">￥{{ props.contractForm.contractTotalPrice || '0.00' }}</span>
                </div>
                <div class="info-item">
                  <span class="label">驻场工程师：</span>
                  <span class="value">{{ props.contractForm.operationTechnologyName }}</span>
                </div>
              </div>
            </div>
            
            <!-- 项目信息部分 -->
            <div class="info-section" v-if='false'>
              <!-- <div class="section-title">
                <el-icon><InfoFilled /></el-icon>
                <span>项目信息</span>
              </div> -->
              <div class="info-grid">
                <div class="info-item">
                  <span class="label">项目名称：</span>
                  <span class="value">郑州中宇系统运维项目</span>
                </div>
                <div class="info-item">
                  <span class="label">项目属性：</span>
                  <span class="value">运维服务</span>
                </div>
                <div class="info-item">
                  <span class="label">项目地址：</span>
                  <span class="value">河南省郑州市高新区科学大道100号</span>
                </div>
                <div class="info-item">
                  <span class="label">开工时间：</span>
                  <span class="value">2025-09-18</span>
                </div>
                <div class="info-item">
                  <span class="label">交付时间：</span>
                  <span class="value">2025-09-30</span>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 服务工单 -->
        <el-card class="service-task-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Tools /></el-icon>
              <span>服务工单</span>
              <el-link type="primary" class="view-more">查看更多</el-link>
            </div>
          </template>
          <div class="task-item">
            <div class="task-date">2025-09-23</div>
            <div class="task-code">T20250922002 111</div>
            <el-tag type="success" size="small">有效收</el-tag>
          </div>
        </el-card>
      </div>

      <!-- 右侧内容 -->
      <div class="right-content">
        <!-- 工单概况报表 -->
        <el-card class="task-overview-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><TrendCharts /></el-icon>
              <span>工单概况报表</span>
            </div>
          </template>
          <div class="overview-stats">
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <span class="stat-label">总工单数</span>
                  <span class="stat-value">1</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <span class="stat-label">进行中</span>
                  <span class="stat-value">1</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="16">
              <el-col :span="12">
                <div class="stat-item">
                  <span class="stat-label">已完成</span>
                  <span class="stat-value">0</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="stat-item">
                  <span class="stat-label">已终止</span>
                  <span class="stat-value">0</span>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-card>

        <!-- 客户满意度 -->
        <el-card class="satisfaction-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon><Star /></el-icon>
              <span>客户满意度</span>
              <div class="satisfaction-actions">
                <el-button size="small" @click="collectFeedback">收集</el-button>
                <el-button size="small" @click="visitCustomer">回访</el-button>
              </div>
            </div>
          </template>
          <div class="satisfaction-charts">
            <div class="satisfaction-item">
              <h4>整改情况</h4>
              <div class="pie-chart">
                <div class="pie-slice" style="--color: #f39c12; --percentage: 60%"></div>
                <div class="pie-slice" style="--color: #e74c3c; --percentage: 40%"></div>
              </div>
              <div class="legend">
                <div class="legend-item">
                  <span class="legend-color" style="background: #f39c12"></span>
                  <span>已整改完成: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #e74c3c"></span>
                  <span>延期整改: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #95a5a6"></span>
                  <span>无需整改: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #3498db"></span>
                  <span>一般: 0</span>
                </div>
              </div>
            </div>
            <div class="satisfaction-item">
              <h4>整改满意度</h4>
              <div class="pie-chart">
                <div class="pie-slice" style="--color: #27ae60; --percentage: 50%"></div>
                <div class="pie-slice" style="--color: #e74c3c; --percentage: 30%"></div>
                <div class="pie-slice" style="--color: #95a5a6; --percentage: 20%"></div>
              </div>
              <div class="legend">
                <div class="legend-item">
                  <span class="legend-color" style="background: #27ae60"></span>
                  <span>非常满意: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #2ecc71"></span>
                  <span>满意: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #95a5a6"></span>
                  <span>一般: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #e74c3c"></span>
                  <span>不满意: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #c0392b"></span>
                  <span>非常不满意: 0</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color" style="background: #f1c40f"></span>
                  <span>分值</span>
                </div>
              </div>
              <div class="score-section">
                <div class="score-title">分值</div>
                <div class="score-numbers">
                  <div>5</div>
                  <div>4</div>
                  <div>3</div>
                  <div>2</div>
                  <div>1</div>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  
</template>

<script setup>
import { ref, reactive } from 'vue';
import { Download, Edit, InfoFilled, Tools, TrendCharts, Star, Document } from '@element-plus/icons-vue';
const props = defineProps({
  contractForm: {
    type: Object,
    default: () => ({})
  }
})
// 响应式数据
const activeTab = ref('detail');

const contractInfo = reactive({
  projectNumber: '4214345',
  projectManager: '吴强、等3人',
  projectStatus: '集合运维',
  engineer: '雷丽影',
  customer: '郑州中宇',
  projectType: '客户',
  startDate: '2025-09-18',
  endDate: '2025-09-30',
  projectCode: '345345',
  stakeholders: '1...等2人',
});

const taskStats = reactive({
  total: 1,
  inProgress: 1,
  completed: 0,
  blocked: 0,
});

// 方法
const downloadReport = () => {
  console.log('下载运维服务报告');
};

const modifyContract = () => {
  console.log('修改合同');
};

const collectFeedback = () => {
  console.log('收集反馈');
};

const visitCustomer = () => {
  console.log('客户回访');
};
</script>

<style lang="scss" scoped>
.contract-detail {
  padding: 10px;
  background: var(--el-bg-color-page);
  height: 100vh;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .header-card {
    margin-bottom: 10px;

    .header-info {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .project-number {
        display: flex;
        align-items: center;
        gap: 24px;

        .number {
          font-size: 20px;
          font-weight: 600;
          color: var(--el-text-color-primary);
        }

        .header-tabs {
          :deep(.el-tabs__header) {
            margin: 0;
          }

          :deep(.el-tabs__nav-wrap) {
            &::after {
              display: none;
            }
          }
        }
      }

      .actions {
        display: flex;
        gap: 12px;
      }
    }
  }

  .content-wrapper {
    display: flex;
    height: 100%;
    gap: 16px;
    flex: 1;
    overflow: hidden;

    .left-content,
    .right-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 16px;
      overflow: hidden;
    }

    .basic-info-card,
    .service-task-card,
    .task-overview-card,
    .satisfaction-card {
      .card-header {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
        color: var(--el-text-color-primary);

        .el-icon {
          color: var(--el-color-primary);
        }

        .view-more {
          margin-left: auto;
        }

        .satisfaction-actions {
          margin-left: auto;
          display: flex;
          gap: 8px;
        }
      }
    }

    .basic-info-card {
      flex: 0 0 auto;

      .info-sections {
        display: flex;
        flex-direction: row;
        gap: 32px;

        .info-section {
          flex: 1;
          
          .section-title {
            display: flex;
            align-items: center;
            gap: 6px;
            margin-bottom: 12px;
            padding-bottom: 6px;
            border-bottom: 1px solid var(--el-border-color-lighter);
            font-weight: 500;
            color: var(--el-text-color-primary);
            font-size: 14px;

            .el-icon {
              color: var(--el-color-primary);
              font-size: 16px;
            }
          }

          .info-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 10px;

            .info-item {
              display: flex;
              align-items: center;

              .label {
                color: var(--el-text-color-regular);
                min-width: 90px;
                font-size: 13px;
              }

              .value {
                color: var(--el-text-color-primary);
                font-size: 13px;
                flex: 1;
              }
            }
          }
        }
      }
    }

    .service-task-card {
      flex: 1;

      .task-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--el-border-color-lighter);

        .task-date {
          color: var(--el-text-color-regular);
          font-size: 14px;
        }

        .task-code {
          color: var(--el-text-color-primary);
          font-weight: 500;
          flex: 1;
          margin: 0 16px;
        }
      }
    }

    .task-overview-card {
      .overview-stats {
        .stat-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 16px;
          background: var(--el-fill-color-light);
          border-radius: var(--el-border-radius-base);

          .stat-label {
            color: var(--el-text-color-regular);
            font-size: 14px;
          }

          .stat-value {
            color: var(--el-text-color-primary);
            font-size: 18px;
            font-weight: 600;
          }
        }
      }
    }

    .satisfaction-card {
      flex: 1;

      .satisfaction-charts {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;

        .satisfaction-item {
          flex: 1;

          h4 {
            margin: 0 0 16px 0;
            color: var(--el-text-color-primary);
            font-size: 14px;
            text-align: center;
            font-weight: 500;
          }

          .pie-chart {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            margin: 0 auto 16px;
            background: conic-gradient(#f39c12 0% 60%, #e74c3c 60% 100%);
          }

          .legend {
            .legend-item {
              display: flex;
              align-items: center;
              gap: 8px;
              margin-bottom: 6px;
              font-size: 12px;
              color: var(--el-text-color-regular);

              .legend-color {
                width: 12px;
                height: 12px;
                border-radius: 2px;
              }
            }
          }

          .score-section {
            margin-top: 16px;
            text-align: center;

            .score-title {
              font-size: 14px;
              color: var(--el-text-color-primary);
              margin-bottom: 8px;
              font-weight: 500;
            }

            .score-numbers {
              display: flex;
              justify-content: center;
              gap: 8px;

              div {
                width: 28px;
                height: 28px;
                background: var(--el-fill-color-light);
                border: 1px solid var(--el-border-color-light);
                border-radius: var(--el-border-radius-base);
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                color: var(--el-text-color-regular);
                transition: all 0.3s;

                &:hover {
                  background: var(--el-color-primary-light-9);
                  border-color: var(--el-color-primary);
                  color: var(--el-color-primary);
                }
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .contract-detail {
    .content-wrapper {
      flex-direction: column;
    }

    .header-info {
      flex-direction: column;
      gap: 5px;
      align-items: flex-start;
    }

    .satisfaction-charts {
      flex-direction: column !important;
    }

    .basic-info-card .info-grid {
      grid-template-columns: 1fr;
    }
  }
}
</style>
