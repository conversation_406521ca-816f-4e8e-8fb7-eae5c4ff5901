<template>
  <el-dialog
    title="选择询价单"
    v-model="dialogVisible"
    style="width: 60%"
    draggable
    class="avue-dialog avue-dialog--top"
  >
    <el-row :gutter="20">
      <el-col :span="24">
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          @keyup.enter="onLoad"
          @row-del="rowDel"
          @search-reset="onLoad"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          @row-click="rowClick"
          v-model="form"
        >
          <template #radio="{ row }">
            <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
          </template>
        </avue-crud>
      </el-col>
    </el-row>

    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,

  searchMenuSpan: 6,
  searchSpan: 10,
  menuWidth: 100,
  header: false,
  searchLabelWidth: 120,
  menu: true,
  border: true,
  column: [
    {
      label: '',
      prop: 'radio',
      width: 60,
      hide: false,
    },
    {
      label: '询价单名称',
      prop: 'businessName',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '关联品牌',
      prop: 'productBrand',
      width: 120,
      overHidden: true,
      search: true,
    },
    {
      label: '关联供应商',
      prop: 'supplierName',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '报价人',

      prop: 'offerPerson',
      width: 110,
      overHidden: true,
    },
    {
      label: '报价人联系电话',
      prop: 'offerPhone',

      width: 150,
      overHidden: true,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      format: 'YYYY-MM-DD',
      search: true,
      searchSpan: 10,
      component: 'wf-daterange-search',
      width: 150,
      searchRange: true,
      type: 'date',
    },
    {
      label: '备注',
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const tableUrl = '/api/vt-admin/businessInquiry/page';

let tableData = ref([{}]);

let { proxy } = getCurrentInstance();
let route = useRoute();
let params = ref({
  businessType: route.query.businessType,
});
let loading = ref(false);

// watch(() => {
//   params.value.businessType = props.businessType;
// });
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
function getTree(id) {
  axios.get('/api/vt-admin/templateCategory/tree?categoryCode=' + id).then(res => {
    treeData.value = res.data.data;
  });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
let selectRow = ref(null);
function rowClick(row) {
  selectRow.value = row.id;
}
function open() {
  onLoad();
  dialogVisible.value = true;
}
function handleConfirm() {
  const value = tableData.value.find(item => item.id === selectRow.value);
  if (!value) {
    proxy.$message.warning('请选择数据');
    return;
  }
  proxy.$emit('change', value, () => {
    dialogVisible.value = false;
    selectRow.value = null;
  });
}
let drawer = ref(false);
let detailForm = ref({});

defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
