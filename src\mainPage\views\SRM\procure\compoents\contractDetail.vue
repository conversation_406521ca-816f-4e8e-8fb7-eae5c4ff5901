<template>
  <basic-container style="height: 100%">
    <Title
      >合同详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="采购合同编号:">
            <el-tag effect='plain' size="large">{{ form.contractCode }}</el-tag>
          </el-form-item>
           <el-form-item label="合同总额:">
            <el-tag effect='plain' size="large">{{ form.contractPrice }}</el-tag>
          </el-form-item>
          <el-form-item label="供应商名称:">
            <el-tag effect='plain' size="large">{{ form.supplierName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="到货状态:">
            <el-tag effect='plain' size="large" v-if="form.arriveStatus == 0" type="danger">未到货</el-tag>
            <el-tag effect='plain' size="large" v-if="form.arriveStatus == 1" type="warning">部分到货</el-tag>
            <el-tag effect='plain' size="large" type="success" v-else-if="form.arriveStatus == 2">全部到货</el-tag>
          </el-form-item>
          <el-form-item label="付款状态:">
            <el-tag effect='plain' size="large" v-if="form.paymentStatus == 0" type="danger">未付款</el-tag>
            <el-tag effect='plain' size="large" v-if="form.paymentStatus == 2" type="warning">部分付款</el-tag>
            <el-tag effect='plain' size="large" type="success" v-else-if="form.paymentStatus == 1">已付款</el-tag>
          </el-form-item>
          <el-form-item label="发票状态:">
            <el-tag effect='plain' size="large" v-if="form.invoiceStatus == 0" type="success">已收票</el-tag>
            <el-tag effect='plain' size="large" v-else-if="form.invoiceStatus == 1" type="danger">未收票</el-tag>
            <el-tag effect='plain' size="large" v-else-if="form.invoiceStatus == 2" type="warning">部分收票</el-tag>
            <el-tag effect='plain' size="large" v-else type="warning">---</el-tag>
          
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- <div style="display: flex">
          <div class="left_content">
            <div class="main_box">
              <div
                class="item"
                v-for="(item, index) in tabArr"
                :class="{ active: currentIndex == index }"
                @click="handleClick(index)"
              >
                <div class="arrow"></div>
                {{ item }}
              </div>
            </div>
          </div>
          <div style="width: calc(100% - 100px)">
            <component
              :is="currentCompoent"
              :form="form"
              :isEdit="isEdit"
              @getDetail="getDetail"
            ></component>
          </div>
        </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <ContractBaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            @getDetail="getDetail"
          ></ContractBaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :is="tabArray.find(item => item.name == activeName).component"
      :productList="form.detailVOList"
      :contractCode="form.contractCode"
      :supplierName="form.supplierName"
      :id="form.id"
      :supplierId="form.supplierId"
      :contractId="form.id"
      :purchaseContractId="form.id"
      @success="getDetail"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import ContractBaseInfo from '../detail/contractBaseInfo.vue';
// 跟进
import Follow from '@/views/CRM/follow/allFollow.vue';
// 商品信息
import productInfo from '../detail/contractProduct.vue';
// 付款记录
import  goodsHistory from '@/views/Finance/payment/goodsHistory.vue'
// 收票
import ticket from '@/views/Finance/ticket/ticket.vue';
import axios from 'axios';
import returnProduct from '../detail/returnProduct.vue';

let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
onMounted(() => {
  getDetail();
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
const conpletArr = ['follow', 'product', 'contract', 'goodsHistory', 'ticket','returnProduct'];
const tabArray = [
  {
    label: '产品信息',
    name: 'product',
    component: productInfo,
  },
  {
    label: '付款记录',
    name: 'goodsHistory',
    component: goodsHistory,
  },
  // {
  //   label: '退款',
  //   name: 'return',
  //   component: productInfo,
  // },
  {
    label: '收票记录',
    name: 'ticket',
    component: ticket,
  },
  {
    label: '退货',
    name: 'returnProduct',
    component: returnProduct,
  },
];
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };
  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  return form;
}
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseContract/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      // form.value = formatData(res.data.data)
      form.value = res.data.data;
      // 获取供应商信息
      getSupplierInfo(form.value.supplierId);
    });
}
let moreInfoDetail = ref(false);
function loadMore() {
  moreInfoDetail.value = true;
}
let currentIndex = ref(0);
// let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}
let { proxy } = getCurrentInstance();
function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '商务',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistant,
        },
        {
          label: '技术',
          prop: 'technicalPersonnel',
          component: 'wf-user-select',
          value: form.value.technicalPersonnel,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          ...res.data,
          id: route.query.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
function getSupplierInfo(id) {
  axios.get(`/api/vt-admin/supplier/detail?id=${id}`).then(res => {
    form.value = {
      ...res.data.data,
      ...form.value,
      paymentMethodForSupplier: res.data.data.paymentMethod || '',
    };
  });
}
const store = useStore();
const tag = computed(() => store.getters.tag);
const activeName = ref('baseInfo');
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
