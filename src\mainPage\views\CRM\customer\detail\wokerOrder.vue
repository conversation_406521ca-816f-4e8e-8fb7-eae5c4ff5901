<template>
  <div>
    <wokerOrderListMy  v-if="props.info.type == 0 || props.info.type == 2"  :customerId="props.info.customerId"></wokerOrderListMy>
    <wokerOrderList v-else :customerId="props.customerId"></wokerOrderList>
  </div>
</template>

<script setup>
import wokerOrderList from '@/views/Contract/wokerOrder/allWokerOrderForOrder.vue';
import wokerOrderListMy from '@/views/Contract/wokerOrder/myMissonForWokerOrder.vue';
const props = defineProps({
 
  info: {
    type: Object,
    default: () => {
      return {};
    },
  },
  customerId: {
    type: String,
    default: '',
  },
});
</script>

<style lang="scss" scoped></style>
