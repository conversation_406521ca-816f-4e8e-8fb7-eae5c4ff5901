<template>
  <div>
    <el-drawer title="查看付款记录" v-model="drawer" direction="rtl" size="50%">
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:search="params"
       
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      </avue-crud>
    </el-drawer>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  menu: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '付款金额',
      prop: 'salary',
      width: 110,
    },

    {
      label: '总工时',
      prop: 'hours',
      width: 110,
    },
    {
      label: '开始时间',
      prop: 'startTime',
      width: 110,
    },
    {
      label: '结束时间',
      prop: 'endTime',
      width: 110,
    },
    {
      label: '状态',
      prop: 'payStatus',
      type: 'select',

      width: 110,
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核通过',
        },
        {
          value: 2,
          label: '已付款',
        },
        {
          value: 3,
          label: '审核失败',
        },
        {
          value: 4,
          label: '部分付款',
        },
       
      ],
    },
    {
      label: '备注',
      prop: 'workContent',
      overHidden: true,
    },
   
    {
      label: '申请人',
      prop: 'createUserName',
      width: 110,
    },
    {
      label: '申请时间',
      prop: 'createTime',
      width: 110,
      overHidden: true,
      valueFormat: 'YYYY-MM-DD',
      component: 'wf-daterange-search',
      //   search: true,
      searchSpan: 6,
      valueFormat: 'YYYY-MM-DD',
    },
  ],
});
let form = ref({});

const props = defineProps({
  humanName: {
    type: String,
    required: true,
  },
  projectName: {
    type: String,
    required: true,
  },
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectHumanSalary/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
watch(() => props,() => {
  onLoad();
},{
  deep: true,
  immediate: true
})
function onLoad() {
  loading.value = true;

  axios
    .get(tableUrl, {
      params: {
        size: 100000,
        current: 1,
        ...params.value,
        humanName: props.humanName,
       
        projectName: props.projectName,
        selectType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
     
    });
}
let router = useRouter();

let drawer = ref(false);
function open() {
  drawer.value = true;
  
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
