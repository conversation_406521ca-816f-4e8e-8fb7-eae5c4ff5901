<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #sealContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
      </template>
      <template #menu="{ row }">
        <!-- <el-button
          type="primary"
          text
          icon="tools"
          @click="distribution(row)"
          v-if="$store.getters.permission['mission:distribution']"
          >分配</el-button
        > -->
        <!-- <el-button type="primary" text icon="Aim" @click="sendInfo(row)">配送信息</el-button> -->
        <el-button
          type="primary"
          text
          icon="CircleCheck"
          @click="complete(row)"
          v-if="row.objectStatus != 2 && $store.getters.permission['mission:complete']"
          >完成</el-button
        >
        <!-- <el-button
          type="primary"
          text
          icon="CirclePlus"
          @click="addProduct(row)"
          v-if="$store.getters.permission['mission:addProduct']"
          >关联产品</el-button
        > -->
        <el-button type="primary" text icon="Promotion" @click="delay(row)">延期</el-button>
        <el-button
          type="primary"
          text
          icon="Printer"
          @click="printOrder(row)"
          v-if="
            row.sealContractId &&
            (row.objectName.indexOf('0') > -1 || row.objectName.indexOf('1') > -1)
          "
          >送货单</el-button
        >
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{
          row.$objectName || row.objectName
        }}</el-link>
      </template>
      <template #isComplete="{ row }">
        <div>配送：<span style="color: var(--el-color-warning)">未完成</span></div>
        <div>
          技术服务：<span v-if="row.isVisitingService == 1" style="color: var(--el-color-warning)"
            >未完成</span
          >
          <span v-if="row.isVisitingService == 1" style="color: var(--el-color-info)"
            >无需服务</span
          >
        </div>
      </template>
      <template #distributionUser="{ row }">
        <el-button @click="viewDetail(row, 2)">查看</el-button>
      </template>
      <template #productNumber="{ row }">
        <el-button @click="viewDetail(row, 3)">查看</el-button>
      </template>
      <template #delay="{ row }">
        <el-button @click="viewDetail(row, 5)">查看</el-button>
      </template>
      <template #files="{ row }">
        <File :fileList="row.fileList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      ref="productSelectRef"
      @confirm="handleConfirm"
      :id="sealContractId"
      url="/api/vt-admin/sealContract/productPageForObject"
    ></productSelect>
    <detail ref="missionDetail"></detail>
    <el-drawer :with-header="false" size="90%" v-model="printDrawer">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="10">
          <el-card style="height: 100%">
            <avue-form :option="printOption" v-model="form">
              <template #productList>
                <avue-crud
                  :option="productOption"
                  @selection-change="selectionChange"
                  :data="productList"
                  ref="productRef"
                >
                  <template #number="{ row }">
                    <el-input
                      size="small"
                      style="width: 90%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input>
                  </template>
                </avue-crud>
              </template>
            </avue-form>
          </el-card>
        </el-col>
        <el-col :span="14">
          <el-card style="padding: 0; overflow-y: scroll; height: 100%">
            <div id="printBox">
              <div
                class="header"
                style="display: flex; align-items: center; border: 1px dashed #ccc"
              >
                <div style="width: 150px; margin-right: 30px">
                  <img
                    style="width: 150px"
                    :src="form.companyInfo?.exportLogoUrl"
                    alt="暂无logo"
                  />
                </div>
                <div class="title">
                  <div class="address">
                    {{ form.companyInfo?.address }}
                  </div>
                  <div class="phone">
                    电话：{{ form.companyInfo?.contactPhone }}
                  </div>
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <p class="name" style="font-size: 22px; margin-right: 20px; font-weight: bold">
                      {{ form.companyInfo?.companyName }}
                    </p>
                    <p class="name">送货单号：{{ form.deliveryNumber }}</p>
                  </div>
                </div>
              </div>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table border style="width: 100%">
                <colgroup>
                  <col style="width: 20%" />

                  <col style="width: 30%" />

                  <col style="width: 20%" />

                  <col style="width: 30%" />
                </colgroup>
                <tr>
                  <td style="width: 100px">客户名称：</td>
                  <td>{{ form.customerName }}</td>
                  <!-- <td></td> -->
                  <td style="width: 100px">送货日期：</td>
                  <td>{{ form.deliveryDate }}</td>
                </tr>
                <tr>
                  <td style="width: 100px">联系人：</td>
                  <td>{{ form.contact }}</td>
                  <td style="width: 100px">送货地址：</td>
                  <td>{{ form.distributionAddress }}</td>
                </tr>
                <tr>
                  <td style="width: 100px">手机：</td>
                  <td>{{ form.contactPhone }}</td>
                  <td style="width: 100px">签收日期：</td>
                  <td></td>
                </tr>
                <tr>
                  <td style="width: 100px">邮箱：</td>
                  <td></td>
                  <td style="width: 100px">客户订单号：</td>
                  <td>{{ form.customerOrderNumber }}</td>
                </tr>
              </table>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table center style="width: 100%" border>
                <thead>
                  <tr>
                    <th>商品编码</th>
                    <th>商品名称</th>
                    <th>品牌</th>
                    <th>型号规格</th>
                    <th>单位</th>
                    <th>数量</th>
                    <th v-if="form.type == 1">单价</th>
                    <th v-if="form.type == 1">金额</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in selectList">
                    <td>{{ item.product?.productCode }}</td>
                    <td>{{ item.customProductName }}</td>
                    <td>{{ item.productBrand }}</td>
                    <td>{{ item.customProductSpecification }}</td>
                    <td>{{ item.customUnit || item.product.unitName }}</td>
                    <td>{{ item.number }}</td>
                    <td v-if="form.type == 1">{{ item.sealPrice }}</td>
                    <td v-if="form.type == 1">{{ item.number * 1 * item.sealPrice }}</td>
                    <td style="max-width: 150px" contenteditable="true"></td>
                  </tr>
                  <tr v-if="form.type == 1">
                    <td>合计金额：（RMB）</td>
                    <td style="text-align: left" colspan="5">（大写）{{ totalPriceText() }}</td>
                    <td style="text-align: left">（小写）</td>
                    <td>{{ totalPrice().toFixed(2) }}</td>
                  </tr>
                </tbody>
              </table>
              <h4>备注：</h4>
              <p>
                {{ form.remark }}
              </p>
              <el-row :gutter="20">
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>送（发）货经手人签字：</span>
                </el-col>
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>客户签收：</span>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <el-button @click="printDrawer = false">取消</el-button>
        <!-- <el-button @click="exportDocx" type="primary">导出为word</el-button> -->
        <el-button type="primary" v-print="print">确定 打印</el-button>
        <el-button type="primary" @click="exportExcel">导出 excel</el-button>
      </template>
    </el-drawer>
    <el-dialog
      title="完成工单"
      :width="completeForm.isOutStorage == 1 ? '80%' : ''"
      class="avue-dialog avue-dialog--top"
      @close="handleClose"
      v-model="completeDialogVisible"
    >
      <el-row :gutter="20">
        <el-col :span="completeForm.isOutStorage == 1 ? 12 : 24">
          <el-card shadow="never">
            <avue-form
              :option="completeOption"
              ref="completeFormRef"
              @submit="completeSubmit"
              v-model="completeForm"
            ></avue-form>
          </el-card>
        </el-col>
        <el-col :span="12" v-if="completeForm.isOutStorage == 1">
          <el-card shadow="never">
            <template #header> 出库产品 </template>
            <missionProductList :objectId="currentRow.id"></missionProductList>
          </el-card>
        </el-col>
      </el-row>
      <div class="avue-dialog__footer">
        <el-button @click="completeDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="
            () => {
              $refs.completeFormRef.submit();
            }
          "
          >确定</el-button
        >
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import template from './../../template.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, h } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '../customer/compoents/productSelect.vue';
import detail from '@/views/Order/salesOrder/compoents/missionDetail.vue';
import missionProductList from '@/views/Order/salesOrder/compoents/missionProductList.vue';
import { objectType } from '@/const/const';
import { DX } from '@/utils/util';
import { dateFormat } from '@/utils/date';
import { downloadXls } from '@/utils/download';
import { getList } from '@/api/system/param';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 220,
  border: true,
  column: [
    {
      label: '订单名称',
      // hide: true,
      search: true,
      overHidden: true,
      editDisplay: false,
      addDisplay: false,
      prop: 'contractName',
    },
    {
      type: 'checkbox',
      dicData: objectType,
      label: '工单名称',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'objectName',
      search: true,
    },
    {
      type: 'input',
      label: '工单描述',
      span: 24,
      display: true,
      prop: 'durationNode',
      overHidden: true,
    },
    {
      type: 'datetime',
      label: '完成时间要求',
      span: 12,
      display: true,
      span: 24,
      width: 140,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'planTime',
    },

    {
      label: '关联产品',
      prop: 'productNumber',
      addDisplay: false,
      editDisplay: false,
    },
    {
      type: 'select',
      label: '是否完成',
      span: 6,
      search: true,
      editDisplay: false,
      addDisplay: false,
      value: '0',
      dicData: [
        {
          label: '未开始',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        },
        {
          label: '已完成',
          value: 2,
        },
      ],
      prop: 'objectStatus',
    },
    {
      type: 'date',
      label: '完成时间',
      span: 12,
      editDisplay: false,
      addDisplay: false,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD',
      prop: 'completeTime',
    },
    // {
    //   type: 'textarea',
    //   label: '延期说明',
    //   span: 24,
    //   editDisplay: false,
    //   addDisplay: false,
    //   prop: 'delay',
    // },

    {
      label: '处理人',
      span: 12,
      component: 'wf-user-select',
      display: true,

      span: 24,
      overHidden: true,
      prop: 'handleUserName',
    },

    // {
    //   type: 'select',
    //   label: '状态',
    //   span: 24,

    //   editDisplay: false,
    //   addDisplay: false,
    //   prop: 'status',
    // },
    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'remark',
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      span: 12,
      cell: false,
      editDisplay: false,
      addDisplay: false,
      dicData: [
        {
          value: 0,
          label: '未到货',
        },
        {
          value: 1,
          label: '部分到货',
        },
        {
          value: 2,
          label: '已到货',
        },
        {
          value: 3,
          label: '未关联',
        },
      ],
    },
    {
      label: '派单人',
      prop: 'createName',
      editDisplay: false,
      addDisplay: false,
    },
    {
      type: 'datetime',
      label: '派单时间',
      editDisplay: false,
      addDisplay: false,
      span: 12,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'createTime',
    },
  ],
});

let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObject/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObject/page';
let params = ref({
  objectStatus: 0,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
  // getCompanyAddressAndPhone()
});

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function distribution(row) {
  proxy.$refs.dialogForm.show({
    title: '分配',
    option: {
      column: [
        {
          label: '技术人员',
          type: 'input',
          value: row.contractTechnologyUser,
          component: 'wf-user-select',
          params: {
            checkType: 'checkBox',
          },
          prop: 'technologyUser',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractObject/distributionTechnology', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function sendInfo(row) {
  proxy.$refs.dialogForm.show({
    title: '配送信息',
    option: {
      column: [
        {
          label: '配送方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 24,
          value: row.distributionMethod || 0,
          dicData: [
            {
              value: 0,
              label: '送货上门',
            },
            {
              value: 1,
              label: '快递',
            },
            {
              value: 2,
              label: '自提',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择配送方式',
              trigger: 'change',
            },
          ],
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 0 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              distributionDate: {
                label: val == 0 ? '送货日期' : val == 1 ? '发货日期' : '自提日期',
              },
              distributionAddress: {
                label: val == 0 ? '送货地址' : val == 1 ? '发货地址' : '',
                display: val !== 2 ? true : false,
              },
            };
          },
        },
        {
          label: '送货人',
          component: 'wf-user-select',
          prop: 'distributionUser',
          value: row.distributionUser,
          span: 12,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请选择送货人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货日期',
          prop: 'distributionDate',
          span: 12,
          type: 'datetime',
          value: row.distributionDate,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [
            {
              required: true,
              message: '请选择送货日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货地址',
          prop: 'distributionAddress',
          span: 24,
          value: row.distributionAddress,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入送货地址',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系人',
          prop: 'contact',
          span: 12,
          type: 'input',
          value: row.contact,
          rules: [
            {
              required: true,
              message: '请输入联系人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          span: 12,
          value: row.contactPhone,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'change',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractObject/distributionDistributionUser', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let completeDialogVisible = ref(false);
let completeForm = ref({});
let coverId = '';
let currentRow = ref({});
let completeOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'checkbox',
      dicData: objectType,
      label: '工单名称',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'objectName',
      disabled: true,
      search: true,
      control: val => {
        return {
          deliveryChannel: {
            display: val.indexOf('0') > -1,
          },
          courierCompany: {
            display: val.indexOf('0') > -1,
          },
          courierNumber: {
            display: val.indexOf('0') > -1,
          },
          useCarType: {
            display: val.indexOf('1') > -1,
          },
          feeRegistration: {
            display: val.indexOf('1') > -1,
          },

          serviceTime: {
            display:
              val.indexOf('2') > -1 ||
              val.indexOf('3') > -1 ||
              val.indexOf('4') > -1 ||
              val.indexOf('5') > -1,
          },
          serviceReorder: {
            display: val.indexOf('2') > -1 || val.indexOf('3') > -1,
          },
          questionFeedback: {
            display: val.indexOf('4') > -1,
          },
          pickAddress: {
            display: val.indexOf('5') > -1,
          },
          distributionAddress: {
            display: val.indexOf('5') > -1,
          },
          isOutStorage: {
            display: val.indexOf('0') > -1 || val.indexOf('1') > -1,
          },
        };
      },
    },
    {
      label: '发货渠道',
      prop: 'deliveryChannel',
      type: 'radio',
      dicData: [
        { label: '快递', value: 0 },
        { label: '物流', value: 1 },
      ],
      span: 12,
      display: false,
      rules: [
        {
          required: true,
          message: '请选择发货渠道',
          trigger: 'change',
        },
      ],
      control: val => {
        return {
          logisticsCompany: {
            display: val == 1 && completeForm.value.objectName.indexOf('0') > -1,
          },
          courierCompany: {
            display: val == 0 && completeForm.value.objectName.indexOf('0') > -1,
          },

          courierNumber: {
            label: val == 0 ? '快递单号' : '物流单号',
            display: (val == 0 || val == 1) && completeForm.value.objectName.indexOf('0') > -1,
          },
        };
      },
    },

    {
      type: 'select',
      label: '快递公司',
      dicUrl: '/blade-system/dict-biz/dictionary?code=courierCompany',
      cascader: [],
      span: 12,
      // search: true,
      display: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'courierCompany',
    },
    {
      type: 'select',
      label: '物流公司',
      dicUrl: '/blade-system/dict/dictionary?code=logisticsCompany',
      cascader: [],
      span: 12,
      // search: true,
      display: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'logisticsCompany',
    },
    {
      label: '快递单号',
      prop: 'courierNumber',
      span: 24,
      display: false,
    },
    {
      label: '用车类型',
      prop: 'useCarType',
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=carType',
      cascader: [],
      display: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      span: 12,
      display: false,
    },
    {
      label: '费用登记',
      prop: 'feeRegistration',
      type: 'switch',
      dicData: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
      control: val => {
        return {
          costList: {
            display: val == 1 && completeForm.value.objectName.indexOf('1') > -1,
          },
        };
      },
      span: 12,
      display: false,
    },
    {
      label: '费用列表',
      prop: 'costList',
      value: [{}],
      type: 'dynamic',
      span: 24,
      children: {
        align: 'center',
        index: false,
        border: true,
        type: 'form',

        rowAdd: done => {
          if (completeForm.value.costList.length >= 1)
            return proxy.$message.warning('最多添加一条数据');
          done();
        },

        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            type: 'tree',
            label: '费用类型',
            dicUrl: '/blade-system/dict-biz/dictionary-tree?code=expenseType',
            cascader: [],
            span: 12,
            width: 110,
            // search: true,
            display: true,
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            parent: false,
            prop: 'expenseType',
          },
          // {
          //   type: 'number',
          //   label: '票据张数',
          //   span: 12,
          //   display: true,
          //   hide: true,
          //   prop: 'a170080951774565537',
          // },
          {
            type: 'number',
            label: '费用金额',
            span: 12,
            display: true,
            prop: 'expensePrice',
          },

          {
            type: 'textarea',
            label: '用途',
            span: 24,
            display: true,
            prop: 'purpose',
            showWordLimit: true,
          },

          {
            type: 'textarea',
            label: '备注',
            span: 24,
            display: true,
            prop: 'remark',
            showWordLimit: true,
          },
          {
            type: 'input',
            label: '登记人',
            span: 12,
            display: true,
            component: 'wf-user-select',
            prop: 'reimbursementUser',
            value: proxy.$store.getters.userInfo.user_id,
            showWordLimit: true,
            formatter: (row, column, cellValue) => {
              return row.reimbursementUserName;
            },
          },
          {
            type: 'date',
            label: '日期',
            span: 12,
            display: true,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            prop: 'expenseDate',
            disabled: false,
            readonly: false,
            required: true,
            value: dateFormat(new Date(), 'yyyy-MM-dd'),
            rules: [
              {
                required: true,
                message: '计划收款时间必须填写',
              },
            ],
          },
        ],
      },
    },
    {
      label: '完成日期',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'completeTime',
      span: 24,
      display: true,
      change: val => {
        console.log(val);
        const { value } = val;
        completeForm.value.serviceStartTime = value;
        completeForm.value.serviceEndTime = value;
      },
    },
    {
      label: '所用时间',
      prop: 'serviceTime',
      component: 'wf-daterange-search',
      timeType: 'datetime',
      display: false,
      span: 24,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      rules: [
        {
          required: true,
          message: '请选择时间',
          trigger: 'change',
        },
      ],
    },
    {
      label: '服务人员',
      prop: 'serviceUser',
      component: 'wf-user-select',
      display: false,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择服务人员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '服务复盘',
      prop: 'serviceReorder',
      type: 'textarea',
      display: false,
      span: 24,
      value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
    },
    {
      label: '问题反馈',
      prop: 'questionFeedback',
      type: 'textarea',
      display: false,
      span: 24,
    },
    {
      label: '拿货地址',
      prop: 'pickAddress',
      display: false,
      span: 24,
    },
    {
      label: '货拿目的地',
      prop: 'distributionAddress',
      display: false,
      span: 24,
    },
    {
      label: '附件',
      prop: 'completeFiles',
      type: 'upload',
      dataType: 'object',
      // listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      drag: true,
      limit: 1,
      multiple: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        coverId = res.id;
        done();
      },
      uploadExceed: () => {
        proxy.$message.error('只能上传一个附件');
      },
    },
    {
      label: '同步出库',
      prop: 'isOutStorage',
      type: 'radio',
      display: false,
      dicData: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择是否同步出库',
          trigger: 'change',
        },
      ],
    },
    {
      label: '备注',
      prop: 'completeRemark',
      type: 'textarea',
      span: 24,
    },
  ],
});
function completeSubmit(form, done) {
  console.log(currentRow.value);
  if (currentRow.value.arriveStatus != 2 && form.isOutStorage == 1) {
    proxy.$message.error('只有全部到货状态才能同步出库');
    done();
    return;
  }
  const data = {
    id: currentRow.value.id,
    ...form,
    completeFiles: coverId,
    serviceStartTime: form.serviceTime && form.serviceTime[0],
    serviceEndTime: form.serviceTime && form.serviceTime[1],
    serviceTime: null,
    courierCompany: form.deliveryChannel == 0 ? form.courierCompany : form.logisticsCompany,
  };
  axios.post('/api/vt-admin/sealContractObject/complete', data).then(r => {
    proxy.$message.success(r.data.msg);
    proxy.$store.dispatch('getMessageList');
    const data = {
      ...form.costList[0],
      sealContractId: currentRow.value.sealContractId.split(',')[0],
    };
    if (form.feeRegistration == 1) {
      axios
        .post('/api/vt-admin/sealContractExpense/save', data)
        .then(res => {
          if (res.data.code == 200) {
            proxy.$message.success('添加费用成功');
          }
        })
        .catch(err => {
          done();
        });
    } else {
      done();
      onLoad();
      completeDialogVisible.value = false;
    }
  });
}
function complete(row) {
  completeDialogVisible.value = true;
  currentRow.value = row;
  completeForm.value.objectName = row.objectName;
  completeForm.value.distributionAddress = row.distributionAddress;
  const feeRegistrationType = proxy.findObject(completeOption.value.column, 'feeRegistration');
  const costList = proxy.findObject(completeOption.value.column, 'costList');
  console.log(feeRegistrationType);
  if (!row.sealContractId) {
    feeRegistrationType.disabled = true;
    costList.display = false;
  } else {
    feeRegistrationType.disabled = false;
  }
}
function handleClose() {
  proxy.$refs.completeFormRef.resetFields();
}

function delay(row) {
  proxy.$refs.dialogForm.show({
    title: '延期',
    option: {
      column: [
        {
          label: '延期时间',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'delayDate',
          rules: [
            {
              required: true,
              message: '请选择延期时间',
            },
          ],
          span: 24,
        },
        {
          label: '延期理由',
          type: 'textarea',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入延期理由',
            },
          ],
          prop: 'delayReason',
        },
      ],
    },
    callback(res) {
      const data = {
        objectId: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/vt-admin/sealContractObjectDelay/save', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}

function viewDetail(row, active = 1) {
  console.log(proxy.$refs.missionDetail);
  proxy.$refs.missionDetail.viewDetail(row, active);
}
let currentObjectId = ref(null);
function addProduct(row) {
  currentObjectId.value = row.id;
  proxy.$refs.productSelectRef.open();
  proxy.$refs.productSelectRef.reset();
}
function handleConfirm(list) {
  console.log(list);
  const data = {
    id: currentObjectId.value,
    productDTOList: list.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
      };
    }),
  };
  axios.post('/api/vt-admin/sealContractObject/relationProduct', data).then(res => {
    proxy.$message.success(res.data.msg);
  });
}
function toDetail(row) {
  router.push({
    path: '/Order/salesOrder/compoents/detail',
    query: {
      id: row.sealContractId,
    },
  });
}

let printDrawer = ref(false);
let companyList = ref([]);
let printOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: {
    deliverCompany: {
      label: '发货公司',
      type: 'select',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        companyList.value  = res.data.records;
        form.value.deliverCompany = companyList.value[0].id;
        return res.data.records;
      },
      change: val => {
        const data = companyList.value.find(item => item.id === val.value);
        form.value.companyInfo = data
      },
      overHidden: true,
      cell: false,
      span:24,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    deliveryDate: {
      span: 24,
      label: '送货时间',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    type: {
      label: '类型',
      type: 'radio',
      span: 24,
      value: 1,
      dicData: [
        {
          value: 1,
          label: '有价格',
        },
        {
          value: 0,
          label: '无价格',
        },
      ],
    },
    productList: {
      label: '产品列表',
      type: 'input',
      span: 24,
    },
    remark: {
      span: 24,
      label: '备注',
      type: 'textarea',
      overHidden: true,
    },
  },
});
let productOption = {
  menu: false,
  selection: true,
  tip: false,
  header: false,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },

    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
};
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}
const print = ref({
  id: 'printBox',
});
function printOrder(row) {
  axios
    .get('/api/vt-admin/sealContractObject/print', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      printDrawer.value = true;
      getDetail(row.id);
      form.value = {
        ...row,
        deliveryNumber: res.data.data.deliveryNumber,
      };
    });
}
let productList = ref([]);
function getDetail(id) {
  axios
    .get('/api/vt-admin/sealContractObjectProduct/printList', {
      params: {
        objectId: id,
      },
    })
    .then(res => {
      productList.value = [...res.data.data];
      proxy.$refs.productRef.$refs.table.toggleAllSelection();
    });
}
function total() {
  return selectList.value.reduce((a, b) => {
    a += b.zhhsze * 1;
    return a;
  }, 0);
}
function totalPriceText() {
  return DX(total());
}
function totalPrice() {
  return total();
}
function exportExcel() {
  const {
    deliverCompany: companyName,
    type: isHasPrice,
    deliveryDate,
    remark,
    id: objectId,
  } = form.value;
  const data = {
    companyName,
    isHasPrice,
    deliveryDate,
    remark,
    objectId,
    detailDTOList: selectList.value,
  };
  axios({
    url: '/api/vt-admin/sealContract/printDeliveryNote',
    data,
    method: 'post',
  }).then(res => {
    window.open(res.data.data);
  });
}
function getCompanyAddressAndPhone(params) {
  getList(1, 10, {
    paramKey: 'companyAddress',
  });
}
</script>

<style lang="scss" scoped>
table td {
  text-align: center;
}
</style>
