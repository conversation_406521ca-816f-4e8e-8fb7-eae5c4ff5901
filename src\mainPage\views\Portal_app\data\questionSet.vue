<template>
  <basic-container>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in questionTypeData"
        :label="item.dictValue"
        :name="item.id"
      ></el-tab-pane>
    </el-tabs>
   
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
    
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let questionTypeData = ref([]);
let activeName = ref('')
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '问题标题',
      prop: 'title',
      width: 250,
      overHidden: true,
      //   search: true,
    },
    {
      type: 'select',
      label: '问题分类',
      span: 12,
      display:false,
      rules: [
        {
          required: true,
          message: '请选择类型',
        },
      ],

      
      prop: 'type',
      dicUrl: '/blade-system/dict-biz/dictionary?code=questionType',
      dicFormatter: res => {
        questionTypeData.value = res.data;
        activeName.value = res.data[0]?.id
        onLoad()
        return res.data;
      },
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '搜索量',
      prop: 'searchNumber',
      display: false,
    },
    {
      label: '排序',
      prop: 'sort',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/question/save';
const delUrl = '/api/vt-admin/question/remove?ids=';
const updateUrl = '/api/vt-admin/question/update';
const tableUrl = '/api/vt-admin/question/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        type: activeName.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    type: activeName.value,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done,type) {
    if(type == 'add'){
        form.value.sort = page.value.total + 1
    }
    done()
}
function handleTabChange() {
    page.value.currentPage = 1
    onLoad()
}
</script>

<style lang="scss" scoped></style>
