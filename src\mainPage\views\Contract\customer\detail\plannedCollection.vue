<template>

  <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
    @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud" :before-open="beforeOpen"
    @keyup.enter="onLoad" @row-del="rowDel" @search-reset="onLoad" @search-change="searchChange"
    @current-change="onLoad" @refresh-change="onLoad" @size-change="onLoad" v-model="form">
    <template #invoiceId-form>
      <wfCustomerInvoiceDrop v-model="form.invoiceId" :id="props.sealContractId"></wfCustomerInvoiceDrop>
    </template>
    <template #planCollectionPrice-form>
      <div style="display: flex; align-items: center">
        <el-input-number style="width: calc(100% - 390px)" v-model="form.planCollectionPrice"
          controls-position="right"></el-input-number>
        <el-popover class="box-item" popper-style="width:auto" placement="right">
          <template #default>
            <div style="display: flex; align-items: center" v-for="item in visaList">
              <el-checkbox style="margin-right: 5px" @change="setTotalPrice" v-model="item.isCheck"></el-checkbox>
              {{ item.title }}: <el-text type="primary">{{ item.totalPrice }}</el-text>
              <!-- <el-button @click="form.planCollectionPrice = item.totalPrice" text type="primary"
                >填入</el-button
              > -->
            </div>
          </template>
          <template #reference>
            <el-tag effect="plain" size="large" style="width: 150px; margin: 0 20px; cursor: pointer"
              type="primary">合同额：{{ props.contractTotalPrice }}</el-tag>
          </template>
        </el-popover>

        <el-input v-model="form.rate" placeholder="填写比例以计算" style="width: 200px" @change="
          value => {
            form.planCollectionPrice = (
              props.contractTotalPrice * (form.rate / 100) || 0
            ).toFixed(2);
          }
        ">
          <template #append>%</template>
        </el-input>
      </div>
    </template>
    <template #menu="{ row, $index }">
      <el-button text type="primary" icon="pointer" @click="collection(row)" v-if="
        (row.collectionStatus == 0 || row.collectionStatus == 1) &&
        row.invoiceStatus != 3 && props.form.contractType == 5 && props.form.cooperationType == 0
      ">收款</el-button>
      <el-button text type="primary" icon="edit" v-if="row.collectionStatus == 0 && !row.invoiceId"
        @click="$refs.crud.rowEdit(row, $index)">编辑</el-button>
      <el-button text type="primary" icon="pointer" @click="collectionCord(row)">收款记录</el-button>
      <el-button text type="primary" icon="delete" v-if="row.collectionStatus == 0"
        @click="$refs.crud.rowDel(row, $index)">删除</el-button>
      <el-button text type="primary" icon="Stopwatch" v-if="row.collectionStatus == 0 && row.isStop == 0"
        @click="stop(row)">中止</el-button>
    </template>
    <template #collectionStatus="{ row }">
      <el-tag effect="plain" size="small" type="success" v-if="row.collectionStatus == 2">已收款</el-tag>
      <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 1">部分收款</el-tag>
      <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 0">未收款</el-tag>
      <el-tag type="danger" v-if="row.isStop == 1" style="margin-left:5px" effect="dark" size="small">中止</el-tag>
    </template>
    <template #invoiceStatus="{ row }">
      <el-tag effect="plain" :type="row.invoiceStatus == 3
          ? 'danger'
          : row.invoiceStatus == 0
            ? 'primary'
            : row.invoiceStatus == null
              ? 'info'
              : 'success'
        ">{{ row.$invoiceStatus }}</el-tag>
    </template>
    <template #planCollectionDate="{ row }">
      <div style="display: flex; align-items: center; gap: 5px">
        <span>{{ row.planCollectionDate }}</span>
        <el-tooltip v-if="row.remindTriggeringCondition && row.remindTriggeringCondition != 0"
          :content="getHitRuleText(row)" placement="top">
          <el-icon style="color: var(--el-color-primary); cursor: pointer">
            <InfoFilled />
          </el-icon>
        </el-tooltip>
      </div>
    </template>
    <template #planName-form>
      <div style="display: flex; gap: 20px; flex-direction: column">
        <el-radio-group v-model="form.planName">
          <el-radio :label="item.label" v-for="item in planNameData" :key="item.value"></el-radio>
        </el-radio-group>

        <el-input v-if="form.planName == '其它'" v-model="form.planNameOther" style="width: 50%"
          placeholder="请输入计划名称"></el-input>
      </div>
    </template>
    <template #planCollectionDate-form>
      <div style="display: flex; align-items: center; gap: 10px">
        <el-select style="width: 250px" v-model="form.remindTriggeringCondition" placeholder="">
          <el-option v-for="item in remindOptions" :key="item.value" :label="item.label" :disabled="item.disabled"
            :value="item.value">
          </el-option>
        </el-select>
        <el-select v-if="form.remindTriggeringCondition == 1" style="width: 250px" v-model="form.conditionStageId"
          placeholder="请选择项目计划">
          <el-option v-for="item in projectPlanList" :key="item.id" :label="item.stage" :value="item.id">
          </el-option>
        </el-select>
        <el-input v-if="form.remindTriggeringCondition == 2" style="width: 150px" v-model="form.conditionPercentage"
          placeholder="请输入百分比">
          <template #append>%</template>
        </el-input>
        <div v-if="form.remindTriggeringCondition != 0">
          <el-input style="width: 100px" v-model="form.conditionValue" placeholder="请输入天数"></el-input>天后
        </div>

        <el-date-picker v-model="form.planCollectionDate" type="date" v-if="form.remindTriggeringCondition == 0"
          value-format="YYYY-MM-DD" placeholder="选择计划收款日期" />
      </div>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-dialog title="收款记录" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <!-- <slot ></slot> -->
    <collectionList :planCollectionId="currentId"></collectionList>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import wfCustomerInvoiceDrop from '../compoents/wf-customerInvoice-drop.vue';
import { dateFormat } from '@/utils/date';
import collectionList from '../compoents/collectionList.vue';
import { planNameData } from '@/const/const';
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
  contractTotalPrice: String,
  projectId: String,
  form: Object,
});
let route = useRoute();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 270,
  border: true,
  column: [
    // {
    //   type: 'input',
    //   label: '客户名称',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080949446133484',
    // },
    // {
    //   type: 'input',
    //   label: '项目名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      label: '计划名称',
      prop: 'planName',
      type: 'radio',
      dicData: planNameData,
      span: 24,
      change: val => {
        form.value.rate = '';
        if (val.value == '全款') {
          console.log(val, props.contractTotalPrice);
          form.value.planCollectionPrice = props.contractTotalPrice;
        } else {
          form.value.planCollectionPrice = '';
        }
      },
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    // {
    //   type: 'input',
    //   label: '关联发票',
    //   span: 24,
    //   hide: true,
    //   display: true,
    //   prop: 'invoiceId',
    // },
    // {
    //   type: 'input',
    //   label: '关联标的',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080947476223862',
    // },

    // {
    //   type: 'select',
    //   label: '关联标的',
    //   span: 12,
    //   display: true,
    //   prop: 'contractObjectId',
    //   required: true,
    //   dicUrl:
    //     '/api/vt-admin/sealContractObject/page?sealContractId=' + route.query.id + '&size=5000',
    //   width: 300,
    //   dicFormatter: res => {
    //     console.log(res);
    //     return res.data.records;
    //   },
    //   props: {
    //     value: 'id',
    //     label: 'objectName',
    //   },
    // },
    {
      type: 'date',
      label: '计划收款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      overHidden: true,
      rules: [
        {
          required: true,
          trigger: 'blur',
          validator: (rule, value, callback) => {
            switch (form.value.remindTriggeringCondition) {
              case 0:
                if (!form.value.planCollectionDate) {
                  callback(new Error('请选择计划收款时间'));
                } else {
                  callback()
                }
                break;
              case 1:
                if (!form.value.conditionStageId) {
                  callback(new Error('请选择项目计划'));
                } else {
                  callback()
                }
                break;
              case 2:
                if (!form.value.conditionPercentage) {
                  callback(new Error('请输入采购百分比'));
                } else {
                  callback()
                }
                break;
              default:
                callback();
                break;
            }
          },
        },
      ],
      span: 24,
      disabled: false,
      readonly: false,
      required: true,
    },

    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 18,
      display: true,
      rules: [
        {
          required: true,
          message: '计划收款金额必须填写',
        },
      ],
      prop: 'planCollectionPrice',
    },
    {
      type: 'number',
      label: '计划收款比例',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'collectionRate',
      formatter: row => {
        return row.collectionRate + '%';
      },
    },
    {
      type: 'number',
      label: '实际收款金额',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollection',
    },
    // {
    //   type: 'number',
    //   label: '实际回款比例',
    //   controls: true,
    //   span: 24,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'actualCollectionRate',
    //   formatter: row => {
    //     return row.actualCollectionRate + '%';
    //   },
    // },
    {
      type: 'input',
      label: '逾期',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'overDays',
      html: true,
      formatter: row => {
        if (row.overDays) {
          return `<span style="color:var(--el-color-danger)">${row.overDays}天</span>`;
        } else {
          return `---`;
        }
      },
    },
    {
      label: '收款状态',
      prop: 'collectionStatus',
      addDisplay: false,
      editDisplay: false,
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      width: 100,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '无需开票',
          value: 10,
        },
        {
          label: '未关联',
          value: null,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractPlanCollection/save';
const delUrl = '/api/vt-admin/sealContractPlanCollection/remove?ids=';
const updateUrl = '/api/vt-admin/sealContractPlanCollection/update';
const tableUrl = '/api/vt-admin/sealContractPlanCollection/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
        selectType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
const emits = defineEmits(['success']);
function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    planName: form.planName == '其它' ? form.planNameOther : form.planName,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');
        onLoad();
        done();
        emits('success')
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}

function searchChange(params, done) {
  onLoad();
  done();
}
function collection(row) {
  let coverId = '';
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          display: true,
          value: row.planCollectionPrice * 1 - row.actualCollection * 1,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDate',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        // {
        //   type: 'select',
        //   label: '收款账号',
        //   cascader: [],
        //   span: 12,
        //   // search: true,
        //   display: true,
        //   dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
        //   dicFormatter: res => {
        //     return res.data.records;
        //   },
        //   props: {
        //     label: 'abbreviation',
        //     value: 'id',
        //     desc: 'desc',
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择收款账号',
        //       trigger: 'blur',
        //     },
        //   ],
        //   prop: 'collectionAccount',
        // },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
        {
          label: '收款凭证',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          // listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          // uploadAfter: (res, done) => {
          //   coverId = res.id;
          //   done();
          // },
        },
      ],
    },
    callback(res) {
      const data = {
        planCollectionId: row.id,
        ...res.data,
        collectionFiles: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/sealContractPlanCollection/collection', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let dialogVisible = ref(false);
let currentId = ref('');
function collectionCord(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}

function beforeOpen(done, type) {
  getVisaList();
  if (type == 'edit') {
    const keys = planNameData.filter(item => item.value != '其它').map(item => item.value);
    if (!keys.includes(form.planName)) {
      form.value.planNameOther = form.value.planName;
      form.value.planName = '其它';
    }
  }
  if (type == 'add') {
    form.value.remindTriggeringCondition = 0;
    form.value.conditionValue = 3;
  }
  done();
}

// 定义存储签证列表的数据
let visaList = ref([]);

// 获取签证列表的函数
async function getVisaList() {
  axios
    .post('/api/vt-admin/projectChange/completeListBySealContractId?id=' + route.query.id)
    .then(res => {
      visaList.value = res.data.data;
    });
}
function setTotalPrice() {
  const totalPrice = visaList.value
    .filter(item => item.isCheck)
    .reduce((total, item) => {
      return total + item.totalPrice * 1;
    }, 0);
  form.value.planCollectionPrice = totalPrice && totalPrice.toFixed(2);
}

let remindOptions = ref([
  {
    value: 0,
    label: '收款时间',
  },
  {
    value: 1,
    label: '到某个项目计划完成多少天',
    disabled: !props.projectId,
  },
  {
    value: 2,
    label: '项目采购金额达百分比',
    disabled: !props.projectId,
  },
  {
    value: 3,
    label: '验收完成后多少天',
    disabled: !props.projectId,
  },
  {
    value: 4,
    label: '签订时间多少天',
  },
]);
let projectPlanList = ref([]);
onMounted(() => {
  getPorjectPlanList();
});
function getPorjectPlanList() {
  if (!props.projectId) return
  axios.get('/api/vt-admin/projectStage/list?size=5000&projectId=' + props.projectId).then(res => {

    projectPlanList.value = res.data.data;
  });
}

function getHitRuleText(row) {
  if (!row.remindTriggeringCondition || row.remindTriggeringCondition == 0) {
    return '固定收款时间';
  }
  switch (row.remindTriggeringCondition) {
    case 1:
      const stage = projectPlanList.value.find(item => item.id == row.conditionStageId);
      return stage ? `${stage.stage}完成后${row.conditionValue || 0}天` : '项目计划完成后多少天';
    case 2:
      return `项目采购金额达${row.conditionPercentage || 0}%后${row.conditionValue || 0}天`;
    case 3:
      return `验收完成后${row.conditionValue || 0}天`;
    case 4:
      return `签订时间${row.conditionValue || 0}天后`;
    default:
      return '---';
  }
}
function stop(row) {
  proxy.$confirm('确定要中止吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    axios.post('/api/vt-admin/sealContractPlanCollection/stopPlanCollection', {
      id: row.id,
    }).then(res => {
      proxy.$message.success(res.data.msg);
      onLoad();
    });
  });
}
</script>

<style lang="scss" scoped></style>
