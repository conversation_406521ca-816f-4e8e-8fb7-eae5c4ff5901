<template>
  <avue-crud
    :option="props.form.cooperationType == 1 ? collectOption : payOption"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    :beforeOpen="beforeOpen"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  <template #payFile="{ row }">
    <File :fileList="row.fileList" ></File>
  </template>
  <template #payStatus="{ row }">
    <div style="display: flex;align-items: center;justify-content: center;">
       <span>{{ row.$payStatus }}</span>
    <el-icon
      v-if="row.payStatus == 2"
      @click="viewBackDetail(row)"
      style="margin-left: 8px; cursor: pointer; color: var(--el-color-primary);"
      title="查看退回详情"
    >
      <InfoFilled />
    </el-icon>
    </div>
  </template>
  <template #menu="{row}">
    <el-button type="primary" @click="$refs.crud.rowEdit(row)" icon="edit" v-if="row.payStatus == 1" text>编辑</el-button>
    <el-button type="primary" @click="$refs.crud.rowDel(row)" v-if="row.payStatus == 1" icon="delete" text>删除</el-button>
  </template>

  </avue-crud>
  <dialogForm ref="dialogFormRef"></dialogForm>

  <!-- 退回详情弹窗 -->
  <el-dialog v-model="backDetailVisible" title="退回详情" width="600px">
    <el-form label-width="100px">
      <el-form-item label="退回时间">
        <el-input v-model="backDetailData.backTime" disabled />
      </el-form-item>
      <el-form-item label="退回附件">
        <File :fileList="backDetailData.backFileList || []" />
      </el-form-item>
      <el-form-item label="退回备注">
        <el-input
          v-model="backDetailData.backRemark"
          type="textarea"
          :rows="4"
          disabled
        />
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { InfoFilled } from '@element-plus/icons-vue';
import { dateFormat } from '@/utils/date';


const props = defineProps({
  sealContractId: String,
  form: Object,
});
let collectOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '类型',
      prop: 'type',
      type: 'select',
      width: 250,
      value: 0,
      dicData: [
        {
          value: 0,
          label: '伙伴预支',
        },
      ],
      overHidden: true,
    },
    {
      label: '预支金额',
      prop: 'amount',
    },
    {
      label: '状态',
      prop: 'payStatus',
      type: 'select',
      display: false,
      slot: true,
      dicData: [
        {
          value: 1,
          label: '已收',
        },
        {
          value: 2,
          label: '已退回',
        },
      ],
      overHidden: true,
    },
    {
      type: 'datetime',
      label: '收款日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
      prop: 'payTime',
    },
    {
      type: 'select',
      label: '收款账户',

      cascader: [],
      span: 12,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'payAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账户',
          trigger: 'change',
        },
      ],
    },
    {
      label: '收款凭证',
      prop: 'payFile',
      type: 'upload',
      dataType: 'object',
     
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let payOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '类型',
      prop: 'type',
      type: 'select',
      width: 250,
      value: 1,
      dicData: [
        {
          value: 1,
          label: '企业预支',
        },
      ],
      overHidden: true,
    },
    {
      label: '预支金额',
      prop: 'amount',
    },
    {
      label: '状态',
      prop: 'payStatus',
      type: 'select',
      display: false,
      slot: true,
      dicData: [
        {
          value: 1,
          label: '已付',
        },
        {
          value: 2,
          label: '已退回',
        },
      ],
      overHidden: true,
    },
    {
      type: 'datetime',
      label: '付款日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
      prop: 'payTime',
    },
    {
      type: 'select',
      label: '付款账户',

      cascader: [],
      span: 12,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'payAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账户',
          trigger: 'change',
        },
      ],
    },
    {
      label: '付款凭证',
      prop: 'payFile',
      type: 'upload',
      dataType: 'object',
    
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let form = ref({
    payFile: [],
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractPrePayment/save';
const delUrl = '/api/vt-admin/sealContractPrePayment/remove?ids=';
const updateUrl = '/api/vt-admin/sealContractPrePayment/edit';
const tableUrl = '/api/vt-admin/sealContractPrePayment/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let backDetailVisible = ref(false);
let backDetailData = ref({});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    payFile: form.payFile && form.payFile.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
     payFile: row.payFile && row.payFile.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done,type) {
    if(type == 'edit'){
        form.value.payFile = form.value.fileList && form.value.fileList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
    }
    done()
}

function back(row) {
  proxy.$refs.dialogFormRef.show({
    title: '退回',
    option: {
      column: [
        // 退回时间
        {
          type: 'backTime',
          label: '退回时间',
          type:"datetime",
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          prop: 'backTime',
        },
        {
          label: '退回附件',
          prop: 'backFile',
          type: 'upload',
          dataType: 'object',
          span: 24,
          limit: 5,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '退回备注',
          prop: 'backRemark',
          type: 'textarea',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入退回备注',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        backRemark: res.data.backRemark,
        backFile: res.data.backFile && res.data.backFile.map(item => item.value).join(','),
        backTime: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
      };
      axios
        .post('/api/vt-admin/sealContractPrePayment/returnBack', data)
        .then(() => {
          proxy.$message.success('退回成功');
          res.close();
          onLoad();
        })
        .catch(() => {
          proxy.$message.error('退回失败');
        });
    },
  });
}

function viewBackDetail(row) {
  backDetailData.value = {
    backTime: row.backTime,
    backRemark: row.backRemark,
    backFileList: row.backFileList || [],
  };
  backDetailVisible.value = true;
}

</script>

<style lang="scss" scoped></style>
