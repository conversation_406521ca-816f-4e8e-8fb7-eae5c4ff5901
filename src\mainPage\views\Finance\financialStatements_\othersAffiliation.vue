<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #menu="{row}">
        <el-button type="primary" icon="document" @click="settlement(row)" text>结算</el-button>
        <el-button type="primary" icon="document"  text>结算记录</el-button>
    </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="合同结算" v-model="drawer"  size="1000"
     :destroy-on-close="true" :show-close="true" :wrapperClosable="true">
    </el-drawer>
    
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '合同编号',
      prop: 'contractCode',
      overHidden: true,
      // search: true,
      searchSpan: 4,
      width: 160,
      display: false, // 设置 display 为 false
    },
    {
      label: '合同名称',
      prop: 'contractName',
    
      overHidden: true,
      search: true,
      slot: true,
      display: false, // 设置 display 为 false
    },

    {
      label: '客户名称',
      prop: 'customerName',
      //width: 150,
      search: true,
      component: 'wf-customer-drop',

     
      display: false, // 设置 display 为 false
    },
    {
      label: '合同总额',
      prop: 'contractTotalPrice',
      // search: true,
      // hide: true,
      searchSpan: 6,
      searchLabelWidth: 120,
      searchSlot: true,
    
      width: 100,
      display: false, // 设置 display 为 false
    },
    {
      label: '业务员',
      prop: 'businessName',
      // search: true,
      component: 'wf-user-drop',
      formatter: (row, value, column, cell) => {
        return row.businessUserName;
      },
      // hide: true,
      display: false, // 设置 display 为 false
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      // hide: true,
      sortable: true,
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      display: false, // 设置 display 为 false
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
        tableData.value = [{}];
      loading.value = false;
    });
}
let router = useRouter();
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}


let drawer = ref(false);
function settlement(params) {
    drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
