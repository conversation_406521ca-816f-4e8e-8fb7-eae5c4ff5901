<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowSupplierUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowSupplierDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>

</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
  productId: String,
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  //   header:false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  labelWidth:150,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 180,
  border: true,
  column: [
    {
      label: '供应商名称',
      prop: 'supplierName',
      width: 250,
      overHidden: true,
      editDisplay: false,
      // search: true,
    },
    // {
    //   label: '联系人',
    //   prop: 'contact',
    // },
    // {
    //   label: '联系电话',
    //   prop: 'contactPhone',
    // },
    {
      label: '供应商地址',
      prop: 'address',
      overHidden: true,
      editDisplay: false,
    },

    {
      type: 'select',
      label: '供应商分类',
      span: 12,
     
      rules: [
        {
          required: true,
          message: '请选择供应商分类',
        },
      ],
   
      prop: 'supplierClassify',
      dicUrl: '/blade-system/dict-biz/dictionary?code=supplierClassify',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      editDisplay: false,
    },
    {
      type: 'input',
      label: '供应商特色',
      span: 12,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: '请选择供应商级别',
        },
      ],
     
      prop: 'supplierFeature',
    },
    {
      label: '供应商网址',
      prop: 'webUrl',
      editDisplay: false,
    },
    {
      type: 'radio',
      label: '账期',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择账期',
        },
      ],
      span: 24,
    
      editDisplay: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentMethod',
      dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
      remote: false,
    },
    {
            label: '报价时间',
            prop: 'offerDate',
            type: 'date',
            span: 12,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
          {
            label: '产品质保期（年）',
            prop: 'warrantyPeriod',
            type: 'number',
            span: 12,
          },
          {
            label: '是否含税',
            prop: 'isHasTax',
            type: 'switch',
            value: 1,
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            span: 12,
          },
          {
            label: '税率',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            formatter: val => {
              return parseFloat(val.taxRate) + '%';
            },
          },
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      span: 12,
    },
  ],
});

let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/supplierProduct/remove';
const updateUrl = '/api/vt-admin/supplierProduct/update';
const tableUrl = '/api/vt-admin/supplierProduct/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
watch(() =>props.productId, () => {
    onLoad()
})
let loading = ref(false);
function onLoad() {
  loading.value = true;
  
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        productId: props.productId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowSupplierUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/supplierProduct/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowSupplierDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/supplierProduct/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
defineExpose({
  onLoad,
});
</script>

<style lang="scss" scoped></style>
