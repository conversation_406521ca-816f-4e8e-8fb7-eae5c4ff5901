<template>
  <!-- <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex === item.value ? 'active' : ''"
        @click="handleTabClick(item,index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div> -->
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
      @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" :before-open="beforeOpen" ref="crud"
      @keyup.enter="onLoad" @row-del="rowDel" @search-reset="reset" @search-change="searchChange"
      @current-change="onLoad" :cell-style="cellStyle" @refresh-change="onLoad" @size-change="onLoad" v-model="form">
      <template #menu-left>
        <div style="display: inline-block">
          <div style="display: flex; align-items: center; gap: 20px">
            <!-- <el-button type="primary" @click="handleAdd" icon="plus">新增报价合同</el-button> -->
            <el-button type="primary" @click="handleAdd" icon="plus" style="margin-left: -10px">新增合同</el-button>
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
            <span style="font-weight: bolder">未收款总额：</span>
            <el-text type="primary" size="large">￥{{ (noReceivedPrice * 1).toLocaleString() }}</el-text>
          </div>
        </div>
      </template>
      <template #contractCode="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag>
          <el-popover ref="popover1" placement="top-start" :disabled="!row.remark" width="200" trigger="hover"
            :content="row.remark">
            <template #reference>
              <el-tag type="danger" v-if="row.contractStatus == 2" effect="dark" size="small">中止</el-tag>
            </template>
          </el-popover>
          <el-tag v-if="row.isPreOrder == 1" type="danger" size="small" effect="plain" title="预订单">预</el-tag>
          {{ row.contractCode }}
          <i title="该订单正在申请撤销" v-if="row.isApplyCancel == 1" class="element-icons el-icon-chehui1"
            style="color: var(--el-color-warning); font-size: 20px"></i>
        </el-link>
      </template>
      <template #menu="{ row }">
        <el-button text type="primary" v-if="!route.query.type || route.query.type == 4" @click="edit(row)"
          icon="edit">编 辑</el-button>
        <el-button text type="primary" @click="toDetail(row)" icon="view">详 情</el-button>
        <el-dropdown @command="command => handleDropdownCommand(command, row)">
          <el-button text type="primary" icon="more">
            更多<el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="cancel" icon="back"
                v-if="(!route.query.type || route.query.type == 4) && row.isApplyCancel != 1 && (row.contractType == 0 || row.contractType == 1)">撤
                回</el-dropdown-item>
              <el-dropdown-item command="confirm" v-if="row.contractType == 4" icon="check">确 认</el-dropdown-item>
              <el-dropdown-item command="stop" icon="Stopwatch">中 止</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template #contractTotalPrice-search="{ row }">
        <div style="display: flex">
          <el-input placeholder="最小合同额" v-model.number="params.contractMinPrice"></el-input>-
          <el-input placeholder="最大合同额" v-model.number="params.contractMaxPrice"></el-input>
        </div>
      </template>
      <template #planCollectionPrice="{ row }">
        <div v-if="row.noReceivedPrice == 0" style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
        <div v-else>
          <span :style="{
            color:
              row.planCollectionDays > 7
                ? 'var(--el-color-success)'
                : row.planCollectionDays <= 7 && row.planCollectionDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
          }" class="planCollectionDays">{{ row.planCollectionDays }}</span><span>{{ row.planCollectionDays ||
            row.planCollectionDays == 0 ? '天' : '' }}</span>
        </div>
      </template>
      <template #customerName="{ row }">
        <el-link type="primary" @click="toCustomerDetail(row)">{{ row.customerName }}</el-link>
      </template>
      <template #contractType1="{ row }">
        <div style="display: flex;align-items: center;justify-content: center;">
          <img title="伙伴挂靠" v-if="row.contractType == 5 && row.cooperationType == 1"
            src="../../../assets/imgIcon/伙伴挂靠.png" style="height: 20px;width: 20px;" alt="">
          <img title="挂靠伙伴" v-if="row.contractType == 5 && row.cooperationType == 0"
            src="../../../assets/imgIcon/挂靠伙伴.png" style="height: 20px;width: 20px;" alt=""><span
            style="text-overflow: ellipsis;white-space: nowrap;">
            {{[
              {
                value: 0,
                label: '订单合同',
              },
              {
                value: 1,
                label: '项目合同',
              },
              {
                value: 2,
                label: '运维合同',
              },
              {
                value: 3,
                label: '实施合同',
              },
              {
                value: 4,
                label: '订单待确认合同',
              },
              {
                value: 5,
                label: '合作合同',
              },
            ].find(item => item.value == row.contractType)?.label}}
          </span>
        </div>
      </template>
      <template #contractTotalPrice-form>
        <div v-if="editType == 1">
          <div style="display: flex;"> <el-input type="number" style="width: 100%" controls-position="right" readonly
              v-model="form.contractTotalPrice" @change="inputChange" placeholder="请输入合同总额" label="label"></el-input>
            <el-button type="primary" @click="handleCheckProduct">检查产品</el-button>
          </div>
          <!-- 提示 将未确认合同但转为确认合同时 合同总额 来自与产品总额 请完善产品销售单价 -->
          <el-text type="info">合同总额来自产品销售单价*数量</el-text>
        </div>
        <el-input v-else type="number" style="width: 100%" controls-position="right" v-model="form.contractTotalPrice"
          @change="inputChange" placeholder="请输入合同总额" label="label"></el-input>
      </template>
        <template #cooperationCompanyId-form="{disabled,size,type}">
       <partnerSelect :select-type="1" v-model="form.cooperationCompanyId" :type="form.cooperationType == 1 ? 0 : 1"></partnerSelect>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <!-- 中止抽屉 -->
    <el-drawer v-model="stopDrawerVisible" title="合同中止" direction="rtl" size="800px" :before-close="cancelStop">
      <div style="padding: 20px">
        <el-form :model="stopForm" label-width="120px">
          <el-form-item label="中止原因:" required>
            <el-input v-model="stopForm.remark" type="textarea" :rows="4" placeholder="请输入中止原因" maxlength="200"
              show-word-limit />
          </el-form-item>
        </el-form>

        <el-divider content-position="left">计划收款列表</el-divider>
        <p style="color: #666; margin-bottom: 15px">请选择要中止的收款计划：</p>

        <el-table class="avue-crud" @selection-change="handleSelectionChange" :data="planData" border
          style="width: 100%">
          <el-table-column type="selection"> </el-table-column>
          <el-table-column align="center" prop="planName" label="计划收款名称" />
          <el-table-column align="center" prop="planCollectionDate" label="计划收款时间" />
          <el-table-column align="center" label="计划收款金额" prop="planCollectionPrice">
            <!-- <template #default="{ row }">
              <el-input-number
                v-if="row.editable"
                v-model="row.planAmount"
                :min="0"
                :precision="2"
                controls-position="right"
                style="width: 100%"
              />
              <span v-else>￥{{ row.planAmount.toLocaleString() }}</span>
            </template> -->
            <template #default="{ row }">
              ￥{{ row.planCollectionPrice.toLocaleString() }}
            </template>
          </el-table-column>
          <!-- <el-table-column align="center" prop="actualAmount" label="实际收款金额" width="120">
            <template #default="{ row }"> ￥{{ row.actualCollection.toLocaleString() }} </template>
          </el-table-column> -->
        </el-table>
      </div>
      <template #footer>
        <el-button @click="cancelStop">取消</el-button>
        <el-button type="primary" @click="submitStopForm">确认中止</el-button>
      </template>
    </el-drawer>
    <!-- 产品检查抽屉 -->
    <el-drawer v-model="productDrawerVisible" title="检查产品" direction="rtl" size="1200px"
      :before-close="cancelProductCheck">
      <div style="padding: 20px">
        <el-alert type="info" :closable="false" style="margin-bottom: 20px">
          <template #title>
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <span>合同总额来自产品销售单价*数量，请完善产品销售单价</span>
              <span style="font-weight: bold; font-size: 16px;">
                合同总额：<el-text type="primary" size="large">￥{{ productTotalAmount.toLocaleString() }}</el-text>
              </span>
            </div>
          </template>
        </el-alert>

        <el-table class="avue-crud" :data="productList" border style="width: 100%" v-loading="productLoading">
          <el-table-column align="center" prop="customProductName" label="产品名称" min-width="150" show-overflow-tooltip />
          <el-table-column align="center" prop="customProductSpecification" label="产品型号" min-width="120"
            show-overflow-tooltip />
          <el-table-column align="center" prop="productBrand" label="品牌" min-width="100" show-overflow-tooltip />
          <el-table-column align="center" prop="customUnit" label="单位" width="80" />
          <el-table-column align="center" prop="customProductDescription" label="描述" min-width="150"
            show-overflow-tooltip />
          <el-table-column align="center" prop="number" label="数量" width="100">
            <template #default="{ row }">
              {{ row.number }}
            </template>
          </el-table-column>
          <el-table-column align="center" prop="sealPrice" label="单价" width="150">
            <template #default="{ row }">
              <el-input-number v-model="row.sealPrice" :min="0" :precision="2" controls-position="right"
                style="width: 100%" @change="handlePriceChange" />
            </template>
          </el-table-column>
          <el-table-column align="center" label="金额" width="120">
            <template #default="{ row }">
              ￥{{ ((row.sealPrice || 0) * (row.number || 0)).toFixed(2) }}
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <el-button @click="cancelProductCheck">取消</el-button>
        <el-button type="primary" @click="confirmProductCheck">确认</el-button>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ArrowDown } from '@element-plus/icons-vue';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import partnerSelect from './compoents/partnerSelect.vue';
const props = defineProps(['customerId']);

console.log(window);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  addBtnText: '新增合同',
  calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 6,
  searchIcon: true,
  searchIndex: 3,
  searchLabelWidth: 120,
  menuWidth: 250,
  border: true,
  dialogType: 'drawer',
  labelWidth: 120,
  column: [
    {
      label: '项目信息',
      display: false,
      children: [
        {
          label: '合同编号',
          prop: 'contractCode',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
          display: false, // 设置 display 为 false
        },
        {
          label: '对方订单编号',
          prop: 'customerOrderNumber',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
          display: false, // 设置 display 为 false
        },
        // {
        //   label: '类型',
        //   prop: 'type',
        //   searchType: 'radio',
        //   search: true,
        //   hide: true,
        //   dicData: [
        //     {
        //       label: '全部',
        //       value: '',
        //     },
        //     {
        //       label: '我的',
        //       value: 1,
        //     },
        //     {
        //       label: '我参与的',
        //       value: 2,
        //     },
        //   ],
        // },
        {
          label: '合同名称',
          prop: 'contractName',
          width: 200,
          overHidden: true,
          search: true,
          slot: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '客户名称',
          prop: 'customerName',
          //width: 150,
          search: !props.customerId,
          component: 'wf-customer-drop',
          hide: !!props.customerId,
          width: 200,
          display: false, // 设置 display 为 false
        },
        // {
        //   label: '关联商机',
        //   prop: 'businessOpportunityName',
        //   //width: 150,
        //   // search: true,
        //   overHidden: true,
        //   width: 200,
        //   display: false, // 设置 display 为 false
        // },
        {
          label: '合同类型',
          width: 130,
          prop: 'contractType1',
          display: false,

          formatter: row => {
            const data = [
              {
                value: 0,
                label: '订单合同',
              },
              {
                value: 1,
                label: '项目合同',
              },
              {
                value: 2,
                label: '运维合同',
              },
              {
                value: 3,
                label: '实施合同',
              },
              {
                value: 4,
                label: '订单待确认合同',
              },
              {
                value: 5,
                label: '合作合同',
              },
            ];
            return data.find(item => item.value == row.contractType)?.label;
          },
        },
        // {
        //   label: '商机名称',
        //   prop: 'businessOpportunityName',
        //   //width: 150,
        //   width: 200,
        //   search: true,
        // },
        {
          label: '签订日期',
          type: 'date',
          prop: 'signDate',
          // hide: true,
          sortable: true,
          search: true,
          component: 'wf-daterange-search',
          search: true,
          searchSpan: 6,
          width: 120,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          display: false, // 设置 display 为 false
        },

        //   {
        //     label: "建设单位",
        //     prop: "ownerName",
        //     overHidden: true,
        //     search: true,
        //     //width: 150,
        //   },
      ],
    },
    {
      label: '财务信息',
      children: [
        {
          label: '合同总额',
          prop: 'contractTotalPrice',
          // search: true,
          // hide: true,
          searchSpan: 6,
          searchLabelWidth: 120,
          searchSlot: true,
          sortable: true,
          width: 100,
          display: false, // 设置 display 为 false
        },
        {
          label: '开票金额',
          prop: 'hasInvoice',
          width: 100,
          sortable: true,
          formatter: row => {
            if (row.isNeedInvoice == 0) {
              return '无需开票';
            } else {
              return row.hasInvoice;
            }
          },
          display: false, // 设置 display 为 false
        },
        {
          label: '未开票金额',
          prop: 'noInvoice',
          width: 100,
          sortable: true,
          formatter: row => {
            if (row.isNeedInvoice == 0) {
              return '无需开票';
            } else {
              return row.noInvoice;
            }
          },
          display: false, // 设置 display 为 false
        },
        {
          label: '离回款时间',
          prop: 'planCollectionPrice',
          width: 100,
          html: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '收讫状态',
          prop: 'isReceived',
          width: 100,
          hide: true,
          search: true,
          type: 'select',
          dicData: [
            {
              value: 0,
              label: '已收讫',
            },
            {
              value: 1,
              label: '未收讫',
            },
            {
              value: 2,
              label: '回款为空',
              desc: '没填写计划收款或收款未到100%',
            },
          ],
          display: false, // 设置 display 为 false
        },
        {
          label: '已收款',
          prop: 'receivedPrice',
          width: 100,
          sortable: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '未收款',
          prop: 'noReceivedPrice',
          width: 100,
          sortable: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '回款比例(%)',
          prop: 'receiveRate',
          formatter: (row, value, column, cell) => {
            return row.receiveRate + '%';
          },
          display: false, // 设置 display 为 false
        },
      ],
    },
    // {
    //   label: "回款逾期",
    //   prop: "overdue",
    // },
    {
      label: '业务员',
      prop: 'businessName',
      // search: true,
      component: 'wf-user-drop',
      formatter: (row, value, column, cell) => {
        return row.businessUserName;
      },
      // hide: true,
      display: false, // 设置 display 为 false
    },
    {
      label: '最终用户',
      prop: 'finalCustomer',
      span: 12,
      hide: true,
      search: true,
      display: false, // 设置 display 为 false
    },

    // {
    //   label: '业务员',
    //   prop: 'reason',
    //   // hide: true,
    // },
    // {
    //   label: '状态',
    //   children: [
    //     // {
    //     //   label: "合同签订时间",
    //     //   prop: "contractSignDate",
    //     // },
    //     {
    //       label: '合同状态',
    //       prop: 'contractStatus',
    //       dicData: [
    //         {
    //           value: 0,
    //           label: '待审批',
    //         },
    //         {
    //           value: 1,
    //           label: '执行中',
    //         },
    //         {
    //           value: 2,
    //           label: '待执行',
    //         },
    //         {
    //           value: 3,
    //           label: '暂停',
    //         },
    //         {
    //           value: 4,
    //           label: '终止',
    //         },
    //         {
    //           value: 5,
    //           label: '诉讼',
    //         },
    //         {
    //           value: 6,
    //           label: '待签订',
    //         },
    //         {
    //           value: 7,
    //           label: '已签未回',
    //         },
    //       ],
    //       width: 80,
    //       type: 'select',
    //       dataType: 'string',
    //     },
    //   ],
    // },
  ],
  group: [
    {
      label: '合同信息',
      prop: 'contractInfo',
      column: [
        {
          label: '合同类型',
          prop: 'contractType',
          type: 'radio',
          value: 5,
          span: 12,
          props: {
            label: 'label',
            value: 'value',
          },
          rules: [
            {
              required: true,
              message: '请选择合同类型',
            },
          ],
          dicData: [
            {
              value: 2,
              label: '运维合同',
            },
            {
              value: 3,
              label: '实施合同',
            },
            {
              value: 4,
              label: '订单未确认合同',
            },
            {
              value: 5,
              label: '合作合同',
            },
          ],
          change: val => {
            if (val.value == 4 || val.value == 5) {
              const projectInfoRef = proxy.findObject(option.value.group, 'projectInfo');
              projectInfoRef.display = false;
              const finalCustomerInfo = proxy.findObject(option.value.group, 'finalCustomerInfo');
              finalCustomerInfo.display = false;
              if (val.value == 5) {
                const cooperationType = proxy.findObject(option.value.group, 'cooperationType');
                cooperationType.display = true;
                const cooperationCompanyId = proxy.findObject(
                  option.value.group,
                  'cooperationCompanyId'
                );
                cooperationCompanyId.display = true;
                const managementFeePoints = proxy.findObject(
                  option.value.group,
                  'managementFeePoints'
                );
                managementFeePoints.display = true;
              } else {
                const cooperationType = proxy.findObject(option.value.group, 'cooperationType');
                cooperationType.display = false;
                const cooperationCompanyId = proxy.findObject(
                  option.value.group,
                  'cooperationCompanyId'
                );
                cooperationCompanyId.display = false;
                const managementFeePoints = proxy.findObject(
                  option.value.group,
                  'managementFeePoints'
                );
                managementFeePoints.display = false;
              }
            } else {
              const finalCustomerInfo = proxy.findObject(option.value.group, 'finalCustomerInfo');
              finalCustomerInfo.display = true;
              const cooperationType = proxy.findObject(option.value.group, 'cooperationType');
              cooperationType.display = false;
              const cooperationCompanyId = proxy.findObject(
                option.value.group,
                'cooperationCompanyId'
              );
              cooperationCompanyId.display = false;
              const managementFeePoints = proxy.findObject(
                option.value.group,
                'managementFeePoints'
              );
              managementFeePoints.display = false;
            }
            // const companyName = proxy.findObject(option.value.group, 'companyName');
            // companyName.display = val.value != 5;
            const operationTechnology = proxy.findObject(option.value.group, 'operationTechnology');
            operationTechnology.display = [2, 3].includes(val.value);
            const finalCustomer = proxy.findObject(option.value.group, 'finalCustomer');
            finalCustomer.display = [4].includes(val.value);


            ['paymentDeadline', 'isNeedInvoice', 'invoiceType', 'billingCompany', 'taxRate'].forEach(item => {
              const itemRef = proxy.findObject(option.value.group, item);
              itemRef.display = !(val.value == 5 && form.value.cooperationType == 0);

            })

            if (form.value.id) return;
            form.value.isCreateProject = val.value == 3 ? 1 : 0;
          },
          control: val => {
            ;
            return {
              isCreateProject: {
                display: val != 4 && !form.value.id,
              },
              operationTechnology: {
                display: val != 4,
              },

            };
          },
        },
        {
          label: '合作类型',
          prop: 'cooperationType',
          span: 12,

          type: 'radio',
          display: true,
          value: 1,
          labelTip: '挂靠伙伴请从商机或者报价流程',
          dicData: [
            {
              value: 1,
              label: '伙伴挂靠',
            },
            {
              value: 0,
              // disabled: true,
              label: '挂靠伙伴',
            },
          ],
          change: val => {
            ['paymentDeadline', 'isNeedInvoice', 'invoiceType', 'billingCompany', 'taxRate'].forEach(item => {
              const itemRef = proxy.findObject(option.value.group, item);
              itemRef.display = !(form.value.contractType == 5 && val.value == 0);

            })
             
          }
        },
          
        {
          label: '合作伙伴',
          prop: 'cooperationCompanyId',
          placeholder: '请选择合作伙伴',
         span:12,
          rules: [
            {
              required: true,
              message: '请选择合作伙伴',
            },
          ],
        
        },
        // {
        //   label: '签订公司',
        //   prop: 'companyName',
        //   span: 12,
        //   hide: true,
        //   dicUrl: '/api/vt-admin/company/page?size=1000',
        //   dicFormatter: d => {
        //     return d.data.records;
        //   },
        //   props: {
        //     label: 'companyName',
        //     value: 'id',
        //   },
        //   type: 'select',
        //   search: true,
        // },
        {
          label: '关联客户',
          prop: 'customerId',
          placeholder: '请选择关联客户',
          change: val => {
            setCustomerInfo(val.value);
          },
          params: {
            Url: '/vt-admin/customer/page?type=2'
          },
          component: 'wf-customer-select',
          rules: [
            {
              required: true,
              message: '请选择关联客户',
            },
          ],
        },
        {
          label: '合同名称',
          prop: 'contractName',
          blur: () => {
            ;
            form.value.projectName = form.value.contractName;
          },
          rules: [
            {
              required: true,
              message: '请输入合同名称',
            },
          ],
        },
        {
          label: '客户联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择联系人',
          // disabled: true,
          params: {},
          change: val => {
            setContactInfo(val.value);
          },
        },
        {
          label: '合同金额',
          prop: 'contractTotalPrice',
          placeholder: '请输入合同金额',
          type: 'number',
          // rules: [
          //   {
          //     required: true,
          //     message: '请输入合同金额',
          //   },
          // ],
        },

        {
          type: 'input',
          label: '联系人电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        // {
        //   label: '预计开始时间',
        //   prop: 'expectedStartTime',
        //   placeholder: '请选择预计开始时间',
        //   type: 'date',
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD',
        // },
        // {
        //   label: '预计结束时间',
        //   prop: 'expectedEndTime',
        //   placeholder: '请选择预计结束时间',
        //   type: 'date',
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD',
        // },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
        },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务板块',
          // multiple: true,
          span: 12,
          parent: true,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          display: true,
          filterable: true,
          prop: 'businessTypeId',
          checkStrictly: true,
        },
        // 管理费点数
        {
          label: '管理费点数(%)',
          type: 'input',
          span: 12,
          display: true,
          prop: 'managementFeePoints',
          append: '%',
          rules: [
            {
              required: true,
              message: '请输入管理费点数',
            },
          ],
        },

        {
          label: '付款期限',
          type: 'radio',
          span: 24,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            return res.data;
          },

          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '是否开票',
          prop: 'isNeedInvoice',
          type: 'radio',
          value: 1,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
         change: val => {
            ['invoiceType', 'billingCompany', 'taxRate'].forEach(item => {
              const itemRef = proxy.findObject(option.value.group, item);
              itemRef.display = val.value == 1;

            })
          }
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '开票公司',
          type: 'select',

          prop: 'billingCompany',

          props: {
            label: 'companyName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.records;
          },

          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },

          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '技术人员',
          prop: 'operationTechnology',
          component: 'wf-user-select',
          checkType: 'checkbox',
          params: {
            userUrl: '/api/blade-system/search/user?functionKeys=engineer',
          },
          span: 12,
        },

        // {
        //   label: '派单人',
        //   prop: 'assistUser',
        //   placeholder: '请选择派单人',
        //   checkType: 'checkBox',
        //   component: 'wf-user-select',
        // },

        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        // {
        //   label: '是否生成项目',
        //   prop: 'isCreateProject',
        //   type: 'radio',
        //   value: 0,
        //   editDisplay: false,
        //   dicData: [
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //   ],
        //   change: val => {
        //     const { value } = val;
        //     ;
        //     const projectInfoRef = proxy.findObject(option.value.group, 'projectInfo');
        //     projectInfoRef.display = value == 1;
        //   },
        // },
        {
          label: '备注',
          prop: 'remark',
          placeholder: '请输入备注',
          span: 24,
          type: 'textarea',

          showWordLimit: true,
          autosize: {
            minRows: 3,
            maxRows: 4,
          },
        },
      ],
    },
    {
      label: '服务客户信息',
      prop: 'finalCustomerInfo',
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入服务客户名称',
            },
          ],
          span: 24,
        },
        {
          label: '服务联系人',
          prop: 'finalCustomerConcat',
        },
        {
          label: '服务联系电话',
          prop: 'finalCustomerPhone',
        },
        {
          label: '服务地址',
          prop: 'deliveryAddress',
          span: 24,
        },
      ],
    },

    {
      label: '项目信息',
      prop: 'projectInfo',
      arrow: true,
      collapse: true,
      display: false,

      column: [
        {
          type: 'input',
          label: '项目名称',
          span: 12,
          display: true,
          prop: 'projectName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },

        {
          label: '项目属性',
          prop: 'projectAttribute',
          overHidden: true,
          dicData: [
            {
              label: '总包',
              value: 0,
            },
            {
              label: '分包',
              value: 1,
            },
          ],
          type: 'radio',
          hide: true,
        },

        {
          label: '开工时间',
          prop: 'startDate',
          type: 'date',
          span: 12,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '交付时间',
          prop: 'deliveryDate',
          type: 'date',
          span: 12,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          change: val => {
            console.log(val);
            form.value.contractDeliveryDate = val.value;
          },
        },
        {
          label: '项目地址',
          prop: 'projectAddress',
          overHidden: true,
          type: 'input',
          span: 24,
          hide: true,
        },

        {
          label: '附件',
          prop: 'fileIds',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '项目备注',
          prop: 'projectRemark',
          overHidden: true,
          span: 24,
          type: 'textarea',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

// 中止抽屉相关变量
let stopDrawerVisible = ref(false);
let stopForm = ref({
  remark: '',
  stopTime: '',
  selectedPlans: [],
});
let currentStopRow = ref(null);

// 模拟计划收款数据
let planData = ref([]);

// 产品检查抽屉相关变量
let productDrawerVisible = ref(false);
let productList = ref([]);
let productLoading = ref(false);
let productTotalAmount = ref(0);
onActivated(() => {
  onLoad();
});
let route = useRoute();
let params = ref({
  contractMinPrice: '',
  contractMaxPrice: '',
  ids: route.query.ids,
  signDate: [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
watch(
  () => route.query.ids,
  val => {
    if (val) {
      params.value = {
        contractMinPrice: '',
        contractMaxPrice: '',
        ids: route.query.ids,
      };
      onLoad();
    }
  }
);
watch(
  () => route.query.customerName,
  val => {
    if (val) {
      params.value = {
        customerName: route.query.customerName,
        signDate: [route.query.signStartDate, route.query.signEndDate],
      };

      onLoad();
    }
  },
  {
    immediate: true,
  }
);
let tabs = ref([
  {
    label: '全部合同',
    value: null,
  },
  {
    label: '订单合同',
    value: 0,
  },
  {
    label: '项目合同',
    value: 1,
  },
]);

let tabIndex = ref(null);
function handleTabClick(item, index) {
  tabIndex.value = item.value;
  onLoad();
}
const addUrl = '/api/vt-admin/sealContract/saveWorkOrderSealContract';
const delUrl = '/api/vt-admin/sealContract/remove?ids=';
const updateUrl = '/api/vt-admin/sealContract/update';
const tableUrl = '/api/vt-admin/sealContract/page';

let tableData = ref([]);
let { proxy } = getCurrentInstance();

// onMounted(() => {
//   onLoad();
// });
let loading = ref(false);
let totalPrice = ref(0);
let noReceivedPrice = ref(0);
// let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId || null,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        contractTotalPrice: null,
        selectType: 4,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        customerId: props.customerId || null,
        contractTotalPrice: null,
        selectType: 4,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noReceivedPrice.value = res.data.data.noReceivedPrice;
    });
}
let router = useRouter();
function rowSave(form, done, loading) {
  const data = {
    ...form,

    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };

  if (form.contractType == 5) {
    axios
      .post('/api/vt-admin/sealContract/saveCooperationContract', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  } else {
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();

          if (form.isCreateProject == 1) {
            const data = {
              ...form,
              projectFiles: form.fileIds && form.fileIds.map(item => item.value).join(','),
              contractId: res.data.data.id,
            };
            axios
              .post('/api/vt-admin/project/noOfferAdd', data)
              .then(res => {
                if (res.data.code == 200) {
                  proxy.$message.success(res.data.msg);
                  done();
                }
              })
              .catch(err => {
                proxy.$message.error(err.data.msg);
              });
          }

          done();
        }
      })
      .catch(err => {
        done();
      });
  }
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    contractFiles: row.contractFiles && row.contractFiles.map(item => item.value).join(','),
  };
  const url = editType.value == 1 ? '/api/vt-admin/sealContract/orderConfirm' : updateUrl;
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.contractFiles = form.value.attachList.map(item => {
      return {
        label: item.originalName,
        value: item.id,
      };
    });
    form.value.operationTechnology = form.value.operationTechnology || null;
    form.value.isCreateProject = 0;
    const projectInfoRef = proxy.findObject(option.value.group, 'projectInfo');
    projectInfoRef.display = false;
  }
 
  done();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function addContract() {
  router.push({
    path: '/Contract/editContract',
  });
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
      name: row.contractName,
      selectType: 4,
    },
  });
}
function reset() {
  params.value.contractMaxPrice = '';
  params.value.contractMinPrice = '';
  params.value.ids = null;
  onLoad();
}
function edit(row) {
  if (row.contractType != 0 && row.contractType != 1) {
    proxy.$refs.crud.rowEdit(row);
  } else {
    router.push({
      path: '/Contract/editContract',
      query: {
        id: row.id,
        name: `编辑-${row.contractName}`,
        isAdmin: row.isPreOrder,
      },
    });
  }
}
function cancel(row) {
  proxy.$refs.dialogForm.show({
    title: '撤 回',
    tip: '撤回到报价可编辑状态，需要重新走流程',
    option: {
      column: [
        {
          label: '撤回原因',
          prop: 'applyReason',
          span: 24,
          rules: [{ required: true, message: '请输入撤回原因', trigger: 'blur' }],
          type: 'textarea',
          placeholder: '请输入撤回原因',
          maxlength: 200,
          showWordLimit: true,
          rows: 4,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContractCancel/save', {
          ...res.data,
          sealContractId: row.id,
        })
        .then(r => {
          proxy.$message({
            type: 'success',
            message: '申请成功',
          });
          onLoad();
          res.close();
        });
    },
  });
}

function handleDropdownCommand(command, row) {
  if (command === 'cancel') {
    cancel(row);
  }
  if (command === 'stop') {
    handleStop(row);
  }
  if (command === 'confirm') {
    handleConfirm(row);
  }
}
function cellStyle({ row, column }) {
  if (row.hasInvoice == 0 && column.property == 'hasInvoice' && row.isNeedInvoice == 1) {
    return {
      color: '#fff',
      backgroundColor: 'var(--el-color-danger-light-7)',
    };
  }
}
async function handleStop(row) {
  const res = await axios.get('/api/vt-admin/sealContractPlanCollection/page', {
    params: {
      size: 10000,
      sealContractId: row.id,
    },
  });
  planData.value = res.data.data.records.filter(item => item.collectionStatus == 0);
  currentStopRow.value = row;
  stopForm.value = {
    remark: '',
    stopTime: '',
    selectedPlans: [],
  };

  // 重置计划收款选择状态
  planData.value.forEach(item => {
    item.selected = false;
    item.editable = false;
  });
  stopDrawerVisible.value = true;
}
let selectList = ref([]);
// 处理计划收款选择
function handleSelectionChange(list) {
  selectList.value = list;
}

// 提交中止表单
function submitStopForm() {
  if (!stopForm.value.remark) {
    proxy.$message.error('请填写中止原因');
    return;
  }

  const selectedPlans = selectList.value.map(item => item.id);

  console.log('中止表单数据:', {
    id: currentStopRow.value.id,
    remark: stopForm.value.remark,

    selectedPlans: selectedPlans,
  });
  axios
    .post('/api/vt-admin/sealContract/stopSealContract', {
      id: currentStopRow.value.id,
      remark: stopForm.value.remark,
      selectedPlans: selectedPlans,
    })
    .then(res => {
      proxy.$message.success('合同中止成功');
      stopDrawerVisible.value = false;
      onLoad(); // 刷新列表
    });
}

// 取消中止
function cancelStop() {
  stopDrawerVisible.value = false;
}

/*
设置基础信息
*/
function setCustomerInfo(id) {
  if (!id) return;
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerAddress = res.data.data.address;
      const customerContact1 = proxy.findObject(option.value.group[0].column, 'customerContact');
      customerContact1.params.Url = '/vt-admin/customerContact/page?customerId=' + id;

      const { id: cotactId, phone, name } = res.data.data.customerContactVO || {};
      form.value.customerContact = cotactId;
      form.value.customerPhone = phone;

      const { customerName, address } = res.data.data;
      form.value.finalCustomer = customerName;
      form.value.finalCustomerConcat = name;
      form.value.finalCustomerPhone = phone;
      form.value.deliveryAddress = address;
      form.value.projectAddress = address;
    });
}

function setContactInfo(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { phone } = res.data.data;
      form.value.customerPhone = phone;
    });
}
function handleAdd() {
  const contractType = proxy.findObject(option.value.group, 'contractType');
  editType.value = null;
  contractType.disabled = false
  proxy.$refs.crud.rowAdd();
}
function toCustomerDetail(row) {
  router.push({
    path: '/CRM/customer/detail/detail',
    query: {
      customerId: row.customerId,
      name: row.customerName,
      type: 0,
    },
  });
}

let editType = ref(0); // 0 编辑，1 确认
function handleConfirm(row) {
  editType.value = 1;
  updateOption()
  proxy.$refs.crud.rowEdit(row);
}
function updateOption() {
  option.value.updateBtnText = editType.value == 0 ? '编辑' : '确认';
  const contractType = proxy.findObject(option.value.group, 'contractType');
  contractType.disabled = editType.value == 1 ? true : false;
}

// 处理检查产品
function handleCheckProduct() {
  if (!form.value.id) {
    proxy.$message.warning('请先保存合同');
    return;
  }

  productDrawerVisible.value = true;
  getProductList();
}

// 获取产品列表
function getProductList() {
  productLoading.value = true;
  axios
    .get('/api/vt-admin/sealContract/productPage', {
      params: {
        current: 1,
        size: 5000,
        offerId: form.value.offerId,
      },
    })
    .then(res => {
      productList.value = res.data.data.records.map(item => ({
        ...item,
        sealPrice: item.sealPrice || 0,
        number: item.number || 0,
      }));
      calculateTotalAmount();
      productLoading.value = false;
    })
    .catch(err => {
      productLoading.value = false;
      proxy.$message.error('获取产品列表失败');
    });
}

// 计算合同总额
function calculateTotalAmount() {
  productTotalAmount.value = productList.value.reduce((total, item) => {
    return total + (item.sealPrice || 0) * (item.number || 0);
  }, 0);
}

// 单价变化时重新计算总额
function handlePriceChange() {
  calculateTotalAmount();
}

// 确认产品检查
function confirmProductCheck() {
  // 更新 form.detailDTOList
  form.value.detailDTOList = productList.value.map(item => ({
    ...item,
    zhhsdj: item.sealPrice,
    zhhsze: item.sealPrice * item.number,
  }));

  // 更新合同总额
  form.value.contractTotalPrice = productTotalAmount.value;

  productDrawerVisible.value = false;
  proxy.$message.success('产品信息已更新');
}

// 取消产品检查
function cancelProductCheck() {
  productDrawerVisible.value = false;
}
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin-bottom: -10px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;

      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }

  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}

:deep(.planCollectionDays) {
  font-size: 25px;
  font-weight: bolder;
}

.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);

  color: var(--el-color-dsuccessanger);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}
</style>
