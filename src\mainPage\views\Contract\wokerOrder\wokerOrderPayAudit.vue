<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :permission="permission"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #payStatus="{ row }">
        <el-tag
          :type="row.payStatus == 0 ? priamry : row.payStatus == 3 ? 'danger' : 'success'"
          effect="plain"
          >{{ row.$payStatus }}</el-tag
        >
      </template>
      <template #menu="{ row }">
        <el-button type="primary" @click="viewDetail(row)" v-if="row.payStatus == 0" text
          >审核</el-button
        >
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.objectName }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wokerOrderSelect
      ref="wokerOrderSelectRef"
      :sealContractId="form.sealContractId"
      @onConfirm="handlewokerOrderSelectConfirm"
    ></wokerOrderSelect>
  </basic-container>
  <el-drawer v-model="drawerVisible" title="工单详情" size="60%">
    <avue-form :option="detailOption" v-model="detailForm">
     <template #taskName>
          <el-descriptions :title="detailForm.objectName">
            <el-descriptions-item label="预约时间">{{
              (detailForm.reservationTime && detailForm.reservationTime.split(' ')[0]) || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务客户">{{
              detailForm.finalCustomer || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务联系人">{{
              detailForm.contact || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              detailForm.contactPhone || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务地址" :span="2">
              {{ detailForm.distributionAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="工单描述" :span="3">
              <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #files>
          <File :fileList="detailForm.fileList"></File>
        </template>
        <template #completeFiles>
          <File :fileList="detailForm.completeFilesList"></File>
        </template>
        <template #engineerServiceInfo>
          <div
            v-if="
              detailForm.sealContractObjectResultVOList &&
              detailForm.sealContractObjectResultVOList.length > 0
            "
            class="engineer-list"
          >
            <el-card
              v-for="(engineer, index) in detailForm.sealContractObjectResultVOList"
              :key="index"
              class="engineer-card"
              shadow="hover"
            >
              <template #header>
                <div class="engineer-header">
                  <h3>{{ engineer.handleName || '工程师' + (index + 1) }}</h3>
                </div>
              </template>
              <div class="engineer-content">
                <div class="info-grid">
                   <div class="info-item">
                    <span class="label">服务开始时间：</span>
                    <span>{{ engineer.serviceStartTime || '--' }}</span>
                  </div>
                  <!-- <div class="info-item">
                    <span class="label">实际完成时间：</span>
                    <span>{{ engineer.completeTime.split(' ')[0] || '--' }}</span>
                  </div> -->
                  <div class="info-item">
                    <span class="label">服务结束时间：</span>
                    <span>{{ engineer.serviceEndTime || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">所用工时：</span>
                    <span>{{ engineer.useTimes || '--' }}小时</span>
                  </div>
                </div>
   <div
                  v-if="engineer.workOrderPhotoList && engineer.workOrderPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">现场图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.workOrderPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.workOrderPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="engineer.handleResultPhotoList && engineer.handleResultPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">处理结果图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.handleResultPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.handleResultPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>

                <div v-if="engineer.serviceReorder" class="info-item">
                  <span class="label">服务复盘：</span>
                  <div class="service-reorder">
                    <pre>{{ engineer.serviceReorder }}</pre>
                  </div>
                </div>

                <div v-if="engineer.completeRemark" class="info-item">
                  <span class="label">备注：</span>
                  <div>{{ engineer.completeRemark }}</div>
                </div>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无工程师服务信息"></el-empty>
        </template>
    </avue-form>
    <template #footer v-if="currentRow.payStatus == 0">
      <div style="border-top: 1px dashed #ccc; padding-top: 5px">
        <el-form ref="form" :model="form" style="width: 100%" label-width="80px">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="审核结果">
                <el-radio-group v-model="auditForm.payStatus">
                  <el-radio :label="1">通过</el-radio>
                  <el-radio :label="3">驳回</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="支付金额">
                <el-input
                  style="width: 100%"
                  v-model="auditForm.totalPrice"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="备注">
            <el-input
              style="width: 100%"
              v-model="auditForm.auditRemark"
              placeholder="请输入审核备注"
              type="textarea"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div style="width: 100%; text-align: right">
        <el-button @click="drawerVisible = false">关 闭</el-button>
        <el-button type="primary" @click="confirm">提 交</el-button>
      </div>
    </template>
  </el-drawer>
    <wokerOrderDetail @success="onLoad()" :payId=currentRow.id :applyInfo="currentRow" :payStatus=currentRow.payStatus
      ref="wokerOrderDetailRef"></wokerOrderDetail>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wokerOrderSelect from './component/wokerOrderSelect.vue';
import wokerOrderDetail from './component/wokerOrderDetail.vue';
let wokerOrderSelectRef = ref(null);
let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        {
          type: 'date',
          label: '预约时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'reservationTime',
          rules: [
            {
              required: true,
              message: '请选择预约时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '故障类型',
          prop: 'lables',
          type: 'select',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
          span: 12,
        },
        // {
        //   label: '现场图',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'string',
        //   loadText: '附件上传中，请稍等',
        //   span: 24,
        //   slot: true,
        //   prop: 'workOrderPhotos',
        //   addDisplay: true,
        //   editDisplay: true,
        //   viewDisplay: false,
        //   listType: 'picture-card',
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
        {
          label: '服务要求',
          prop: 'taskDescription',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop:'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 发布人
        {
          label: '发布人',
          prop: 'createName',
          span: 12,
        },

        // 派单人
        {
          label: '派单人',
          prop: 'projectLeaderName',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 24,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '工程师服务信息',
          prop: 'engineerServiceInfo',
          labelWidth: 0,
          span: 24,
          formslot: true,
        },
      ],
    },
  ],
});
let currentId = ref(null);
let currentRow = ref({});
const detailForm = ref({});
const drawerVisible = ref(false);

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  detailBtn: true,

  dialogWidth: '500',
  //   dialogType: 'drawer',
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      search: true,
    },
    {
      label: '关联工单',
      prop: 'objectName',
      type: 'input',

      readonly: true,
      span: 24,
      placeholder: '请选择一个工单',
      click: row => {
        wokerOrderSelectRef.value.dialogVisible = true;
      },
    },
    // {
    //   label: '工单处理人',
    //   prop: 'handleUserName',
    //   type: 'input',
    //   span: 24,
    //   hide: true,
    // },
    {
      label: '付款金额',
      prop: 'totalPrice',
      type: 'number',
      span: 24,
    },
    {
      label: '申请备注',
      prop: 'applyContent',
      span: 24,
      type: 'textarea',
    },
    
    {
      label: '状态',
      prop: 'payStatus',
      display: false,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核通过',
        },
        {
          value: 2,
          label: '已付款',
        },
        {
          value: 3,
          label: '审核失败',
        },
      ],
    },
    {
      label: '审核备注',
      prop: 'auditRemark',
      overHidden: true,
    },
    {
      label: '申请人',
      prop: 'createName',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObjectPayment/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObjectPayment/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function openDetailDrawer(row) {
  currentId.value = row.id;
  currentRow.value = row;
  axios
    .get('/api/vt-admin/sealContractObject/detail', {
      params: {
        id: row.objectId,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      auditForm.value.payStatus = 1;
      auditForm.value.totalPrice = row.totalPrice;
      drawerVisible.value = true;
    });
}

function handleDetailSubmit(form, done) {
  // 此处可添加提交逻辑
  proxy.$message.success('详情已更新');
  done();
  drawerVisible.value = false;
  onLoad(); // 刷新列表
}

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        selectType: 1,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function handlewokerOrderSelectConfirm(data) {
  const { id, handleUserName, orderPrice, objectName } = data[0];
  form.value.objectId = id;
  form.value.objectName = objectName;
  form.value.handleUserName = handleUserName;
  form.value.totalPrice = orderPrice;
}
function permission(key, row) {
  console.log(key, row);

  if (['editBtn', 'delBtn'].includes(key)) {
    if (row.payStatus == 0) {
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let auditForm = ref({});
function confirm() {
  axios
    .post('/api/vt-admin/sealContractObjectPayment/audit', {
      id: currentId.value,
      ...auditForm.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
      onLoad();
      drawerVisible.value = false;
    });
}
let wokerOrderDetailRef = ref(null);

function viewDetail(row) {
  currentRow.value = row
   wokerOrderDetailRef.value.open(row.objectId)
   const data = {
    payStatus: 1,
    totalPrice: row.totalPrice,
   
   }
   
   wokerOrderDetailRef.value.setAuditForm(data)
}
</script>

<style lang="scss" scoped>



/* 工程师服务信息样式 */
.engineer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.engineer-card {
  margin-bottom: 0;
  border-radius: 4px;
  overflow: hidden;
}

.engineer-card :deep(.el-card__header) {
  padding: 8px 12px;
  background-color: #f5f7fa;
}

.engineer-card :deep(.el-card__body) {
  padding: 10px 12px;
}

.engineer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.engineer-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.engineer-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 6px;
  margin-bottom: 6px;
}

.info-item {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

.label {
  font-weight: 600;
  color: #606266;
  margin-right: 3px;
  font-size: 12px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.result-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 2px;
  cursor: pointer;
  transition: transform 0.2s;
}

.result-image:hover {
  transform: scale(1.05);
}

.service-reorder {
  background-color: #f5f7fa;
  padding: 6px;
  border-radius: 2px;
  margin-top: 2px;
  font-size: 12px;
}

.service-reorder pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  font-size: 12px;
  line-height: 1.3;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.info-card {
  margin-bottom: 3px;
  background: white;
  border-radius: 0px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  // background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  padding: 16px 20px;
  color: #000;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 20px;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.description-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .file-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
