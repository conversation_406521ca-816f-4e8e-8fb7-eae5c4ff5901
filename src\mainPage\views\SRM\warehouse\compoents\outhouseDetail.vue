<template>
  <basic-container>
    <Title
      >出库详情

      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
      </template>
    </Title>
    <avue-form style="margin-top: 20px" :option="option" v-model="form">
      <template #outStatus>
        <el-tag effect="plain" :type="form.outStatus === 1 ? 'success' : 'danger'">{{
          form.outStatus === 0 ? '已出库' : '已收货'
        }}</el-tag>
      </template>
      <template #files>
        <File :fileList="form.attachList || []"></File>
      </template>
      <template #product>
        <el-table class="avue-crud" :data="form.detailVOS" border style="margin-left: -20px">
          <el-table-column
            label="设备名称"
            show-overflow-tooltip
            prop="productName"
          ></el-table-column>
          <el-table-column
            label="规格型号"
            show-overflow-tooltip
            prop="productSpecification"
          ></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column
            label="产品描述"
            show-overflow-tooltip
            width="200"
            prop="description"
          ></el-table-column>
          <el-table-column label="品牌" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>
          <el-table-column label="序列号" prop="productSerialNumber">
            <template #default="{ row }">
              <el-button type="primary" @click="editSerialNumber(row)" size="small">查看</el-button>
            </template>
          </el-table-column>

          <!-- <el-table-column label="实际供应商" #default="{ row }" prop="perSupplierId">
            <suppliyerSelect
              size="small"
              v-model="row.perSupplierId"
              :Url="`/api/vt-admin/supplier/page?productId=${row.productId}`"
            ></suppliyerSelect> -->
          <!-- </el-table-column> -->
        </el-table>
      </template>
    </avue-form>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import suppliyerSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
let route = useRoute();
let router = useRouter();
let { proxy } = getCurrentInstance();
let form = ref({});
let getDetail = async () => {
  let res = await proxy.$axios.get(`/api/vt-admin/purchaseOutStorage/detail?id=${route.query.id}`);
  form.value = {
    ...res.data.data,
    detailVOS: res.data.data.detailVOS
      ? res.data.data.detailVOS.map(item => {
          return {
            ...item.productVO,
            ...item,
          };
        })
      : [],
  };
};
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
let option = ref({
  labelWidth: 110,
  emptyBtn: false,
  submitBtn: false,
  detail: true,
  group: [
    {
      label: '基本信息',
      prop: 'order',
      span: 12,
      row: true,
      column: [
        {
          label: '收货客户',
          prop: 'customerId',
          component: 'wf-customer-select',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '出库单号',
          prop: 'outStorageCode',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '出库类型',
          prop: 'outStorageType',
          type: 'radio',

          dicData: [
            {
              value: 0,
              label: '订单出库',
            },
            {
              value: 1,
              label: '借测出库',
            },
            {
              value: 2,
              label: '其他',
            },
            {
              value: 3,
              label: '采购退货',
            },
            {
              value: 4,
              label: '其他',
            },
          ],
          rules: [{ required: true, message: '请选择出库类型' }],
        },
        {
          label: '关联供应商',
          prop: 'supplierName',
        },
        {
          label: '出库数量',
          prop: 'number',
        },

        {
          label: '出库时间',
          prop: 'createTime',
        },

        {
          label: '出库人',
          prop: 'createName',
        },
        {
          label: '快递单号',
          prop: 'expressNumber',
        },
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict-biz/dictionary?code=courierCompany',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },

          prop: 'courierCompany',
        },
        {
          label: '收货状态',
          prop: 'outStatus',
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '出库信息',
      prop: 'order',
      span: 12,

      row: true,
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'product',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    {
      label: '收货信息',
      prop: '',
      column: [
        {
          label: '收货人',
          prop: 'addressee',
        },
        {
          label: '收件人地址',
          prop: 'addresseeAddress',
        },
        {
          label: '收件人电话',
          prop: 'addresseePhone',
        },
        {
          label: '到货时间',
          prop: 'arriveDate',
        },
      ],
    },
    // {
    //   label: '付款信息',
    //   prop: 'order',
    //   span: 12,
    //   row: true,
    //   column: [],
    // },
  ],
});
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };
  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  return form;
}
function editSerialNumber(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑序列号',
    option: {
      column: [
        {
          label: '序列号',
          prop: 'productSerialNumber',
          type: 'textarea',
          value: row.productSerialNumber,
          span: 24,
          readonly: true,
          rows: 8,
        },
      ],
    },
    callback(res) {},
  });
}
</script>

<style></style>
