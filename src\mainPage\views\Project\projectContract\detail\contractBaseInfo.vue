<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #contractFiles> <File :fileList="form.attachList || []"></File> </template>
      <template #customerInvoiceId>
        <span v-if="!form.customerInvoiceInfoVO"
          >暂无</span
        >
        <el-form v-else inline label-position="left">
          <el-form-item label="客户公司名称：">
            <el-tag effect='plain'>{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.invoiceCompanyName
            }}</el-tag>
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            <el-tag effect='plain'>{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.ratepayerIdentifyNumber
            }}</el-tag>
          </el-form-item>
          <el-form-item label="开户银行：">
            <el-tag effect='plain'>{{ form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.bankName }}</el-tag>
          </el-form-item>
          <el-form-item label="初期末开票余额：">
            <el-text type="primary">{{
              form.customerInvoiceInfoVO &&
              parseFloat(form.customerInvoiceInfoVO.endTermNoInvoiceAmount).toLocaleString()
            }}</el-text>
          </el-form-item>
        </el-form>
      </template>
      <template #deepenStatus>
        <el-tag effect='plain' type="info" v-if="form.deepenStatus == 0">无需设计</el-tag>
        <el-tag effect='plain' type="info" v-if="form.deepenStatus == 1">未开始</el-tag>
        <el-tag effect='plain' type="warning" v-if="form.deepenStatus == 2">进行中</el-tag>
        <el-tag effect='plain' type="success" v-if="form.deepenStatus == 3">已完成</el-tag>
      </template>
    </avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: {
    type: Object,
    default: () => {
      return { customerInvoiceInfoVO: {} };
    },
  },

  isEdit: Boolean,
});
watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
  if(props.sealContractId){
    getDetail()
    console.log('查询详情');
  }
});
const route = useRoute();
let detailOption = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '关联报价',
          display: true,
          component: 'wf-quotation-select',
          prop: 'offerId',
        },
        {
          type: 'input',
          label: '关联商机',
          display: true,
          disabled: true,
          prop: 'businessOpportunityName',
        },
        {
          label: '客户名称',
          span: 12,
          display: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
         
          
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        {
          type: 'switch',
          label: '深化设计',
          span: 12,
          display: true,
          prop: 'isNeedDeepen',
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          type: 'switch',
          label: '技术人员',
          span: 12,
          display: true,
          prop: 'isNeedTechnology',
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '深化设计状态',
          type: 'input',
          prop: 'deepenStatus',
        },
      ],
    },
    {
      label: '合同信息',
      prop: 'contractInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '合同编号',
          span: 12,
          display: true,
          readonly: true,
          placeholder: '自动生成',
          prop: 'contractCode',
        },
        {
          type: 'input',
          label: '项目名称',
          span: 12,
          display: true,
          prop: 'contractName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '对方订单编号',
          span: 12,
          display: true,
          prop: 'customerOrderNumber',
        },
        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '合同金额必须填写',
            },
          ],
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 24,
          prop: 'paymentDeadline',
          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '结算方式',
          type: 'radio',
          prop: 'settlementMethod',
          span: 24,
          dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '确认附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
      ],
    },
    {
      label: '送货信息',
      prop: 'distributionInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              deliveryDate: {
                label:
                  val == 3
                    ? '送货日期'
                    : val == 1
                    ? '发货日期'
                    : val == 4
                    ? '交付日期'
                    : '自提日期',
              },
              deliveryAddress: {
                label: val == 3 ? '收货地址' : val == 1 ? '收货地址' : val == 4 ? '交付地址' : '',
                display: val != 2 ? true : false,
              },
            };
          },
        },
        {
          type: 'date',
          label: '交付日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'deliveryDate',
          required: true,
          rules: [
            {
              required: true,
              message: '交付日期必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '交付地址',
          span: 24,
          display: true,
          prop: 'deliveryAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'contact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'contactPhone',
        },
        {
          type: 'input',
          label: '开票信息',
          span: 24,
          display: true,
          prop: 'customerInvoiceId',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: route.query.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
function getDetail() {
  
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: props.sealContractId,
      },
    })
    .then(res => {
      
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
  
      
    });
}
</script>

<style lang="scss" scoped></style>
