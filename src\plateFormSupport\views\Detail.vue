<template>
  <div class="detail-page">
    <!-- 页面头部 -->
    <div class="page-header bg-gradient-to-r from-blue-600 to-blue-800 text-white py-16 mt-16">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
        
          <p class="text-xl md:text-2xl mb-6 fade-in" v-if="detailData.introduction">
            {{ detailData.introduction }}
          </p>
          <div class="flex items-center justify-center gap-4 text-sm opacity-80 fade-in">
            <span v-if="detailData.createTime">发布时间：{{ formatDate(detailData.createTime) }}</span>
            <span v-if="detailData.author">作者：{{ detailData.author }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
          <!-- 加载状态 -->
          <div v-if="loading" class="text-center py-16">
            <el-icon class="is-loading text-blue-600 text-4xl mb-4">
              <Loading />
            </el-icon>
            <p class="text-gray-600">正在加载内容...</p>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="error" class="text-center py-16">
            <div class="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">加载失败</h2>
            <p class="text-gray-600 mb-6">{{ error }}</p>
            <el-button type="primary" @click="fetchDetailData">重新加载</el-button>
          </div>

          <!-- 内容区域 -->
          <div v-else class="content-wrapper">
           

            <!-- 富文本内容 -->
            <div class="rich-content bg-white rounded-lg shadow-lg p-8 fade-in">
              <div 
                v-if="detailData.content" 
                class="prose prose-lg max-w-none"
                v-html="detailData.content"
              ></div>
              <div v-else class="text-center py-16 text-gray-500">
                <div class="text-4xl mb-4">📄</div>
                <p>暂无详细内容</p>
              </div>
            </div>

            <!-- 相关信息 -->
            <div v-if="detailData.tags || detailData.category" class="meta-info mt-8 fade-in">
              <div class="bg-white rounded-lg shadow-lg p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">相关信息</h3>
                <div class="flex flex-wrap gap-4">
                  <div v-if="detailData.category" class="flex items-center gap-2">
                    <span class="text-gray-600">分类：</span>
                    <el-tag type="primary">{{ detailData.category }}</el-tag>
                  </div>
                  <div v-if="detailData.tags && detailData.tags.length > 0" class="flex items-center gap-2">
                    <span class="text-gray-600">标签：</span>
                    <el-tag 
                      v-for="tag in detailData.tags" 
                      :key="tag" 
                      type="info" 
                      class="mr-2"
                    >
                      {{ tag }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>

            <!-- 返回按钮 -->
            <div class="back-button mt-8 text-center fade-in">
              <el-button 
                type="primary" 
                size="large" 
                @click="goBack"
                class="px-8"
              >
                <el-icon class="mr-2">
                  <ArrowLeft />
                </el-icon>
                返回首页
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Loading, ArrowLeft } from '@element-plus/icons-vue'
import axios from 'axios'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(true)
const error = ref(null)
const detailData = ref({})

// 获取详情数据
const fetchDetailData = async () => {
  try {
    loading.value = true
    error.value = null
    
    const id = route.params.id
    if (!id) {
      throw new Error('缺少详情ID参数')
    }

    // 调用详情接口
    const response = await axios.get(`/api/vt-platform/platformPicture/detail?id=${id}`)
    
    if (response.data.code === 200) {
      detailData.value = response.data.data || {}
    } else {
      throw new Error(response.data.msg || '获取详情失败')
    }
    
  } catch (err) {
    console.error('获取详情数据失败:', err)
    error.value = err.message || '网络错误，请稍后重试'
    
    // 如果是网络错误，使用模拟数据
    if (err.message.includes('网络') || err.code === 'NETWORK_ERROR') {
      detailData.value = getMockData()
      error.value = null
    }
  } finally {
    loading.value = false
  }
}

// 模拟数据
const getMockData = () => {
  const id = route.params.id
  return {
    id: id,
    title: '智聚联云平台介绍',
    introduction: '专注于为客户提供高质量的技术产品和解决方案',
    pictureUrl: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80',
    content: `
      <h2>平台概述</h2>
      <p>智聚联云是一个专业的技术服务平台，致力于为企业和个人提供全方位的技术解决方案。我们拥有丰富的行业经验和专业的技术团队，能够满足不同客户的多样化需求。</p>
      
      <h3>核心优势</h3>
      <ul>
        <li><strong>技术创新：</strong>持续投入研发，采用最新的技术栈，为客户提供前沿的解决方案</li>
        <li><strong>品质保证：</strong>严格的质量控制体系，确保每一件产品都符合最高标准</li>
        <li><strong>专业服务：</strong>经验丰富的技术团队，提供全方位的技术支持和售后服务</li>
      </ul>
      
      <h3>服务范围</h3>
      <p>我们的服务涵盖多个领域，包括但不限于：</p>
      <ol>
        <li>品牌库管理与展示</li>
        <li>解决方案设计与实施</li>
        <li>技术咨询与培训</li>
        <li>系统集成与维护</li>
      </ol>
      
      <blockquote>
        <p>"客户的成功就是我们的成功，我们始终以客户需求为导向，提供最优质的服务。"</p>
      </blockquote>
      
      <h3>联系我们</h3>
      <p>如果您对我们的服务感兴趣，欢迎随时联系我们的专业团队，我们将为您提供详细的咨询和定制化的解决方案。</p>
    `,
    createTime: new Date().toISOString(),
    author: '智聚联云团队',
    category: '平台介绍',
    tags: ['技术服务', '解决方案', '专业团队']
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 返回首页
const goBack = () => {
  router.push('/')
}

// 页面加载时获取数据
onMounted(() => {
  fetchDetailData()
  
  // 添加滚动动画
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible')
      }
    })
  }, observerOptions)

  // 延迟执行，确保DOM已渲染
  setTimeout(() => {
    document.querySelectorAll('.fade-in').forEach(el => {
      observer.observe(el)
    })
  }, 100)
})
</script>

<style scoped>
/* 淡入动画 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 富文本内容样式 */
.rich-content :deep(.prose) {
  color: #374151;
  line-height: 1.75;
}

.rich-content :deep(.prose h2) {
  color: #1f2937;
  font-size: 1.875rem;
  font-weight: 700;
  margin-top: 2rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.rich-content :deep(.prose h3) {
  color: #374151;
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
}

.rich-content :deep(.prose p) {
  margin-bottom: 1rem;
}

.rich-content :deep(.prose ul),
.rich-content :deep(.prose ol) {
  margin-bottom: 1rem;
  padding-left: 1.5rem;
}

.rich-content :deep(.prose li) {
  margin-bottom: 0.5rem;
}

.rich-content :deep(.prose blockquote) {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
}

.rich-content :deep(.prose strong) {
  color: #1f2937;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 3rem 0;
  }
  
  .page-header h1 {
    font-size: 2rem;
  }
  
  .page-header p {
    font-size: 1.125rem;
  }
  
  .main-content {
    padding: 2rem 0;
  }
  
  .rich-content {
    padding: 1.5rem;
  }
}
</style>
