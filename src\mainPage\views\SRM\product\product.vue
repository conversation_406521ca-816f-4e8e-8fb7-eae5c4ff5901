<template>
  <basic-container>
    <el-container>
      <el-aside
        width="200px"
        style="margin-right: 20px; height: calc(100% - 30px); overflow: hidden"
      >
        <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
        </el-input>
        <avue-tree
          :option="treeOption"
          style="height: 660px"
          ref="tree"
          :data="treeData"
          @node-click="nodeClick"
        ></avue-tree>
      </el-aside>
      <el-main>
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @on-load="onLoad"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          :before-open="beforeOpen"
          @row-del="rowDel"
          :row-class-name="tableRowClassName"
          @search-reset="
            () => {
              params.productPropertyVoS = [];
              $refs.tree.setCurrentKey(null);
              categoryId = null;
              onLoad();
            }
          "
          @search-change="searchChange"
          @current-change="onLoad"
          @refresh-change="onLoad"
          @size-change="onLoad"
          @keyup.enter="onLoad"
          v-model="form"
        >
          <template #menu-left>
            <el-button plain icon="upload" @click="upload1" v-show="option.addBtn" type="primary"
              >导入</el-button
            >
          </template>
          <template #productName="{ row }">
            <el-tooltip :content="row.takeEffectReason" v-if="row.isTakeEffect == 1" placement="">
              <i
                style="color: var(--el-color-danger); font-size: 25px"
                class="element-icons el-icon-shixiaozhong"
              ></i>
            </el-tooltip>
            <el-tooltip
              :content="row.takeEffectReason"
              v-if="row.isSpecial == 1"
              effect="light"
              placement=""
            >
              <template #content>
                <Title>专项供应</Title>
                <div v-for="item in row.specialBusinessName && row.specialBusinessName.split(',')">
                  <el-text type="primary">《{{ item }}》</el-text>
                </div>
              </template>

              <i
                style="color: var(--el-color-primary); font-size: 20px; cursor: pointer"
                class="element-icons el-icon-biaoqian"
              ></i>
            </el-tooltip>
            <span>{{ row.productName }}</span>
          </template>
          <template #menu="{ row }">
            <el-button
              type="primary"
              v-if="permission['product:copy']"
              @click="copyData(row)"
              icon="CopyDocument"
              text
            >
              复制
            </el-button>
            <el-button
              type="primary"
              @click="editCategory(row)"
              v-if="permission['product:editCategory']"
              icon="edit"
              text
            >
              修改分类
            </el-button>
            <el-button
              text
              type="primary"
              icon="turnOff"
              v-if="row.isTakeEffect == 0 && permission['product:stop']"
              @click="stop(row)"
              >停产</el-button
            >
            <el-button
              text
              type="primary"
              icon="turnOff"
              v-if="row.isTakeEffect == 1 && permission['product:stop']"
              @click="restore(row)"
              >恢复</el-button
            >
          </template>
          <template #productCode="{ row }">
            <el-link type="primary" @click="toDetail(row)">{{ row.productCode }}</el-link>
          </template>
          <template #productProperty-form>
            <div>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="item in propertyList"
              >
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <el-tag effect="plain">{{ item.propertyName }}</el-tag>
                <span style="margin-left: 15px" v-if="item.type == 1">
                  <el-checkbox-group v-model="item.selectList">
                    <el-checkbox
                      v-for="i in item.valuesEntityList"
                      :label="i.id + `-` + i.value"
                      size="large"
                      >{{ i.value }}</el-checkbox
                    >
                  </el-checkbox-group>
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 0">
                  <el-radio
                    v-model="item.radioSelect"
                    v-for="i in item.valuesEntityList"
                    :label="i.id + `-` + i.value"
                    size="large"
                    @click.native.prevent="
                      item.radioSelect =
                        item.radioSelect == i.id + `-` + i.value ? '' : i.id + `-` + i.value
                    "
                    >{{ i.value }}</el-radio
                  >
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 2">
                  <el-input v-model="item.value" size="small"></el-input>
                </span>
              </div>
            </div>
          </template>
          <template #productParam-search>
            <div v-if="propertyList.length == 0">
              <el-alert :closable="false" size="small" type="info"
                >未配置参数或未选中三级分类</el-alert
              >
            </div>
            <div v-else>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="(item, index) in searchPropertyList"
              >
                <el-form>
                  <el-form-item style="font-weight: bold" :label="item.propertyName + ':'">
                    <el-tag
                      effect="plain"
                      :type="params.productPropertyVoS[index] == i.id ? 'success' : 'info'"
                      style="margin-right: 10px; cursor: pointer"
                      @click="handleClick(i, index)"
                      v-for="i in item.valuesEntityList"
                      >{{ i.value }}</el-tag
                    >
                  </el-form-item>
                </el-form>
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <!-- <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
                <span style="margin-left: 15px">
                  <span  v-for="i in item.valuesEntityList" style="margin-right: 10px;">{{ i.value }}</span>
                  <el-radio-group v-model="params.productPropertyVoS[index]">
                    <el-radio
                      v-for="i in item.valuesEntityList"
                      :label="i.id"
                      size="large"
                      >{{ i.value }}</el-radio
                    >
                  </el-radio-group>
                </span> -->
              </div>
            </div>
          </template>
          <template #productName-form>
            <el-autocomplete
              style="width: 100%"
              v-model="form.productName"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              value-key="productName"
              placeholder="请输入产品名称"
            >
              <template #default="{ item }">
                <div class="productBox">
                  <div><span>名称：</span>{{ item.productName }}</div>
                  <div><span>品牌：</span>{{ item.productBrand }}</div>
                  <div><span>型号：</span>{{ item.productSpecification }}</div>
                </div>
              </template>
            </el-autocomplete>
          </template>
          <template #productBrand-form>
            <el-autocomplete
              style="width: 100%"
              v-model="form.productBrand"
              :fetch-suggestions="querySearchBrand"
              :trigger-on-focus="false"
              placeholder="请输入品牌名称"
            >
            </el-autocomplete>
          </template>
          <template #productSpecification-form>
            <el-autocomplete
              style="width: 100%"
              v-model="form.productSpecification"
              :fetch-suggestions="querySearchSpecification"
              :trigger-on-focus="false"
              placeholder="请输入型号名称"
            >
            </el-autocomplete>
          </template>
        </avue-crud>
      </el-main>
    </el-container>
    <dialogForm ref="dialogForm"></dialogForm>
    <supplierSelect ref="supplier-select" check-type="box" @onConfirm="handleUserSelectConfirm">
    </supplierSelect>
    <el-dialog
      title="导入产品"
      v-model="dialogVisible"
      width="30%"
      class="avue-dialog avue-dialog--top"
    >
      <el-upload
        class="upload-demo"
        drag
        ref="upload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :limit="1"
        action="/api/vt-admin/product/importProduct"
        :headers="{
          [website.tokenHeader]: $store.getters.token,
          Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
        }"
        multiple
        :auto-upload="false"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div><el-link type="primary" slot="tip" @click.stop="download">下载模板</el-link></div>
      </el-upload>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="handleSubmit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <detail_drawer :id="currentId" ref="detailRef"></detail_drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { Base64 } from 'js-base64';
import { ref, getCurrentInstance, onMounted, computed, reactive ,onActivated} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import supplierSelect from '@/components/Y-UI/wf-supplier-select.vue';

import { useStore } from 'vuex';
import { ElNotification } from 'element-plus';
import progress from '@/components/progress/index.vue';
import detail_drawer from './compoents/detail_drawer.vue';
import { loadFile } from '@/utils/file';
let store = useStore();
let permission = computed(() => store.getters.permission);
let crud = ref(null)
const validator = (rules, value, callback) => {
  const { productBrand, productName, productSpecification, id = null,unit } = form.value;
  if (!productName) return callback();
  axios
    .get('/api/vt-admin/product/isExist', {
      params: {
        productBrand,
        productName,
        productSpecification,
        unit,
        id,
      },
    })
    .then(res => {
      if (res.data.data == 1) {
     
           crud.value.clearValidate()
        callback(new Error('系统已经存在此产品'));
      } else {
     
           crud.value.clearValidate()
        callback();
       
      }
    });
};
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchIndex: 4,
  searchIcon: true,
  searchMenuSpan: 6,
  searchSpan: 4,
  menuWidth: 320,
  border: true,
  column: [
    {
      label: '关键字',
      prop: 'keys',
      overHidden: true,
      placeholder: '名称，型号，品牌',
      display: false,
      search: true,
      hide: true,
    },
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,
      // placeholder: '自动生成',
      disabled: true,
      search: true,
    },
    {
      label: '产品类别',
      prop:'categoryName',
      addDisplay: false,
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      search: true,
      span: 24,
      component: 'wf-product-drop',
      type: 'input',
      rules: [
        {
          validator: validator,
          trigger: 'blur',
        },
      ],
    },
    {
      label: '产品分类',
      prop: 'categoryId',
      search: true,
      hide: true,
      filterable: true,
      type: 'tree',
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
      change: async val => {
        getParamsList(val.value);
        if (form.value.id) {
          propertyList.value = await getDetail(form.value.id);
        }
      },
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
        width:110,
      rules: [
        {
          validator: validator,
          trigger: 'blur',
        },
      ],
    },
    {
      label: '单位',
      type: 'select',
      width:60,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
          {
          validator: validator,
          trigger: 'change',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '协议商品',
      prop: 'isAgreement',
      type: 'radio',
      span: 12,   width:100,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    // {
    //   label: '成本价',
    //   prop: 'costPrice',
    //   type: 'number',
    //   hide: true,
    //   editDisplay: false,
    //   overHidden: true,
    // },
    {
      label: '是否专项',
      prop: 'isSpecial',
      type: 'radio',
      hide: true,
      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],

      overHidden: true,
      control: val => {
        console.log(val);
        return {
          specialBusiness: {
            display: !!val,
          },
        };
      },
    },
    {
      label: '成本价',
      prop: 'costPrice',
      type: 'number',
      // readonly: true,
      editDisplay: true,
      // placeholder: '自动计算',
      overHidden: true,
    },
    {
      label: '商机选择',
      prop: 'specialBusiness',
      hide: true,
      span: 24,
      placeholder: '请选择商机',
      component: 'wf-business-select',
      params: {
        checkType: 'checkbox',
        Url: '/vt-admin/businessOpportunity/page?selectType=2',
      },
    },
    {
      label: '参数查询',
      prop: 'productParam',
      hide: true,
      search: true,
      addDisplay: false,
      editDisplay: false,
      searchSpan: 24,
      searchSlot: true,
    },
    {
      label: '产品图片',
      prop: 'coverUrl',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        form.value.coverId = res.id;
        console.log(form.value);
        done();
      },
    },
    {
      label: '产品参数',
      prop: 'productProperty',
      type: 'input',
      hide: true,
      span: 24,
    },
    {
      label: '关联供应商',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      hide: true,
      editDisplay: false,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          console.log(222, proxy.$refs['supplier-select']);
          proxy.$refs['supplier-select'].visible = true;
        },

        column: [
          {
            label: '供应商名称',
            prop: 'supplierName',
            cell: false,
          },
          {
            label: '单价',
            prop: 'unitPrice',
            type: 'number',
            rules: [
              {
                required: true,
                message: '请输入单价',
              },
            ],
          },
        ],
      },
    },
    {
      label: '商品描述',
      prop: 'description',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },

    {
      label: '用途',
      prop: 'purpose',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  //   defaultExpandAll: true,
  menu: false,
  filter: false,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    value: 'id',
    children: 'children',
  },
  lazy: true,
  treeLoad: function (node, resolve) {
    axios
      .get('/api/vt-admin/productCategory/list', {
        params: {
          parentId: node.data.id,
        },
      })
      .then(res => {
        resolve(
          res.data.data.map(item => {
            return {
              ...item,
              leaf: !item.hasChildren,
            };
          })
        );
      });
  },
});
const addUrl = '/api/vt-admin/product/save';
const delUrl = '/api/vt-admin/product/remove?ids=';
const updateUrl = '/api/vt-admin/product/update';
const tableUrl = '/api/vt-admin/product/page';
let params = ref({
  productPropertyVoS: [],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  getTreeData();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        categoryId: categoryId.value,
        isNew: 0,
        valueIds: params.value.productPropertyVoS.filter(item => item).join(),
        productPropertyVoS: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function formatData(data, type) {
  return data.map(item => {
    let valuesDTOList = [];
    let value = '';
    if (item.type == 0) {
      if (!item.radioSelect) {
        valuesDTOList = [];
      } else {
        const [valuesId, value] = item.radioSelect && item.radioSelect.split('-');
        valuesDTOList = [
          {
            valuesId,
            value,
          },
        ];
      }
    } else if (item.type == 1) {
      valuesDTOList =
        item.selectList &&
        item.selectList.map(i => {
          const [valuesId, value] = i && i.split('-');
          return {
            valuesId,
            value,
          };
        });
    } else {
      value = item.value;
    }
    return {
      propertyId: type == 'add' ? item.id : item.propertyId,
      valuesDTOList,
      value,
    };
  });
}
function rowSave(form, done, loading) {
  const propertyDTOList = formatData(propertyList.value, form.id ? null : 'add');
  const data = {
    ...form,
    categoryId: categoryId.value,
    propertyDTOList,
    id: null,
    purchasePrice: null,
    sealPrice: null,
    marketPrice: null,
    referPurchasePrice: null,
    // costPrice: null,
    minSealPrice: null,
    referSealPrice: null,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const propertyDTOList = formatData(propertyList.value, 'edit');
  const data = {
    ...row,
    propertyDTOList,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function getDetail(id) {
  return new Promise((resolve, reject) => {
    axios.get('/api/vt-admin/product/detail?id=' + id).then(res => {
      const data = res.data.data.productPropertyVoS.map(item => {
        let radioSelect = '';
        let selectList = [];
        let value;
        if (item.type == 0) {
          if (item.entityList.filter(item => item.isCheck == 1).length > 0) {
            const { id, value } = item.entityList.filter(item => item.isCheck == 1)[0];
            radioSelect = `${id}-${value}`;
          }
        } else if (item.type == 1) {
          selectList = item.entityList
            .filter(item => item.isCheck == 1)
            .map(item => {
              const { id, value } = item;
              return `${id}-${value}`;
            });
        } else {
          value = item.value;
        }
        return {
          ...item,
          valuesEntityList: item.entityList,
          selectList,
          radioSelect,
          value,
        };
      });
      resolve(data);
    });
  });
}
async function beforeOpen(done, type) {
  if (type == 'edit') {
    propertyList.value = await getDetail(form.value.id);
  }
  if (type == 'add') {
    if (form.value.id) return done();
    getParamsList(categoryId.value);
  }
  done();
}
function searchChange(params, done) {
  page.value.currentPage = 1;
  console.log(page.value);
  // 搜索时不选中左侧分类树
  proxy.$refs.tree.setCurrentKey(null);
  categoryId.value = null;
  onLoad();
  done();
}
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/productCategory/list', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data
        ? res.data.data.map(item => {
            return {
              ...item,
              leaf: !item.hasChildren,
            };
          })
        : [];
    });
}
let filterText = ref('');
let categoryId = ref('');
console.log(permission.value, 'permission');

function nodeClick(val, accountName) {
  categoryId.value = val.id;
  getParamsList(val.id);
  params.value.productPropertyVoS = [];
  onLoad();
  if (val.leaf && permission.value['product:add']) {
    option.value.addBtn = true;
  } else {
    option.value.addBtn = false;
  }
  if (val.leaf && permission.value['product:edit']) {
    option.value.editBtn = true;
  } else {
    option.value.editBtn = false;
  }
}
let propertyList = ref([]);
let searchPropertyList = ref([]);
function getParamsList(categoryId) {
  // 获取参数
  axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId).then(res => {
    propertyList.value = res.data.data.propertyVOList.map(item => {
      return {
        ...item,
      };
    });
    searchPropertyList.value = res.data.data.propertyVOList.filter(item => item.type != 2);
  });
}
function handleClick(i, index) {
  if (params.value.productPropertyVoS[index] == i.id) {
    params.value.productPropertyVoS[index] = '';
  } else {
    params.value.productPropertyVoS[index] = i.id;
  }
}
async function copyData(row) {
  console.log(row);
  propertyList.value = await getDetail(row.id);
  categoryId.value = row.categoryId;
  form.value = row;
  proxy.$refs.crud.rowAdd();
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/supplier/detail?id=' + item).then(r => {
      form.value.supplierList.push({
        ...r.data.data,
        supplierId: item,
        id: null,
      });
    });
  });
}
function editCategory(row) {
  proxy.$refs.dialogForm.show({
    title: '修改分类',
    option: {
      column: [
        {
          label: '产品分类',
          prop: 'categoryId',
          search: true,
          hide: true,
          filterable: true,
          type: 'tree',
          rules: [
            {
              required: true,
              message: '请选择产品分类',
              trigger: 'change',
            },
          ],
          children: 'hasChildren',
          parent: false,
          dicUrl: '/api/vt-admin/productCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
        },
      ],
    },
    callback(res) {
      console.log(res);
      axios
        .post('/api/vt-admin/product/updateProductCategory', {
          id: row.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
let currentId = ref(null);
let detailRef = ref(null);
function toDetail(row) {
  currentId.value = row.id;

  detailRef.value.open();
}
let dialogVisible = ref(false);
function upload1() {
  console.log(111);
  dialogVisible.value = true;
}

function download() {
  // let a = document.createElement('a'); // 创建a标签
  // a.href = '/template/productImport.xlsx'; // 文件路径
  // a.download = '参数导入模板.xlsx'; // 文件名称
  // a.style.display = 'none'; // 隐藏a标签
  // document.body.appendChild(a);
  // // 定时器(可选)
  // setTimeout(() => {
  //   a.click(); // 模拟点击(要加)
  //   document.removeChild(a); //删除元素(要加)
  //   setTimeout(() => {
  //     self.URL.revokeObjectURL(a.href); // 用来释放文件路径(可选)
  //   }, 200);
  // }, 66);
  loadFile('/api/vt-admin/product/exportProductTemplate?categoryId=' + categoryId.value);
}
function handleSuccess(res) {
  console.log(res);
  if (res.code == 200) {
    proxy.$message({
      message: res.msg.split(',').join('<br>'),
      dangerouslyUseHTMLString: true,
      type: 'success',
      duration: 2000,
    });
  } else {
    proxy.$message({
      message: '数据有误',
      dangerouslyUseHTMLString: true,
      type: 'warning',
      duration: 2000,
    });
    // 获取文本内容
    var textContent = res.msg.replace(/,/g, '\n');

    // 创建 Blob 对象
    var blob = new Blob([textContent], { type: 'text/plain' });

    // 创建下载链接
    var downloadLink = document.createElement('a');
    downloadLink.href = URL.createObjectURL(blob);

    // 设置下载文件的名称
    downloadLink.download = '错误数据.txt';

    // 将下载链接添加到页面
    document.body.appendChild(downloadLink);

    // 模拟点击下载链接
    downloadLink.click();

    // 移除下载链接
    document.body.removeChild(downloadLink);
  }

  notice.value.close();
  proxy.$refs.upload.clearFiles();
  // dialogVisible.value = false
}
let notice = ref(null);
function handleError(res) {
  console.log(res);
  proxy.$message.error(res.data.msg);
  proxy.$refs.upload.clearFiles();
  notice.value.close();
}
function handleSubmit(params) {
  proxy.$nextTick(() => {
    proxy.$refs.upload.submit();
    notice.value = ElNotification({
      title: '导入中',
      position: 'bottom-right',
      duration: 0,
      message: h(progress, {
        // 事件要以onXxx的形式书写
        onFinish: status => {
          if (status.value == 'ok') {
            notice.close(); // 关闭ElNotification
          }
        },
      }),
    });
  });
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/product/list', {
      params: {
        keys: val,
        size: 50,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
function querySearchBrand(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/product/getBrandPage', {
      params: {
        productBrand: val,
      },
    })
    .then(res => {
      cb(
        res.data.data.map(item => {
          return {
            value: item,
          };
        })
      );
    });
}
function querySearchSpecification(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/product/getSpecificationPage', {
      params: {
        productSpecification: val,
      },
    })
    .then(res => {
      cb(
        res.data.data.map(item => {
          return {
            value: item,
          };
        })
      );
    });
}
function stop(row) {
  proxy.$refs.dialogForm.show({
    title: '停产',
    width: '50%',
    option: {
      column: [
        {
          label: '停产原因',
          prop: 'takeEffectReason',
          type: 'textarea',
          placeholder: '请输入停产原因',
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(res);
      axios
        .post('/api/vt-admin/product/updateProductEffect', {
          id: row.id,
          isTakeEffect: 1,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function restore(row) {
  proxy
    .$confirm('确认恢复？', '提示', {
      confirmButtonText: '确定',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/product/updateProductEffect', {
          id: row.id,
          isTakeEffect: 0,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          onLoad();
        });
    });
}
function tableRowClassName({ row }) {
  if (row.isTakeEffect == 1) {
    return 'info-row';
  } else {
    return '';
  }
}
</script>

<style lang="scss" scoped>
.productBox div {
  span {
    color: var(--el-color-primary);
    font-weight: bold;
  }
}
</style>
