<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <!-- <div class="stats-container" style="display: flex;">
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><document /></el-icon>
              工单总数
            </div>
            <el-text class="stat-value" type="primary">{{ totalNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><clock /></el-icon>
              进行中的工单
            </div>
            <el-text class="stat-value" type="warning">{{ inProgressNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><circle-check /></el-icon>
              已完成工单
            </div>
            <el-text class="stat-value" type="success">{{ completeNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><alarm-clock /></el-icon>
              总工时
            </div>
            <el-text class="stat-value" type="info">{{ completeHours }}h</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><coin /></el-icon>
              工单总金额
            </div>
            <el-text class="stat-value" type="danger">¥{{ totalPrice }}</el-text>
          </div>
          <div class="stat-item">
  <div class="stat-label">
    <el-icon><money /></el-icon>
    已结算金额
  </div>
  <el-text class="stat-value" type="success">¥{{ paymentPrice }}</el-text>
</div>
          <div class="stat-item">
  <div class="stat-label">
    <el-icon><wallet /></el-icon>
    未结算金额
  </div>
  <el-text class="stat-value" type="danger">¥{{ noPaymentPrice }}</el-text>
</div>
        </div> -->
      </template>
      <template #hours="{ row }">
        <el-link
          type="primary"
          :underline="false"
          @click="viewDetailForHour(row)"
          href=""
          target="_blank"
          >{{ row.hours }}</el-link
        >
      </template>
      <template #totalHumanPrice="{ row }">
        <el-link
          type="primary"
          :underline="false"
          @click="viewDetailForPay(row)"
          href=""
          target="_blank"
          >{{ row.totalHumanPrice }}</el-link
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <!-- <el-drawer title="详情" v-model="drawer" size="80%">
      <allWokerOrderForOrder :query="currentQuery"></allWokerOrderForOrder>
    </el-drawer> -->
    <el-drawer
      size="80%"
      title="实际工时记录"
      v-model="dialogVisibleForHour"
      class="avue-dialog avue-dialog--top"
    >
      <!-- <slot ></slot> -->
      <HoursList
        :humanId="currentRow.humanId"
        :projectId="currentRow.projectId"
        :menu="false"
      ></HoursList>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisibleForHour = false">关 闭</el-button>
        <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
      </div>
    </el-drawer>
    <payRecord
      ref="payRecordRef"
      :projectName="currentRow.projectName"
      :humanName="currentRow.userName"
      
    ></payRecord>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import HoursList from '@/views/Project/detail/hoursList.vue';
import payRecord from '@/views/Project/detail/payRecord.vue';
import { useRoute, useRouter } from 'vue-router';
// import allWokerOrderForOrder from './allWokerOrderForOrder.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  menu: false,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    {
      label: '工程师',
      prop: 'userName',
      width: 180,
      overHidden: true,

      display: false,
      search: true,
    },
    {
      label: '项目名称',
      prop: 'projectName',
      search: true,
      component: 'wf-project-drop',
    },
    {
      label: '客户名称',
      prop: 'customerName',
    },
    {
      label: '项目金额',
      prop: 'projectPrice',
      width: 150,
    },
    {
      label: '签订日期',
      prop: 'signDate',
      width: 150,
    },
    {
      label: '总工时',
      prop: 'hours',
      type: 'number',
      width: 120,
    },
    {
      label: '结算金额',
      prop: 'totalHumanPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,
      width: 150,
      labelWidth: 120,
      display: false,
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/projectHours/humanProjectPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNum = ref(0); // 工单总数
let inProgressNum = ref(0); // 进行中工单数
let completeNum = ref(0); // 完成工单数
let completeHours = ref(0); // 完成工时
let totalPrice = ref(0); // 工单总金额
let paymentPrice = ref(0); // 工单已结算金额
let noPaymentPrice = ref(0); // 工单未结算金额
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });

  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/technologyUserStatisticsMap', {
      params: {
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      totalNum.value = res.data.data.totalNum;
      inProgressNum.value = res.data.data.inProgressNum;
      completeNum.value = res.data.data.completeNum;
      completeHours.value = res.data.data.completeHours;
      totalPrice.value = res.data.data.totalPrice;
      paymentPrice.value = res.data.data.paymentPrice;
      noPaymentPrice.value = res.data.data.noPaymentPrice;
    });
}
let router = useRouter();

function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let currentRow = ref({});
let dialogVisibleForHour = ref(false);
function viewDetailForHour(row) {
  currentRow.value = row;
  dialogVisibleForHour.value = true;
}
let dialogVisibleForPay = ref(false);
let payRecordRef = ref(null);
function viewDetailForPay(row) {
  currentRow.value = row;
  payRecordRef.value.open();
}
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}
</style>
