/*
    字典
*/

const sex = [
  {
    value: 0,
    lable: '男',
  },
];
export default {
  sex,
};

export const followData = [
  {
    value: 0,
    label: '初访',
    color: '#7aafff',
  },
  {
    value: 1,
    label: '意向',
    color: '#2f7bff',
  },
  {
    value: 2,
    label: '合作',
    color: '#11a600',
  },
  {
    value: 3,
    label: '暂停',
    color: '#5e5e5e',
  },
  {
    value: 4,
    label: '未成交',
    color: '#b7b7b7',
  },
];

export const followType = [
  {
    value: 0,
    label: '客户跟进 ',
  },
  {
    value: 1,
    label: '商机跟进',
  },
  {
    value: 2,
    label: '项目跟进',
  },
  {
    value: 3,
    label: '订单跟进',
  },
];
// 客户类型
export const customerStatus = [
  {
    value: 0,
    label: '公共客户 ',
  },
  {
    value: 1,
    label: '跟进中',
  },
  {
    value: 2,
    label: '失效客户',
  },
];
// 商机阶段
export const businessOpportunityData = [
  {
    value: 0,
    label: '新增 ',
    color: '#7aafff',
  },
  {
    value: 1,
    label: '方案',
    color: '#2f7bff',
  },
  {
    value: 2,
    label: '报价',
    color: '#2f7bff',
  },
  {
    value: 3,
    label: '成交',
    color: '#11a600',
  },
  {
    value: 4,
    label: '失单',
    color: '#b7b7b7',
  },
  {
    value: 5,
    label: '暂停',
    color: '#b7b7b7',
  },
];
// 方案状态
export const programmeStatus = [
  {
    value: 3,
    label: '未开始',
  },
  {
    value: 1,
    label: '设计中',
  },
  // {
  //   value: 1,
  //   label: '--',
  // },
  {
    value: 2,
    label: '已完成',
  },
];
// 深化方案状态
export const deepProgrammeStatus = [
  {
    value: 1,
    label: '未开始',
  },
  {
    value: 2,
    label: '进行中',
  },
  // {
  //   value: 1,
  //   label: '--',
  // },
  {
    value: 3,
    label: '已完成',
  },
];

// 报价状态
export const offerStatus = [
  {
    value: 0,
    label: '报价中',
  },
  {
    value: 1,
    label: '报价设计完成',
  },
  {
    value: 3,
    label: '待客户确认',
  },
  {
    value: 2,
    label: '成交',
  },

  {
    value: 4,
    label: '失单',
  },
  {
    value: 5,
    label: '方案修改',
  },
  {
    value: 6,
    label: '暂停',
  },
];
// 投标状态
export const bidStatus = [
  {
    value: 0,
    label: '未投标',
  },
  {
    value: 1,
    label: '投标中',
  },
  {
    value: 2,
    label: '中标',
  },
  {
    value: 3,
    label: '未中标',
  },
];
// 审核状态
export const auditStatus = [
  {
    value: 0,
    label: '待审核',
  },
  {
    value: 1,
    label: '审核成功',
  },
  {
    value: 2,
    label: '审核失败',
  },
];
// 颜色数组
export const colorArr = [''];

// 任务类型

export const objectType = [
  {
    value: '0',
    label: '发货',
  },
  {
    value: '1',
    label: '送货上门',
  },
  {
    value: '2',
    label: '远程运维',
  },
  {
    value: '3',
    label: '上门服务',
  },
  {
    value: '4',
    label: '装机',
  },
  {
    value: '5',
    label: '拿货',
  },
];

// 计划名称
export const planNameData = [
  {
    label: '定金',
    value: '定金',
  },
  {
    label: '全款',
    value: '全款',
  },
  {
    label: '首期款',
    value: '首期款',
  },
  {
    label: '进度款',
    value: '进度款',
  },
  {
    label: '验收款',
    value: '验收款',
  },
  {
    label: '尾款',
    value: '尾款',
  },
  {
    label: '质保款',
    value: '质保款',
  },
  {
    label: '签证款',
    value: '签证款',
  },
  { value: '其它', label: '其它' },
];

// 右下角消息提醒类型
export const remindTypeData = [
  {
    value: 1,
    link: '/CRM/businessOpportunity/renewBusinessOpportunity',
    typeName: '续费商机到期',
  },
  {
    value: 2,
    link: '/CRM/businessOpportunity/qualityGuaranteeOpportunity',
    typeName: '维保商机到期',
  },
  {
    value: 3,
    link: '/Contract/customer/myCustomerContract',
    typeName: '合同未申请开票',
  },
  {
    value: 4,
    link: '/Contract/customer/myCustomerContract',
    typeName: '合同未申请收款',
  },
  {
    value: 5,
    link: '/Contract/customer/myCustomerContract',
    typeName: '计划收款逾期提醒',
  },

  {
    value: 6,
    link: '/CRM/programme/myProgramme',
    typeName: '方案待处理',
  },
  {
    value: 7,
    link: '/Project/deepSign/myDeepSign',
    typeName: '深化设计待处理',
  },
  {
    value: 8,
    link: '/CRM/programme/allocatedprogramme',
    typeName: '待分配技术人员',
  },
  {
    value: 9,
    link: '/CRM/programme/confirmprogramme',
    typeName: '方案待审核',
  },
  {
    value: 10,
    link: '/Project/engineeringProject/categoryProject',
    typeName: '待分配项目经理',
  },
  {
    value: 11,
    link: '/Contract/wokerOrder/todoWokerOrder',
    typeName: '工单待处理',
  },
  {
    value: 12,
    link: '/Project/engineeringProject/myProject',
    typeName: '项目工时未填写',
  },
  {
    value: 13,
    link: '/Project/engineeringProject/myProject',
    typeName: '项目计划未填写',
  },
  {
    value: 14,
    link: '/Project/engineeringProject/myProject',
    typeName: '我的项目',
  },
  {
    value: 15,
    link: '/SRM/procure/puchasePlan',
    typeName: '采购订单待审核',
  },
  {
    value: 16,
    link: '/Finance/invoice/invoice',
    typeName: '待开票',
  },
  {
    value: 17,
    link: '/Order/salesOrder/sealContractCancel',
    typeName: '合同撤销审核',
  },
  {
    value: 18,
    link: '/CRM/quotation/myquation',
    typeName: '报价待下载',
  },
  {
    value: 19,
    link: '/SRM/procure/order',
    typeName: '订单待采购',
  },
  // {
  //   value: 20,
  //   link: '/SRM/procure/order',
  //   typeName: '采购管理',
  // },
  {
    value: 21,
    link: '/CRM/quotation/assistquation',
    typeName: '协助报价待下载',
  },
  {
    value: 22,
    link: '/Finance/reimbursement/reimburseAudit',
    typeName: '报销审核',
  },
  {
    value: 23,
    link: '/Finance/payment/reimbursePayment',
    typeName: '报销申请待处理',
  },
  {
    value: 24,
    link: '/SRM/supplier/supplierBorrow',
    typeName: '供应商借货归还',
  },
  {
    value: 25,
    link: '/Finance/Collection/supplierBorrow',
    typeName: '供应商借货押金退还',
  },
  {
    value: 26,
    link: '/Finance/payment/supplierPayment',
    typeName: '供应商借货押金支付',
  },
  {
    value: 27,
    link: '/CRM/borrow/borrowMy',
    typeName: '客户借货归还',
  },
  {
    value: 28,
    link: '/Project/engineeringProject/payAudit',
    typeName: '项目工钱付款审核',
  },
  {
    value: 29,
    link: '/Project/engineeringProject/payApply',
    typeName: '项目工钱付款审核失败',
  },
  {
    value: 30,
    link:'/SRM/product/confirmproduct',
    typeName:'待确认产品'
  },
  {
    value: 31,
    link:'/Finance/payment/projectPay',
    typeName:'项目工钱付款'
  },
  {
    value: 32,
    link:'/Finance/Collection/projectCollection',
    typeName:'合同待收款'
  },
   {
    value: 33,
    link:'/Contract/wokerOrder/myWokerOrder',
    typeName:'任务待派单'
  },
   {
    value: 34,
    link:'/Contract/wokerOrder/wokerOrderPayAudit',
    typeName:'工单付款待审核'
  },
  {
    value: 35,
    link:'/SRM/warehouse/refundInhouseConfirm',
    typeName:'退货入库确认'
  },
];
