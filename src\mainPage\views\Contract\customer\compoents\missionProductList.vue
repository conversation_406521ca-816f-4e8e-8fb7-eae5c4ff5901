<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps({
  objectId: String,
  delBtn: {
    type: Boolean,
  },
});
watchEffect(() => {
  if (props.objectId) {
    onLoad();
  }
});

let option = ref({
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: true,
  header: false,
  //    menu:false,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品',
      prop: 'productId',
      bind: 'productDTO.productName',
      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      bind: 'productDTO.productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '工单数量/到货数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
      formatter: (row, column, cellValue) => {
        return `${row.number}/${row.arriveNumber}`;
      },
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      span: 12,
      width: 100,
      cell: false,
      dicData: [
        {
          value: 0,
          label: '未到货',
        },
        {
          value: 1,
          label: '部分到货',
        },
        {
          value: 2,
          label: '已到货',
        },
      ],
    },
    //   {
    //     label: '单价',
    //     prop: 'sealPrice',
    //     type: 'number',
    //     span: 12,
    //     cell: false,
    //   },
    //   {
    //     label: '金额',
    //     prop: 'totalPrice',
    //     type: 'number',
    //     span: 12,
    //     cell: false,
    //   },
  ],
});
watchEffect(() => {
  console.log(111);
  option.value.menu = props.delBtn;
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/sealContractObjectProduct/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObjectProduct/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        objectId: props.objectId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
