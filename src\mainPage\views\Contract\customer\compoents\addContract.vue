<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      编辑合同
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form">
      <template #customerInvoiceId>
        <wfInvoiceDrop v-model="form.customerInvoiceId" :id="form.customerId"></wfInvoiceDrop>
      </template>
      <template #deliveryAddress>
        <el-autocomplete
          v-model="form.deliveryAddress"
          style="width: 100%"
          :fetch-suggestions="querySearchAsync"
          value-key="deliveryAddress"
        />
      </template>
    </avue-form>
  </basic-container>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed, nextTick, watch } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import wfInvoiceDrop from './wf-invoice-drop.vue';
const form = ref({
  
});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

function validateName(rule, value, callback) {
  axios.get('/api/vt-admin/customer/existCustomerName?customerName=' + value).then(res => {
    if (res.data.data == 1) {
      callback(new Error('名字不能重复'));
    } else {
      callback();
    }
  });
}
watchEffect(() => {
  if (route.query.id) {
    setTimeout(() => {
      getDetail();
    },100)
  }
});
watchEffect(() => {
  if (route.query.projectId) {
    getProjectDetail();
  }
});
let userInfo = computed(() => store.getters.userInfo);
onMounted(() => {
  if (route.query.offerId) {
    getquotationDetail(route.query.offerId);
  }
});
let isPaymentPeriodData = ref([]);
const type = route.query.type;
console.log(type);
const option = ref({
  labelWidth: 120,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '关联报价',
          display: true,
          component: 'wf-quotation-select',
          prop: 'offerId',  disabled: true,
          value: route.query.offerId || null,
          params: {
            Url:
              type == 'assist'
                ? '/api/vt-admin/offer/pageForAddSealContract?selectType=3'
                : '/api/vt-admin/offer/pageForAddSealContract?selectType=0',
          },
          rules: [
            {
              required: true,
              message: '请选择关联报价',
              trigger: 'change',
            },
          ],
          change: val => {
            if (val.value) {
              getquotationDetail(val.value);
            }
          },
        },
        {
          type: 'input',
          label: '关联商机',
          display: true,
          disabled: true,
          placeholder: '请先选择报价',
          component: 'wf-business-select',
          prop: 'businessOpportunityId',
          change: val => {
            if (val.value) {
              getBusinessDetail(val.value);
            }
          },
        },
        {
          label: '对应客户',
          prop: 'customerId',
          component: 'wf-customer-select',
          params: {},  disabled: true,
          control: val => {
            console.log(val);
            return {
              contactPerson: {
                disabled: val ? false : true,
              },
            };
          },
          params:{
            Url:`/api/vt-admin/customer/page?type=${route.query.isAdmin == 1?'5':'0'}`,
          },
          change: ({ value }) => {
            handleCustomerIdChange(value);
          },
          rules: [
            {
              required: true,
              message: '请选择客户',
            },
          ],
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,  disabled: true,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},  disabled: true,
          change: ({ value }) => {
            setBaseInfo1(value);
          },
        },
        {
          type: 'input',
          label: '电话',
          span: 12,  disabled: true,
          display: true,
          prop: 'customerPhone',
        },
         {
                  label: '最终用户',
                  prop: 'finalCustomer',
                  span: 24,
                },
      ],
    },
    {
      label: '合同信息',
      prop: 'contractInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '是否合作合同',
          prop: 'isCooperation',
          type: 'radio',
          value: 5,
          disabled: true,
          span: 12,
          props: {
            label: 'label',
            value: 'value',
          },
          rules: [
            {
              required: true,
              message: '请选择合同类型',
            },
          ],
          value: 0,
          dicData: [
            {
              value: 0,
              label: '非合作合同',
            },
            {
              value: 1,
              label: '合作合同',
            },
          ],
          change: val => {
          

          
          },
          control: val => {
            ;
            return {
              
              cooperationType: {
                display: val == 1,
              },
              cooperationCompanyId: {
                display: val == 1,
              },
             managementFeePoints:{
              display:val == 1
             }
            };
          },
        },
          {
          label: '合作类型',
          prop: 'cooperationType',
          span: 12,

          type: 'radio',
          display: false,
          value: 0,
          labelTip: '挂靠伙伴请从商机或者报价流程',
          dicData: [
            {
              value: 1,
              label: '伙伴挂靠',
            },
            {
              value: 0,
             
              label: '挂靠伙伴',
            },
          ],
          // control: val => {
          //   return {
          //     cooperationCompanyId: {
          //       display: val == 0,
          //     },
          //     cooperationCompanyName: {
          //       display: val == 1,
          //     },
          //   };
          // },
        },
        //  {
        //   label: '合作伙伴',
        //   type: 'select',
        //    display: false,
        //   prop: 'cooperationCompanyId',

        //   props: {
        //     label: 'companyName',
        //     value: 'id',
        //   },
        //   dicFormatter: res => {
        //     return res.data.records;
        //   },
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择合作伙伴',
        //     },
        //   ],
        //   overHidden: true,
        //   cell: false,
        //   dicUrl: '/api/vt-admin/company/page?size=100',
        // },
       {
          label: '合作伙伴',
          prop: 'cooperationCompanyId',
          placeholder: '请选择合作伙伴',
          span: 12,
          component: 'wf-customer-select',
          rules: [
            {
              required: true,
              message: '请选择合作伙伴',
            },
          ],
          display: false,
        },
        {
          type: 'input',
          label: '合同名称',
          span: 12,
          display: true,
          prop: 'contractName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '对方订单编号',
          span: 12,
          display: true,
          prop: 'customerOrderNumber',
        },
        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,
          disabled: true,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '合同金额必须填写',
            },
          ],
        },
        {
          label: '是否开票',
          type: 'switch',
          prop: 'isNeedInvoice',
          value: 1,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
            control: val => {
            return {
              invoiceType: {
                display: val == 1,
              },
              billingCompany: {
                display: val == 1,
              },
              taxRate: {
                display: val == 1,
              },
            };
          },
        },
          {
          label: '是否预订单',
          type: 'switch',
          prop: 'isPreOrder',
          value: 0,

          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 12,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            isPaymentPeriodData.value = res.data;
            return res.data;
          },
          // control: (val, form, b, c) => {
          //   return {
          //     fixedBillingDate: {
          //       display:
          //         isPaymentPeriodData.value.find(item => item.id == val)?.dictValue == '固定账期',
          //     },
          //   };
          // },
          dicUrl: '/blade-system/dict-biz/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // {
        //   type: 'number',
        //   label: '固定账期时间',
        //   span: 12,
        //   display: true,
        //   min: 1,
        //   max: 31,
        //   tip: '输入1到31之间的数字,账期则为每月这个时间',
        //   prop: 'fixedBillingDate',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入固定账期时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        {
          label: '结算方式',
          type: 'radio',
          prop: 'settlementMethod',
          span: 12,
          dicUrl: '/blade-system/dict-biz/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
         {
            label:'技术人员',
            prop:'operationTechnology',
            component:'wf-user-select',
            checkType: 'checkbox',
            params:{
               userUrl:'/api/blade-system/search/user?functionKeys=engineer'
            },
            span:12
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
          {
          label: '开票公司',
          type: 'select',

          prop: 'billingCompany',

          props: {
            label: 'companyName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.records;
          },

          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },

          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
         
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          disabled: !(route.query.isAdmin == 1) ,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '管理费点数(%)',
          type: 'input',
          span: 12,
          display: false,
          prop: 'managementFeePoints',
          append: '%',
          rules: [
            {
              required: true,
              message: '请输入管理费点数',
            },
          ],
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
         {
          label: '确认附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        }, 
      ],
    },
    {
      label: '送货信息',
      prop: 'distributionInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          value: '1',
          dicUrl: '/blade-system/dict-biz/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              deliveryDate: {
                label:
                  val == 3
                    ? '送货日期'
                    : val == 1
                    ? '发货日期'
                    : val == 4
                    ? '交付日期'
                    : '自提日期',
              },
              deliveryAddress: {
                label: val == 3 ? '收货地址' : val == 1 ? '收货地址' : val == 4 ? '交付地址' : '',
                display: val != 2 ? true : false,
              },
            };
          },
        },
        {
          type: 'date',
          label: '交付日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'deliveryDate',
          required: true,
          // rules: [
          //   {
          //     required: true,
          //     message: '交付日期必须填写',
          //   },
          // ],
        },
        {
          type: 'select',
          label: '交付地址',
          span: 24,
          display: true,
          prop: 'deliveryAddress',
          dicData: [],
          props: {
            value: 'deliveryAddress',
            label: 'deliveryAddress',
          },
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'contact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},
          change: ({ value }) => {
            setBaseInfo2(value);
          },
        },
        {
          type: 'input',
          label: '联系电话',
          span: 12,
          display: true,
          prop: 'contactPhone',
        },
        {
          type: 'input',
          label: '开票信息',
          span: 24,
          display: true,
          prop: 'customerInvoiceId',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
  ],
});
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    contractFiles: form.contractFiles.map(item => item.value).join(','),
    // cooperationCompanyId:form.value.cooperationType == 1? form.value.cooperationCompanyName : form.value.cooperationCompanyId,
  };

  axios
    .post(`/api/vt-admin/sealContract/${form.id ? 'update' : 'save'}`, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        router.$avueRouter.closeTag();
        router.go(-1);
      }
    })
    .catch(() => {
      done();
    });
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        customerName,
        contactPersonName,
        contactPhone,
        address,
      } = res.data.data;
      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.contactPerson = contactPersonName;
      form.value.contactPhone = contactPhone;
      form.value.address = address;
    });
}
function getquotationDetail(id) {
  axios
    .get('/api/vt-admin/offer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        businessOpportunityId,
        customerName,
        customerContact,
        customerPhone,
        customerAddress,
        isHasOption,
        offerPrice,
        offerName,
        businessOpportunityName,
        fixedBillingDate,
        isPaymentPeriod,
      } = res.data.data;
      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.customerId = customerId;
      form.value.customerContact = customerContact;
      form.value.contact = customerContact;
      form.value.customerPhone = customerPhone;
      form.value.contactPhone = customerPhone;
      form.value.customerAddress = customerAddress;
      form.value.deliveryAddress = customerAddress;
      form.value.contractTotalPrice = offerPrice;
      form.value.contractName = businessOpportunityName || offerName;
      form.value.fixedBillingDate = fixedBillingDate;
      form.value.paymentDeadline = isPaymentPeriod;

      setUrl(form.value.customerId);
    });
}
// function setUrl(id) {
//   const contactPerson = proxy.findObject(option.value.group[0].column, 'customerContact');
//   console.log(contactPerson);
//   contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
// }
let lock = ref(true);
function getDetail() {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
        taxRate: res.data.data.taxRate ? '' + res.data.data.taxRate : '',
        cooperationCompanyName:res.data.data.cooperationCompanyId,
        contractFiles: res.data.data.attachList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        }),
      };
      
      console.log(form.value);
      const contactPerson = proxy.findObject(option.value.group[0].column, 'customerContact');
      const contact = proxy.findObject(option.value.group[2].column, 'contact');

      contactPerson.params.Url =
        '/vt-admin/customerContact/page?customerId=' + form.value.customerId;
      contact.params.Url = '/vt-admin/customerContact/page?customerId=' + form.value.customerId;
      
    });
}
function querySearchAsync(val, cb) {
  if (!form.value.customerId) return;
  axios
    .get('/api/vt-admin/sealContract/getAddressPage', {
      params: {
        customerId: form.value.customerId,
        size: 50,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
function setUrl(id) {
  const customerContact = proxy.findObject(option.value.group[0].column, 'customerContact');
  customerContact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
  const contact = proxy.findObject(option.value.group[2].column, 'contact');
  contact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
}
function setBaseInfo2(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contactPhone = res.data.data.phone;
    });
}
function setBaseInfo1(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerPhone = res.data.data.phone;
    });
}
function getProjectDetail() {
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.projectId,
      },
    })
    .then(res => {
      const { offerId, settlementMethod, invoiceType, deliveryDate } = res.data.data;
      form.value.offerId = offerId;
      form.value.settlementMethod = settlementMethod;
      form.value.invoiceType = invoiceType;
      form.value.contractDeliveryDate = deliveryDate;
      getquotationDetail(offerId);
    });
}
function handleCustomerIdChange(id) {
  axios.get('/api/vt-admin/customer/detail?id=' + id).then(res => {
    let data = res.data.data;
    form.value.customerAddress = data.address;
    form.value.customerContact = data.customerContactVO?.id;
    form.value.customerContact = data.customerContactVO?.id;
    form.value.customerPhone = data.customerContactVO?.phone;

    const contactPerson = proxy.findObject(option.value.group[0].column, 'customerContact');
    const contact = proxy.findObject(option.value.group[0].column, 'contact');

    contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
    contact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
  });
}
</script>

<style lang="scss" scoped></style>
