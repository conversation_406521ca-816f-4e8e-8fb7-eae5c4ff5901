# 产品检查功能实现说明

## 功能概述
在合同确认流程中，点击"检查产品"按钮，弹出抽屉显示产品列表，允许编辑产品单价，自动计算合同总额。

## 实现位置
文件：`src/mainPage/views/Contract/customer/myCustomerContract.vue`

## 功能特性

### 1. 触发条件
- 仅在 `editType == 1`（确认模式）时显示"检查产品"按钮
- 点击按钮前需要先保存合同（需要有合同ID）

### 2. 产品列表展示
抽屉中的产品列表包含以下列：
- **产品名称** (`customProductName`) - 显示产品名称，支持溢出提示
- **产品型号** (`customProductSpecification`) - 显示产品规格型号
- **品牌** (`productBrand`) - 显示产品品牌
- **单位** (`customUnit`) - 显示计量单位
- **描述** (`customProductDescription`) - 显示产品描述
- **数量** (`number`) - 只读显示，不可编辑
- **单价** (`sealPrice`) - **可编辑**，使用 `el-input-number` 组件
- **金额** - 自动计算（单价 × 数量）

### 3. 单价编辑功能
- 使用 `el-input-number` 组件
- 最小值：0
- 精度：2位小数
- 控制按钮位置：右侧
- 实时计算：单价变化时自动重新计算合同总额

### 4. 合同总额计算
- 显示在抽屉顶部的提示区域
- 公式：`合同总额 = Σ(产品单价 × 产品数量)`
- 实时更新：任何产品单价变化都会立即更新总额

### 5. 数据绑定
- 确认后，产品数据绑定到 `form.detailDTOList`
- 合同总额同步更新到 `form.contractTotalPrice`

## 技术实现

### 数据结构
```javascript
// 产品检查抽屉相关变量
let productDrawerVisible = ref(false);  // 抽屉显示状态
let productList = ref([]);              // 产品列表
let productLoading = ref(false);        // 加载状态
let productTotalAmount = ref(0);        // 合同总额
```

### 核心函数

#### 1. handleCheckProduct()
- 检查合同是否已保存
- 打开产品检查抽屉
- 调用获取产品列表接口

#### 2. getProductList()
- 调用接口：`/api/vt-admin/sealContract/productPage`
- 参数：
  - `current`: 1
  - `size`: 5000
  - `offerId`: 合同ID
- 初始化产品单价和数量（默认为0）
- 计算初始合同总额

#### 3. calculateTotalAmount()
- 遍历产品列表
- 累加每个产品的金额（单价 × 数量）
- 更新 `productTotalAmount`

#### 4. handlePriceChange()
- 单价输入框的 `@change` 事件处理
- 调用 `calculateTotalAmount()` 重新计算总额

#### 5. confirmProductCheck()
- 将产品列表数据绑定到 `form.detailDTOList`
- 更新 `form.contractTotalPrice` 为计算后的总额
- 关闭抽屉
- 显示成功提示

#### 6. cancelProductCheck()
- 关闭抽屉
- 不保存任何更改

## UI 组件

### 抽屉配置
```vue
<el-drawer 
  v-model="productDrawerVisible" 
  title="检查产品" 
  direction="rtl" 
  size="90%" 
  :before-close="cancelProductCheck"
>
```

### 提示信息
```vue
<el-alert type="info" :closable="false">
  <template #title>
    <div style="display: flex; justify-content: space-between;">
      <span>合同总额来自产品销售单价*数量，请完善产品销售单价</span>
      <span>合同总额：￥{{ productTotalAmount.toLocaleString() }}</span>
    </div>
  </template>
</el-alert>
```

### 产品表格
- 使用 `el-table` 组件
- 带边框、居中对齐
- 加载状态显示
- 单价列使用 `el-input-number` 可编辑

## 使用流程

1. 用户在合同列表中点击"确认"操作
2. 进入编辑模式（`editType = 1`）
3. 在合同总额字段旁边显示"检查产品"按钮
4. 点击"检查产品"按钮
5. 系统检查合同是否已保存
6. 打开产品检查抽屉
7. 加载产品列表数据
8. 用户编辑产品单价
9. 系统实时计算并显示合同总额
10. 用户点击"确认"保存更改
11. 数据绑定到 `form.detailDTOList` 和 `form.contractTotalPrice`
12. 关闭抽屉，显示成功提示

## 注意事项

1. **合同ID必须存在**：点击"检查产品"前必须先保存合同
2. **数据精度**：单价保留2位小数
3. **数量只读**：数量字段不可编辑，只能修改单价
4. **实时计算**：单价变化时立即更新合同总额
5. **数据绑定**：确认后数据会绑定到表单的 `detailDTOList` 字段
6. **取消操作**：点击取消不会保存任何更改

## 接口依赖

### 获取产品列表
- **接口**：`GET /api/vt-admin/sealContract/productPage`
- **参数**：
  - `current`: 当前页码
  - `size`: 每页数量
  - `offerId`: 合同ID
- **返回**：产品列表数据

## 样式说明

- 抽屉宽度：90%
- 表格列宽：
  - 产品名称：150px（最小宽度）
  - 产品型号：120px（最小宽度）
  - 品牌：100px（最小宽度）
  - 单位：80px
  - 描述：150px（最小宽度）
  - 数量：100px
  - 单价：150px
  - 金额：120px
- 所有列居中对齐
- 长文本支持溢出提示

## 测试建议

1. 测试未保存合同时点击"检查产品"的提示
2. 测试产品列表加载
3. 测试单价编辑功能
4. 测试合同总额实时计算
5. 测试确认保存功能
6. 测试取消操作
7. 测试数据绑定是否正确

