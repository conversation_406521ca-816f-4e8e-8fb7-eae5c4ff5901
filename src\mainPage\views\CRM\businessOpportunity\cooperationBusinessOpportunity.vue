<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @keyup.enter="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <!-- <el-button
          type="primary"
          @click="handleAdd"
          icon="Plus"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >新增</el-button
        > -->
        <div class="status_group">
          <statusGroup @click="handleStatusClick"></statusGroup>
        </div>
      </template>
      <template #stage="{ row }">
        <el-tooltip
          :content="row.failReason || row.sendBackReason || row.pauseReason"
          :disabled="
            !(
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
            )
          "
          placement=""
        >
          <el-tag
            effect="plain"
            :type="
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
                ? 'danger'
                : 'primary'
            "
            >{{
              row.$stage == '方案'
                ? `方案(${row.optionStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '报价'
                ? `报价(${row.offerStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '成交'
                ? `成交(${row.isHasOption == 1 ? '经方案' : '经报价'})`
               : row.$stage == '成交'?row.isFrameworkAgreement == 1?'协议成交':'成交': row.$stage
            }}</el-tag
          >
        </el-tooltip>
      </template>
      <template #name="{ row }">
        <div style="display: flex;align-items: center;">
           <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> <el-link type="primary" @click="handleView(row)">{{ row.name }}</el-link>
        </div>
      </template>
      <template #followDays="{row}">
        <el-tag   v-if="row.followDays" style="font-weight:bolder" effect="dark" round :type="row.followDays < 7?'success':row.followDays < 15?'warning':'danger'">{{row.followDays}}天</el-tag>
      </template>
      <template #menu="{ row, index }">
        <!-- <el-button type="primary" text @click="handleEdit(row)" icon="Edit">编辑</el-button> -->
        <!-- <el-button
          type="primary"
          text
          @click="handleView(row)"
          icon="View"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >详情</el-button
        > -->

        <!-- <div style="display: flex; justify-content: center">
          <div>
            <el-button
              type="primary"
              text
              @click="follow(row)"
              icon="Service"
              v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
              >跟进</el-button
            >
          </div>
          <div
            v-if="
              row.stage != 3 &&
              (route.query.type == 0 || route.query.type == 2 || !route.query.type)
            "
          >
            <el-button
              @click="handleCommand(1, row)"
              text
              icon="DocumentAdd"
              type="primary"
              :command="1"
              v-if="row.stage == 0"
              >方案</el-button
            >

            <el-button
              :command="2"
              icon="Tickets"
              @click="handleCommand(2, row)"
              v-if="row.stage == 1 && row.optionStatus == 2"
              text
              type="primary"
              >报价</el-button
            >
            
            <el-button
              :command="3"
              @click="handleCommand(3, row)"
              text
              icon="SetUp"
              type="primary"
              v-if="row.stage != 3"
              >登记</el-button
            >
            <el-button
              :command="5"
              @click="handleCommand(5, row)"
              text
              icon="Tickets"
              type="primary"
              v-if="row.stage == 0"
              >报价</el-button
            >
            <el-button
              :command="6"
              @click="handleCommand(6, row)"
              text
              type="primary"
              v-if="row.bidStatus == 0 && row.classify == 1"
              >投标</el-button
            >
          </div>
        </div> -->
        <el-button type="primary"  @click="$refs.crud.rowDel(row)" v-if="row.stage != 3" text icon="delete">删除</el-button>
      </template>
      <template #bidStatus="{ row }">
        <el-tag effect="plain" :type="['info', 'warning', 'success', 'danger'][row.bidStatus]">{{
          row.$bidStatus
        }}</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <detail_drawer
      :type="currentType"
      :id="currentId"
      :customer-id="customerId"
      ref="detailRef"
    ></detail_drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getDeptTree, getDeptLazyTree } from '@/api/system/dept';
import { useStore } from 'vuex';
import { businessOpportunityData, bidStatus } from '@/const/const.js';
import statusGroup from './compoents/statusGroup.vue';
import detail_drawer from './compoents/detail_drawer.vue';
let route = useRoute();
let store = useStore();
let permission = computed(() => store.getters.permission);
console.log(permission.value.business_menu);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,

  searchIndex: 4,
  searchIcon: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 100,
  menu: false,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      component: 'wf-bussiness-drop',
      overHidden: true,
      search: !route.query.id,
    },
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,
    //   width: 85,

    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    // },
    {
      // type: 'tree',
      label: '业务板块',
      // multiple: true,
      width: 140,
      span: 12,
      parent: false,
      search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择商机关联',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'type',
      checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },
    {
      label: '商机描述',
      prop: 'description',
 
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      // search: !route.query.id,
      overHidden: true,
      search: false,
      width: 85,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机阶段',
      prop: 'stage',
      width: 100,
      slot: true,
      type: 'select',
      search: !route.query.id,
      dicData: businessOpportunityData,
    },
    {
      label: '投标状态',
      prop: 'bidStatus',
      width: 100,
      hide: true,
      slot: true,
      type: 'select',
      // search: !route.query.id,
      search: false,
      dicData: bidStatus,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      component: 'wf-customer-drop',
      search: !props.customerId,
      hide: !props.customerId,
    },

    {
      label: '业务员',
      prop: 'businessPersonName',
      component: 'wf-user-drop',
      search: true,
      // hide: true,
      width:110,
      formatter: (row, column, cellValue) => {
        return row.businessPersonName;
      },
    },
    {
      label:'售前技术',
      prop: 'technicalPersonnelName',
      width:100
    },
    
    {
      label: '登记时间',
      width: 135,
      prop: 'createTime',
      type: 'datetime',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      type: 'datetime',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
      width: 100,
    },
    {
      label: '介绍人',
      width: 120,
      prop: 'introducer',
      type: 'input',
    },
    {
      label: '未更新',
      width: 80,
      prop: 'followDays',
      type: 'input',
      html:true,
      formatter(row) {
        if(!row.followDays )return null
        return `<span style="color: ${row.followDays > 15? 'red' : 'green'}">${row.followDays}天</span>`;
      }
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },

    {
      label: '部门',
      prop: 'createDept',
      type: 'tree',
      lazy: true,
      treeLoad: function (node, resolve) {
        const parentId = node.level === 0 ? 0 : node.data.id;
        getDeptLazyTree(parentId).then(res => {
          resolve(
            res.data.data.map(item => {
              return {
                ...item,
                leaf: !item.hasChildren,
              };
            })
          );
        });
      },
      props: {
        labelText: '标题',
        label: 'title',
        value: 'value',
        children: 'children',
      },
      search: true,
      hide: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
const props = defineProps(['customerId']);
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 4,
        customerId: props.customerId,
        startTime: params.value.createTime ? params.value.createTime[0] : null,
        endTime: params.value.createTime ? params.value.createTime[1] : null,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

let customerId = ref(null);
let currentType = ref(null);
let currentId = ref(null);
let detailRef = ref(null);
function handleView(row) {
  router.push({
    path: '/CRM/businessOpportunity/compoents/detail',
    query: {
      type: 'detail',
      businessId: row.id,
      customerId: row.customerId,
    },
  });
}

function handleStatusClick(value) {
  params.value.stage = value;
  onLoad();
}
</script>

<style lang="scss" scoped></style>
