<template>
  <div ref="projectFile_container">
    <el-row :gutter="20" style="height: 100%">
      <el-col style="height: 100%; overflow-y: scroll" :span="5">
        <avue-tree
          ref="tree"
          style="height: 100%"
          :option="treeOption"
          :data="treeData"
          @node-click="nodeClick"
          @save="handleSave"
          @del="handleDel"
          :before-open="handleOpen"
          @update="handleUpdate"
          node-key="id"
        >
          <template #name-form="{ type }">
            {{ type }}
          </template>
        </avue-tree>
      </el-col>
      <el-col style="height: 100%" :span="19">
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          :before-open="beforeOpen"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          @keyup.enter="onLoad"
          @row-del="rowDel"
          @search-reset="onLoad"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          @selection-change="handleSelectionChange"
          v-model="form"
        >
          <template #menu-left>
            <el-button type="primary" @click="handleAddFile" icon="plus">新增文件</el-button>
            <el-button type="primary" @click="handleAdd" icon="plus">新增文件夹</el-button>
            <el-button type="primary" plain @click="handleDownload(0)" icon="download"
              >下载选中目录</el-button
            >
            <el-button type="primary" plain @click="handleDownload(1)" icon="download"
              >下载选中文件</el-button
            >
          </template>
          <template #file="{ row }">
            <File :fileList="[row.attach] || []"></File>
          </template>
        </avue-crud>
      </el-col>
    </el-row>
  </div>

  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer
    :title="`添加文件夹(${currentCatalogueName})`"
    v-model="drawer"
    direction="rtl"
    size="50%"
  >
    <el-form label-width="80px">
      <el-form-item label="附件">
        <div style="width: 100%">
          <div class="upload-container">
            <input
              type="file"
              id="folderUpload"
              webkitdirectory
              directory
              multiple
              @change="handleFolderSelect"
              style="display: none"
            />
            <label for="folderUpload" class="el-button el-button--primary"> 选择文件夹 </label>

            <div v-if="selectedFolder" style="margin-top: 10px">
              已选择文件夹：{{ selectedFolder }}
            </div>
          </div>

          <el-tree
            :data="fileTree"
            :props="treeProps"
            node-key="path"
            default-expand-all
            style="margin-top: 20px"
          >
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon v-if="data.type === 'directory'" class="folder-icon">
                  <FolderOpened />
                </el-icon>
                <el-icon v-else class="file-icon">
                  <Document />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div style="flex: auto; display: flex; justify-content: flex-end; gap: 10px">
        <el-button @click="drawer = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="uploadLoading">
          提交上传
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { downloadByUrl } from '@/utils/download';

let option = ref({
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  rowKey: 'id',
  height: 'auto',
  index: true,
  calcHeight: 30,
  addBtn: false,
  labelWidth: 120,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 170,
  selection: true,
  reserveSelection: true,
  border: true,
  column: [
    {
      label: '附件',
      prop: 'file',
      type: 'upload',
      dataType: 'object',
      dragFile: true,
      multiple: true,
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      uploadExceed(error, column) {
        proxy.$message.error('限制一个,请先删除当前文件');
      },
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      uploadAfter: (res, done, file, fileList) => {
        done();
      },
      change: value => {
        console.log(value);
        form.value.name = value.value.map(item => item.label).join(',');
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '文件名字',
      prop: 'name',
      type: 'input',
      span: 24,
    },
    {
      label: '文件目录',
      prop: 'catalogueName',
      type: 'input',
      display: false,
      overHidden: true,
      span: 24,
    },
    {
      label: '文件在线地址',
      prop: 'onlineAddress',
      type: 'input',
      span: 24,
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    {
      label: '上传人',
      prop: 'createName',
      type: 'input',
      display: false,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps(['projectId']);
const addUrl = '/api/vt-admin/projectFiles/save';
const delUrl = '/api/vt-admin/projectFiles/remove?ids=';
const updateUrl = '/api/vt-admin/projectFiles/update';
const tableUrl = '/api/vt-admin/projectFiles/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let currentCatalogueId = ref('');
let currentCatalogueName = ref('');

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
        catalogueId: currentCatalogueId.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function handleAddFile() {
  if (!currentCatalogueId.value) {
    proxy.$message.error('请先选择目录');
    return;
  }
  proxy.$refs.crud.rowAdd();
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
    fileId: form.file.map(item => item.value).join(','),
    catalogueId: currentCatalogueId.value,
    projectId: route.query.id,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    fileId: row.file.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.file = [
      {
        value: (form.value.attach || {}).id,
        label: (form.value.attach || {}).originalName,
      },
    ];
  }
  done();
}
// 左边树操作
onMounted(() => {

 
  getTreeData();
  onLoad();
});
let treeData = ref([]);
let treeOption = ref({
  defaultExpandAll: false,
  addBtn: true,
  dialogCustomClass: 'projectFileCrud',
  menuPosition: 'right',
  multiple: true,
  checkStrictly: true,
  formOption: {
    labelWidth: 100,
    dialogDrag: true,

    column: [
      {
        label: '目录名称',
        prop: 'name',
        span: 24,
        type: 'checkbox',
        // multiple: true,
        dicUrl: '/blade-system/dict-biz/dictionary?code=projectFileCatalogue',
        props: {
          label: 'dictValue',
          value: 'dictValue',
        },
      },
    ],
  },
  props: {
    label: 'name',
    value: 'id',
    children: 'children',
    disabled: 'parentId',
  },
});
function getTreeData() {
  axios
    .get('/api/vt-admin/projectFileCatalogue/tree', {
      params: {
        size: 5000,
        projectId: route.query.id,
      },
    })
    .then(res => {
      treeData.value = res.data.data;
      // proxy.$nextTick(() => {
      //   proxy.$refs.tree.setCurrentKey(treeData.value[0].id);
      //   currentCatalogueId.value = treeData.value[0].id;
      //   onLoad();
      // });
    });
}
function handleSave(parent, data, done, loading) {
  const value = {
    list: Array.isArray(data.name)
      ? data.name.map(item => {
          return {
            name: item,
            parentId: parent.data?.id,
          };
        })
      : [{ name: data.name, parentId: parent.data?.id }],
    projectId: props.projectId,
  };
  axios
    .post('/api/vt-admin/projectFileCatalogue/batchAdd', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function handleUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/projectFileCatalogue/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function handleDel(form) {
  console.log(form);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/projectFileCatalogue/remove?ids=' + form.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
      });
    })
    .catch(() => {});
}

function nodeClick(node) {
  currentCatalogueId.value = node.id;
  currentCatalogueName.value = node.name;
  onLoad();
}

function handleOpen(done, type) {
  console.log(type);
  if (type == 'parentAdd') {
    treeOption.value.formOption.column[0].type = 'checkbox';
    done();
  } else if (type == 'add') {
    done();
    treeOption.value.formOption.column[0].type = 'input';
  } else {
    treeOption.value.formOption.column[0].type = 'input';
    done();
  }
}
let drawer = ref(false);
function handleAdd(params) {
  drawer.value = true;
}
const fileTree = ref([]);
const treeProps = {
  label: 'name',
  children: 'children',
};

const handleUpload = ({ file }) => {
  const entries = file.webkitGetAsEntry ? file.webkitGetAsEntry() : null;
  if (entries && entries.isDirectory) {
    processDirectory(entries);
  }
};

const processDirectory = (dirEntry, path = '', tree = []) => {
  const reader = dirEntry.createReader();
  reader.readEntries(entries => {
    entries.forEach(entry => {
      const node = {
        name: entry.name,
        path: `${path}/${entry.name}`,
        type: entry.isDirectory ? 'directory' : 'file',
      };

      if (entry.isDirectory) {
        node.children = [];
        processDirectory(entry, node.path, node.children);
      }

      tree.push(node);
    });
    fileTree.value = [...fileTree.value];
  });
};

const selectedFolder = ref('');

const handleFolderSelect = e => {
  ;
  const files = Array.from(e.target.files);
  if (files.length === 0) return;

  // 获取根目录名称
  const firstFilePath = files[0].webkitRelativePath;
  selectedFolder.value = firstFilePath.includes('/') ? firstFilePath.split('/')[0] : firstFilePath;

  // 构建目录树
  const newTree = [];
  files.forEach(file => {
    const pathSegments = file.webkitRelativePath.split('/');
    let currentLevel = newTree;

    pathSegments.forEach((segment, index) => {
      const existingPath = currentLevel.find(item => item.name === segment);

      if (existingPath) {
        currentLevel = existingPath.children;
      } else {
        const newEntry = {
          name: segment,
          path: pathSegments.slice(0, index + 1).join('/'),
          type: index === pathSegments.length - 1 ? 'file' : 'directory',
          children: [],
          raw: file,
        };

        currentLevel.unshift(newEntry);
        currentLevel = newEntry.children;
      }
    });
  });

  fileTree.value = newTree;
};
const uploadLoading = ref(false);

const handleSubmit = async () => {
  if (fileTree.value.length === 0) {
    proxy.$message.warning('请先选择要上传的文件夹');
    return;
  }

  try {
    uploadLoading.value = true;
    let files = [];

    // 收集所有文件
    const collectFiles = nodes => {
      nodes.forEach(node => {
        if (node.type === 'file' && node.raw) {
          files.push({
            file: node.raw,
            fileName: node.path,
            projectId: props.projectId,
            catalogueId: currentCatalogueId.value,
          });
        }
        if (node.children) {
          collectFiles(node.children);
        }
      });
    };

    collectFiles(fileTree.value);
    proxy.$upload.uploadFiles(files, {
      onFileComplete: (file, err) => {},
      onAllComplete: () => {
        getTreeData();
        proxy.$message.success('文件上传成功');
        drawer.value = false;
      },
    });
  } catch (error) {
    console.error('上传失败:', error);
    proxy.$message.error('文件上传失败');
  } finally {
    uploadLoading.value = false;
  }
};

let selectList = ref([]);
function handleSelectionChange(list) {
  selectList.value = list;
}
function handleDownload(type) {
  if (type == 1) {
    if (selectList.value.length === 0) {
      proxy.$message.warning('请先选择要下载的文件');
      return;
    }
    downloadByUrl(
      '/api/vt-admin/projectFiles/downProjectFiles?ids=' +
        selectList.value.map(item => item.id).join(',') +
        '&type=1'
    );
    proxy.$refs.crud.toggleSelection();
  } else {
    const ids = proxy.$refs.tree.getCheckedKeys();
    if (ids.length === 0) {
      proxy.$message.warning('请先选择要下载的目录');
      return;
    }

    downloadByUrl('/api/vt-admin/projectFiles/downProjectFiles?ids=' + ids.join(',') + '&type=0');
    proxy.$refs.tree.setCheckedKeys([]);
  }
}
</script>

<style lang="scss" scoped>
.tree-node {
  display: flex;
  align-items: center;
  .el-icon {
    margin-right: 5px;
    &.folder-icon {
      color: #f8d775;
    }
    &.file-icon {
      color: #909399;
    }
  }
}
:deep(.crud .el-checkbox-group) {
  display: flex;
  flex-direction: column;
}
</style>
<style lang="scss">
.avue-form .el-checkbox-group {
  display: flex;
  flex-direction: column;
}
</style>
