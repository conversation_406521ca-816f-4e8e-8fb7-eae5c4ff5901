<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      ><template #menu-left>
        <div class="stats-container" style="display: flex">
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><document /></el-icon>
              工单总数
            </div>
            <el-text class="stat-value" type="primary">{{ totalNumber }}</el-text>
          </div>

          <div class="stat-item">
            <div class="stat-label">
              <el-icon><alarm-clock /></el-icon>
              总工时
            </div>
            <el-text class="stat-value" type="info">{{ totalTimes }}h</el-text>
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          icon="edit"
          text
           v-if="row.isNeedSign == 1 ? (row.isSign == 1 && row.objectStatus == 1 ) :  row.objectStatus == 1"
          @click="completeTask(row)"
          >完成</el-button
        >
         <el-button
          type="primary"
          icon="position"
          text
          v-if="
             row.objectStatus == 1 && row.isNeedSign == 1 && !row.isSign
          "
          @click="handleSign(row)"
          >确认签到</el-button
        >
        <el-button
          type="primary"
          icon="edit"
          text
          v-if="row.objectStatus == 3"
          @click="viewDetail(row, 'accept')"
          >接单</el-button
        >
      </template>

      <template #objectStatus="{ row }">
        <el-tag :type="['info', 'warning', 'success', 'info'][row.objectStatus]" effect="plain">{{
          row.$objectStatus
        }}</el-tag>
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)" target="_blank">{{
          row.objectName
        }}</el-link>
      </template>
      <template #filesDetail-form>
        <File :fileList="form.fileList"></File>
      </template>
      <template #completeFiles-form>
        <File :fileList="form.completeFilesList"></File>
      </template>
      <template #taskName-form>
        <el-descriptions :title="form.labelName">
          <el-descriptions-item label="预约时间">{{
            (form.planStartTime && form.planStartTime.split(' ')[0]) || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务客户">{{
            form.finalCustomer || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务联系人">{{ form.contact || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            form.contactPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">
            {{ form.distributionAddress || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单描述" :span="3">
            <span style="white-space: pre-line"> {{ form.taskDescription || '--' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <template #menu-form v-if="form.objectStatus == 3">
        <el-button type="primary" icon="edit" @click="accept(form)">确认接单</el-button>
      </template>
    </avue-crud>

    <el-drawer title="详情" v-model="drawer" size="50%">
      <avue-form :option="detailOption" v-model="detailForm">
        <template #taskName>
          <el-descriptions :title="detailForm.objectName">
            <el-descriptions-item label="预约时间">{{
              (detailForm.reservationTime && detailForm.reservationTime.split(' ')[0]) || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务客户">{{
              detailForm.finalCustomer || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务联系人">{{
              detailForm.contact || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              detailForm.contactPhone || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务地址" :span="2">
              {{ detailForm.distributionAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="工单描述" :span="3">
              <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #files>
          <File :fileList="detailForm.fileList"></File>
        </template>
        <template #completeFiles>
          <File :fileList="detailForm.completeFilesList"></File>
        </template>
        <template #engineerServiceInfo>
          <div
            v-if="
              detailForm.sealContractObjectResultVOList &&
              detailForm.sealContractObjectResultVOList.length > 0
            "
            class="engineer-list"
          >
            <el-card
              v-for="(engineer, index) in detailForm.sealContractObjectResultVOList"
              :key="index"
              class="engineer-card"
              shadow="hover"
            >
              <template #header>
                <div class="engineer-header">
                  <h3>{{ engineer.handleName || '工程师' + (index + 1) }}</h3>
                </div>
              </template>
              <div class="engineer-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">服务开始时间：</span>
                    <span>{{ engineer.serviceStartTime || '--' }}</span>
                  </div>
                  <!-- <div class="info-item">
                    <span class="label">实际完成时间：</span>
                    <span>{{ engineer.completeTime.split(' ')[0] || '--' }}</span>
                  </div> -->
                  <div class="info-item">
                    <span class="label">服务结束时间：</span>
                    <span>{{ engineer.serviceEndTime || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">所用工时：</span>
                    <span>{{ engineer.useTimes || '--' }}小时</span>
                  </div>
                </div>
                <div
                  v-if="engineer.workOrderPhotoList && engineer.workOrderPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">现场图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.workOrderPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.workOrderPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="engineer.handleResultPhotoList && engineer.handleResultPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">处理结果图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.handleResultPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.handleResultPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>

                <div v-if="engineer.serviceReorder" class="info-item">
                  <span class="label">服务复盘：</span>
                  <div class="service-reorder">
                    <pre>{{ engineer.serviceReorder }}</pre>
                  </div>
                </div>

                <div v-if="engineer.completeRemark" class="info-item">
                  <span class="label">备注：</span>
                  <div>{{ engineer.completeRemark }}</div>
                </div>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无工程师服务信息"></el-empty>
        </template>
      </avue-form>
      <template #footer v-if="detailForm.objectStatus == 3">
        <!-- 操作按钮 -->
        <div class="drawer-footer" v-if="detailForm.objectStatus == 3">
          <el-button type="primary" :loading="acceptLoading" @click="accept(detailForm)"
            >确认接单</el-button
          >
        </div>
      </template>
    </el-drawer>

    <!-- 完成工单对话框 -->
    <el-drawer
      v-model="completeDialogVisible"
      title="完成工单"
      size="40%"
      :before-close="() => (completeDialogVisible = false)"
    >
      <avue-form ref="completeFormRef" :option="completeFormOption" v-model="completeFormData">
        <template #useTimes>
          <el-input-number style="width:50%" v-model="completeFormData.useTimes"  :min="1" :max="10" label="label"></el-input-number> -  预估工时：{{ completeFormData.preUseTimes }}
        </template>
      </avue-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="completeDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitCompleteForm">确认</el-button>
        </span>
      </template>
    </el-drawer>

    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect
      userUrl="/api/blade-system/search/user?roleKeys=ywzy,swzy,technology_leader,technology_person"
      ref="userSelectRef"
      @onConfirm="handleConfirm"
    ></wfUserSelect>
    <!-- <humanSelect ref="userSelectRefOut" @select="handleConfirmOut"></humanSelect> -->
      <wokerOrderDetail  @success="onLoad()" :objectStatus="currentRow.objectStatus" ref="wokerOrderDetailRef"></wokerOrderDetail>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import userSelect from './component/userSelect.vue';
import { dateFormat } from '@/utils/date.js';
import humanSelect from '@/views/Project/compoents/humanSelect.vue';
import wokerOrderDetail from './component/wokerOrderDetail.vue';
import { ElMessage } from 'element-plus';
// 完成工单相关变量
const completeDialogVisible = ref(false);
const completeFormRef = ref(null);
const completeFormData = ref({});
const currentRow = ref({});



// 完成工单表单配置
const completeFormOption = ref({
  labelWidth: 120,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'datetime',
      label: '服务开始时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceStartTime',
      rules: [{ required: true, message: '请选择服务开始时间', trigger: 'blur' }],
    },
    {
      type: 'datetime',
      label: '服务结束时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceEndTime',
      rules: [{ required: true, message: '请选择服务结束时间', trigger: 'blur' }],
    },
    // {
    //   type: 'date',
    //   label: '实际完成时间',
    //   span: 12,
    //   value: dateFormat(new Date(), 'yyyy-MM-dd'),
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
    //   prop: 'completeTime',
    //   rules: [{ required: true, message: '请选择实际完成时间', trigger: 'blur' }],
    // },
    {
      label: '实际使用工时',
      prop: 'useTimes',
      type: 'number',
      span:24,
      rules: [{ required: true, message: '请输入实际使用工时', trigger: 'blur' }],
    },
      //  // 完成情况
      //   {
      //     label: '完成情况',
      //     prop: 'completeStatus',
      //     type: 'select',
      //     props: {
      //       value: 'id',
      //       label: 'dictValue',
      //     },
      //     dicUrl: '/blade-system/dict-biz/dictionary?code=completeType',
      //   },
    {
      label: '现场图',
      prop: 'workOrderPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
    {
      label: '处理结果图',
      prop: 'handleResultPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
    {
          label: '完成附件',
          prop: 'completeFiles',
          type: 'upload',
          span: 24,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',

          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
    {
      label: '服务复盘',
      prop: 'serviceReorder',
      type: 'textarea',
      span: 24,
      // rules: [{ required: true, message: '请输入服务复盘', trigger: 'blur' }],
    },
    {
      label: '备注',
      prop: 'completeRemark',
      type: 'textarea',
      span: 24,
    },
  ],
});

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  labelWidth: 120,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  viewTitle: '工单详情',
  column: [
    // {
    //   label: '工单编号',
    //   prop: 'objectNo',
    //   display: false,
    // },

    // {
    //   label: '工单类型',
    //   prop: 'lables',
    //   placeholder: '请输入工单类型',
    //   type: 'tree',
    //   display: false,
    //   props: {
    //     value: 'id',
    //     label: 'dictValue',
    //   },
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
    // },
    {
      label: '工单名称',
      prop: 'objectName',
      search: true,
      display: false,
    },
    {
      label: '关联合同',
      prop: 'sealContractId',
      placeholder: '请选择关联合同',
      addDisplay: true,
      editDisplay: false,
      component: 'wf-contract-select',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5',
      },
      change: val => {
        setBaseInfo(val.value);
      },
      formatter: row => {
        return row.contractName;
      },
    },
    {
      label: '工单描述',
      prop: 'taskDescription',
      placeholder: '请输入工单描述',
      type: 'textarea',
      span: 24,
    },
    {
      label: '客户名称',
      prop: 'finalCustomer',
    },
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '服务时间',
      prop: 'planTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
      overHidden: true,
      formatter: row => {
        return `${row.serviceStartTime} - ${row.serviceEndTime}`;
      },
    },
    {
      label: '发布时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      span: 12,
      component: 'wf-daterange-search',
      searchSpan: 6,
      search: true,
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '实际完成时间',
      prop: 'completeTime',
      type: 'date',
      overHidden: true,
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      searchSpan: 6,
      rules: [
        {
          required: true,
          message: '请选择实际完成时间',
          trigger: 'blur',
        },
      ],
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '使用工时',
      prop: 'useTimes',
      display: false,
      rules: [
        {
          required: true,
          message: '请输入使用工时',
          trigger: 'blur',
        },
      ],
      width: 110,
    },
    {
      label: '派单人',
      prop: 'projectLeaderName',
      width: 110,
      display: false,
      // search: true,
    },

    {
      label: '工单状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      width: 110,
      dicData: [
        {
          label: '待派单',

          value: 0,
        },
        {
          label: '待接单',
          value: 3,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        }, {
          label: '已关单',
          value: 4,
        },
      ],
    },
  ],
  group: [
    {
      label: '工单信息',
      prop: 'taskInfo',
      addDispla: true,
      disabled: true,
      column: [
        {
          labelWidth: 0,
          span: 24,
          prop: 'taskName',
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '派单人',
          prop: 'projectLeaderName',
          width: 110,

          // search: true,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',
          addDisplay: false,
          editDisplay: false,
          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '指派工程师',
          prop: 'externalHandleUser',
          component: 'wf-user-select',
          rules: [
            {
              required: true,
              message: '请选择工程师',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          type: 'date',
          label: '实际完成时间',
          span: 12,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
        },
        {
          label: '实际使用工时',
          prop: 'useTimes',
          type: 'number',
        },
     
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '服务复盘',
          prop: 'serviceReorder',
          type: 'textarea',

          span: 24,
          value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});

let drawer = ref(false);
let detailForm = ref({});
let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 250,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        {
          type: 'date',
          label: '预约时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'reservationTime',
          rules: [
            {
              required: true,
              message: '请选择预约时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '故障类型',
          prop: 'lables',
          type: 'select',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
          span: 12,
        },
        // {
        //   label: '现场图',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'string',
        //   loadText: '附件上传中，请稍等',
        //   span: 24,
        //   slot: true,
        //   prop: 'workOrderPhotos',
        //   addDisplay: true,
        //   editDisplay: true,
        //   viewDisplay: false,
        //   listType: 'picture-card',
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
        {
          label: '服务要求',
          prop: 'taskDescription',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 发布人
        {
          label: '发布人',
          prop: 'createName',
          span: 12,
        },

        // 派单人
        {
          label: '派单人',
          prop: 'projectLeaderName',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 24,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '工程师服务信息',
          prop: 'engineerServiceInfo',
          labelWidth: 0,
          span: 24,
          formslot: true,
        },
      ],
    },
  ],
});
let wokerOrderDetailRef = ref(null);

function viewDetail(row) {
  currentRow.value = row
   wokerOrderDetailRef.value.open(row.id)
}

/*
设置基础信息
*/
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contact = res.data.data.finalCustomerConcat;
      form.value.finalCustomer = res.data.data.finalCustomer;
      form.value.contactPhone = res.data.data.finalCustomerPhone;
      form.value.distributionAddress = res.data.data.deliveryAddress;
      option.value.group[0].display = false;
    });
}

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNumber = ref(0);
let totalTimes = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        selectType: 2,
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/externalPcPageStatistics', {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        selectType: 2,
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      totalNumber.value = res.data.data.totalNumber;
      totalTimes.value = res.data.data.totalTimes;
    });
}
let router = useRouter();

function beforeOpen(done, type) {
  if (['add', 'view'].includes(type)) {
    if (type == 'add') {
      option.value.group[0].display = false;
    }
    if (type == 'view') {
      if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
        option.value.group[0].display = false;
      } else {
        option.value.group[0].display = false;
      }
    }
  } else {
    form.value.files =
      form.value.fileList &&
      form.value.fileList.map(item => {
        // 修正拼写错误，将 reuturn 改为 return
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    if (form.value.externalHandleUser) {
      form.value.externalHandleUserList = [
        {
          id: form.externalHandleUser,
          realName: form.value.handleUserName,
        },
      ];
    }
    form.value.objectName = form.value.objectName || form.value.labelName;
    form.value.remark = form.value.remark || form.value.taskDescription;
    form.value.planTime = form.value.planTime || form.value.planStartTime;
    if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
      option.value.group[0].display = false;
    } else {
      option.value.group[0].display = true;
    }
  }
  done();
}
function rowSave(form, done, loading) {
  if (form.sealContractId) {
    const data = {
      ...form,

      files: form.files && form.files.map(item => item.url).join(','),
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } else {
    addContract(form, done, loading);
  }
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    contractType: 2,
    deliveryAddress: form.distributionAddress,
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
        form.sealContractId = res.data.data;
        rowSave(form, done, loading);
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const url =
    row.createUser == proxy.$store.getters.userInfo.user_id
      ? updateUrl
      : '/api/vt-admin/sealContractObject/sendOrder';
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
  };
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function completeTask(row) {
  // 设置表单数据和对话框可见性
  completeFormData.value = {
      serviceStartTime: row.serviceStartTime,
    serviceEndTime: row.serviceEndTime,
    // serviceReorder: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
    handleResultPhotos: [],
    preUseTimes:row.preUseTimes
  };

  

  completeDialogVisible.value = true;
  currentRow.value = row;
}

// 提交完成表单
function submitCompleteForm() {
  completeFormRef.value.validate((valid, done) => {
    if (valid) {
      if(completeFormData.value.preUseTimes){
        const value = completeFormData.value.preUseTimes * 1  <  completeFormData.value.useTimes * 1
        if(value && !completeFormData.value.completeRemark){
            done()
           return  ElMessage.warning('实际使用工时超过了预估工时 ，请在备注填写超时原因')
        }
      }
      
      let url = '/api/vt-admin/sealContractObject/completeWorkOrder';
      const data = {
        id: currentRow.value.id,
        ...completeFormData.value,
        workOrderPhotos: completeFormData.value.handleResultPhotos
          ?.map(item => item.label)
          .join(','),
        handleResultPhotos: completeFormData.value.handleResultPhotos
          ?.map(item => item.label)
          .join(','),
        completeFiles: completeFormData.value.completeFiles
          ? completeFormData.value.completeFiles.map(item => item.value).join(',')
          : '',
      };
      axios
        .post(url, data)
        .then(r => {
          proxy.$message.success(r.data.msg);
          proxy.$store.dispatch('getMessageList');
          completeDialogVisible.value = false;
          onLoad();
          done();
        })
        .catch(err => {
          done();
        });
    }
  });
}

let acceptLoading = ref(false);
function accept(row) {
  proxy
    .$confirm('确认是否接单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      acceptLoading.value = true;
      const url = '/api/vt-admin/sealContractObject/acceptOrder?id=' + row.id;
      axios
        .post(url)
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success(res.data.msg);
            drawer.value = false;
            onLoad();
          }
          acceptLoading.value = false;
        })
        .catch(err => {
          proxy.$message.error('接单失败');
          acceptLoading.value = false;
        });
    })
    .catch(() => {
      // 用户取消操作，不做处理
    });
}
function handleSign(row) {
   alert('请前往小程序签到')
}
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}

/* 工单详情抽屉样式 */
.work-order-detail {
  padding: 0;
  height: 100%;
  // background-color: #f5f7fa;
}

/* 工程师服务信息样式 */
.engineer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.engineer-card {
  margin-bottom: 0;
  border-radius: 4px;
  overflow: hidden;
}

.engineer-card :deep(.el-card__header) {
  padding: 8px 12px;
  background-color: #f5f7fa;
}

.engineer-card :deep(.el-card__body) {
  padding: 10px 12px;
}

.engineer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.engineer-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.engineer-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 6px;
  margin-bottom: 6px;
}

.info-item {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

.label {
  font-weight: 600;
  color: #606266;
  margin-right: 3px;
  font-size: 12px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.result-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 2px;
  cursor: pointer;
  transition: transform 0.2s;
}

.result-image:hover {
  transform: scale(1.05);
}

.service-reorder {
  background-color: #f5f7fa;
  padding: 6px;
  border-radius: 2px;
  margin-top: 2px;
  font-size: 12px;
}

.service-reorder pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  font-size: 12px;
  line-height: 1.3;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.info-card {
  margin-bottom: 3px;
  background: white;
  border-radius: 0px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  // background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  padding: 16px 20px;
  color: #000;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 20px;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.description-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .file-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}

.drawer-footer {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin-top: 20px;
  text-align: right;

  .el-button {
    min-width: 120px;
  }
}

.el-descriptions {
  margin-bottom: 20px;

  :deep(.el-descriptions__title) {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    margin-bottom: 15px;
  }

  :deep(.el-descriptions-item__label) {
    font-weight: 600;
    color: #606266;
    background-color: #fafbfc !important;
  }

  :deep(.el-descriptions-item__content) {
    color: #303133;
    background-color: white !important;
  }

  :deep(.el-descriptions) {
    border-radius: 8px;
    overflow: hidden;
  }

  :deep(.el-descriptions__table) {
    border-radius: 8px;
  }
}
</style>
