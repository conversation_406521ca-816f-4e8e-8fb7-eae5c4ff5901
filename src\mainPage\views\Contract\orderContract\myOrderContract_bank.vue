<template>
  <div class="wrap">
    <!-- 页面头部导航 -->
    <div class="page-header">
      <div class="nav-tabs">
     
        <div class="tab-item active">
          <el-icon><Document /></el-icon>
          维保合同
        </div>
        <div class="tab-item">
          <el-icon><Document /></el-icon>
          实施合同
        </div>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <!-- <div class="search-section">
      <div class="search-row">
        <div class="search-item">
          <span class="label">合同类别：</span>
          <el-select v-model="searchForm.category" placeholder="请选择" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="维保" value="维保"></el-option>
          </el-select>
        </div>
        <div class="search-item">
          <span class="label">合同状态：</span>
          <el-select v-model="searchForm.status" placeholder="请选择" clearable>
            <el-option label="全部" value=""></el-option>
            <el-option label="进行中" value="进行中"></el-option>
          </el-select>
        </div>
        <div class="search-item">
          <el-input 
            v-model="searchForm.keyword" 
            placeholder="请输入合同名称/编号/描述"
            clearable
            style="width: 300px;"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>
      
      <div class="action-row">
        <div class="left-actions">
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            开始日期
          </el-button>
          <el-button @click="handleBatch">
            续签日期
          </el-button>
          <el-button @click="handleCustomer">
            客户单位
          </el-button>
          <el-button @click="handlePeriod">
            超期未收款
          </el-button>
          <el-button @click="handleSettings">
            <el-icon><Setting /></el-icon>
            设置
          </el-button>
        </div>
        <div class="right-actions">
          <span class="total-amount">毛利润：¥ 3544.62</span>
          <el-button @click="handleImport">
            <el-icon><Upload /></el-icon>
            导入维保合同
          </el-button>
          <el-button @click="handleExport">
            <el-icon><Download /></el-icon>
            导出
          </el-button>
          <el-button type="primary" @click="handleAdd">
            新增
            <el-icon><ArrowDown /></el-icon>
          </el-button>
        </div>
      </div>
    </div> -->

    <!-- 合同列表表格 -->
    <div class="table-section">
      <el-table :data="contractList" style="width: 100%" border>
      
        
        <el-table-column label="合同信息" width="400" >
          <template #default="scope">
            <div class="project-info">
              
              <div class="project-details">
                <div class="project-number">{{ scope.row.projectNumber }}</div>
                <div class="project-meta">
                  <span>合同编号：{{ scope.row.projectCode }}</span><br>
                  <span>合同类别：{{ scope.row.category }}</span><br>
                  <span>客户单位：{{ scope.row.customer }}</span><br>
                  <span>维护期间：{{ scope.row.maintenancePeriod }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="合同收支" >
          <template #default="scope">
            <div class="financial-info">
              <div class="amount-row">
                <div class="amount-item">
                  <span class="label">合同总金额：</span>
                  <span class="amount">{{ scope.row.totalAmount }}</span>
                </div>
                <div class="amount-item">
                  <span class="label">合同总成本：</span>
                  <span class="amount">{{ scope.row.totalCost }}</span>
                </div>
              </div>
              <div class="amount-row">
                <div class="amount-item">
                  <span class="label">投标成本：</span>
                  <span class="amount">{{ scope.row.bidCost }}</span>
                </div>
                <div class="amount-item">
                  <span class="label">已回款：</span>
                  <span class="amount">{{ scope.row.receivedAmount }}</span>
                </div>
              </div>
              <div class="amount-row">
                <div class="amount-item">
                  <span class="label">服务成本：</span>
                  <span class="amount">{{ scope.row.serviceCost }}</span>
                </div>
                <div class="amount-item">
                  <span class="label">支出成本：</span>
                  <span class="amount">{{ scope.row.expenditureCost }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="合同成员" width="120">
          <template #default="scope">
            <div class="member-info">
              {{ scope.row.members }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="服务工单数" width="100">
          <template #default="scope">
            <div class="task-count">
              {{ scope.row.taskCount }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="success" size="small" @click="handleEdit(scope.row)">
              查看
              <el-icon><ArrowDown /></el-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
</div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { Document, Search, Plus, Setting, Upload, Download, ArrowDown } from '@element-plus/icons-vue'

// 搜索表单
const searchForm = reactive({
  category: '',
  status: '',
  keyword: ''
})

// 全选状态
const selectAll = ref(false)

// 合同列表数据
const contractList = ref([
  {
    id: 1,
    selected: false,
    statusText: '合同立项',
    statusClass: 'status-active',
    projectNumber: '4214345',
    projectCode: '345345',
    category: '维保',
    customer: '惠州中学',
    maintenancePeriod: '2025-09-18 ~ 2025-09-30',
    totalAmount: '¥ 3,660',
    totalCost: '¥ 115.38',
    bidCost: '¥ 0.00',
    receivedAmount: '¥ 0',
    serviceCost: '¥ 115.38',
    expenditureCost: '¥ 0.00',
    members: '吴强,高明新等3人',
    taskCount: '1个'
  },
  {
    id: 2,
    selected: false,
    statusText: '合同立项',
    statusClass: 'status-active',
    projectNumber: '电力工程外包服务合同',
    projectCode: '202409001',
    category: '维保',
    customer: '中山电力工程有限公司',
    maintenancePeriod: '维保',
    totalAmount: '¥ 0',
    totalCost: '¥ 0.00',
    bidCost: '¥ 0.00',
    receivedAmount: '¥ 0',
    serviceCost: '¥ 0.00',
    expenditureCost: '¥ 0.00',
    members: '吴强',
    taskCount: '1个'
  }
])

// 处理全选
const handleSelectAll = (val) => {
  contractList.value.forEach(item => {
    item.selected = val
  })
}

// 处理操作
const handleAdd = () => {
  console.log('新增')
}

const handleBatch = () => {
  console.log('批量操作')
}

const handleCustomer = () => {
  console.log('客户单位')
}

const handlePeriod = () => {
  console.log('超期未收款')
}

const handleSettings = () => {
  console.log('设置')
}

const handleImport = () => {
  console.log('导入')
}

const handleExport = () => {
  console.log('导出')
}

const handleEdit = (row) => {
  console.log('查看', row)
}
</script>

<style lang="scss" scoped>
.wrap{
    height: 100%;
    // padding: 10px;
    margin: 10px;
    box-sizing: border-box;
    background: #fff;
}
.page-header {
  margin-bottom: 10px;
  
  .nav-tabs {
    display: flex;
    border-bottom: 1px solid #e4e7ed;
    
    .tab-item {
      display: flex;
      align-items: center;
      padding: 6px 10px;
      font-size: 14px;
      cursor: pointer;
      color: #606266;
      border-bottom: 2px solid transparent;
      
      .el-icon {
        margin-right: 8px;
      }
      
      &.active {
        color: var(--el-color-primary);
        border-bottom-color: var(--el-color-primary);
        background-color: #f0f9ff;
      }
      
      &:hover:not(.active) {
        color: #409eff;
      }
    }
  }
}

.search-section {
  background: #fff;
  padding: 20px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  .search-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    
    .search-item {
      display: flex;
      align-items: center;
      margin-right: 30px;
      
      .label {
        margin-right: 10px;
        color: #606266;
        white-space: nowrap;
      }
    }
  }
  
  .action-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .left-actions {
      display: flex;
      gap: 10px;
    }
    
    .right-actions {
      display: flex;
      align-items: center;
      gap: 10px;
      
      .total-amount {
        color: #f56c6c;
        font-weight: bold;
        margin-right: 20px;
      }
    }
  }
}

.table-section {
  background: #fff;
  border-radius: 4px;
//   box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  
  .project-info {
    display: flex;
    flex-direction: column;
    
    .status-badge {
      display: inline-block;
      padding: 2px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin-bottom: 8px;
      width: fit-content;
      
      &.status-active {
        background-color: var(--el-color-primary);
        color: white;
      }
      
      &.status-pending {
        background-color: #e6a23c;
        color: white;
      }
      
      &.status-completed {
        background-color: #909399;
        color: white;
      }
    }
    
    .project-details {
      .project-number {
        font-weight: bold;
        margin-bottom: 5px;
      }
      
      .project-meta {
        font-size: 12px;
        color: #606266;
        line-height: 1.4;
      }
    }
  }
  
  .financial-info {
    .amount-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 4px;
      font-size: 12px;
      
      .amount-item {
        display: flex;
        justify-content: space-between;
        flex: 1;
        margin-right: 10px;
        
        &:last-child {
          margin-right: 0;
        }
        
        .label {
          color: #606266;
        }
        
        .amount {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
  
  .member-info {
    font-size: 12px;
    color: #606266;
  }
  
  .task-count {
    font-size: 12px;
    color: #606266;
  }
}

:deep(.el-table) {
  .el-table__header {
    background-color: #f5f7fa;
  }
  
  .el-table__row {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}
</style>