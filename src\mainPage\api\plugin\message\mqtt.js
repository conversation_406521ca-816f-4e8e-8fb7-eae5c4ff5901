let MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD, SOCKET_SERVICE, MQTT_MY,MQTT_ENV;
const mode = import.meta.env.VITE_APP_ENV;
if (mode === 'development') {
  // 开发环境
  MQTT_SERVICE = 'ws://10.100.105.54:15674/ws';
  MQTT_USERNAME = 'admin';
  MQTT_PASSWORD = '123456';
  SOCKET_SERVICE = 'ws://127.0.0.1:8083/ws';
  MQTT_MY = 'mqtt://127.0.0.1:5883/ws';
  MQTT_ENV = 'dev';
} else if (mode === 'production') {
  //生产环境
  MQTT_SERVICE = 'wss://oa.sysvt.cn/ws';
  MQTT_USERNAME = 'admin';
  MQTT_PASSWORD = 'vt@2023';
  SOCKET_SERVICE = 'wss://127.0.0.1:8083/ws';
  MQTT_MY = 'mqtt://127.0.0.1:5883/ws';
  MQTT_ENV = 'prod';
} else if (mode === 'test') {
  //测试环境
  MQTT_SERVICE = 'ws://10.100.105.41:15674/ws';
  MQTT_USERNAME = 'admin';
  MQTT_PASSWORD = '123456';
  SOCKET_SERVICE = 'ws://127.0.0.1:8083/ws';
  MQTT_MY = 'mqtt://127.0.0.1:5883/ws';
  MQTT_ENV = 'test';
}

export {
  // export const MQTT_SERVICE = 'ws://192.168.1.218:15674/ws' // rabbitmq服务地址
  MQTT_SERVICE, // rabbitmq服务地址 正式 //export const MQTT_SERVICE = 'ws://183.11.235.91/:15674/ws' // rabbitmq测试服务地址
  MQTT_USERNAME, // rabbitmq连接用户名 //export const MQTT_PASSWORD = 'jc@@2023' // rabbitmq连接密码
  MQTT_PASSWORD, // rabbitmq连接密码
  SOCKET_SERVICE, // netty websocket地址
  MQTT_MY, // mica-mqtt服务地址
  MQTT_ENV, // 环境
};
