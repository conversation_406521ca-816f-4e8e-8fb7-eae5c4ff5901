<template>
  <el-dialog
    title="选择工人"
    top="2vh"
    width="80%"
    class="avue-dialog avue-dialog--top"
    v-model="dialogVisible"
  >
    <el-radio-group v-model="form.type" @change="onLoad" style="margin-bottom: 10px;">
      <el-radio-button label="0">本地个人</el-radio-button>
      <el-radio-button label="1">本地公司</el-radio-button>
      <el-radio-button label="2">外地个人</el-radio-button>
      <el-radio-button label="3">外地公司</el-radio-button>
    </el-radio-group>

    <avue-crud
      :option="form.type == 0 || form.type == 2 ? selfOption : companyOption"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @selection-change="handleSelectionChange"
      @size-change="onLoad"
      v-model="form"
      @row-click="rowClick"
    >
      <template #specialty-form>
        <el-autocomplete
          style="width: 100%"
          v-model="form.specialty"
          value-key="value"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入强项"
        />
      </template>
     
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleClick" type="primary">确 定</el-button>
    </div>
  </el-dialog>

  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const emits = defineEmits(['select']);
const props = defineProps({
  checkType: {
    type: String,
    default: 'checkbox' // 'radio' 或 'checkbox'
  }
});

// 内部管理的checkType状态
let currentCheckType = ref(props.checkType);
let selfOption = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  selection:true,
  reserveSelection: true,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  column: [
    // {
    //   label: '姓名',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label:'',
      prop:'radio',
    },
    {
      label: '姓名',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      search: true,
    },
    {
      label: '年龄',
      prop: 'age',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '性别',
      prop: 'sex1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      dicData: [
        {
          label: '男',
          value: 1,
        },
        {
          label: '女',
          value: 0,
        },
      ],
      props: {
        label: 'label',
        value: 'value',
      },
      formatter: (row, value) => {
        return row.sex == 1 ? '男' : '女';
      },
    },

    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },

    {
      label: '技能标签',
      prop: 'specialty',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      overHidden: true,
    },

    {
      label: '态度标签',
      prop: 'attitudeLabel',
      type: 'input',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      type: 'dynamic',
      hide: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      children: {
        column: [
          {
            label: '工种',
            prop: 'workType1',
            formatter: (row, value) => {
              return row.$workType;
            },
          },
          {
            label: '工价',
            prop: 'salary1',
            formatter: (row, value) => {
              return row.salary;
            },
          },
        ],
      },
    },
    {
      label: '所在区域',
      prop: 'address',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return `${row.provinceName}/${row.cityName}/${row.areaName}`;
      },
      overHidden: true,
    },
    {
      label: '是否保险',
      prop: 'isInsurance',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '是否开票',
      prop: 'isInvoice1',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],

      formatter: (row, value) => {
        return row.$isInsurance;
      },
    },
    {
      label: '工种',
      prop: 'workType',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      formatter: row => {
        return row.salaryVOS?.map(item => item.workTypeName).join('/');
      },
      type: 'select',
    },
    {
      label: '工价(元/天)',
      prop: 'salary',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      placeholder: '请输入工价',
      formatter: row => {
        console.log(row);
        return row.salaryVOS?.map(item => item.salary).join('/');
      },
    },
    // {
    //   label: '开户行',
    //   prop: 'openBank1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.openBank;
    //   },
    // },
    // {
    //   label: '银行账号',
    //   prop: 'bankNumber1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.bankNumber;
    //   },
    // },
    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
          searchSpan: 6,
        },
        {
          label: '性别',
          prop: 'sex',
          type: 'radio',
          dicData: [
            {
              label: '男',
              value: 1,
            },
            {
              label: '女',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '年龄',
          prop: 'age',
          width: 250,
          overHidden: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },
        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },

        {
          label: '是否开票',
          prop: 'isInvoice',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
          control: val => {
            return {
              invoiceType: {
                display: val == 1,
              },
              taxRate: {
                display: val == 1,
              },
            };
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '所在区域',
          prop: 'province_city_area',
          search: true,
          hide: true,
          searchSpan: 5,
          type: 'cascader',
          props: {
            label: 'title',
            value: 'id',
          },

          lazy: true,
          lazyLoad(node, resolve) {
            let stop_level = 2;
            let level = node.level;
            let data = node.data || {};
            let id = data.id;
            let list = [];
            let callback = () => {
              resolve(
                (list || []).map(ele => {
                  return Object.assign(ele, {
                    leaf: level >= stop_level || !ele.hasChildren,
                  });
                })
              );
            };
            axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
              list = res.data.data;
              callback();
            });
          },
        },
        {
          label: '详细地址',
          prop: 'address',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
      ],
    },
    {
      label: '工种信息',
      column: [
        {
          type: 'dynamic',
          hide: true,
          span: 24,
          prop: 'salaryDTOList',
          children: {
            column: [
              {
                label: '工种',
                prop: 'workType',
                dicUrl: '/blade-system/dict-biz/dictionary?code=workType',
                props: {
                  label: 'dictValue',
                  value: 'id',
                },
                type: 'select',
              },
              {
                label: '工价(元/天)',
                prop: 'salary',
                placeholder: '请输入工价',
              },
            ],
          },
        },
      ],
    },
    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let companyOption = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 100,  selection:true,
  reserveSelection: true,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  column: [
    // {
    //   label: '公司名称',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label:'',
      prop:'radio',
    },
    {
      label: '公司名称',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      overHidden: true,
      editDisplay: false,
      viewDisplay: false,
      searchSpan: 6,
      search: true,
    },
    // {
    //   label: '性别',
    //   prop: 'sex1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   dicData: [
    //     {
    //       label: '男',
    //       value: 1,
    //     },
    //     {
    //       label: '女',
    //       value: 0,
    //     },
    //   ],
    //   props: {
    //     label: 'label',
    //     value: 'value',
    //   },
    //   formatter: (row, value) => {
    //     return row.sex == 1 ? '男' : '女';
    //   },
    // },
    {
      label: '公司地址',
      prop: 'address',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
      formatter: (row, value) => {
        return row.address;
      },
    },
    {
      label: '联系人',
      prop: 'contact',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },
    {
      label: '团队管理标签',
      prop: 'teamManageLabels',
      type: 'input',
      addDisplay: false,
      overHidden: true,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '交付能力标签',
      prop: 'teamPaymentLabels',
      type: 'input',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
    },
    // {
    //   label: '技能标签',
    //   prop: 'specialty',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   overHidden:true,
    // },
    // {
    //   label: '是否保险',
    //   prop: 'isInsurance',
    //   type: 'select',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   type: 'radio',
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },

    // {
    //   label: '态度标签',
    //   prop: 'attitudeLabel',
    //   type: 'input',

    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    // },
    {
      type: 'select',
      label: '发票资料',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      // search: true,

      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      prop: 'invoiceType',
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      type: 'dynamic',
      hide: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      children: {
        column: [
          {
            label: '工种',
            prop: 'workType1',
            formatter: (row, value) => {
              return row.$workType;
            },
          },
          {
            label: '工价',
            prop: 'salary1',
            formatter: (row, value) => {
              return row.salary;
            },
          },
        ],
      },
    },

    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '公司名称',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '联系人',
          prop: 'contact',
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },
        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },
        {
          label: '团队管理标签',
          prop: 'teamManageLabels',
          type: 'input',
        },
        {
          label: '交付能力标签',
          prop: 'teamPaymentLabels',
          type: 'input',
        },
        {
          label: '所在区域',
          prop: 'province_city_area',
          search: true,
          hide: true,
          searchSpan: 5,
          type: 'cascader',
          props: {
            label: 'title',
            value: 'id',
          },
          lazy: true,
          lazyLoad(node, resolve) {
            let stop_level = 2;
            let level = node.level;
            let data = node.data || {};
            let id = data.id;
            let list = [];
            let callback = () => {
              resolve(
                (list || []).map(ele => {
                  return Object.assign(ele, {
                    leaf: level >= stop_level || !ele.hasChildren,
                  });
                })
              );
            };
            axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
              list = res.data.data;
              callback();
            });
          },
        },
        {
          label: '公司地址',
          prop: 'address',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
      ],
    },

    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let form = ref({
  type: '0',
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/humanResource/save';
const delUrl = '/api/vt-admin/humanResource/remove?ids=';
const updateUrl = '/api/vt-admin/humanResource/update';
const tableUrl = '/api/vt-admin/humanResource/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  const typeData = {
    userType: form.value.type == 0 || form.value.type == 1 ? null : form.value.type == 2 ? 0 : 1,
    type: form.value.type == 0 ? 0 : form.value.type == 1 ? 1 : 2,
  };
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        ...typeData
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function querySearchAsync(value, cb) {
  axios.get('/api/vt-admin/humanResource/getSpecialtyList').then(res => {
    console.log(res.data.data);
    cb(
      res.data.data.map(item => {
        return {
          value: item,
        };
      })
    );
  });
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.salaryDTOList = form.value.salaryVOS;
  }
  done();
}

let selectList = ref([]);
function handleSelectionChange(list) {
  selectList.value = list;
}
let dialogVisible = ref(false);
function open(checkType = props.checkType) {
  // 重置选择状态
  selectList.value = [];
  proxy.$refs.crud?.clearSelection();
  // 更新checkType
  currentCheckType.value = checkType;
  
  dialogVisible.value = true;
  onLoad();
}
function handleClick() {
  emits('select', selectList.value)
  dialogVisible.value = false;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
