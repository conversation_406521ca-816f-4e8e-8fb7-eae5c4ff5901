<template>
  <basic-container>
    <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
      @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud" @keyup.enter="onLoad"
      @row-del="rowDel" @search-reset="reset" @selection-change="handleSectionChange" @search-change="searchChange"
      @refresh-change="onLoad" @current-change="onLoad" @size-change="onLoad" v-model="form">
      <template #menu-left>
        <el-button type="primary" icon="plus" @click="addDemand">新增</el-button>

        <el-popover @hide='$refs.crud.clearSelection' placement="bottom" :disabled="selectList.length == 0"
          trigger="click">
          <div style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
            ">
            <el-text size="small" type="primary">微信扫一扫分享给对方</el-text>
            <el-image style="width: 100px; height: 100px" :src="shareUrl"></el-image>
          </div>
          <template #reference>
            <el-button type="primary" plain icon="share" @click="shareAll">客户填</el-button>
          </template>
        </el-popover>
      </template>
      <template #menu="{ row, index }">
        <el-button text type="primary" @click="set(row)"
          v-if="row.requirementStatus == null || row.requirementStatus == 0" icon="tools">编辑</el-button>
        <el-button type="primary" text plain circle @click="edit(row, false)" icon="edit">自填</el-button>
        <el-popover placement="bottom" trigger="click">
          <div style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
            ">
            <el-text size="small" type="primary">微信扫一扫分享给对方</el-text>
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
          </div>
          <template #reference>
            <el-button type="primary" v-if="row.requirementStatus == 0" icon="Share" @click="share(row)"
              text>客户填</el-button>
          </template>
        </el-popover>
        <el-button text type="primary" @click="$refs.crud.rowDel(row, index)" icon="delete">删除</el-button>
      </template>
      <template #requirementStatus="{ row }">
        <div>
          <el-tag effect="plain" :type="row.requirementStatus == 0 ? 'warning' : 'success'">{{
            row.$requirementStatus
          }}</el-tag>

          <!-- <el-button
            type="primary"
            size="small"
            v-if="row.requirementStatus == 1"
            plain
            circle
            @click="editOrView(row, 'view')"
            icon="view"
          ></el-button> -->
        </div>
      </template>
      <template #clueName="{ row }">
        <el-link type="primary" @click="edit(row, true)">{{ row.clueName }}</el-link>
      </template>
      <template #businessName>
        <el-tag effect="plain" type="warning">未关联</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <el-drawer @close="option.emptyBtn = true" title="需求采集配置" v-model="drawer" direction="rtl" size="80%">
      <el-steps finish-status="success" style="margin-bottom: 10px" simple :active="form.step" align-center>
        <el-step title="需求信息" />
        <el-step title="采集配置" />
      </el-steps>
      <el-divider v-if="form.step == 0" />
      <avue-form ref="addFormRef" v-if="form.step == 0" :option="option" v-model="form"></avue-form>
      <demandSet v-if="form.step == 1" ref="demandSetRef" :data="form"></demandSet>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取 消</el-button>
          <el-button @click="nextStep" v-if="form.step == 0">下一步</el-button>
          <el-button @click="form.step = 0" v-if="form.step == 1">上一步</el-button>
          <el-button type="primary" @click="confirmClick">确 认</el-button>
        </div>
      </template>
    </el-drawer>
    <el-dialog title="需求调查表" v-model="dialogVisible" append-to-body class="avue-dialog avue-dialog--top">

      <avue-form ref="dialogForm" :option="editOption" @submit="editSubmit" v-model="editForm">
        <template v-for="item in propIds" :key="item.id" #[item.id]>
          <el-checkbox-group v-if="item.type == 1" v-model="editForm[item.id]">
            <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
            <el-checkbox :disabled="editOption.detail" v-for="item in item.requirementPropertyValuesVOS"
              :label="item.value" :key="item.value" />
          </el-checkbox-group>
          <avue-radio v-else :disabled="editOption.detail" v-model="editForm[item.id]" :dic="item.requirementPropertyValuesVOS.map(item => {
            return {
              label: item.value,
              value: item.value,
            };
          })
            "></avue-radio>
          <el-input :disabled="editOption.detail" v-if="JSON.stringify(editForm[item.id]).indexOf('其他') > -1"
            v-model="editForm[item.id + 'input']" type="textarea" placeholder="请输入内容"></el-input>
        </template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer" style="text-align: center">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button @click="$refs.dialogForm.submit()" v-if="!editOption.detail" type="primary">完成提交</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import demandSet from './components/demandSet.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  submitBtn: false,
  selection: true,
  selectable: (row) => {
    return row.requirementStatus == null || row.requirementStatus == 0;
  },
  reserveSelection: true,
  emptyBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 6,
  menuWidth: 280,
  border: true,
  column: {
    clueName: {
      label: '需求名称',
      type: 'input',
      search: true,
      rules: [
        {
          required: true,
          message: '请输入需求名称',
        },
      ],
    },
    // source: {
    //   label: '需求来源',
    //   type: 'select',
    //   span: 12,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择需求来源',
    //     },
    //   ],
    //   display: true,
    //   prop: 'source',
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=businessOpportunityResource',
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //   },
    // },
    customerId: {
      label: '客户名称',
      component: 'wf-customer-select',
      hide: true,
      width: 200,
      isHasAdd: true,
      formatter: row => {
        return row.customerName;
      },
      rules: [
        {
          required: true,
          message: '请选择客户名称',
        },
      ],
    },

    customerName: {
      label: '关联客户',
      display: false,
      search: true,
      component: 'wf-customer-drop',
    },
    remark: {
      label: '需求描述',
      type: 'textarea',
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入需求描述',
        },
      ],
    },
    // clueRequirementProperty: {
    //   label: '需求需求单',
    //   type: 'textarea',
    //   span: 24,
    //   width: 100,
    //   display: false,
    // },
    requirementStatus: {
      label: '需求状态',
      width: 120,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '未填写',
        },
        {
          value: 1,
          label: '已填写',
        },
      ],
      display: false,
    },
    createTime: {
      label: '创建时间',
      type: 'date',

      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      display: false,
      search: true,
      span: 12,
      width: 140,
      display: false,
    },
  },
});
let form = ref({ step: 0 });
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/clue/save';
const delUrl = '/api/vt-admin/clue/remove?ids=';
const updateUrl = '/api/vt-admin/clue/update';
const tableUrl = '/api/vt-admin/clue/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
      tableData.value = [{}];
      page.value.total = 1;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}
function reset() {
  onLoad();
}
let drawer = ref(false);
let currentRow = ref({});
let detailForm = ref({});
function set(row) {
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
        keys: [...new Set(res.data.data.propertyVOList.map(item => item.categoryId))],
      };

      form.value = res.data.data;
      currentRow.value = row;
      form.value.step = 1;
      drawer.value = true;
      option.value.emptyBtn = false;
    });
}
function confirmClick() {
  const data = proxy.$refs.demandSetRef.getData();
  if (form.value.id) {
    axios
      .post('/api/vt-admin/clue/addPropertyList', {
        id: currentRow.value.id,
        propertyDTOList: data.map(item => {
          return {
            requirementPropertyId: item.id,
          };
        }),
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        drawer.value = false;
        onLoad();
      });
  } else {
    axios
      .post(addUrl, {
        ...form.value,
        propertyDTOList: data.map(item => {
          return {
            requirementPropertyId: item.id,
          };
        }),
      })
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          drawer.value = false;
        }
      })
      .catch(err => {
        done();
      });
  }
}
let url = ref(null);
function share(row) {
  axios
    .get('/api/vt-admin/clue/getQrCode', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      url.value = `data:image/jpg;base64,${res.data.data}`;
    });
}
function editOrView(row, type) {
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
      };
      proxy.$refs.dialogForm.show({
        title: (type == 'edit' ? '编辑' : '查看') + '需求',

        option: {
          labelPosition: 'top',
          detail: type == 'view',
          column: res.data.data.propertyVOList.map(item => {
            return {
              label: item.name,
              prop: item.id,
              label: item.collectionContent,
              span: 24,
              value: item.type == 1 ? item.value && item.value.split(',') : item.value,
              type: item.type == 0 ? 'radio' : item.type == 1 ? 'checkbox' : 'textarea',
              props: {
                value: 'value',
                label: 'value',
              },
              dicData: item.type == 1 || item.type == 0 ? item.requirementPropertyValuesVOS : [],
            };
          }),
        },
        callback(res) {
          console.log(res);
          const data = {
            ...row,
            propertyDTOList: Object.keys(res.data).map(item => {
              return {
                id: item,
                value: Array.isArray(res.data[item]) ? res.data[item].join() : res.data[item],
              };
            }),
          };
          console.log(data);
          axios.post('/api/vt-admin/clue/complete', data).then(r => {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          });
        },
      });
    });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function addDemand() {
  form.value = {
    step: 0,
  };
  drawer.value = true;
  option.value.emptyBtn = false;
  proxy.$refs.addFormRef.clearValidate();
  proxy.$refs.addFormRef.resetForm();
}
let dialogVisible = ref(false);
let editOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelPosition: 'top',
  labelSuffix: ' ',
  column: [],
});
let editForm = ref({});
let propIds = ref([]);
function edit(row, type) {
  dialogVisible.value = true;
  currentRow.value = row;
  editOption.value.detail = type;
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
      };

      res.data.data.propertyVOList.forEach(item => {
        editForm.value[item.id] =
          item.type == 1 ? (item.value ? item.value.split(',') : []) : item.value;
        editForm.value[item.id + 'input'] =
          item.type == 0 || item.type == 1 ? item.otherInput : null;
        if (item.type == 0 || item.type == 1) {
          if (
            item.requirementPropertyValuesVOS.some(
              item => item.value && item.value.indexOf('其他') > -1
            )
          ) {
            propIds.value.push({
              ...item,
            });
          }
        }
      });
      const allData = res.data.data.propertyVOList.reduce((pre, cur) => {
        const data = pre.find(iten => iten.id == cur.categoryId);
        if (data) {
          data.tableData.push({ ...cur });
        } else {
          pre.push({
            id: cur.categoryId,
            categoryName: cur.categoryName,
            tableData: [{ ...cur }],
          });
        }
        return pre;
      }, []);

      editOption.value.group = allData.map(item => {
        return {
          label: item.categoryName,
          prop: item.id,
          column: item.tableData.map((item, index) => {
            return {
              prop: item.id,
              label: `${index + 1}.${item.collectionContent}${item.type == 0 ? '(单选)' : item.type == 1 ? '(多选)' : ''
                }`,
              span: 24,
              rules: [
                {
                  required: item.isRequired == 1,
                  message: '请填写此项',
                },
              ],
              placeholder: '请输入内容',
              value: item.type == 1 ? item.value && item.value.split(',') : item.value,
              type: item.type == 0 ? 'radio' : item.type == 1 ? 'checkbox' : 'textarea',
              props: {
                value: 'value',
                label: 'value',
              },
              dicData: item.type == 1 || item.type == 0 ? item.requirementPropertyValuesVOS : [],
            };
          }),
        };
      });
    });
  // const data = {
  //         ...row,
  //         propertyDTOList: Object.keys(res.data).map(item => {
  //           return {
  //             id: item,
  //             value: Array.isArray(res.data[item]) ? res.data[item].join() : res.data[item],
  //           };
  //         }),
  //       };

  //       axios.post('/api/vt-admin/clue/complete', data).then(r => {
  //         proxy.$message.success(r.data.msg);
  //         onLoad();
  //         res.close();
  //       });
}
function editSubmit(form, done, loading) {
  const data = {
    ...currentRow.value,
    propertyDTOList: Object.keys(form)
      .map(item => {
        return {
          id: item,
          value: Array.isArray(form[item]) ? form[item].join() : form[item],
          otherInput: form[item + 'input'] || null,
        };
      })
      .filter(item => item.id.indexOf('input') == -1),
  };

  axios.post('/api/vt-admin/clue/complete', data).then(r => {
    proxy.$message.success(r.data.msg);
    onLoad();
    done();
    dialogVisible.value = false;
  });
}
function nextStep(params) {
  proxy.$refs.addFormRef.validate(valid => {
    if (valid) {
      form.value.step = 1;
    } else {
      proxy.$message.error('请填写完整信息');
    }
  });
}

// 分享
let selectList = ref([]);
let shareDialog = ref(false);
let shareUrl = ref(null);
function handleSectionChange(list) {
  selectList.value = list;
}
function shareAll(row) {
  if (selectList.value.length == 0) {
    proxy.$message.error('请选择要分享的数据');
    return;
  }
  axios
    .get('/api/vt-admin/clue/getBatchQrCode', {
      params: {
        ids: selectList.value.map(item => item.id).join(','),
      },
    })
    .then(res => {
      shareUrl.value = `data:image/jpg;base64,${res.data.data}`;

    });
}
</script>

<style lang="scss" scoped></style>
