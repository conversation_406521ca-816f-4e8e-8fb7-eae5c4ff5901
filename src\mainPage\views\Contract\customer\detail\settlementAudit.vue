<template>
  <div class="settlement-apply-container">
    <el-form :model="form" label-width="150px" :rules="rules" ref="formRef">
       <!-- 合同信息 -->
      <div class="section-title">合同信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同名称:" prop="contractName">
            <span class="form-text">{{ props.detailForm.contractName }}</span>
          </el-form-item>
        </el-col>
          <!-- 合同总额 -->
        <el-col :span="12">
          <el-form-item label="合同总额:" prop="contractTotalPrice">
            <span class="form-text">{{ props.detailForm.contractTotalPrice }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理费点数:" prop="managementFeePoints">
                <el-input v-model="form.managementFeePoints" placeholder="请输入管理费点数">
                  <template #append>
                    <span>%</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理费额:" prop="managementFeeAmount">
                <span class="form-text">{{ form.managementFeeAmount }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
           <el-col :span="12">
              <el-form-item label="本合同已付管理费额:" prop="hasManageFee">
                <span class="form-text">{{ props.detailForm.hasManageFee }}</span>
              </el-form-item>
            </el-col>
             <el-col :span="12" v-if="(form.managementFeeAmount - (props.detailForm.hasManageFee || 0)) > 0">
              <el-form-item label="本合同可付管理费额:" prop="hasManageFee">
                <span class="form-text">{{ form.managementFeeAmount -( props.detailForm.hasManageFee || 0) }}</span>
              </el-form-item>
            </el-col>
      </el-row>
      <!-- 基本信息 -->
      <div class="section-title">基本信息</div>
      
      <el-row :gutter="20">
     
        <el-col :span="12">
          <el-form-item label="计划名称:" prop="planIds">
            <el-select
              @change="handlePlanChange($event)"
              multiple
              v-model="form.planIds"
              placeholder="请选择结算计划"
              clearable
              style="width: 100%"
            >
              <el-option
                :label="item.planName"
                v-for="item in planList"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划付款金额:" prop="planAmount">
            <span class="form-text">{{ form.planAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="税率:" prop="taxPoints">
            <el-select
              v-model="form.taxPoints"
              placeholder="请选择税点"
              clearable
              style="width: 100%"
            >
              <el-option
                :label="item.dictValue"
                v-for="item in taxRateData"
                :value="item.dictKey"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税额:" prop="taxAmount">
            <span class="form-text">{{ form.taxAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="(form.managementFeeAmount - (props.detailForm.hasManageFee || 0)) > 0">
        <el-col :span="24">
          <el-form-item label="本次付管理费金额:" prop="currentManagementFee">
            <div style="display: flex; flex: 1 1">
              <el-input
                v-model="form.currentManagementFee"
                @input="handleCurrentFeeInput"
                placeholder="本次付管理费金额"
              ></el-input>
              <el-input
                v-model="form.managementFeeRatio"
                @input="handleRatioInput"
                style="width: 200px"
                placeholder="比例"
              >
                <template #append>
                  <span>%</span>
                </template>
              </el-input>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
<!-- 相关订单抵扣 -->
      <div class="section-title">采购订单抵扣</div>
      <el-table
        :data="props.detailForm.orderVOList"
        style="width: 100%"
        border
        align="center"
        class="order-table"
        @selection-change="handleSelectionChange"
        ref="tableRef"
      >
        <el-table-column type="selection" align="center" width="55"></el-table-column>
        <el-table-column prop="orderNo" align="center" label="合同编号" ></el-table-column>
        <el-table-column prop="orderTaxPoint" align="center" label="合同税率">
          <template #default="scope">
             <el-select
             
              placeholder="请选择税点"
              clearable
              v-if="scope.row.selected"
               v-model="scope.row.orderTaxPoint" size="small" @change="calculateTax(scope.row)"
              style="width: 100%"
            >
              <el-option
                :label="item.dictValue"
                v-for="item in taxRateData"
                :value="item.dictKey"
              ></el-option>
            </el-select>
           
            <span v-else>{{ scope.row.orderTaxPoint }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderNotSettleAmount" align="center" label="合同不含税金额">
          <!-- <template #default="scope">
            <el-input v-if="scope.row.selected" v-model="scope.row.orderNotSettleAmount" size="small" @change="calculateFromNotTaxAmount(scope.row)"></el-input>
            <span v-else>{{ scope.row.orderNotSettleAmount }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="orderSettleAmount" align="center" label="合同税额"></el-table-column>
        <el-table-column prop="totalPrice" align="center" label="合同金额">
          <template #default="scope">
            <el-input v-if="scope.row.selected" v-model="scope.row.totalPrice" size="small" @change="calculateTaxPoint(scope.row)"></el-input>
            <span v-else>{{ scope.row.totalPrice }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 结算信息 -->
      <div class="section-title">结算信息（非最终结算金额）</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="settlement-info">
            <div class="settlement-section">
              <div class="settlement-title">本次收票应包含税额：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">税额:</span>
                  <span class="item-value">+{{ form.taxAmount || 0 }}</span>
                </div>
                <div class="settlement-item">
                  <span class="item-label">选中合同税额:</span>
                  <span class="item-value">-{{ getSelectedContractTaxAmount() }}</span>
                </div>
                <div class="settlement-item total">
                  <span class="item-label">总计:</span>
                  <span class="item-value">{{ calculateInvoiceAmount() }}</span>
                </div>
              </div>
            </div>
            <div class="settlement-section">
              <div class="settlement-title">本次应付款：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">付款额:</span>
                  <span class="item-value">+{{ form.planAmount || 0 }}</span>
                </div>
                <div class="settlement-item">
                  <span class="item-label">本次付管理费:</span>
                  <span class="item-value">-{{ form.currentManagementFee || 0 }}</span>
                </div>
                <div class="settlement-item">
                  <span class="item-label">选中合同金额:</span>
                  <span class="item-value">-{{ getSelectedContractAmount() }}</span>
                </div>
                <div class="settlement-item total">
                  <span class="item-label">总计</span>
                  <span class="item-value">{{ calculateReceivableAmount() }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <!-- 按钮已移至抽屉footer插槽 -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, defineProps, defineEmits, nextTick } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  detailForm: Object,
});

const emit = defineEmits(['submit-success']);

// 表单数据
const form = reactive({
  contractTotalPrice: '',
  planIds: [],
  planAmount: '',
  taxPoints: '',
  taxAmount: '',
  managementFeePoints: '',
  managementFeeAmount: '',
  currentManagementFee: '',
  managementFeeRatio: '',
  orderDeductions: [], // 订单抵扣数据
});

// 计算并更新税额和管理费额
watch(
  () => [form.planAmount, form.taxPoints],
  ([planAmount, taxPoints]) => {
   
    if (planAmount && taxPoints) {
      // 计算税额
      form.taxAmount = ((planAmount / (1 + taxPoints / 100)) * (taxPoints / 100)).toFixed(2);
    } else {
      form.taxAmount = '0.00';
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => [form.planAmount, form.managementFeePoints],
  ([planAmount, managementFeePoints]) => {
    if (planAmount && managementFeePoints) {
      // 计算管理费额
      form.managementFeeAmount = ((Number(props.detailForm.contractTotalPrice) * Number(managementFeePoints)) / 100).toFixed(
        2
      );
      // 如果已有比例，则更新本次付管理费金额
      if (form.managementFeeRatio) {
        calculateCurrentFee();
      }
    } else {
      form.managementFeeAmount = '0.00';
    }
  },
  { immediate: true, deep: true }
);
const planList = computed(() => {
  
  console.log( [
    ...(props.detailForm?.planCollectionVOList?.filter(item => item.isCheck != 1) || []),
    ...(props.rowData?.planVOList?.map(item => {
      return {
        planName: item.planName,
        id: item.planId,
      };
    }) || []),
  ]);
  
  return [
    ...(props.detailForm?.planCollectionVOList?.filter(item => item.isCheck != 1) || []),
    ...(props.rowData?.planVOList?.map(item => {
      return {
        planName: item.planName,
        id: item.planId,
      };
    }) || []),
  ];
});

const tableRef = ref(null);

// 处理表格选择变更
const handleSelectionChange = (selection) => {
  // 重置所有行的选中状态
  if (props.detailForm.orderVOList) {
    props.detailForm.orderVOList.forEach(row => {
      row.selected = false;
    });
  }
  
  // 设置选中行的状态
  selection.forEach(row => {
    const index = props.detailForm.orderVOList.findIndex(item => item.orderNo === row.orderNo);
    if (index !== -1) {
      props.detailForm.orderVOList[index].selected = true;
      // 如果税率为空或未设置，设置默认值13%
      if (!props.detailForm.orderVOList[index].orderTaxPoint || props.detailForm.orderVOList[index].orderTaxPoint === '') {
        props.detailForm.orderVOList[index].orderTaxPoint = '13';
      }
    }
  });
};

// 根据税率计算税额和总金额
const calculateTax = (row) => {
  // 移除百分号并转换为数字
  const taxPointStr = String(row.orderTaxPoint).replace('%', '');
  const taxPoint = parseFloat(taxPointStr) / 100;
  const notSettleAmount = parseFloat(row.orderNotSettleAmount);
  
  if (!isNaN(taxPoint) && !isNaN(notSettleAmount)) {
    // 计算税额
    const taxAmount = notSettleAmount * taxPoint;
    row.orderSettleAmount = taxAmount.toFixed(2);
    
    // 计算总金额
    row.totalPrice = (notSettleAmount + taxAmount).toFixed(2);
  }
};

// 根据合同金额反推不含税金额和税额
const calculateTaxPoint = (row) => {
  const totalAmount = parseFloat(row.totalPrice);
  const taxPointStr = String(row.orderTaxPoint).replace('%', '');
  const taxPoint = parseFloat(taxPointStr) / 100;
  
  if (!isNaN(totalAmount) && !isNaN(taxPoint) && taxPoint >= 0) {
    // 根据公式：含税金额 = 不含税金额 * (1 + 税率)
    // 反推：不含税金额 = 含税金额 / (1 + 税率)
    const notSettleAmount = totalAmount / (1 + taxPoint);
    const taxAmount = totalAmount - notSettleAmount;
    
    row.orderNotSettleAmount = notSettleAmount.toFixed(2);
    row.orderSettleAmount = taxAmount.toFixed(2);
  }
};

// 根据不含税金额计算税额和总金额
const calculateFromNotTaxAmount = (row) => {
  const taxPointStr = String(row.orderTaxPoint).replace('%', '');
  const taxPoint = parseFloat(taxPointStr) / 100;
  const notSettleAmount = parseFloat(row.orderNotSettleAmount);
  
  if (!isNaN(taxPoint) && !isNaN(notSettleAmount)) {
    // 计算税额
    const taxAmount = notSettleAmount * taxPoint;
    row.orderSettleAmount = taxAmount.toFixed(2);
    
    // 计算总金额
    row.totalPrice = (notSettleAmount + taxAmount).toFixed(2);
  }
};

// 监听rowData变化，更新表单数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal && Object.keys(newVal).length > 0) {
      // 根据传入的行数据更新表单，用于编辑模式
      form.planAmount = newVal.applySettlementPrice || '';
      form.taxPoints = '' + parseFloat(newVal.taxPoints) || '';
      form.managementFeePoints = parseFloat(newVal.managementFeePoints) || '';
      // form.managementFeeAmount = newVal.managementFee || '';
      form.currentManagementFee = newVal.managementFee || '';
      form.planIds = newVal.planIds || [];
      nextTick(() => {
        calculateRatio();
      });
    }
  },
  { immediate: true, deep: true }
);

// 表单验证规则
const rules = {
  planIds: [{ required: true, message: '请选择结算计划', trigger: 'change' }],
  taxPoints: [{ required: true, message: '请选择税点', trigger: 'change' }],
  taxAmount: [{ required: true, message: '税额不能为空', trigger: 'blur' }],
  managementFeePoints: [{ required: true, message: '请输入管理费点数', trigger: 'blur' }],
  managementFeeAmount: [{ required: true, message: '管理费额不能为空', trigger: 'blur' }],
  currentManagementFee: [{ required: true, message: '请输入本次付管理费金额', trigger: 'blur' }],
};

const formRef = ref(null);

// 计算管理费比例
const calculateRatio = () => {
  if (form.managementFeeAmount && form.currentManagementFee) {
    const ratio =
      (parseFloat(form.currentManagementFee) / parseFloat(form.managementFeeAmount)) * 100;
    form.managementFeeRatio = ratio.toFixed(2);
  }
};

// 根据比例计算本次付管理费金额
const calculateCurrentFee = () => {
  if (form.managementFeeAmount && form.managementFeeRatio) {
    const fee = (parseFloat(form.managementFeeAmount) * parseFloat(form.managementFeeRatio)) / 100;
    form.currentManagementFee = fee.toFixed(2);
  }
};

// 处理本次付管理费金额输入
const handleCurrentFeeInput = () => {
  // 当用户手动输入本次付管理费金额时，自动计算对应的比例
  calculateRatio();
};

// 处理比例输入
const handleRatioInput = () => {
  // 移除可能的百分号
  if (
    form.managementFeeRatio &&
    typeof form.managementFeeRatio === 'string' &&
    form.managementFeeRatio.includes('%')
  ) {
    form.managementFeeRatio = form.managementFeeRatio.replace('%', '');
  }
  // 当用户输入比例时，自动计算本次付管理费金额
  calculateCurrentFee();
};

// 监听管理费点数变化
const watchManagementFeePoint = () => {
  if (form.managementFeePoints && form.planAmount) {
    const amount = (parseFloat(form.managementFeePoints) / 100) * parseFloat(form.planAmount);
    form.managementFeeAmount = amount.toFixed(2);
    // 如果已有比例，则更新本次付管理费金额
    if (form.managementFeeRatio) {
      calculateCurrentFee();
    }
  }
};

// 监听本次付管理费金额变化
const watchCurrentManagementFee = () => {
  calculateRatio();
};

// 计算本次应收票金额
const calculateInvoiceAmount = () => {
  // 本次收票应包含税额 = 税额 - 选中的合同数据算出来的税额
  const taxAmount = parseFloat(form.taxAmount || 0);
  
  // 计算选中合同的税额总和
  let selectedContractTaxAmount = 0;
  if (props.detailForm.orderVOList) {
    props.detailForm.orderVOList.forEach(row => {
      if (row.selected && row.orderSettleAmount) {
        selectedContractTaxAmount += parseFloat(row.orderSettleAmount || 0);
      }
    });
  }
  
  const invoiceAmount = taxAmount - selectedContractTaxAmount;
  return invoiceAmount.toFixed(2);
};

// 计算本次应付款
const calculateReceivableAmount = () => {
  // 本次应付款 = 付款额 - 管理费 - 选中的合同的合同金额
  const planAmount = parseFloat(form.planAmount || 0);
  const currentManagementFee = parseFloat(form.currentManagementFee || 0);
  
  // 计算选中合同的合同金额总和
  let selectedContractAmount = 0;
  if (props.detailForm.orderVOList) {
    props.detailForm.orderVOList.forEach(row => {
      if (row.selected && row.totalPrice) {
        selectedContractAmount += parseFloat(row.totalPrice || 0);
      }
    });
  }
  
  const receivableAmount = planAmount - currentManagementFee - selectedContractAmount;
  return receivableAmount.toFixed(2);
};

// 获取选中合同的税额总和
const getSelectedContractTaxAmount = () => {
  let selectedContractTaxAmount = 0;
  if (props.detailForm.orderVOList) {
    props.detailForm.orderVOList.forEach(row => {
      if (row.selected && row.orderSettleAmount) {
        selectedContractTaxAmount += parseFloat(row.orderSettleAmount || 0);
      }
    });
  }
  return selectedContractTaxAmount.toFixed(2);
};

// 获取选中合同的合同金额总和
const getSelectedContractAmount = () => {
  let selectedContractAmount = 0;
  if (props.detailForm.orderVOList) {
    props.detailForm.orderVOList.forEach(row => {
      if (row.selected && row.totalPrice) {
        selectedContractAmount += parseFloat(row.totalPrice || 0);
      }
    });
  }
  return selectedContractAmount.toFixed(2);
};

// 提交表单
const submitForm = () => {
  formRef.value.validate(valid => {
    if (valid) {
      const data = {
        ...form,
        ...props.rowData,
        purchaseDTOList: props.detailForm.orderVOList.filter(row => row.selected).map(item => {
          return {
            purchaseOrderId: item.id,
            totalPrice: item.totalPrice,
            taxRate: item.orderTaxPoint,
          };
        }), // 过滤出选中的合同
        shouldSettmentPrice: calculateReceivableAmount(),
        invoicePrice: calculateInvoiceAmount(),
        managementFee: form.currentManagementFee,
        sealContractId: props.detailForm.id,
        isDeductManagementFee: 1,
        totalTaxPrice:form.taxAmount,
        purchaseTaxPrice: getSelectedContractTaxAmount(),
        surplusTaxPrice: calculateInvoiceAmount(),
        purchaseTotalPrice: getSelectedContractAmount(),
      };

     
        
        axios.post('/api/vt-admin/sealContractSettlement/updateSettlement', data).then(res => {
          emit('submit-success');
        });
      
    } else {
      ElMessage.error('请填写必填项');
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value.resetFields();
};

onMounted(() => {
  // 可以在这里加载初始数据
  // 初始化时，如果已有管理费额和比例，计算本次付管理费金额
  if (form.managementFeeAmount && form.managementFeeRatio) {
    calculateCurrentFee();
  }
});

function handlePlanChange(val) {
  // 计算选中计划的总金额
  let totalAmount = 0;
  const selectedPlans = props.detailForm.planCollectionVOList.filter(item => val.includes(item.id));
  selectedPlans.forEach(plan => {
    totalAmount += Number(plan.planCollectionPrice);
  });
  form.planAmount = totalAmount.toFixed(2);
}
// 获取税率数据
let taxRateData = ref([]);
onMounted(() => {
  getTaxRate();
});
const getTaxRate = () => {
  axios.get('/api/blade-system/dict/dictionary?code=tax').then(res => {
    taxRateData.value = res.data.data;
  });
};
defineExpose({
  submitForm,
  resetForm,
  handlePlanChange,
});
</script>

<style lang="scss" scoped>
.settlement-apply-container {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 15px 0;
  padding-left: 10px;
  border-left: 4px solid var(--el-color-primary);
}

.settlement-info {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.settlement-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.settlement-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.settlement-content {
  padding-left: 20px;
}

.settlement-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;

  &.total {
    font-weight: bold;
    border-top: 1px solid #dcdfe6;
    padding-top: 5px;
    margin-top: 5px;
  }
}

.item-label {
  color: #606266;
}

.item-value {
  font-weight: 500;
}

.form-text {
  display: inline-block;
  min-height: 32px;
  line-height: 32px;
  padding: 0 15px;
  color: #606266;
  width: 100%;
  border-radius: 4px;
  background-color: #f5f7fa;
}
</style>
