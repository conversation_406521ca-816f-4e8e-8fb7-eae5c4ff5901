<template>
    <basic-container>
        <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
            @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud" @keyup.enter="onLoad"
            @row-del="rowDel" @search-reset="reset" @search-change="searchChange" @refresh-change="onLoad"
            @current-change="onLoad" @size-change="onLoad" v-model="form">

            <!-- 开票公司插槽 -->
            <template #invoiceCompanyName="{ row }">
                <div style="display: flex;align-items: center;justify-content: center;">
                    <span>{{ row.invoiceCompanyName }}</span>
                    <el-icon style="margin-left: 8px; cursor: pointer; color: #409EFF;" @click="showInvoiceInfo(row)">
                        <InfoFilled />
                    </el-icon>
                </div>
            </template>

            <!-- 银行信息插槽 -->
            <template #bankInfo="{ row }">
                <div style="display: flex;align-items: center;justify-content: center;">
                    <span>{{ row.bankName || '查看详情' }}</span>
                    <el-icon style="margin-left: 8px; cursor: pointer; color: #409EFF;" @click="showBankInfo(row)">
                        <InfoFilled />
                    </el-icon>
                </div>
            </template>
            <template #partnerName="{ row }">
                <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.partnerName }}</el-link>

            </template>
        </avue-crud>
        <dialogForm ref="dialogForm"></dialogForm>

        <!-- 开票信息弹窗 -->
        <el-dialog v-model="invoiceDialogVisible" title="开票信息" width="500px">
            <el-descriptions :column="1" border>
                <el-descriptions-item label="开票公司">{{ currentInvoiceInfo.invoiceCompanyName }}</el-descriptions-item>
                <el-descriptions-item label="纳税人识别号">{{ currentInvoiceInfo.ratepayerIdentifyNumber
                    }}</el-descriptions-item>
            </el-descriptions>
        </el-dialog>

        <!-- 银行信息弹窗 -->
        <el-dialog v-model="bankDialogVisible" title="银行信息" width="500px">
            <el-descriptions :column="1" border>
                <el-descriptions-item label="账号名称">{{ currentBankInfo.accountName }}</el-descriptions-item>
                <el-descriptions-item label="开户行账号">{{ currentBankInfo.bankAccount }}</el-descriptions-item>
                <el-descriptions-item label="开户行名称">{{ currentBankInfo.bankName }}</el-descriptions-item>
            </el-descriptions>
        </el-dialog>
    </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { InfoFilled } from '@element-plus/icons-vue';

let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: true,
    editBtn: true,
    delBtn: true,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    searchLabelWidth: 120,
    dialogType: 'drawer',
    menuWidth: 160,
    border: true,
    labelWidth: 120,
    column: [
        {
            label: '公司名称',
            prop: 'partnerName',
            search: true,
            align: 'center',
            rules: [
                { required: true, message: '请输入公司名称', trigger: 'blur' },
            ],
        },
        {
            label: '联系人',
            prop: 'contactPerson',
            width: 120,
            align: 'center',
        },
        {
            label: '联系电话',
            prop: 'contactPhone',
            width: 160,
            align: 'center',
        },
        // {
        //     label: '业务员',
        //     component: 'wf-user-select',
        //     prop: 'businessPerson',
        //     width: 120,
        //     formatter: (row) => {
        //         return row.businessPersonName
        //     },
        //     rules: [
        //         { required: true, message: '请选择业务员', trigger: 'blur' },
        //     ],
        //     params: {
        //         userUrl: '/api/blade-system/search/user?functionKeys=bussinessUser'
        //     },
        //     // params: {
        //     //   checkType: 'checkbox',
        //     // },

        // },
        {
            label: '开票公司',
            prop: 'invoiceCompanyName',
            align: 'center',
            display: false,
            slot: true,
        },
        {
            label: '银行信息',
            prop: 'bankInfo',
            align: 'center',
           
            display: false,
            slot: true,
        },
        {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            overHidden: true,
            span: 24,
        }
    ],
    group: [{
        label: '开票信息',
        prop: 'invoiceInfo',
        column: [{
            label: '开票公司',
            prop: 'invoiceCompanyName',
            span: 12,
            search: true,
        },
        {
            label: '纳税人识别号',
            prop: 'ratepayerIdentifyNumber',
            span: 12,
        },
        ]
    },
    // 银行信息
    {
        label: '银行信息',
        prop: 'bankInfo',
        column: [    {
            label: '开户行名称',
            prop: 'bankName',
            span: 24,
        },{
            label: '账号名称',
            prop: 'accountName',
            span: 12,
        },
        {
            label: '开户行账号',
            prop: 'bankAccount',
            span: 12,
        },
    
        ]
    }
    ]
});
let form = ref({});
let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
});

const addUrl = '/api/vt-admin/partner/save'
const delUrl = '/api/vt-admin/partner/remove?ids='
const updateUrl = '/api/vt-admin/partner/update'
const tableUrl = '/api/vt-admin/partner/page'
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);

// 弹窗控制
let invoiceDialogVisible = ref(false);
let bankDialogVisible = ref(false);
let currentInvoiceInfo = ref({});
let currentBankInfo = ref({});
function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
        .get(tableUrl, {
            params: {
                type: 1,
                size,
                current,
                ...params.value,
                selectType: 1
            },
        })
        .then(res => {
            loading.value = false;
            tableData.value = res.data.data.records;
            page.value.total = res.data.data.total;
        });
}
let router = useRouter();

function rowSave(form, done, loading) {
    const data = {
        ...form,
        type: 1
    };
    axios
        .post(addUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(err => {
            done();
        });
}
function rowUpdate(row, index, done, loading) {
    const data = {
        ...row,
    };
    axios
        .post(updateUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(err => {
            done();
        });
}
function rowDel(form) {
    proxy
        .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(() => {
            console.log(222);
            axios.post(delUrl + form.id).then(res => {
                proxy.$message({
                    type: 'success',
                    message: '删除成功',
                });
                onLoad();
            });
        })
        .catch(() => { });
}
function reset() {
    onLoad()
}
function searchChange(params, done) {
    onLoad();
    done();
}

// 显示开票信息
function showInvoiceInfo(row) {
    currentInvoiceInfo.value = {
        invoiceCompanyName: row.invoiceCompanyName,
        ratepayerIdentifyNumber: row.ratepayerIdentifyNumber
    };
    invoiceDialogVisible.value = true;
}

// 显示银行信息
function showBankInfo(row) {
    currentBankInfo.value = {
        accountName: row.accountName,
        bankAccount: row.bankAccount,
        bankName: row.bankName
    };
    bankDialogVisible.value = true;
}
</script>

<style lang="scss" scoped></style>