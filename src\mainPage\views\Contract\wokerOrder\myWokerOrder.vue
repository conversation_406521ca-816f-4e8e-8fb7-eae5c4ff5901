<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div class="stats-container" style="display: flex; align-items: center">
          <div>
            <el-button type="primary" @click="addNew" icon="plus">新增</el-button>
            <el-button type="primary" @click="addHistory" icon="plus">补录历史工单</el-button>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><document /></el-icon>
              工单总数
            </div>
            <el-text class="stat-value" type="primary">{{ totalNumber }}</el-text>
          </div>

          <div class="stat-item">
            <div class="stat-label">
              <el-icon><alarm-clock /></el-icon>
              总工时
            </div>
            <el-text class="stat-value" type="info">{{ totalTimes }}h</el-text>
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          icon="pointer"
          v-if="!row.externalHandleUser && !row.handleUser"
          text
          @click="add(row)"
          >派单</el-button
        >
        <el-button
          type="primary"
          icon="pointer"
          v-if="row.objectStatus == 2"
          text
          @click="closeOrder(row)"
          >关单</el-button
        >
        <!--改派 -->
        <el-button
          type="primary"
          icon="pointer"
          v-if="
            row.objectStatus != 4 &&
            row.objectStatus != 2 &&
            (row.externalHandleUser || row.handleUser)
          "
          text
          @click="changeEngineer(row)"
          >改派</el-button
        >
        <!-- 再次派单 -->
        <el-button
          type="primary"
          icon="pointer"
          v-if="row.objectStatus == 2 || row.objectStatus == 4"
          text
          @click="addAgain(row)"
          >再次派单</el-button
        >
        <!-- <el-button
          type="primary"
          icon="edit"
          text
          v-if="row.objectStatus != 2 && row.objectStatus != 4"
          @click="$refs.crud.rowEdit(row)"
          >编辑</el-button
        > -->
        <!-- <el-button type="primary" icon="delete"  text @click="$refs.crud.rowDel(row)"
          >删除
        </el-button> -->
      </template>
      <template #externalHandleUser-form="{ type }">
        <div v-if="type !== 'view'">
          <!-- 工程师标签列表 -->
          <div
            v-if="form.externalHandleUserList && form.externalHandleUserList.length > 0"
            style="margin-bottom: 16px; display: flex; flex-wrap: wrap; gap: 10px"
          >
            <el-tag
              v-for="row in form.externalHandleUserList"
              :key="row.id"
              type="info"
              closable
              @close="handleDelete(row)"
              style="font-size: 15px; padding: 6px 16px; display: flex; align-items: center"
            >
              {{ row.realName }}
            </el-tag>
          </div>

          <!-- 添加工程师按钮 -->
          <div style="display: flex; gap: 12px">
            <el-button
              type="primary"
              v-if="form.objectType == 0"
              icon="plus"
              @click="handleAddIn"
              size="small"
              >添加内部工程师</el-button
            >
            <el-button
              type="primary"
              v-if="form.objectType == 1"
              icon="plus"
              @click="handleAddOut"
              size="small"
              >添加外部工程师</el-button
            >
          </div>
        </div>
        <el-text style="padding-left: 30px" disabled v-else>
          {{ form.handleUserName }}
        </el-text>
      </template>
      <template #objectStatus="{ row }">
        <el-tag :type="['info', 'warning', 'success', 'info'][row.objectStatus]" effect="plain">{{
          row.$objectStatus
        }}</el-tag>
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)" target="_blank">{{
          row.objectName
        }}</el-link>
      </template>
      <template #filesDetail-form>
        <File :fileList="form.fileList"></File>
      </template>
      <template #completeFiles-form>
        <File :fileList="form.completeFilesList"></File>
      </template>
      <template #taskName-form>
        <el-descriptions :title="form.objectName">
          <el-descriptions-item label="预约时间">{{
            (form.reservationTime && form.reservationTime.split(' ')[0]) || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务客户">{{
            form.finalCustomer || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务联系人">{{ form.contact || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            form.contactPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">
            {{ form.distributionAddress || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="工单描述" :span="3">
            <span style="white-space: pre-line"> {{ form.taskDescription || '--' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <template #completeInfoList-form>
        <el-collapse v-model="activeName">
          <el-collapse-item
            :title="item.realName"
            v-for="(item, index) in form.externalHandleUserList"
            :name="item.realName"
          >
            <avue-form
              :option="completeFormOption"
              v-model="item.formData"
              :ref="`completeForm${index}`"
            ></avue-form>
          </el-collapse-item>
        </el-collapse>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect
      userUrl="/api/blade-system/search/user?functionKeys=engineer"
      ref="userSelectRef"
      checkType="checkbox"
      @onConfirm="handleConfirm"
    ></wfUserSelect>
    <humanSelect
      ref="userSelectRefOut"
      checkType="checkbox"
      @select="handleConfirmOut"
    ></humanSelect>
    <el-drawer title="详情" v-model="drawer" size="50%">
      <avue-form :option="detailOption" v-model="detailForm">
        <template #taskName>
          <el-descriptions :title="detailForm.objectName">
            <el-descriptions-item label="预约时间">{{
              (detailForm.reservationTime && detailForm.reservationTime.split(' ')[0]) || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务客户">{{
              detailForm.finalCustomer || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务联系人">{{
              detailForm.contact || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              detailForm.contactPhone || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务地址" :span="2">
              {{ detailForm.distributionAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="工单描述" :span="3">
              <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #files>
          <File :fileList="detailForm.fileList"></File>
        </template>
        <!-- <template #completeFiles>
          <File :fileList="detailForm.completeFilesList"></File>
        </template> -->
        <template #engineerServiceInfo>
          <div
            v-if="
              detailForm.sealContractObjectResultVOList &&
              detailForm.sealContractObjectResultVOList.length > 0
            "
            class="engineer-list"
          >
            <el-card
              v-for="(engineer, index) in detailForm.sealContractObjectResultVOList"
              :key="index"
              class="engineer-card"
              shadow="hover"
            >
              <template #header>
                <div class="engineer-header">
                  <h3>{{ engineer.handleName || '工程师' + (index + 1) }}</h3>
                </div>
              </template>
              <div class="engineer-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="label">服务开始时间：</span>
                    <span>{{ engineer.serviceStartTime || '--' }}</span>
                  </div>
                  <!-- <div class="info-item">
                    <span class="label">实际完成时间：</span>
                    <span>{{ engineer.completeTime.split(' ')[0] || '--' }}</span>
                  </div> -->
                  <div class="info-item">
                    <span class="label">服务结束时间：</span>
                    <span>{{ engineer.serviceEndTime || '--' }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">所用工时：</span>
                    <span>{{ engineer.useTimes || '--' }}小时</span>
                  </div>
                </div>
                <div
                  v-if="engineer.workOrderPhotoList && engineer.workOrderPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">现场图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.workOrderPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.workOrderPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>
                <div
                  v-if="engineer.handleResultPhotoList && engineer.handleResultPhotoList.length > 0"
                  class="info-item"
                >
                  <span class="label">处理结果图：</span>
                  <div class="image-list">
                    <el-image
                      v-for="(photo, photoIndex) in engineer.handleResultPhotoList"
                      :key="photoIndex"
                      :src="photo.link"
                      :preview-src-list="engineer.handleResultPhotoList.map(p => p.link)"
                      class="result-image"
                    >
                    </el-image>
                  </div>
                </div>

                <div v-if="engineer.serviceReorder" class="info-item">
                  <span class="label">服务复盘：</span>
                  <div class="service-reorder">
                    <pre>{{ engineer.serviceReorder }}</pre>
                  </div>
                </div>

                <div v-if="engineer.completeRemark" class="info-item">
                  <span class="label">备注：</span>
                  <div>{{ engineer.completeRemark }}</div>
                </div>
              </div>
            </el-card>
          </div>
          <el-empty v-else description="暂无工程师服务信息"></el-empty>
        </template>
      </avue-form>
    </el-drawer>

    <!-- 改派 -->
    <el-dialog
      title="改派"
      v-model="changeEngineerDialogVisible"
      width="30%"
      class="avue-dialog"
      :before-close="closeChangeEngineerDialog"
    >
      <avue-form
        ref="changeEngineerFormRef"
        :option="changeEngineerOption"
        v-model="changeEngineerForm"
      >
        <template #externalHandleUser="{ type }">
          <div>
            <!-- 工程师标签列表 -->
            <div
              v-if="
                changeEngineerForm.externalHandleUserList &&
                changeEngineerForm.externalHandleUserList.length > 0
              "
              style="margin-bottom: 16px; display: flex; flex-wrap: wrap; gap: 10px"
            >
              <el-tag
                v-for="row in changeEngineerForm.externalHandleUserList"
                :key="row.id"
                type="info"
                closable
                @close="handleDelete(row)"
                style="font-size: 15px; padding: 6px 16px; display: flex; align-items: center"
              >
                {{ row.realName }}
              </el-tag>
            </div>

            <!-- 添加工程师按钮 -->
            <div style="display: flex; gap: 12px">
              <el-button
                type="primary"
                v-if="changeEngineerForm.objectType == 0"
                icon="plus"
                @click="handleAddIn"
                size="small"
                >添加内部工程师</el-button
              >
              <el-button
                type="primary"
                v-if="changeEngineerForm.objectType == 1"
                icon="plus"
                @click="handleAddOut"
                size="small"
                >添加外部工程师</el-button
              >
            </div>
          </div>
        </template></avue-form
      >
      <div class="avue-dialog__footer">
        <span>
          <el-button @click="changeEngineerDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmChangeEngineer">确认</el-button>
        </span>
      </div>
    </el-dialog>

    <wokerOrderDetail ref="wokerOrderDetailRef"></wokerOrderDetail>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import userSelect from './component/userSelect.vue';
import { dateFormat } from '@/utils/date.js';
import humanSelect from './component/humanSelect.vue';
import wokerOrderDetail from './component/wokerOrderDetail.vue';
let contactList = ref([]);
let changeType = ref(0); // 0 新增工程师  1 改派工程师
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 180,
  labelWidth: 120,
  updateBtnText: '提交',

  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    // {
    //   label: '工单编号',
    //   prop: 'objectNo',
    //   display: false,
    // },

    {
      label: '服务类型',
      type: 'tree',
      display: false,
      props: {
        value: 'id',
        label: 'dictValue',
      },
      width: 120,
      rules: [
        {
          required: true,
          message: '请选择工单类型',
          trigger: 'blur',
        },
      ],
      dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
      prop: 'serverType',
    },
    {
      label: '工单名称',
      prop: 'objectName',
      search: true,
      display: false,
    },
    {
      label: '关联合同',
      prop: 'sealContractId',
      placeholder: '请选择关联合同',
      addDisplay: true,

      component: 'wf-contract-select',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5',
      },
      change: val => {
        setBaseInfo(val.value);
      },
      formatter: row => {
        return row.contractName;
      },
      // rules: [
      //   {
      //     required: true,
      //     message: '请选择关联合同',
      //     trigger: 'blur',
      //   },
      // ],
    },
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '服务时间',
      prop: 'planTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
      overHidden: true,
      formatter: row => {
        return `${dateFormat(new Date(row.serviceStartTime), 'yyyy-MM-dd hh:mm')} - ${dateFormat(
          new Date(row.serviceEndTime),
          'yyyy-MM-dd hh:mm'
        )}`;
      },
    },
    {
      label: '工单价格',
      prop: 'orderPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,
      width: 110,
      labelWidth: 120,
      display: false,
    },
    {
      label: '发布人',
      prop: 'createName',
      display: false,
      width: 110,
    },
    {
      label: '发布时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      span: 12,
      component: 'wf-daterange-search',
      searchSpan: 6,
      search: true,
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '工程师',
      prop: 'handleUserName',
      width: 150,
      display: false,
      overHidden: true,
      search: true,
    },
    {
      label: '工单状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      width: 110,
      dicData: [
        {
          label: '待派单',

          value: 0,
        },
        {
          label: '待接单',
          value: 3,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        },
        {
          label: '已关单',
          value: 4,
        },
      ],
    },
  ],
  group: [
    // {
    //   label: '工单信息',
    //   prop: 'taskInfo',
    //   addDispla: true,
    //   disabled: true,
    //   column: [
    //     {
    //       labelWidth: 0,
    //       span: 24,
    //       prop: 'taskName',
    //     },
    //   ],
    // },
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
          type: 'select',
          allowCreate: true,
          filterable: true,
          placeholder: '请选择联系人',
          props: {
            label: 'label',
            value: 'value',
          },

          dicFormatter: res => {
            contactList.value = res.data.records;
            return res.data.records
              .map(item => {
                return {
                  label: item.name,
                  value: item.name,
                  customerId: item.customerId,
                };
              })
              .filter(item => item.customerId == form.value.customerId);
          },
          // disabled: true,
          dicUrl: '/api/vt-admin/customerContact/page',
          params: {},
          change: val => {
            setContactInfo(contactList.value.find(item => item.name == val) || {});
          },
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        // {
        //   type: 'date',
        //   label: '预约时间',
        //   span: 12,

        //   width: 140,
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //   prop: 'reservationTime',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择预约时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
        // {
        //   label: '故障类型',
        //   prop: 'lables',
        //   type: 'select',
        //   props: {
        //     value: 'id',
        //     label: 'dictValue',
        //   },
        //   dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
        //   span: 12,
        // },
        // {
        //   label: '现场图',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'string',
        //   loadText: '附件上传中，请稍等',
        //   span: 12,
        //   slot: true,
        //   prop: 'workOrderPhotos',
        //   addDisplay: true,
        //   editDisplay: true,
        //   viewDisplay: false,
        //   listType: 'picture-card',
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
        // {
        //   label: '服务要求',
        //   prop: 'taskDescription',
        //   type: 'textarea',
        //   span: 24,

        // },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务开始时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'serviceStartTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'datetime',
          label: '服务结束时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'serviceEndTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '预估工时',
          prop: 'preUseTimes',
          type: 'number',
        },
        // 是否需要签到
        {
          label: '是否需要签到',
          prop: 'isNeedSign',
          type: 'radio',

          rules: [
            {
              required: true,
              message: '请选择是否需要签到',
            },
          ],
          props: {
            value: 'value',
            label: 'label',
          },
          dicData: [
            {
              label: '需要',
              value: 1,
            },
            {
              label: '不需要',
              value: 0,
            },
          ],
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'externalHandleUser',
          span: 24,
          component: 'wf-user-select',
          // rules: [
          //   {
          //     message: '请选择工程师',
          //     trigger: 'blur',
          //     validator: (rule, value, callback) => {
          //       if (
          //         !form.value.externalHandleUserList ||
          //         form.value.externalHandleUserList.length == 0
          //       ) {
          //         callback('请选择工程师');
          //       } else {
          //         callback();
          //       }
          //     },
          //   },
          // ],
        },
        {
          label: '工单描述',
          prop: 'taskDescription',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          span: 24,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',

          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',
          addDisplay: false,
          editDisplay: false,
          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'completeInfo',
      editDisplay: false,
      column: [
        {
          label: '',
          labelWidth: 0,
          span: 24,
          prop: 'completeInfoList',
        },
      ],
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});
/*
设置基础信息
*/
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const customerContact1 = proxy.findObject(option.value.group[0].column, 'contact');
      customerContact1.dicUrl = '/vt-admin/customerContact/page?customerId=' + id;

      form.value.contact = res.data.data.finalCustomerConcat || res.data.data.customerContactName;
      form.value.finalCustomer = res.data.data.finalCustomer || res.data.data.customerName;
      form.value.contactPhone = res.data.data.finalCustomerPhone || res.data.data.customerPhone;
      form.value.distributionAddress = res.data.data.deliveryAddress;
      // form.value.objectName = res.data.data.contractName;
    });
}

// 删除工程师
function handleDelete(item) {
  let form1 = changeType.value == 0 ? form.value : changeEngineerForm.value;
  const index = form1.externalHandleUserList.findIndex(i => i.id === item.id);
  if (index !== -1) {
    form1.externalHandleUserList.splice(index, 1);
    const key = form1.value.objectType == 0 ? 'handleUser' : 'externalHandleUser';
    form1[key] = form1.value.externalHandleUserList.map(item => item.id).join(',');
  }
}

// 添加内部工程师
function handleAddIn() {
  // form.value.externalHandleUserList = [];
  // form.value.handleUser = '';
  // form.value.externalHandleUser = '';
  proxy.$refs.userSelectRef.visible = true;
}
function handleConfirm(ids) {
  let form1 = changeType.value == 0 ? form.value : changeEngineerForm.value;
  ids.split(',').forEach(item => {
    axios
      .get('/api/blade-system/user/detail', {
        params: {
          id: item,
        },
      })
      .then(res => {
        const engineerData = {
          ...res.data.data,
          phone: res.data.data.phone || '',
          price: '',
        };
        if (!form1.externalHandleUserList) {
          form1.externalHandleUserList = [engineerData];
        } else {
          form1.externalHandleUserList.push(engineerData);
        }
        form1.handleUser = form1.externalHandleUserList.map(item => item.id).join(',');
        activeName.value = form1.externalHandleUserList.map(item => item.realName);
      });
  });
}
function handleConfirmOut(data) {
  ;
  let form1 = changeType.value == 0 ? form.value : changeEngineerForm.value;
  if (!form1.externalHandleUserList) {
    form1.externalHandleUserList = data.map(item => {
      return {
        ...item,
        realName: item.name,
        phone: item.phone || '',
        price: '',
      };
    });
  } else {
    form1.externalHandleUserList.push(
      ...data.map(item => {
        return {
          ...item,
          realName: item.name,
          phone: item.phone || '',
          price: '',
        };
      })
    );
  }
  form1.externalHandleUser = form1.externalHandleUserList.map(item => item.id).join(',');
  form1.objectType = 1;
  activeName.value = form1.externalHandleUserList.map(item => item.realName);
}
function handleAddOut() {
  // form.value.externalHandleUserList = [];
  // form.value.handleUser = '';
  // form.value.externalHandleUser = '';
  proxy.$refs.userSelectRefOut.open();
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNumber = ref(0);
let totalTimes = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        selectType: 5,
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/externalPcPageStatistics', {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',
        selectType: 5,
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      totalNumber.value = res.data.data.totalNumber;
      totalTimes.value = res.data.data.totalTimes;
    });
}
let router = useRouter();
function beforeOpen(done, type) {
  if (['add', 'view'].includes(type)) {
    if (type == 'add') {
      // option.value.group[0].display = false;
      changeType.value = 0;
      option.value.updateBtnText = '提交';

      const sealContractIdRef = proxy.findObject(option.value.column, 'sealContractId');
      sealContractIdRef.display = true;
    }
    if (type == 'view') {
      if (
        form.value.createUser == proxy.$store.getters.userInfo.user_id &&
        !form.value.projectLeader
      ) {
        // option.value.group[0].display = false;
      } else {
        // option.value.group[0].display = true;
      }
    }
  } else {
    form.value.files =
      form.value.fileList &&
      form.value.fileList.map(item => {
        // 修正拼写错误，将 reuturn 改为 return
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    if (form.value.externalHandleUser) {
      form.value.externalHandleUserList = form.value.externalHandleUser
        .split(',')
        .map((item, index) => ({
          id: item,
          realName: form.value.handleUserName.split(',')[index],
          phone: '',
          price: '',
        }));
    }
    if (form.value.handleUser) {
      form.value.externalHandleUserList = form.value.handleUser.split(',').map((item, index) => ({
        id: item,
        realName: form.value.handleUserName.split(',')[index],
        phone: '',
        price: '',
      }));
    }
    form.value.objectName = form.value.objectName || form.value.objectName;
    form.value.taskDescription = form.value.taskDescription;
    form.value.planTime = form.value.planTime || form.value.reservationTime;

    if (
      form.value.createUser == proxy.$store.getters.userInfo.user_id &&
      !form.value.projectLeader
    ) {
      option.value.group[0].display = false;
      option.value.updateBtnText = '提交';

      // const sealContractIdRef = proxy.findObject(option.value.column, 'sealContractId');
      // sealContractIdRef.display = true;
    } else {
      option.value.group[0].display = true;
      option.value.updateBtnText = '派单';
      // const sealContractIdRef = proxy.findObject(option.value.column, 'sealContractId');
      // sealContractIdRef.display = false;
    }
    if (addType.value == 1) {
      form.value.serviceStartTime = '';
      form.value.serviceEndTime = '';
    }
  }
  done();
}
function rowSave(form, done, loading) {
  if (!form.externalHandleUser && !form.handleUser) {
    proxy.$message.warning('请选择工程师');
    return loading();
  }
  if (addType.value == 2) {
    let bol = true;
    form.externalHandleUserList.forEach((item, index) => {
      const key = `completeForm${index}`;
      console.log(proxy.$refs[key]);

      proxy.$refs[key][0].validate(valid => {
        ;
        if (!valid) {
          bol = false;
          proxy.$message.warning('请填写所有工程师完成信息');
          return loading();
        }
      });
    });

    const data = {
      ...form,
      projectLeader: proxy.$store.getters.userInfo.user_id,
      files: form.files && form.files.map(item => item.value).join(','),
      userList: form.externalHandleUserList.map(item => {
        return {
          ...item.formData,
          handleUser: item.id,
          handleResultPhotos:
            item.formData.handleResultPhotos &&
            item.formData.handleResultPhotos.map(item => item.label).join(','),
          workOrderPhotos:
            item.formData.workOrderPhotos && item.formData.workOrderPhotos.map(item => item.label).join(','),
          completeFiles:
            item.formData.completeFiles && item.formData.completeFiles.map(item => item.value).join(','),
        };
      }),
    };

    axios
      .post('/api/vt-admin/sealContractObject/addHistoryOrder', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } else {
    const data = {
      ...form,
      projectLeader: proxy.$store.getters.userInfo.user_id,
      files: form.files && form.files.map(item => item.value).join(','),
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  }
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    contractType: 2,
    deliveryAddress: form.distributionAddress,
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
        form.sealContractId = res.data.data;
        rowSave(form, done, loading);
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  ;
  if (!row.externalHandleUser && !row.handleUser) {
    proxy.$message.warning('请选择工程师');
    return loading();
  }
  if (addType.value == 0) {
    const url =
      row.createUser == proxy.$store.getters.userInfo.user_id && !row.projectLeader
        ? updateUrl
        : '/api/vt-admin/sealContractObject/sendOrder';
    const data = {
      ...row,
      files: row.files && row.files.map(item => item.value).join(','),
    };
    axios
      .post(url, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  } else {
    const url = '/api/vt-admin/sealContractObject/addSubOrder';
    const data = {
      ...row,
      id: null,
      parentObjectId: currentItem.value.id,
      files: row.files && row.files.map(item => item.value).join(','),
    };
    axios
      .post(url, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let drawer = ref(false);
let detailForm = ref({});
let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail: true,
      column: [
        {
          label: '服务客户名称',
          prop: 'finalCustomer',
          span: 24,
        },

        {
          label: '服务联系人',
          prop: 'contact',
          type: 'select',
          dicUrl: '/blade-system/dict-biz/dictionary?code=contact',
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
        },
        {
          label: '预约时间',
          prop: 'reservationTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          span: 12,
          width: 150,
          labelWidth: 110,
          display: false,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
          span: 24,
        },
        {
          type: 'date',
          label: '预约时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'reservationTime',
          rules: [
            {
              required: true,
              message: '请选择预约时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '故障类型',
          prop: 'lables',
          type: 'select',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          dicUrl: '/blade-system/dict-biz/dictionary?code=faultType',
          span: 12,
        },
        // {
        //   label: '现场图',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'string',
        //   loadText: '附件上传中，请稍等',
        //   span: 24,
        //   slot: true,
        //   prop: 'workOrderPhotos',
        //   addDisplay: true,
        //   editDisplay: true,
        //   viewDisplay: false,
        //   listType: 'picture-card',
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
        {
          label: '服务要求',
          prop: 'taskDescription',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '服务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules: [
            {
              required: true,
              message: '请选择工单类型',
              trigger: 'blur',
            },
          ],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
          prop: 'serverType',
        },
        {
          label: 'SLA',
          type: 'select',
          prop: 'slaType',
          dicUrl: '/api/blade-system/dict-biz/dictionary?code=slaType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        // 发布人
        {
          label: '发布人',
          prop: 'createName',
          span: 12,
        },

        // 派单人
        {
          label: '派单人',
          prop: 'projectLeaderName',
          span: 12,
        },
        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 24,
        },
        {
          label: '工单描述',
          prop: 'taskDescription',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '工程师服务信息',
          prop: 'engineerServiceInfo',
          labelWidth: 0,
          span: 24,
          formslot: true,
        },
      ],
    },
  ],
});
let wokerOrderDetailRef = ref(null);
function viewDetail(row) {
  wokerOrderDetailRef.value.open(row.id);
}
function setContactInfo(info) {
  if (!info.phone) return;
  form.value.contactPhone = info.phone;
}

// 关单
async function closeOrder(row) {
  if (row.objectStatus != 2) {
    ElMessage.error('只有已完成的工单才能关单');
    return;
  }
  proxy.$refs.dialogForm.show({
    title: '关单',

    option: {
      column: [
        {
          label: '关单时间',
          prop: 'completeTime',
          type: 'datetime',
          width: 140,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        {
          label: '完成情况',
          prop: 'completeCondition',
          type: 'select',
          span: 12,
          dicUrl: '/blade-system/dict-biz/dictionary?code=completeType',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractObject/closeWorkOrder', data).then(r => {
        proxy.$message.success('关单成功');
        res.close();
        onLoad();
      });
    },
  });
}

// 改派
let changeEngineerDialogVisible = ref(false);
let currentItem = ref(null);
let changeEngineerFormRef = ref(null);
let changeEngineerOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '工单类型',
      type: 'radio',
      span: 24,
      dicData: [
        {
          value: 0,
          label: '内部工单',
        },
        {
          value: 1,
          label: '外部工单',
        },
      ],
      rules: [
        {
          required: true,
          message: '请选择工单类型',
        },
      ],
      prop: 'objectType',
      control: val => {
        return {
          orderPrice: {
            display: val == 1,
          },
        };
      },
    },
    {
      label: '指派工程师',
      prop: 'externalHandleUser',
      span: 24,

      rules: [
        {
          message: '请选择工程师',
          trigger: 'blur',
          validator: (rule, value, callback) => {
            if (
              !changeEngineerForm.value.externalHandleUserList ||
              changeEngineerForm.value.externalHandleUserList.length == 0
            ) {
              callback('请选择工程师');
            } else {
              callback();
            }
          },
        },
      ],
    },
  ],
});
let changeEngineerForm = ref({});

function changeEngineer(row) {
  currentItem.value = row;
  changeEngineerDialogVisible.value = true;
  changeType.value = 1;
  changeEngineerForm.value = {
    objectType: row.objectType,
    externalHandleUserList: row.handleUserName.split(',').map((item, index) => {
      return {
        realName: item,
        id:
          row.objectType == 0
            ? row.handleUser.spit(',')[index]
            : row.externalHandleUser.split(',')[index],
      };
    }),
  };
  ;
}
function confirmChangeEngineer() {
  const data = {
    id: currentItem.value.id,
    ...changeEngineerForm.value,
  };
  changeEngineerFormRef.value.validate((valid, done) => {
    if (valid) {
      axios.post('/api/vt-admin/sealContractObject/changePerson', data).then(r => {
        proxy.$message.success('改派成功');
        changeEngineerDialogVisible.value = false;
        onLoad();
        done();
      });
    } else {
      done();
    }
  });
}

let addType = ref(0); // 0 首次派单  1 再次派单  2 补录工单

function add(row) {
  addType.value = 0;
  changeType.value = 0;
  proxy.$refs.crud.rowEdit(row);
}
function addAgain(row) {
  addType.value = 1;
  changeType.value = 0;
  currentItem.value = row;
  proxy.$refs.crud.rowEdit(row);
}
function addNew() {
  addType.value = null;
  proxy.$refs.crud.rowAdd();
  const completeInfoRef = proxy.findObject(option.value.group, 'completeInfo');
  completeInfoRef.addDisplay = false;
}
function addHistory() {
  addType.value = 2;
  proxy.$refs.crud.rowAdd();
  const completeInfoRef = proxy.findObject(option.value.group, 'completeInfo');
  completeInfoRef.addDisplay = true;
}
let activeName = ref([]);
const completeFormOption = ref({
  labelWidth: 120,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'datetime',
      label: '服务开始时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceStartTime',
      rules: [{ required: true, message: '请选择服务开始时间', trigger: 'blur' }],
    },
    {
      type: 'datetime',
      label: '服务结束时间',
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'serviceEndTime',
      rules: [{ required: true, message: '请选择服务结束时间', trigger: 'blur' }],
    },
    // {
    //   type: 'date',
    //   label: '实际完成时间',
    //   span: 12,
    //   value: dateFormat(new Date(), 'yyyy-MM-dd'),
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
    //   prop: 'completeTime',
    //   rules: [{ required: true, message: '请选择实际完成时间', trigger: 'blur' }],
    // },
    {
      label: '实际使用工时',
      prop: 'useTimes',
      type: 'number',
      span: 12,
      rules: [{ required: true, message: '请输入实际使用工时', trigger: 'blur' }],
    },
    //  // 完成情况
    //   {
    //     label: '完成情况',
    //     prop: 'completeStatus',
    //     type: 'select',
    //     props: {
    //       value: 'id',
    //       label: 'dictValue',
    //     },
    //     dicUrl: '/blade-system/dict-biz/dictionary?code=completeType',
    //   },
    {
      label: '现场图',
      prop: 'workOrderPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
    {
      label: '处理结果图',
      prop: 'handleResultPhotos',

      type: 'upload',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      dataType: 'object',
      addDisplay: true,
      editDisplay: true,
      viewDisplay: true,
      listType: 'picture-card',
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'id',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/api/blade-resource/attach/upload',
    },
    {
      label: '完成附件',
      prop: 'completeFiles',
      type: 'upload',
      span: 24,
      value: [],
      dataType: 'object',
      loadText: '附件上传中，请稍等',

      slot: true,
      addDisplay: true,
      editDisplay: true,
      viewDisplay: false,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '服务复盘',
      prop: 'serviceReorder',
      type: 'textarea',
      span: 24,
      // rules: [{ required: true, message: '请输入服务复盘', trigger: 'blur' }],
    },
    {
      label: '备注',
      prop: 'completeRemark',
      type: 'textarea',
      span: 24,
    },
  ],
});
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}
/* 工程师服务信息样式 */
.engineer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.engineer-card {
  margin-bottom: 0;
  border-radius: 4px;
  overflow: hidden;
}

.engineer-card :deep(.el-card__header) {
  padding: 8px 12px;
  background-color: #f5f7fa;
}

.engineer-card :deep(.el-card__body) {
  padding: 10px 12px;
}

.engineer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.engineer-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.engineer-content {
  padding: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 6px;
  margin-bottom: 6px;
}

.info-item {
  margin-bottom: 6px;
  font-size: 12px;
  line-height: 1.3;
}

.label {
  font-weight: 600;
  color: #606266;
  margin-right: 3px;
  font-size: 12px;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 4px;
}

.result-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 2px;
  cursor: pointer;
  transition: transform 0.2s;
}

.result-image:hover {
  transform: scale(1.05);
}

.service-reorder {
  background-color: #f5f7fa;
  padding: 6px;
  border-radius: 2px;
  margin-top: 2px;
  font-size: 12px;
}

.service-reorder pre {
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
  font-family: inherit;
  font-size: 12px;
  line-height: 1.3;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
  padding: 20px;
}

.info-card {
  margin-bottom: 3px;
  background: white;
  border-radius: 0px;
  // box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: all 0.3s ease;
}

.info-card:hover {
  // box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  // background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
  padding: 16px 20px;
  color: #000;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-body {
  padding: 20px;
}

.info-value {
  font-weight: 500;
  color: #303133;
}

.description-content {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
  line-height: 1.6;
}

.file-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;

  .file-item {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 1px solid #bbdefb;
    border-radius: 20px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 14px;

    &:hover {
      background: linear-gradient(135deg, #bbdefb 0%, #e1bee7 100%);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }
}
</style>
