import Layout from '@/page/index/index.vue';
import Index from '@/page/index/layout.vue';
import Store from '@/store/';

export default [
  {
    path: '/home',
    component: () =>
      Store.getters.isMacOs ? import('@/mac/index.vue') : import('@/page/index/index.vue'),
    redirect: '/home/<USER>',
    children: [
      {
        path: 'index',
        name: '首页',
        meta: {
          i18n: 'dashboard',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/home/<USER>'),
      },
      {
        path: 'dashboard',
        name: '控制台',
        meta: {
          i18n: 'dashboard',
          menu: false,
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/wel/dashboard.vue'),
      },
    ],
  },
  
  {
    path: '/test',
    component: Layout,
    redirect: '/test/index',
    children: [
      {
        path: 'index',
        name: '测试页',
        meta: {
          i18n: 'test',
          keepAlive: true,
          isOpen: 2,
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/util/test.vue'),
      },
    ],
  },
  {
    path: '/dict-horizontal',
    component: Layout,
    redirect: '/dict-horizontal/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/util/demo/dict-horizontal.vue'),
      },
    ],
  },
  {
    path: '/dict-vertical',
    component: Layout,
    redirect: '/dict-vertical/index',
    children: [
      {
        path: 'index',
        name: '字典管理',
        meta: {
          i18n: 'dict',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/util/demo/dict-vertical.vue'),
      },
    ],
  },
  {
    path: '/info',
    component: Layout,
    redirect: '/info/index',
    children: [
      {
        path: 'index',
        name: '个人信息',
        meta: {
          i18n: 'info',
        },
        component: () => import(/* webpackChunkName: "views" */ '@/views/system/userinfo.vue'),
      },
      {
        path: 'companyIndex',
        name: '系统设置',
        
        component: () => import(/* webpackChunkName: "views" */ '@/views/system/companyInfo.vue'),
      },
    ],
  },
  {
    path: '/work/process/leave',
    component: Layout,
    redirect: '/work/process/leave/form',
    children: [
      {
        path: 'form/:processDefinitionId',
        name: '请假流程',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/form.vue'),
      },
      {
        path: 'handle/:taskId/:processInstanceId/:businessId',
        name: '处理请假流程',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/handle.vue'),
      },
      {
        path: 'detail/:processInstanceId/:businessId',
        name: '请假流程详情',
        meta: {
          i18n: 'work',
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/work/process/leave/detail.vue'),
      },
    ],
  },
  {
    path: '/workflow',
    component: Layout,
    children: [
      {
        path: 'design/process/:id',
        name: '模型设计2',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/plugin/workflow/pages/design/index.vue'),
      },
      {
        path: 'design/model/history/:id',
        name: '模型历史',
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/plugin/workflow/pages/design/model-history.vue'
          ),
      },
      {
        path: 'design/form/history/:id',
        name: '表单历史',
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/plugin/workflow/pages/design/form-history.vue'
          ),
      },
      {
        path: 'process/start/:params',
        name: '新建流程2',
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/plugin/workflow/pages/process/form/start.vue'
          ),
      },
      {
        path: 'process/detail/:params',
        name: '流程详情',
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/plugin/workflow/pages/process/form/detail.vue'
          ),
      },
    ],
  },
  // {
  //   path: '/customer',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'add',
  //       name: '新增客户',
  //       meta: {
  //         keepAlive:true
  //       },
  //       component: () =>
  //         import(/* webpackChunkName: "views" */ '@/views/CRM/customer/compoents/customerAdd.vue'),
  //     },
  //     {
  //       path: 'detail',
  //       name: '客户详情',
  //       meta: {
  //         // keepAlive: true,
  //       },
  //       component: () =>
  //         import(/* webpackChunkName: "views" */ '@/views/CRM/customer/detail/detail.vue'),
  //     },
  //   ],
  // },
  {
    path: '/CRM/follow',
    component: Layout,
    children: [
      {
        path: 'compoents/update',
        name: '新增跟进',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/CRM/follow/compoents/update.vue'),
      },
      {
        path: 'compoents/detail',
        name: '跟进详情',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/CRM/follow/compoents/detail.vue'),
      },
    ],
  },
   {
    path: '/CRM/salesWorkbench',
    component: Layout,
    children: [
      {
        path: 'index',
        name: '工作台',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/CRM/salesWorkbench/index.vue'),
      },
    ],
  },
  {
    path: '/SRM',
    component: Layout,
    children: [
      {
        path: 'supplierDetail',
        name: '供应商详情',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/SRM/supplier/compoents/detail.vue'),
        meta: {
          keepAlive: true,
        },
        // children:[{
        //   path:'add',
        //   name:'新增供应商',
        //   meta:{
        //     keepAlive:true,
        //     isHide:true,
        //   },
        //   component: () =>
        //   import(/* webpackChunkName: "views" */ '@/views/SRM/supplier/compoents/update.vue'),
        // }]
      },
    ],
  },
  {
    path: '/SRM',
    component: Layout,
    children: [
      {
        path: 'productDetail',
        name: '商品详情',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/SRM/product/compoents/detail.vue'),
        meta: {
          keepAlive: true,
        },
        // children:[{
        //   path:'add',
        //   name:'新增供应商',
        //   meta:{
        //     keepAlive:true,
        //     isHide:true,
        //   },
        //   component: () =>
        //   import(/* webpackChunkName: "views" */ '@/views/SRM/supplier/compoents/update.vue'),
        // }]
      },
    ],
  },
  // {
  //   path: '/businessOpportunity',
  //   component: Layout,
  //   children: [
  //     {
  //       path: 'add',
  //       name: '新增商机',
  //       meta: {
  //         keepAlive: false,
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */ '@/views/CRM/businessOpportunity/compoents/update.vue'
  //         ),
  //     },
  //     {
  //       path: 'detail',
  //       name: '商机详情',
  //       meta: {
  //         // keepAlive: true,
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */ '@/views/CRM/businessOpportunity/compoents/detail.vue'
  //         ),
  //     },
  //     {
  //       path: 'bid',
  //       name: '投标',
  //       meta: {
  //         // keepAlive: true,
  //       },
  //       component: () =>
  //         import(
  //           /* webpackChunkName: "views" */ '@/views/CRM/businessOpportunity/compoents/bid.vue'
  //         ),
  //     },
  //   ],
  // },
  // {
  //   path: '/programme',
  //   component: Layout,
  //   children: [
  //     {
  //       path: '/CRM/programme/compoents/addOldSheet',
  //       name: '编辑方案',
  //       meta: {
  //         keepAlive: true,
  //       },
  //       component: () =>
  //         import(/* webpackChunkName: "views" */ '@/views/CRM/programme/compoents/update.vue'),
  //     },
  //   ],
  // },
  {
    path: '/quotation',
    component: Layout,
    children: [
      {
        path: '/CRM/quotation/compoents/addOld',
        name: '新增报价表',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/CRM/quotation/compoents/addOld.vue'),
      },
      {
        path: 'editOffer',
        name: '编辑报价',
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/CRM/quotation/compoents/editOffer.vue'),
      },
    ],
  },
  {
    path: '/procure',
    component: Layout,
    children: [
      // {
      //   path: 'orderDetail',
      //   name: '采购详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/SRM/procure/compoents/orderDetail.vue'),
      // },
      {
        path: 'contractDetail',
        name: '采购合同详情',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/SRM/procure/compoents/contractDetail.vue'
          ),
      },
      // {
      //   path: 'inquirySheetDetail',
      //   name: '询价单',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/SRM/procure/compoents/inquirySheet.vue'),
      // },
      {
        path: 'orderAdd',
        name: '新增采购订单',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/SRM/procure/compoents/orderAdd.vue'),
      },
    ],
  },
  {
    path: '/Contract',
    component: Layout,
    children: [
      {
        path: 'editContract',
        name: '编辑销售合同',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(
            /* webpackChunkName: "views" */ '@/views/Contract/customer/compoents/addContract.vue'
          ),
      },
      {
        path: 'orderContractDetail',
        name: '工单合同详情',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/Contract/orderContract/component/detail.vue'),
      },
    ],
  },
  {
    path: '/Order',
    component: Layout,
    children: [
      {
        path: 'add',
        name: '新增订单',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/Order/salesOrder/compoents/addOrder.vue'),
      },
      // {
      //   path: 'orderDetail',
      //   name: '销售订单详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/Order/salesOrder/compoents/detail.vue'),
      // },
    ],
  },
  {
    path: '/Project',
    component: Layout,
    children: [
      {
        path: 'add',
        name: '新增项目有报价',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/Project/compoents/addProject.vue'),
      },
      {
        path: 'addNoOffer',
        name: '新增项目无报价',
        meta: {
          keepAlive: true,
        },
        component: () =>
          import(/* webpackChunkName: "views" */ '@/views/Project/compoents/projectNoOffer.vue'),
      },
      // {
      //   path: 'engeerProjectDetail',
      //   name: '工程项目详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //   import(/* webpackChunkName: "views" */ '@/views/Project/detail/detail.vue'),

      // },
    ],
  },
  {
    path: '/warehouse',
    component: Layout,
    children: [
      // {
      //   path: 'addInhouse',
      //   name: '新增入库',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/SRM/warehouse/compoents/addInhouse.vue'),
      // },
      // {
      //   path: 'addOuthouse',
      //   name: '新增出库',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/SRM/warehouse/compoents/addOuthouse.vue'),
      // },
      // {
      //   path: 'inhouseDetail',
      //   name: '入库详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "views" */ '@/views/SRM/warehouse/compoents/InhouseDetail.vue'
      //     ),
      // },
      // {
      //   path: 'outhouseDetail',
      //   name: '出库详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(
      //       /* webpackChunkName: "views" */ '@/views/SRM/warehouse/compoents/outhouseDetail.vue'
      //     ),
      // },
      // {
      //   path: 'houseDetail',
      //   name: '库存详情',
      //   meta: {
      //     keepAlive: true,
      //   },
      //   component: () =>
      //     import(/* webpackChunkName: "views" */ '@/views/SRM/warehouse/detail/houseDetail.vue'),
      // },
    ],
  },
];
