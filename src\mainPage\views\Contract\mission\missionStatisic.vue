<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{
          row.$objectName || row.objectName
        }}</el-link>
      </template>
      <template #useTime="{ row }">
        <el-tag effect='plain' type="success">{{ row.diffHours.toFixed(1) }}小时</el-tag>
      </template>
      <template #serviceReorder="{ row }">
        <el-popover placement="left" :width="600" trigger="hover">
          <template #default>
            <div
              v-html="row.serviceReorder.replace(/\n|\r\n/g, '<br>').replace(/ /g, ' &nbsp')"
            ></div>
          </template>
          <template #reference>
            <div
              calss="custom-popover"
              style="
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 3;
                overflow: hidden;
                text-overflow: ellipsis;
              "
              v-if="row.objectName == 2 || row.objectName == 3"
            >
              {{ row.serviceReorder }}
            </div>
          </template>
        </el-popover>
      </template>
      <template #footer>
        <el-form inline>
          <!-- <el-form-item label="本页数量小计：">{{
             tableData.reduce((pre, cur) => {
              pre += cur.endNumber * 1;
              return pre;
            }, 0)
            }}</el-form-item> -->
          <el-form-item label="工单数:">{{ page.total }} </el-form-item>
          <!-- <el-form-item label="总数量：">{{ statisticData.totalNumber }}</el-form-item> -->
          <el-form-item label="工单时长：">{{ totalForm.totalHours.toFixed(1) }}</el-form-item>
        </el-form>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <detail ref="missionDetail"></detail>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { objectType } from '@/const/const.js';
import detail from '@/views/Order/salesOrder/compoents/missionDetail.vue';
import moment from 'moment';
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  calcHeight: 75,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'checkbox',
      dicData: objectType,
      label: '工单名称',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'objectName',
      width: 250,
      search: true,
      rules: [
        {
          required: true,
          message: '请选择工单名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '时间段',
      prop: 'dateStr',
      type: 'month',
      search: true,
      format: 'YYYY-MM',
      valueFormat: 'YYYY-MM',

      hide: true,
    },
    {
      type: 'textarea',
      label: '工单描述',
      overHidden: true,
      span: 24,
      width: 250,
      display: true,
      prop: 'durationNode',
    },
    {
      component: 'wf-user-drop',
      label: '技术人员',
      overHidden: true,
      span: 24,
      width: 100,
      display: true,
      search: !(
        proxy.$store.getters.userInfo.role_name.indexOf('technology_person') > -1 ||
        proxy.$store.getters.userInfo.role_name.indexOf('ywzy') > -1
      ),
      prop: 'handleUserName',
      //   formatter: row => {
      //     return row.handleUserName;
      //   },
    },
    {
      label: '开始时间',
      prop: 'serviceStartTime',
      width: 150,

      timeType: 'date',
      display: false,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '结束时间',
      prop: 'serviceEndTime',
      width: 150,

      display: false,
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '所用时间',
      display: false,
      type: 'datetime',
      prop: 'useTime',
      width: 150,
    },
    {
      label: '服务复盘',
      prop: 'serviceReorder',
      type: 'textarea',
      display: false,
      span: 24,
      align: 'left',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObject/statisticsPage';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
let loading = ref(false);
let totalForm = ref({});
onMounted(() => {
  onLoad();
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        handleUser:
          proxy.$store.getters.userInfo.role_name.indexOf('technology_person') > -1 ||
          proxy.$store.getters.userInfo.role_name.indexOf('ywzy') > -1
            ? proxy.$store.getters.userInfo.user_id
            : null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/statisticsPageMap', {
      params: {
        size,
        current,
        ...params.value,
        handleUser:
          proxy.$store.getters.userInfo.role_name.indexOf('technology_person') > -1 ||
          proxy.$store.getters.userInfo.role_name.indexOf('ywzy') > -1
            ? proxy.$store.getters.userInfo.user_id
            : null,
      },
    })
    .then(res => {
      totalForm.value = res.data.data;
    });
}
let router = useRouter();

function viewDetail(row, active = 1) {
  proxy.$refs.missionDetail.viewDetail(row, active);
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped>
.custom-popover {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
