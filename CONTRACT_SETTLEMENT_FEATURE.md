# 合同结算功能说明文档

## 功能概述
实现了合同结算抽屉的分类显示功能，采用**一页到底**的布局设计，包含6个主要信息分类，每个列表都有汇总统计。

## 功能结构

### 📋 六大分类信息

#### 1. **合同基本信息** 
使用 `el-descriptions` 组件展示（2列布局）
- 合同编号：HT2024001
- 合同名称：智能化系统集成项目合同
- 客户名称：某某科技有限公司
- 合同总额：¥1,500,000.00
- 业务员：张三
- 签订日期：2024-01-15
- 合同状态：执行中
- 备注：详细说明

#### 2. **合作伙伴信息**
使用 `el-descriptions` 组件展示（2列布局）
- 合作伙伴名称
- 联系人、联系电话、联系邮箱
- 公司地址
- 开户银行、银行账号

#### 3. **计划收款列表** ✅ 含汇总
使用 `el-table` 组件展示，底部显示汇总行
- 计划收款日期
- 计划收款金额 → **汇总：¥1,500,000.00**
- 实际收款金额 → **汇总：¥950,000.00**
- 实际收款日期
- 状态（已收款/部分收款/未收款）- 彩色标签
- 备注

#### 4. **开票列表** ✅ 含汇总
使用 `el-table` 组件展示，底部显示汇总行
- 发票号码
- 开票日期
- 开票金额 → **汇总：¥950,000.00**
- 税率、税额 → **汇总：¥123,500.00**
- 发票类型
- 状态（已开票/待开票）- 彩色标签
- 备注

#### 5. **付款列表** ✅ 含汇总
使用 `el-table` 组件展示，底部显示汇总行
- 付款日期
- 付款金额 → **汇总：¥450,000.00**
- 付款方式
- 付款账户、收款账户
- 状态（已付款/付款中/待付款）- 彩色标签
- 备注

#### 6. **收票列表** ✅ 含汇总
使用 `el-table` 组件展示，底部显示汇总行
- 发票号码
- 收票日期
- 发票金额 → **汇总：¥350,000.00**
- 税率、税额 → **汇总：¥31,500.00**
- 发票类型
- 供应商名称
- 状态（已收票/待收票）- 彩色标签
- 备注

## 🎨 设计特点

### 布局特性
- ✅ **一页到底**：所有内容在一个抽屉中垂直滚动显示
- ✅ **分区清晰**：每个分类用 `section` 容器分隔，有蓝色下划线标题
- ✅ **响应式**：抽屉宽度 1000px，内容自适应

### 样式优化
- 📌 **标题样式**：16px 加粗，蓝色下划线（#409eff）
- 📌 **表格样式**：汇总行背景色 #f5f7fa，字体加粗，蓝色文字
- 📌 **描述列表**：标签背景色 #f5f7fa，加粗显示
- 📌 **状态标签**：
  - 成功状态（绿色）：已收款、已开票、已付款、已收票
  - 警告状态（黄色）：部分收款、待开票、付款中、待收票
  - 信息状态（蓝色）：未收款、待付款

### 数据特性
- 📊 **模拟数据**：完整的真实业务场景
- 📊 **汇总统计**：每个列表都有底部汇总行
- 📊 **状态管理**：使用彩色标签区分不同状态
- 📊 **文本处理**：长文本使用省略号 + tooltip

## 📝 使用方式

### 打开抽屉
点击表格中的"结算"按钮，触发 `settlement()` 函数打开抽屉

```javascript
function settlement() {
    drawer.value = true;
}
```

### 数据结构
所有数据都使用 `ref()` 定义，可以轻松替换为真实 API 数据

```javascript
let contractData = ref({...})
let partnerData = ref({...})
let planReceiveList = ref([...])
let invoiceList = ref([...])
let paymentList = ref([...])
let receiveInvoiceList = ref([...])
```

### 汇总方法
每个列表都有对应的汇总方法：
- `getPlanReceiveSummary()` - 计划收款汇总
- `getInvoiceSummary()` - 开票汇总
- `getPaymentSummary()` - 付款汇总
- `getReceiveInvoiceSummary()` - 收票汇总

## 🔄 后续集成建议

1. **替换模拟数据**：将 `ref()` 中的数据替换为 API 调用
2. **动态加载**：在 `settlement()` 函数中根据合同ID加载数据
3. **编辑功能**：可添加编辑按钮实现数据修改
4. **导出功能**：可添加导出按钮实现数据导出
5. **权限控制**：根据用户权限显示/隐藏某些字段

## 📦 文件位置
`src/mainPage/views/Finance/financialStatements_/contractSettlement.vue`

