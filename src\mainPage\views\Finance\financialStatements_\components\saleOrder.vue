<template>
    <avue-crud :option="option" :data="props.tableData" v-model:search="params" @row-update="rowUpdate"
        @row-save="rowSave" :table-loading="loading" ref="crud" @row-del="rowDel" @search-reset="reset"
        @selection-change="handleSectionChange" @search-change="searchChange" @current-change="onLoad"
        @refresh-change="onLoad" @keyup.enter="onLoad" @size-change="onLoad" v-model="form">
        <template #menu-left>
            <!-- 新增 -->
            <el-button type="primary" icon="plus" @click="openAddDrawer">新增</el-button>
        </template>

        <template #orderStatus="{ row }">
            <el-tag effect="plain" size="small" v-if="row.orderStatus == 0" type="warning">待采购</el-tag>
            <el-tag effect="plain" size="small" v-if="row.orderStatus == 1" type="warning">采购中</el-tag>
            <el-tag effect="plain" size="small" type="success" v-else-if="row.orderStatus == 3">采购完成</el-tag>
            <el-tag effect="plain" size="small" type="warning" v-else-if="row.orderStatus == 2">询价中</el-tag>
        </template>
        <template #menu="{ row }">
            <el-button text type="primary" @click="handleView(row, true)" v-if="row.applyStatus == 0"
                icon="edit">编辑</el-button>
            <el-button text type="primary" @click="$refs.crud.rowDel(row)" v-if="row.applyStatus == 0"
                icon="delete">删除</el-button>

            <el-button text type="primary" @click="handleView(row)" icon="view">详情</el-button>

            <!-- <el-button type="primary" text icon="Back" @click="back(row)" v-if="row.orderStatus == 0"
        >撤回</el-button
      > -->
        </template>
    </avue-crud>
    <!-- 新增采购单抽屉 -->
    <el-drawer v-model="addDrawerVisible" :title="drawerTitle" :size="'80%'" :before-close="handleDrawerClose">
        <div class="drawer-content">
            <el-row :gutter="10">
                <el-col :span="8">
                    <el-card shadow="never" style="height: 100%">
                        <!-- 基本信息表单 -->
                        <el-form ref="addFormRef" :model="addForm" :rules="addFormRules" label-width="120px"
                            class="add-form" :readonly="drawerMode === 'view'">
                            <el-form-item label="交付时间" prop="deliveryDate">
                                <el-date-picker v-model="addForm.deliveryDate" type="date" placeholder="请选择交付时间"
                                    value-format="YYYY-MM-DD" style="width: 100%" :readonly="drawerMode === 'view'" />
                            </el-form-item>

                            <el-form-item label="交付地址" prop="deliveryAddress">
                                <el-input v-model="addForm.deliveryAddress" placeholder="请输入交付地址"
                                    :readonly="drawerMode === 'view'" />
                            </el-form-item>

                            <el-form-item label="联系人" prop="deliveryUser">
                                <el-input v-model="addForm.deliveryUser" placeholder="请输入联系人"
                                    :readonly="drawerMode === 'view'" />
                            </el-form-item>

                            <el-form-item label="联系电话" prop="deliveryContact">
                                <el-input v-model="addForm.deliveryContact" placeholder="请输入联系电话"
                                    :readonly="drawerMode === 'view'" />
                            </el-form-item>

                            <el-form-item label="备注">
                                <el-input v-model="addForm.remark" type="textarea" :rows="3" placeholder="请输入备注信息"
                                    :readonly="drawerMode === 'view'" />
                            </el-form-item>
                        </el-form>
                    </el-card>
                </el-col>
                <el-col :span="16">
                    <!-- 产品拆解区域 -->
                    <div class="decompose-section">
                        <el-row :gutter="20" style="height: 500px">
                            <!-- 左侧：采购产品列表 -->
                            <el-col :span="24">
                                <el-card shadow="never" body-style="padding: 0;height: calc(100% - 61px)"
                                    style="height: 100%">
                                    <template #header>
                                        <div style="display: flex; justify-content: space-between; align-items: center">
                                            <span>采购产品列表</span>
                                            <div v-if="drawerMode !== 'view'">
                                                <el-button type="primary" icon="Plus"
                                                    v-if="props.form.contractType == 0" size="small"
                                                    @click="openContractProductSelector">
                                                    从报价单选择
                                                </el-button>
                                                <el-button icon="Plus" v-if="props.form.contractType != 0"
                                                    type="primary" size="small" @click="handAddProduct">
                                                    从产品库选择
                                                </el-button>
                                            </div>
                                        </div>
                                    </template>
                                    <!-- <el-alert
                    type="success"
                    :closable="false"
                    style="margin-bottom: 10px"
                    v-if="contractProducts.length > 0"
                  >
                    请将需要拆分的产品勾选上
                  </el-alert> -->
                                    <div v-if="contractProducts.length === 0" class="empty-state"
                                        style="text-align: center; padding: 50px 0; height: 100%">
                                        <el-empty description="暂无产品数据，请点击上方'选择产品'按钮添加产品" />
                                    </div>
                                    <el-table v-else :data="contractProducts" border="0" @row-click="handleRowClick"
                                        style="height: 100%" show-summary :summary-method="getSummary">
                                        <el-table-column header-align="center" align="center" type="index" width="60"
                                            label="序号">
                                        </el-table-column>
                                        <el-table-column prop="customProductName" align="center" width="120"
                                            label="产品名称" />
                                        <el-table-column prop="customProductSpecification" align="center" width="120"
                                            show-overflow-tooltip label="规格型号" />
                                        <el-table-column prop="productBrand" label="品牌" align="center" width="80"
                                            show-overflow-tooltip />
                                        <el-table-column prop="customProductDescription" label="描述" align="center"
                                            show-overflow-tooltip />
                                        <el-table-column prop="customUnit" label="单位" align="center" width="60" />
                                        <el-table-column prop="totalNumber" v-if="drawerMode == 'add' && props.offerId"
                                            label="总数" align="center" width="80" />
                                        <el-table-column prop="purchaseNums" label="已采购" align="center"
                                            v-if="drawerMode == 'add' && props.offerId" width="80" />
                                        <el-table-column label="采购数量" align="center" width="120">
                                            <template #default="{ row }">
                                                <el-input-number v-model="row.number" :min="0"
                                                    :max="row.totalNumber - row.purchaseNums" :precision="0"
                                                    size="small" controls-position="right" style="width: 100%"
                                                    :readonly="drawerMode === 'view'" />
                                            </template>
                                        </el-table-column>
                                        <el-table-column prop="purchasedPrice" label="销售单价" align="center" width="130">
                                            <template #default="{ row }">
                                                <el-input-number v-model="row.zhhsdj" :min="0"
                                                    :max="row.totalNumber - row.purchaseNums" :precision="2"
                                                    size="small" controls-position="right" style="width: 100%"
                                                    v-if="!row.detailId" :readonly="drawerMode === 'view'" />
                                                <span v-else>{{ row.zhhsdj }}</span>
                                            </template>
                                        </el-table-column>
                                        <!-- 金额 -->
                                        <el-table-column label="金额" align="center" width="120">
                                            <template #default="{ row }">
                                                <span v-if="!row.detailId">{{
                                                    (parseFloat(row.zhhsdj) || 0) * (parseFloat(row.number) || 0)
                                                }}</span>
                                                <span v-else>{{ row.zhhsze }}</span>
                                            </template>
                                        </el-table-column>

                                        <!-- <el-table-column align="center" label="拆解" width="80">
                      <template #default="{ row }">
                        <el-switch
                          v-model="row.isDepose"
                          :active-value="1"
                          :inactive-value="0"
                          @change="handleDecomposeToggle(row)"
                        />
                      </template>
                    </el-table-column> -->
                                        <el-table-column align="center" label="操作" width="100"
                                            v-if="drawerMode !== 'view'">
                                            <template #default="{ row, $index }">
                                                <el-button type="danger" text size="small"
                                                    @click="removeContractProduct($index)" icon="Delete">
                                                    删除</el-button>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                </el-card>
                            </el-col>

                            <!-- 右侧：拆解产品详情
             <el-col :span="24">
               <el-row style="margin-bottom: 10px; height: 200px">
                 <el-col :span="24">
                   <el-card shadow="never" style="height: 100%">
                     <template #header>
                       <div>产品详情</div>
                     </template>
                     <el-descriptions 
                       v-if="detailForm.customProductName"
                       border 
                       :title="detailForm.customProductName" 
                       :column="2"
                     >
                       <el-descriptions-item label="规格型号">
                         {{ detailForm.customProductSpecification }}
                       </el-descriptions-item>
                       <el-descriptions-item label="品牌">
                         {{ detailForm.productBrand }}
                       </el-descriptions-item>
                       <el-descriptions-item label="单位">
                         {{ detailForm.unitName }}
                       </el-descriptions-item>
                       <el-descriptions-item label="数量">
                         {{ detailForm.number }}
                       </el-descriptions-item>
                     </el-descriptions>
                     <div v-else class="empty-state">
                       <el-empty description="请点击左侧产品查看详情" />
                     </div>
                   </el-card>
                   
                 </el-col>

               </el-row>
               <el-row :gutter="10" style="margin-top: 40px;">
                   <el-col :span="24">
                    <el-card shadow="never" style="height: 100%">
                     <template #header>
                       <div style="display: flex; justify-content: space-between; align-items: center">
                         <span>拆解产品</span>
                         <div v-if="detailForm.isDepose">
                           <el-form inline>
                             <el-form-item label="保留原产品">
                               <el-switch
                                 v-model="detailForm.isPre"
                                 :readonly="!detailForm.productId"
                                 @change="handleIsPreChange"
                               />
                             </el-form-item>
                           </el-form>
                         </div>
                       </div>
                     </template>
                     
                     <div v-if="detailForm.isDepose" style="height: calc(100% - 60px)">
                       <div style="margin-bottom: 10px; display: flex; justify-content: space-between; align-items: center;">
                         <div style="display: flex; gap: 8px;">
                           <el-button
                             icon="Plus"
                             type="primary"
                             size="small"
                             @click="openProductSelector"
                           >
                             从产品库选择
                           </el-button>
                         
                            <el-button
                              type="danger"
                              size="small"
                              @click="clearAllDecomposeProducts"
                              :readonly="!detailForm.detailDTOList || detailForm.detailDTOList.length === 0"
                            >
                              清空所有
                            </el-button>
                         </div>
                         <div style="font-size: 12px; color: #909399;" v-if="detailForm.detailDTOList && detailForm.detailDTOList.length > 0">
                           已配置: {{ detailForm.detailDTOList.reduce((sum, item) => sum + (parseFloat(item.number) || 0), 0) }} / {{ detailForm.number || 0 }}
                         </div>
                       </div>
                       
                       <el-table
                           :data="detailForm.detailDTOList || []"
                           border
                           style="height: calc(100% - 40px)"
                         >
                           <el-table-column prop="productName" label="产品名称">
                             
                           </el-table-column>
                           <el-table-column prop="productSpecification" label="规格型号">
                           
                           </el-table-column>
                           <el-table-column prop="productBrand" label="品牌" width="100">
                            
                           </el-table-column>
                           <el-table-column prop="unitName" label="单位" width="100">
                            
                           </el-table-column>
                          
                           <el-table-column prop="unitName" label="单位" width="100">
                             
                           </el-table-column>
                           <el-table-column label="数量" width="120">
                             <template #default="{ row, $index }">
                               <el-input-number
                                 v-model="row.number"
                                 :min="0"
                                 :max="detailForm.number || 999999"
                                 :precision="2"
                                 size="small"
                                 controls-position="right"
                                 style="width: 100%"
                                 @change="validateDecomposeQuantity"
                               />
                             </template>
                           </el-table-column>
                           <el-table-column label="操作" width="80">
                             <template #default="{ row, $index }">
                               <el-button
                                 type="danger"
                                 size="small"
                                 @click="removeDecomposeProduct($index)"
                                 icon="Delete"
                               />
                             </template>
                           </el-table-column>
                         </el-table>
                     </div>
                     
                     <div v-else class="empty-state">
                       <el-empty description="请先开启产品拆解开关" />
                     </div>
                   </el-card>
                   </el-col>
               </el-row>
               
             </el-col> -->
                        </el-row>
                    </div>
                </el-col>
            </el-row>
        </div>
        <!-- 操作按钮 -->
        <template #footer>
            <el-button @click="handleDrawerClose">{{
                drawerMode === 'view' ? '关闭' : '取消'
            }}</el-button>
            <el-button @click="submitAddForm(0)" v-if="drawerMode !== 'view'">保存草稿</el-button>

            <el-button v-if="drawerMode !== 'view'" type="primary" @click="submitAddForm(1)" :loading="submitLoading">
                {{ drawerMode === 'edit' ? '保存并提交' : '确定' }}
            </el-button>
        </template>
    </el-drawer>

    <!-- 合同产品选择对话框 -->
    <el-dialog v-model="contractProductSelectorVisible" title="选择报价产品" width="70%"
        :before-close="handleContractProductSelectorClose">
        <el-table :data="availableContractProducts" border max-height="400"
            @selection-change="handleContractProductSelection">
            <el-table-column type="selection" :selectable="selectable" width="55" />
            <el-table-column prop="customProductName" label="产品名称" />
            <el-table-column prop="customProductSpecification" label="规格型号" />
            <el-table-column prop="productBrand" label="品牌" />
            <el-table-column prop="customUnit" label="单位" width="80" />
            <el-table-column prop="number" label="数量" width="80" />
            <el-table-column label="已采购数量" width="100">
                <template #default="{ row }">
                    {{ (row.purchaseNums && parseFloat(row.purchaseNums)) || 0 }}
                </template>
            </el-table-column>
        </el-table>

        <template #footer>
            <div class="dialog-footer">
                <el-button @click="handleContractProductSelectorClose">取消</el-button>
                <el-button type="primary" @click="confirmContractProductSelection">确定</el-button>
            </div>
        </template>
    </el-dialog>

    <!-- 产品选择对话框 -->

    <dialogForm ref="dialogForm"></dialogForm>
    <wfProductSelect @onConfirm="handleProductSelectConfirm" checkType="checkbox" ref="productSelectRef">
    </wfProductSelect>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed, watch } from 'vue';
const props = defineProps({
    tableData: {
        type: Array,
        default: () => [],
    },
    sealContractId: {
        type: String,
        default: '',
    },
    cooperationType: {
        type: Number,

    },
})
// 表格汇总方法
function getSummary(param) {
    const { columns, data } = param;
    const sums = [];
    columns.forEach((column, index) => {
        if (column.label === '金额') {
            const total = data.reduce((sum, row) => {
                return sum + (parseFloat(row.zhhsdj) || 0) * (parseFloat(row.number) || 0);
            }, 0);
            sums[index] = '' + total.toFixed(2);
        } else {
            sums[index] = '';
        }
    });
    return sums;
}
// ...existing code...
import { useRoute, useRouter } from 'vue-router';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';


// 新增抽屉相关状态
const addDrawerVisible = ref(false);
const addFormRef = ref(null);
const submitLoading = ref(false);

// 添加抽屉模式状态
const drawerMode = ref('add'); // 'add', 'edit', 'view'
const drawerTitle = ref('新增采购单');
const currentEditId = ref(null);

// 新增表单数据
const addForm = ref({
    deliveryDate: '',
    deliveryAddress: '',
    deliveryUser: '',
    deliveryContact: '',
    remark: '',
});

// 表单验证规则
const addFormRules = {
    deliveryDate: [{ required: true, message: '请选择交付时间', trigger: 'change' }],
    deliveryAddress: [{ required: true, message: '请输入交付地址', trigger: 'blur' }],
    deliveryUser: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    deliveryContact: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
};

// 合同列表和产品相关数据
const contractList = ref([]);
const contractProducts = ref([]);
// const contractProducts = ref([]);
const detailForm = ref({});

// 合同产品选择器相关数据
const contractProductSelectorVisible = ref(false);
const availableContractProducts = ref([]);
const selectedContractProducts = ref([]);

// 打开新增抽屉
function openAddDrawer() {
    drawerMode.value = 'add';
    drawerTitle.value = '新增采购单';
    currentEditId.value = null;
    addDrawerVisible.value = true;
    const { customerContactName, customerPhone, deliveryAddress } = props.form;
    addForm.value = {
        deliveryDate: '',
        deliveryAddress: deliveryAddress || '',
        deliveryUser: customerContactName || '',
        deliveryContact: customerPhone || '',
        remark: '',
    };

    // 不再自动加载产品数据，改为通过弹窗选择
}

// 处理查看/编辑
function handleView(row, isEdit = false) {
    if (isEdit) {
        drawerMode.value = 'edit';
        drawerTitle.value = '编辑采购单';
    } else {
        drawerMode.value = 'view';
        drawerTitle.value = '采购单详情';
    }

    currentEditId.value = row.id;

    // 调用详情接口获取数据
    axios
        .get('/api/vt-admin/sealContract/detailForPurchase', {
            params: { id: row.id },
        })
        .then(res => {
            const data = res.data.data;

            // 填充基本信息
            addForm.value = {
                deliveryDate: data.deliveryDate || '',
                deliveryAddress: data.deliveryAddress || '',
                deliveryUser: data.deliveryUser || '',
                deliveryContact: data.deliveryContact || '',
                remark: data.remark || '',
            };

            // 填充产品列表
            contractProducts.value = data.detailVOS ? data.detailVOS.map(item => ({ ...item, number: item.number || 0, zhhsdj: item.zhhsdj * 1 || 0 })) : [];
            debugger
            addDrawerVisible.value = true;
        })
        .catch(err => {
            console.error('获取采购单详情失败:', err);
            proxy.$message.error('获取采购单详情失败，请重试');
        });
}

// 关闭抽屉
function handleDrawerClose() {
    addDrawerVisible.value = false;
    resetAddForm();
}

// 重置表单
function resetAddForm() {
    addForm.value = {
        deliveryDate: '',
        deliveryAddress: '',
        deliveryUser: '',
        deliveryContact: '',
        remark: '',
    };
    contractProducts.value = [];
    contractProducts.value = [];
    detailForm.value = {};
    availableContractProducts.value = [];
    selectedContractProducts.value = [];
    if (addFormRef.value) {
        addFormRef.value.resetFields();
    }
}

// 原有的加载合同产品函数已移除，改为通过弹窗选择产品
// 保留此注释以说明修改原因

// 处理行点击 - 选择产品查看详情
function handleRowClick(row) {
    detailForm.value = { ...row };
    if (!detailForm.value.detailDTOList) {
        detailForm.value.detailDTOList = [];
    }
}

// 处理拆解开关切换
function handleDecomposeToggle(row) {
    if (row.isDepose === 1) {
        // 开启拆解时，确保有拆解列表
        if (!row.detailDTOList) {
            row.detailDTOList = [];
        }

        // 检查采购数量
        if (!row.number || row.number <= 0) {
            proxy.$message.warning('请先设置采购数量再开启拆解功能');
            row.isDepose = 0;
            return;
        }

        // 更新详情表单
        if (detailForm.value.id === row.id) {
            detailForm.value = { ...row };
        }

        proxy.$message.success('已开启产品拆解功能');
    } else {
        // 关闭拆解时，询问用户是否确认
        if (row.detailDTOList && row.detailDTOList.length > 0) {
            proxy
                .$confirm('关闭拆解功能将清空已配置的拆解产品，是否继续？', '提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                .then(() => {
                    row.detailDTOList = [];
                    if (detailForm.value.id === row.id) {
                        detailForm.value = { ...row };
                    }
                    proxy.$message.success('已关闭产品拆解功能');
                })
                .catch(() => {
                    row.isDepose = 1; // 取消时恢复开关状态
                });
        } else {
            row.detailDTOList = [];
            if (detailForm.value.id === row.id) {
                detailForm.value = { ...row };
            }
            proxy.$message.success('已关闭产品拆解功能');
        }
    }
}

// 打开产品选择器
function openProductSelector() {
    if (!detailForm.value.id) {
        proxy.$message.warning('请先选择要拆解的产品');
        return;
    }

    proxy.$refs.productSelectRef.visible = true;
}

// 从产品库添加产品到采购列表
function handAddProduct() {
    if (props.form.contractType == 0)
        return proxy.$message.warning('已确认的订单合同无法从产品库新增采购产品');
    // 打开产品选择器
    if (proxy.$refs.productSelectRef) {
        proxy.$refs.productSelectRef.visible = true;
    } else {
        proxy.$message.error('产品选择器未初始化');
    }
}

// 确认产品选择
// 处理产品选择确认
function handleProductSelectConfirm(ids) {
    if (!ids || (Array.isArray(ids) ? ids.length === 0 : !ids)) {
        proxy.$message.warning('请选择要添加的产品');
        return;
    }

    // 处理ids（可能是字符串或数组）
    const productIds = Array.isArray(ids) ? ids.map(item => item.id || item) : ids.split(',');

    // 如果是从产品库选择（ids是id数组），需要调用详情接口获取数据
    if (Array.isArray(productIds) && ids.length > 0 && typeof ids[0] !== 'object') {
        // 检查是否有重复产品
        const existingProductIds = contractProducts.value.map(p => p.productId || p.id);
        const duplicateIds = productIds.filter(id => existingProductIds.includes(id));

        if (duplicateIds.length > 0) {
            proxy.$message.warning('选择的产品中有重复项，请重新选择');
            return;
        }

        // 批量获取产品详情
        const promises = productIds.map(id => axios.get(`/api/vt-admin/product/detail?id=${id}`));

        Promise.all(promises)
            .then(responses => {
                const products = responses.map(res => res.data.data);

                // 将获取到的产品添加到采购列表
                const newProducts = products.map(product => ({
                    id: null,
                    productId: product.id,
                    customProductName: product.productName,
                    customProductSpecification: product.productSpecification,
                    productBrand: product.productBrand,
                    customUnit: product.unitName,
                    totalNumber: '-', // 从产品库选择的产品没有总数限制
                    purchaseNums: '-',
                    number: 1, // 默认采购数量为1
                    isDepose: 0,
                    detailDTOList: [],
                }));

                contractProducts.value.push(...newProducts);
                proxy.$message.success(`成功添加 ${products.length} 个产品到采购列表`);
            })
            .catch(err => {
                console.error('获取产品详情失败:', err);
                proxy.$message.error('获取产品详情失败，请重试');
            });

        return;
    }

    // 如果是从产品库选择（不是拆解产品），且ids是产品对象数组，直接添加到采购列表
    if (Array.isArray(ids) && ids.length > 0 && typeof ids[0] === 'object') {
        // 检查是否有重复产品
        const existingProductIds = contractProducts.value.map(p => p.productId || p.id);
        const duplicateProducts = ids.filter(
            product =>
                existingProductIds.includes(product.id) || existingProductIds.includes(product.productId)
        );

        if (duplicateProducts.length > 0) {
            proxy.$message.warning('选择的产品中有重复项，请重新选择');
            return;
        }

        // 将选中的产品添加到采购列表
        const newProducts = ids.map(product => ({
            id: product.id,
            productId: product.id,
            customProductName: product.productName,
            customProductSpecification: product.productSpecification,
            productBrand: product.productBrand,
            unitName: product.unitName,
            totalNumber: 999999, // 从产品库选择的产品没有总数限制
            purchaseNums: 0,
            number: 1, // 默认采购数量为1
            isDepose: 0,
            detailDTOList: [],
        }));

        contractProducts.value.push(...newProducts);
        proxy.$message.success(`成功添加 ${ids.length} 个产品到采购列表`);
        return;
    }

    // 以下是原有的拆解产品逻辑
    if (!detailForm.value.id) {
        proxy.$message.warning('请先选择要拆解的产品');
        return;
    }

    // 验证采购数量
    if (!detailForm.value.number || detailForm.value.number <= 0) {
        proxy.$message.warning('请先设置采购数量再添加拆解产品');
        return;
    }

    // 检查是否有重复产品
    const existingProductIds = (detailForm.value.detailDTOList || []).map(p => p.productId);
    const duplicateIds = productIds.filter(id => existingProductIds.includes(id));

    if (duplicateIds.length > 0) {
        proxy.$message.warning('选择的产品中有重复项，请重新选择');
        return;
    }

    // 如果ids是产品对象数组，直接使用
    if (Array.isArray(ids) && ids.length > 0 && typeof ids[0] === 'object') {
        ids.forEach(product => {
            const decomposeProduct = {
                productId: product.id,
                productName: product.productName,
                productSpecification: product.productSpecification,
                productBrand: product.productBrand,
                unitName: product.unitName,
                number: 1,
            };

            if (!detailForm.value.detailDTOList) {
                detailForm.value.detailDTOList = [];
            }

            detailForm.value.detailDTOList.push(decomposeProduct);
        });

        // 同步更新原数据
        const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
        if (originalProduct) {
            originalProduct.detailDTOList = [...detailForm.value.detailDTOList];
        }

        proxy.$message.success(`成功添加 ${ids.length} 个产品到拆解列表`);
    } else {
        // 如果是ID数组，需要获取产品详情
        let addedCount = 0;
        productIds.forEach(id => {
            axios
                .get(`/api/vt-admin/product/detail?id=${id}`)
                .then(res => {
                    const product = res.data.data;
                    const decomposeProduct = {
                        productId: product.id,
                        productName: product.productName,
                        productSpecification: product.productSpecification,
                        productBrand: product.productBrand,
                        unitName: product.unitName,
                        number: 1,
                    };

                    if (!detailForm.value.detailDTOList) {
                        detailForm.value.detailDTOList = [];
                    }

                    detailForm.value.detailDTOList.push(decomposeProduct);
                    addedCount++;

                    // 同步更新原数据
                    const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
                    if (originalProduct) {
                        originalProduct.detailDTOList = [...detailForm.value.detailDTOList];
                    }

                    if (addedCount === productIds.length) {
                        proxy.$message.success(`成功添加 ${addedCount} 个产品到拆解列表`);
                    }
                })
                .catch(err => {
                    console.error('获取产品详情失败:', err);
                    proxy.$message.error(`获取产品${id}详情失败`);
                });
        });
    }
}

// 打开合同产品选择器
function openContractProductSelector() {
    contractProductSelectorVisible.value = true;
    loadAvailableContractProducts();
}

// 加载可用的合同产品
function loadAvailableContractProducts() {
    if (props.offerId) {
        // 调用实际API获取产品数据
        axios
            .get('/api/vt-admin/sealContract/productPage', {
                params: {
                    size: 100000,
                    offerId: props.offerId,
                },
            })
            .then(res => {
                availableContractProducts.value = res.data.data.records.map(item => ({
                    ...item,
                }));
            })
            .catch(err => {
                console.error('获取产品列表失败:', err);
                // 如果API调用失败，使用模拟数据
                availableContractProducts.value = [
                    {
                        id: 1,
                        customProductName: '联想ThinkPad笔记本',
                        customProductSpecification: 'T14 Gen3 i7-1255U 16G 512G',
                        unitName: '台',
                        number: 50,
                        productBrand: '联想',
                        productId: 'P001',
                        purchaseNums: 15,
                    },
                    {
                        id: 2,
                        customProductName: '戴尔显示器',
                        customProductSpecification: 'U2422H 24英寸 IPS',
                        unitName: '台',
                        number: 100,
                        productBrand: '戴尔',
                        productId: 'P002',
                        purchaseNums: 25,
                    },
                    {
                        id: 3,
                        customProductName: '罗技无线鼠标',
                        customProductSpecification: 'MX Master 3S',
                        unitName: '个',
                        number: 200,
                        productBrand: '罗技',
                        productId: 'P003',
                        purchaseNums: 50,
                    },
                    {
                        id: 4,
                        customProductName: '机械键盘',
                        customProductSpecification: '87键青轴',
                        unitName: '个',
                        number: 80,
                        productBrand: '雷蛇',
                        productId: 'P004',
                        purchaseNums: 10,
                    },
                ];
            });
    } else {
        // 如果没有offerId，使用模拟数据
        availableContractProducts.value = [
            {
                id: 1,
                customProductName: '联想ThinkPad笔记本',
                customProductSpecification: 'T14 Gen3 i7-1255U 16G 512G',
                unitName: '台',
                number: 50,
                productBrand: '联想',
                productId: 'P001',
                purchaseNums: 15,
            },
            {
                id: 2,
                customProductName: '戴尔显示器',
                customProductSpecification: 'U2422H 24英寸 IPS',
                unitName: '台',
                number: 100,
                productBrand: '戴尔',
                productId: 'P002',
                purchaseNums: 25,
            },
            {
                id: 3,
                customProductName: '罗技无线鼠标',
                customProductSpecification: 'MX Master 3S',
                unitName: '个',
                number: 200,
                productBrand: '罗技',
                productId: 'P003',
                purchaseNums: 50,
            },
            {
                id: 4,
                customProductName: '机械键盘',
                customProductSpecification: '87键青轴',
                unitName: '个',
                number: 80,
                productBrand: '雷蛇',
                productId: 'P004',
                purchaseNums: 10,
            },
        ];
    }
}

// 处理合同产品选择
function handleContractProductSelection(selection) {
    selectedContractProducts.value = selection;
}

// 确认合同产品选择
function confirmContractProductSelection() {
    ;
    if (selectedContractProducts.value.length === 0) {
        proxy.$message.warning('请选择要添加的产品');
        return;
    }

    // 将选中的产品添加到采购列表
    const newProducts = selectedContractProducts.value.map(product => ({
        ...product,
        isDepose: 0,
        detailDTOList: [],
        purchaseNums: parseFloat(product.purchaseNums) || 0,
        totalNumber: product.number || 0,
        number: 0,
        detailId: product.id,
        id: null,
    }));

    contractProducts.value.push(...newProducts);

    proxy.$message.success(`已添加 ${selectedContractProducts.value.length} 个产品到采购列表`);
    handleContractProductSelectorClose();
}

// 关闭合同产品选择器
function handleContractProductSelectorClose() {
    contractProductSelectorVisible.value = false;
    selectedContractProducts.value = [];
}

// 删除合同产品
function removeContractProduct(index) {
    const removedProduct = contractProducts.value[index];

    // 如果当前详情显示的是被删除的产品，清空详情
    if (detailForm.value.id === removedProduct.id) {
        detailForm.value = {};
    }

    contractProducts.value.splice(index, 1);
}

// 验证拆解产品数量
function validateDecomposeQuantity() {
    if (!detailForm.value.detailDTOList || !detailForm.value.number) {
        return;
    }

    const totalDecomposeQuantity = detailForm.value.detailDTOList.reduce((sum, item) => {
        return sum + (parseFloat(item.quantity) || 0);
    }, 0);

    if (totalDecomposeQuantity > detailForm.value.number) {
        proxy.$message.warning(
            `拆解产品总数量(${totalDecomposeQuantity})不能超过采购数量(${detailForm.value.number})`
        );
        return false;
    }

    return true;
}

// 批量设置拆解产品数量
function batchSetDecomposeQuantity() {
    if (!detailForm.value.detailDTOList || detailForm.value.detailDTOList.length === 0) {
        proxy.$message.warning('请先添加拆解产品');
        return;
    }

    const averageQuantity =
        Math.floor(((detailForm.value.number || 0) / detailForm.value.detailDTOList.length) * 100) /
        100;

    proxy
        .$confirm(
            `将为所有拆解产品平均分配数量，每个产品数量将设为 ${averageQuantity}，是否继续？`,
            '批量设置',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info',
            }
        )
        .then(() => {
            detailForm.value.detailDTOList.forEach(item => {
                item.quantity = averageQuantity;
            });

            // 同步更新原数据
            const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
            if (originalProduct) {
                originalProduct.detailDTOList = [...detailForm.value.detailDTOList];
            }

            proxy.$message.success('批量设置成功');
        });
}

// 清空所有拆解产品
function clearAllDecomposeProducts() {
    if (!detailForm.value.detailDTOList || detailForm.value.detailDTOList.length === 0) {
        return;
    }

    proxy
        .$confirm(`确认清空所有拆解产品吗？此操作不可恢复。`, '清空确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(() => {
            detailForm.value.detailDTOList = [];

            // 同步更新原数据
            const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
            if (originalProduct) {
                originalProduct.detailDTOList = [];
            }

            proxy.$message.success('已清空所有拆解产品');
        });
}

// 删除拆解产品
function removeDecomposeProduct(index) {
    if (
        !detailForm.value.detailDTOList ||
        index < 0 ||
        index >= detailForm.value.detailDTOList.length
    ) {
        return;
    }

    const productToRemove = detailForm.value.detailDTOList[index];

    proxy
        .$confirm(`确认删除拆解产品"${productToRemove.productName}"吗？`, '删除确认', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(() => {
            detailForm.value.detailDTOList.splice(index, 1);

            // 同步更新原数据
            const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
            if (originalProduct) {
                originalProduct.detailDTOList = [...detailForm.value.detailDTOList];
            }

            proxy.$message.success('删除成功');
        })
        .catch(() => {
            // 用户取消删除
        });
}

// 处理保留原产品开关
function handleIsPreChange(val) {
    if (val && detailForm.value.productId) {
        // 检查采购数量
        if (!detailForm.value.number || detailForm.value.number <= 0) {
            proxy.$message.warning('请先设置采购数量再开启保留原产品');
            detailForm.value.isPre = false;
            return;
        }

        // 保留原产品时，添加原产品到拆解列表
        const originalProduct = {
            productId: detailForm.value.productId,
            productName: detailForm.value.customProductName + '(原产品)',
            productSpecification: detailForm.value.customProductSpecification,
            productBrand: detailForm.value.productBrand,
            unitName: detailForm.value.unitName,
            number: detailForm.value.number || 1,
        };

        if (!detailForm.value.detailDTOList) {
            detailForm.value.detailDTOList = [];
        }

        // 检查是否已存在
        const exists = detailForm.value.detailDTOList.some(
            item => item.productId === originalProduct.productId
        );

        if (!exists) {
            detailForm.value.detailDTOList.unshift(originalProduct);
            proxy.$message.success('已添加原产品到拆解列表');
        }
    } else {
        // 不保留原产品时，询问用户确认
        if (detailForm.value.detailDTOList && detailForm.value.productId) {
            const originalProducts = detailForm.value.detailDTOList.filter(
                item => item.productId === detailForm.value.productId
            );

            if (originalProducts.length > 0) {
                proxy
                    .$confirm('关闭保留原产品将从拆解列表中移除原产品，是否继续？', '提示', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        type: 'warning',
                    })
                    .then(() => {
                        detailForm.value.detailDTOList = detailForm.value.detailDTOList.filter(
                            item => item.productId !== detailForm.value.productId
                        );

                        // 同步更新原数据
                        const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
                        if (originalProduct) {
                            originalProduct.detailDTOList = [...(detailForm.value.detailDTOList || [])];
                            originalProduct.isPre = false;
                        }

                        proxy.$message.success('已移除原产品');
                    })
                    .catch(() => {
                        detailForm.value.isPre = true; // 取消时恢复开关状态
                    });
                return;
            }
        }
    }

    // 同步更新原数据
    const originalProduct = contractProducts.value.find(p => p.id === detailForm.value.id);
    if (originalProduct) {
        originalProduct.detailDTOList = [...(detailForm.value.detailDTOList || [])];
        originalProduct.isPre = val;
    }
}

// 提交表单
function submitAddForm(value) {
    if (!addFormRef.value) return;

    addFormRef.value.validate(valid => {
        if (valid) {
            // 检查选中产品的采购数量
            const invalidProducts = contractProducts.value.filter(
                item => !item.number || item.number <= 0
            );
            if (invalidProducts.length > 0) {
                proxy.$message.warning('请为选中的产品设置采购数量');
                return;
            }

            submitLoading.value = true;

            // 只收集选中产品的拆解数据
            const decomposeData = contractProducts.value
                .filter(item => item.isDepose === 1)
                .map(item => ({
                    id: item.id,
                    productId: item.productId,
                    customProductName: item.customProductName,
                    customProductSpecification: item.customProductSpecification,
                    number: item.number,
                    detailDTOList: item.detailDTOList || [],
                }));

            // 收集所有选中产品的基本信息
            const purchaseProducts = contractProducts.value.map(item => ({
                id: item.id,
                detailId: item.detailId,
                productId: item.productId,
                customProductName: item.customProductName,
                customProductSpecification: item.customProductSpecification,
                productBrand: item.productBrand,
                customUnit: item.customUnit,
                number: item.number,
                totalNumber: item.totalNumber,
                purchaseNums: item.purchaseNums,
                isDepose: item.isDepose,
                zhhsdj: item.zhhsdj,
                zhhsze: item.zhhsdj * item.number,
                detailDTOList: item.detailDTOList || [],
            }));

            const submitData = {
                ...addForm.value,
                offerId: props.offerId,
                applyStatus: value,
                contractId: props.sealContractId,
                purchaseDetailDTOList: purchaseProducts,
                decomposeProducts: decomposeData,
            };

            // 根据模式选择不同的API
            let apiUrl = '/api/vt-admin/sealContract/addPurchase';
            let successMessage = '采购单新增成功！';

            if (drawerMode.value === 'edit') {
                apiUrl = '/api/vt-admin/sealContract/updatePurchase';
                submitData.id = currentEditId.value;
                successMessage = '采购单更新成功！';
            }

            // 模拟API提交
            console.log('提交的采购单数据:', submitData);

            // 模拟网络请求延迟
            setTimeout(() => {
                submitLoading.value = false;
                axios
                    .post(apiUrl, submitData)
                    .then(res => {
                        proxy.$message.success(successMessage);
                        handleDrawerClose();
                        onLoad();
                    })
                    .catch(err => {
                        console.error('提交失败:', err);
                        proxy.$message.error('操作失败，请重试');
                    });
            }, 1500);
        }
    });
}

let option = ref({
    //   height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    header: false,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 100,
    menu: true,
    border: true,
    column: [
        {
            label: '请购人',
            prop: 'applyName',
            width: 120,
            overHidden: true,
        },
        {
            label: '请购时间',
            prop: 'createTime',
            width: 140,
            type: 'date',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'YYYY-MM-DD HH:mm',
            overHidden: true,
        },

        // {
        //     label: '收货人',
        //     prop: 'deliveryUser',
        //     width: 120,
        // },
        // {
        //     label: '联系方式',
        //     prop: 'deliveryContact',
        //     searchSpan: 6,
        //     searchRange: true,
        //     type: 'date',
        //     width: 150,
        // },
        // {
        //     label: '收货地址',
        //     prop: 'deliveryAddress',
        //     type: 'select',
        // },
        {
            label: '交付时间',
            prop: 'deliveryDate',
            type: 'date',
            width: 150,
        },
        {
            label: '备注',
            prop: 'remark',
            overHidden: true,
        },

        {
            label: '申请状态',
            prop: 'applyStatus',
            dicData: [
                {
                    value: 0,
                    label: '草稿',
                },
                {
                    value: 1,
                    label: '已确认',
                },
            ],
        },
        {
            label: '销售总额',
            prop: 'totalPrice',
            width: 120,
            formatter: (row) => {
                return parseFloat(row.totalPrice).toFixed(2)
            }
        },
        {
            label: '订单状态',
            prop: 'orderStatus',
            width: 120
        },
        // {
        //   label: '发货状态',
        //   prop: 'sendStatus',
        //   type: 'select',
        //   width: 100,
        //   dicData: [
        //     {
        //       value: 0,
        //       label: '未发货',
        //     },
        //     {
        //       value: 1,
        //       label: '已发货',
        //     },
        //   ],
        //   slot: true,
        // },
        {
            label: '收货状态',
            prop: 'deliveryStatus',
            type: 'select',
            width: 100,
            dicData: [
                {
                    value: 0,
                    label: '未收货',
                },
                {
                    value: 1,
                    label: '已收货',
                },
            ],
        },
        // {
        //   label: '附件',
        //   prop: 'file',
        // },
        // {
        //   label: '批示内容',
        //   prop: 'content',
        // },
    ],
});
let form = ref({});
let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/sealContract/deletePurchase?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContract/pageForPurchase';
let params = ref({});
let tableData = ref([
    {
        id: 1,
        orderNo: 'PO202401',
        deliveryDate: '2024-02-15',
        number: 25,
        orderDate: '2024-01-15 10:30:00',
        auditStatus: 1,
        orderStatus: 1,
        inStorageStatus: 0,
        outStorageStatus: 0,
        auditType: 0,
        cancelStatus: 0,
        purchaseType: 1,
    },
    {
        id: 2,
        orderNo: 'PO202402',
        deliveryDate: '2024-02-20',
        number: 50,
        orderDate: '2024-01-16 14:20:00',
        auditStatus: 0,
        orderStatus: 0,
        inStorageStatus: 0,
        outStorageStatus: 0,
        auditType: 1,
        cancelStatus: 0,
        purchaseType: 1,
    },
]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
        .get(tableUrl, {
            params: {
                ...params.value,
                size,
                current,
                contractId: props.sealContractId,
            },
        })
        .then(res => {
            tableData.value = res.data.data.records;
            page.value.total = res.data.data.total;
            loading.value = false;
        });
}
let router = useRouter();

function rowSave(form, done, loading) {
    const data = {
        ...form,
    };
    axios
        .post(addUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(err => {
            done();
        });
}
function rowUpdate(row, index, done, loading) {
    const data = {
        ...row,
    };
    axios
        .post(updateUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(err => {
            done();
        });
}
function rowDel(form) {
    proxy
        .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(() => {
            console.log(222);
            axios.post(delUrl + form.id).then(res => {
                proxy.$message({
                    type: 'success',
                    message: '删除成功',
                });
                onLoad();
            });
        })
        .catch(() => { });
}
function reset() {
    onLoad();
}
function searchChange(params, done) {
    onLoad();
    done();
}

// 添加缺失的函数
function add() {
    openAddDrawer();
}

function handleSectionChange(selection) {
    // 处理选择变更
}

function back(row) {
    // 退回处理
}

function deleteProduct(row) {
    // 删除产品处理
}

function wareHousing(row) {
    // 入库处理
}

function outHouse(row) {
    // 出库处理
}

function printOrder(row) {
    // 打印送货单
}

function toDetail(row) {
    // 跳转详情
}

function toInquiry(row) {
    // 跳转询价单
}

function viewProduct(row) {
    // 查看产品
}

function selectable(row) {
    ;
    return row.number * 1 != row.purchaseNums * 1;
}

const emits = defineEmits(['data-changed']);
</script>

<style lang="scss" scoped>
.drawer-content {
    height: 100%;
    /* 减去抽屉头部和底部按钮的高度 */
    display: flex;
    flex-direction: column;
}

.drawer-content .el-row {
    flex: 1;
    height: 100%;
}

.drawer-content .el-col {
    height: 100%;
}

.drawer-content .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.drawer-content .el-card .el-card__body {
    flex: 1;
    padding: 0;
    overflow: auto;
}

.add-form {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
}

.decompose-section {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.decompose-section .el-row {
    flex: 1;
    height: 100%;
}

.decompose-section .el-card {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.decompose-section .el-card .el-card__body {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.decompose-section .el-table {
    flex: 1;
    height: auto;
}

.product-section {
    margin-bottom: 30px;

    h3 {
        margin-bottom: 15px;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
    }
}

.decompose-section {
    margin-bottom: 30px;

    h3 {
        margin-bottom: 15px;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
    }

    .product-decompose {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;

        h4 {
            margin-bottom: 10px;
            color: #606266;
            font-size: 14px;
            font-weight: 500;
        }

        .decompose-table {
            margin-top: 10px;
        }
    }
}

.drawer-footer {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid #e4e7ed;
}

.empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #909399;
}

.decompose-section {
    .el-card {
        .el-card__header {
            padding: 12px 20px;
            background-color: #f5f7fa;
            border-bottom: 1px solid #ebeef5;

            .el-form--inline .el-form-item {
                margin-bottom: 0;
            }
        }

        .el-card__body {
            padding: 15px 20px;
        }
    }

    .el-table {
        .el-table__header {
            th {
                background-color: #fafafa;
                color: #606266;
                font-weight: 500;
            }
        }

        .el-table__row {
            cursor: pointer;

            &:hover {
                background-color: #f5f7fa;
            }
        }
    }

    .el-descriptions {
        .el-descriptions__title {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            margin-bottom: 10px;
        }
    }
}
</style>
