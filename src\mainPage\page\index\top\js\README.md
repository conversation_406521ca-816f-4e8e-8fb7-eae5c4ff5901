# 智能体切换功能说明

## 功能概述

实现了智能体列表的点击切换功能，用户可以在智能体助手窗口中点击左侧列表中的任意智能体，实时切换到对应的智能体对话界面。

## 主要功能

### 1. 智能体列表展示
- 左侧面板显示所有可用的智能体列表
- 显示智能体名称、图标和备注信息
- 显示智能体总数
- 支持空状态提示

### 2. 智能体切换
- **点击切换**：点击列表中的任意智能体即可切换
- **视觉反馈**：
  - 当前选中的智能体高亮显示（蓝色边框和背景）
  - 鼠标悬停时显示浅蓝色背景
  - 平滑的过渡动画效果
- **加载状态**：
  - 切换时显示加载动画
  - 显示"正在切换智能体..."提示
  - 加载完成后自动隐藏

### 3. 智能体选中状态同步
- **双向同步**：
  - 在顶部导航栏（top-ai.vue）点击智能体时
  - 弹窗中的智能体列表会自动同步选中状态
  - 确保两个列表的选中状态始终一致
- **实现机制**：
  - 每个智能体项添加 `data-agent-token` 属性
  - 通过 `updateAgentListSelection(token)` 函数更新选中状态
  - 初始化时自动设置第一个智能体为选中状态

### 4. 技术实现

#### 公共方法（代码重构）
为了避免代码重复，提取了以下公共方法：

1. **`compressAndEncodeBase64(input)`**
   - 压缩和 Base64 编码字符串
   - 使用 gzip 压缩算法

2. **`getCompressedInputs(inputs, mode)`**
   - 处理 inputs 参数
   - chat 模式下不压缩，其他模式压缩

3. **`getCompressedSystemVariables(systemVariables)`**
   - 处理 systemVariables 参数
   - 添加 `sys.` 前缀
   - 始终压缩

4. **`getCompressedUserVariables(userVariables, mode)`**
   - 处理 userVariables 参数
   - 添加 `user.` 前缀
   - chat 模式下不压缩，其他模式压缩

5. **`buildIframeUrl(config)`**
   - 根据配置生成完整的 iframe URL
   - 返回 `{ iframeUrl, baseUrl }`
   - 自动检查 URL 长度（>2048 字符时警告）

6. **`updateAgentListSelection(selectedToken)`**
   - 更新智能体列表的选中状态
   - 根据 token 匹配对应的智能体项
   - 清除其他项的选中状态，设置匹配项为选中

#### 核心函数：`switchAgent(agent)`
```javascript
async function switchAgent(agent) {
    // 1. 显示加载状态
    // 2. 重新构建配置（使用新的 agent.dictKey）
    // 3. 调用 buildIframeUrl() 生成新的 URL
    // 4. 更新 iframe src
    // 5. 监听加载完成，隐藏加载状态
}
```

#### 参数处理
- 支持 `inputs`、`systemVariables`、`userVariables` 三类参数
- 自动压缩和 Base64 编码（chat 模式除外）
- 保持用户上下文信息（user_id、tenant_id、avatar_url 等）

#### 加载状态
- 使用半透明遮罩层
- CSS3 旋转动画的加载图标
- 自动在 iframe 加载完成后隐藏

## 使用方法

### 基本使用
1. 点击顶部导航栏的 AI 助手图标
2. 在弹出的窗口左侧查看智能体列表
3. 点击任意智能体即可切换
4. 等待加载完成后开始对话

### 快捷操作
- **拖动窗口**：点击窗口顶部区域可拖动整个窗口
- **关闭窗口**：点击右上角的关闭按钮

## 数据结构

### Agent 对象
```javascript
{
  dictKey: "智能体的 token",
  dictValue: "智能体名称",
  remark: "智能体备注说明",
  id: "智能体ID"
}
```

### Config 对象
```javascript
{
  token: "智能体 token",
  baseUrl: "AI 服务地址",
  mode: "chat",
  inputs: {
    user_id: "用户ID",
    tenant_id: "租户ID"
  },
  systemVariables: {
    user_id: "用户ID"
  },
  userVariables: {
    avatar_url: "用户头像",
    name: "用户姓名",
    user_id: "用户ID"
  }
}
```

## 文件说明

- **index.js**：主要逻辑文件，包含智能体切换功能
- **top-ai.vue**：顶部 AI 助手入口组件
- **README.md**：本说明文档

## 注意事项

1. **网络要求**：切换智能体需要重新加载 iframe，请确保网络连接稳定
2. **数据保持**：切换智能体会清空当前对话历史，开始新的对话
3. **权限控制**：智能体列表从 Vuex store 中获取，确保已正确配置
4. **URL 长度**：如果参数过多导致 URL 超过 2048 字符，会在控制台输出警告

## 开发说明

### 调试信息
在浏览器控制台可以看到以下日志：
- `切换智能体:` - 开始切换时的智能体信息
- `正在切换智能体:` - 新的 URL 信息
- `智能体切换完成:` - 切换成功的智能体名称

### 扩展开发
如需添加新功能，可以在以下位置进行扩展：
- **切换前钩子**：在 `switchAgent` 函数开始处添加
- **切换后钩子**：在 `onIframeLoad` 回调中添加
- **自定义参数**：在 `newConfig` 对象中添加

## 代码优化

### 重构亮点
- **消除重复代码**：将 `embedChatbot` 和 `switchAgent` 中重复的压缩编码逻辑提取为公共方法
- **提高可维护性**：统一的 URL 生成逻辑，修改一处即可影响所有调用
- **增强可读性**：函数职责单一，代码结构更清晰
- **便于扩展**：新增智能体相关功能时可直接复用公共方法

### 代码对比
**重构前**：
- `embedChatbot` 中有 47 行重复代码
- `switchAgent` 中有 47 行重复代码
- 总计约 94 行重复代码

**重构后**：
- 提取 5 个公共方法（约 56 行）
- `embedChatbot` 简化为 3 行调用
- `switchAgent` 简化为 1 行调用
- 减少约 85 行重复代码

## 更新日志

### v1.2.0 (2025-11-21)
- ✅ 实现智能体选中状态同步功能
- ✅ 添加 `updateAgentListSelection` 函数
- ✅ 为智能体列表项添加 `data-agent-token` 属性
- ✅ 支持从外部（top-ai.vue）点击智能体时同步选中状态

### v1.1.0 (2025-11-21)
- ✅ 重构代码，提取公共方法
- ✅ 消除重复代码，提高可维护性
- ✅ 优化代码结构，增强可读性

### v1.0.0 (2025-11-21)
- ✅ 实现智能体列表展示
- ✅ 实现点击切换功能
- ✅ 添加加载状态提示
- ✅ 添加视觉反馈效果
- ✅ 支持参数压缩和编码
- ✅ 完善错误处理

