<template>
    <!-- 输入框 -->
    <el-input v-model="displayName" :placeholder="placeholder" style="width: 100%" clearable readonly
        @click="openDialog" @clear="handleClear"></el-input>

    <!-- 选择弹窗 -->
    <el-dialog :title="dialogTitle" v-model="drawer" width="70%">
        <avue-crud :option="currentOption" @row-click="rowClick" :data="tableData" v-model:page="page"
            v-model:search="params" @on-load="onLoad" @row-update="rowUpdate" @row-save="rowSave"
            :table-loading="loading" ref="crud" @keyup.enter="onLoad" @row-del="rowDel" @search-reset="reset"
            @search-change="searchChange" @refresh-change="onLoad" @current-change="onLoad" @size-change="onLoad"
            v-model="form">
            <!-- 单选列 -->
            <template #radio="{ row }">
                <el-radio v-model="selectedId" :label="row.id" @change="handleSelect(row)">
                    <span></span>
                </el-radio>
            </template>

            <!-- 开票伙伴插槽 -->
            <template #invoiceCompanyName="{ row }">
                <div style="display: flex;align-items: center;justify-content: center;">
                    <span>{{ row.invoiceCompanyName }}</span>
                    <el-icon style="margin-left: 8px; cursor: pointer; color: #409EFF;" @click="showInvoiceInfo(row)">
                        <InfoFilled />
                    </el-icon>
                </div>
            </template>

            <!-- 银行信息插槽 -->
            <template #bankInfo="{ row }">
                <div style="display: flex;align-items: center;justify-content: center;">
                    <span>{{ row.bankName || '查看详情' }}</span>
                    <el-icon style="margin-left: 8px; cursor: pointer; color: #409EFF;" @click="showBankInfo(row)">
                        <InfoFilled />
                    </el-icon>
                </div>
            </template>

            <template #partnerName="{ row }">
                <el-popover
                    placement="right"
                    :width="500"
                    trigger="click"
                >
                    <template #reference>
                        <el-link type="primary">{{ row.partnerName }}</el-link>
                    </template>
                    <template #default>
                        <div>
                            <h4 style="margin: 0 0 16px 0; padding-bottom: 8px; border-bottom: 1px solid #eee;">
                                合作伙伴详情
                            </h4>
                            <el-descriptions :column="1" border size="small">
                                <el-descriptions-item label="伙伴名称">
                                    {{ row.partnerName || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="联系人">
                                    {{ row.contactPerson || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="联系电话">
                                    {{ row.contactPhone || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="业务员">
                                    {{ row.businessPersonName || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="开票伙伴">
                                    {{ row.invoiceCompanyName || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="纳税人识别号">
                                    {{ row.ratepayerIdentifyNumber || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="开户行名称">
                                    {{ row.bankName || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="账号名称">
                                    {{ row.accountName || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="开户行账号">
                                    {{ row.bankAccount || '-' }}
                                </el-descriptions-item>
                                <el-descriptions-item label="备注">
                                    {{ row.remark || '-' }}
                                </el-descriptions-item>
                            </el-descriptions>
                        </div>
                    </template>
                </el-popover>
            </template>
        </avue-crud>

        <template #footer>
            <span>
                <el-button @click="drawer = false">取消</el-button>
                <el-button type="primary" @click="confirmSelect">确认</el-button>
            </span>
        </template>
    </el-dialog>

    <!-- 开票信息弹窗 -->
    <el-dialog v-model="invoiceDialogVisible" title="开票信息" width="500px">
        <el-descriptions :column="1" border>
            <el-descriptions-item label="开票伙伴">{{ currentInvoiceInfo.invoiceCompanyName }}</el-descriptions-item>
            <el-descriptions-item label="纳税人识别号">{{ currentInvoiceInfo.ratepayerIdentifyNumber }}</el-descriptions-item>
        </el-descriptions>
    </el-dialog>

    <!-- 银行信息弹窗 -->
    <el-dialog v-model="bankDialogVisible" title="银行信息" width="500px">
        <el-descriptions :column="1" border>
            <el-descriptions-item label="账号名称">{{ currentBankInfo.accountName }}</el-descriptions-item>
            <el-descriptions-item label="开户行账号">{{ currentBankInfo.bankAccount }}</el-descriptions-item>
            <el-descriptions-item label="开户行名称">{{ currentBankInfo.bankName }}</el-descriptions-item>
        </el-descriptions>
    </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, computed, watch } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';

// Props定义
const props = defineProps({
    modelValue: {
        type: [String, Number],
        default: ''
    },
    type: {
        type: Number,
        default: 0, // 0: partnerOption, 1: qualificationOption
        validator: (value) => [0, 1].includes(value)
    },
    selectType: {
        type: Number,
        default: 0, // 0: 查询自己的, 1: 查所有
        
    }
});

// Emit定义
const emit = defineEmits(['update:modelValue']);

// 内部状态
const drawer = ref(false);
const selectedId = ref('');
const selectedRow = ref(null);
const displayName = ref('');

// 监听外部v-model变化
watch(() => props.modelValue, (newVal) => {
 
    if (newVal) {
        selectedId.value = newVal;
        // 如果有值，需要加载对应的名称
        loadSelectedName(newVal);
    } else {
        selectedId.value = '';
        selectedRow.value = null;
        displayName.value = '';
    }
}, { immediate: true });

// 计算当前使用的配置
const currentOption = computed(() => {
    return props.type === 0 ? partnerOption.value : qualificationOption.value;
});
const placeholder = computed(() => {
    return props.type === 0 ? '请选择伙伴' : '请选择伙伴';
});
// 计算弹窗标题
const dialogTitle = computed(() => {
    return props.type === 0 ? '选择伙伴' : '选择伙伴';
});

// 打开弹窗
function openDialog() {
    drawer.value = true;
    onLoad();
}

// 清空选择
function handleClear() {
    selectedId.value = '';
    selectedRow.value = null;
    displayName.value = '';
    emit('update:modelValue', '');
}

// 选择行
function handleSelect(row) {
    selectedRow.value = row;
    selectedId.value = row.id;
}

// 确认选择
function confirmSelect() {
    if (selectedId.value && selectedRow.value) {
        displayName.value = selectedRow.value.partnerName;
        emit('update:modelValue', selectedId.value);
        drawer.value = false;
    } else {
        proxy.$message.warning('请选择一条数据');
    }
}
function rowClick(row) {
    
    selectedRow.value = row;
    selectedId.value = row.id;
}
// 加载选中项的名称
function loadSelectedName(id) {
    if (!id) return;

    axios.get(`/api/vt-admin/partner/detail`, {
        params: { id }
    }).then(res => {
        if (res.data.code === 200 && res.data.data) {
            displayName.value = res.data.data.partnerName;
            selectedRow.value = res.data.data;
        }
    }).catch(() => {
        // 如果加载失败，清空显示
        displayName.value = '';
    });
}

let partnerOption = ref({

    align: 'center',
    addBtn: false,
    editBtn: true,
    delBtn: true,
    menu: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 8,
    searchLabelWidth: 120,
    dialogType: 'drawer',
    menuWidth: 160,
    border: true,
    labelWidth: 120,
    column: [
        {
            label: ' ',
            prop: 'radio',
            type: 'radio',
            width: 55,
            display:false
        },
        {
            label: '伙伴名称',
            prop: 'partnerName',
            search: true,
            align: 'center',
            rules: [
                { required: true, message: '请输入伙伴名称', trigger: 'blur' },
            ],
        },
        {
            label: '联系人',
            prop: 'contactPerson',
            width: 120,
            align: 'center',
        },
        {
            label: '联系电话',
            prop: 'contactPhone',
            width: 160,
            align: 'center',
        },
        {
            label: '业务员',
            component: 'wf-user-select',
            prop: 'businessPerson',
            width: 120,
            formatter: (row) => {
                return row.businessPersonName
            },
            rules: [
                { required: true, message: '请选择业务员', trigger: 'blur' },
            ],
            params: {
                userUrl: '/api/blade-system/search/user?functionKeys=bussinessUser'
            },
            // params: {
            //   checkType: 'checkbox',
            // },

        },
        {
            label: '开票伙伴',
            prop: 'invoiceCompanyName',
            align: 'center',
            display: false,
            slot: true,
        },
        {
            label: '银行信息',
            prop: 'bankInfo',
            align: 'center',

            display: false,
            slot: true,
        },
        {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            overHidden: true,
            span: 24,
        }
    ],
    group: [{
        label: '开票信息',
        prop: 'invoiceInfo',
        column: [{
            label: '开票伙伴',
            prop: 'invoiceCompanyName',
            span: 12,
            search: true,
        },
        {
            label: '纳税人识别号',
            prop: 'ratepayerIdentifyNumber',
            span: 12,
        },
        ]
    },
    // 银行信息
    {
        label: '银行信息',
        prop: 'bankInfo',
        column: [{
            label: '开户行名称',
            prop: 'bankName',
            span: 24,
        }, {
            label: '账号名称',
            prop: 'accountName',
            span: 12,
        },
        {
            label: '开户行账号',
            prop: 'bankAccount',
            span: 12,
        },
        ]
    }
    ]
});
let qualificationOption = ref({

    align: 'center',
    addBtn: false,
    editBtn: true,
    delBtn: true,
    menu: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    searchLabelWidth: 120,
    dialogType: 'drawer',
    menuWidth: 160,
    border: true,
    labelWidth: 120,
    column: [
        {
            label: ' ',
            prop: 'radio',
            type: 'radio',
            width: 55, display:false

        },
        {
            label: '伙伴名称',
            prop: 'partnerName',
            search: true,
            align: 'center',
            rules: [
                { required: true, message: '请输入伙伴名称', trigger: 'blur' },
            ],
        },
        {
            label: '联系人',
            prop: 'contactPerson',
            width: 120,
            align: 'center',
        },
        {
            label: '联系电话',
            prop: 'contactPhone',
            width: 160,
            align: 'center',
        },
        // {
        //     label: '业务员',
        //     component: 'wf-user-select',
        //     prop: 'businessPerson',
        //     width: 120,
        //     formatter: (row) => {
        //         return row.businessPersonName
        //     },
        //     rules: [
        //         { required: true, message: '请选择业务员', trigger: 'blur' },
        //     ],
        //     params: {
        //         userUrl: '/api/blade-system/search/user?functionKeys=bussinessUser'
        //     },
        //     // params: {
        //     //   checkType: 'checkbox',
        //     // },

        // },
        {
            label: '开票伙伴',
            prop: 'invoiceCompanyName',
            align: 'center',
            display: false,
            slot: true,
        },
        {
            label: '银行信息',
            prop: 'bankInfo',
            align: 'center',

            display: false,
            slot: true,
        },
        {
            label: '备注',
            prop: 'remark',
            type: 'textarea',
            overHidden: true,
            span: 24,
        }
    ],
    group: [{
        label: '开票信息',
        prop: 'invoiceInfo',
        column: [{
            label: '开票伙伴',
            prop: 'invoiceCompanyName',
            span: 12,
            search: true,
        },
        {
            label: '纳税人识别号',
            prop: 'ratepayerIdentifyNumber',
            span: 12,
        },
        ]
    },
    // 银行信息
    {
        label: '银行信息',
        prop: 'bankInfo',
        column: [{
            label: '开户行名称',
            prop: 'bankName',
            span: 24,
        }, {
            label: '账号名称',
            prop: 'accountName',
            span: 12,
        },
        {
            label: '开户行账号',
            prop: 'bankAccount',
            span: 12,
        },

        ]
    }
    ]
})
let form = ref({});
let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
});

const addUrl = '/api/vt-admin/partner/save'
const delUrl = '/api/vt-admin/partner/remove?ids='
const updateUrl = '/api/vt-admin/partner/update'
const tableUrl = '/api/vt-admin/partner/page'
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let loading = ref(false);

// 弹窗控制
let invoiceDialogVisible = ref(false);
let bankDialogVisible = ref(false);
let currentInvoiceInfo = ref({});
let currentBankInfo = ref({});

function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
        .get(tableUrl, {
            params: {
                type: props.type, // 使用props传入的type
                size,
                current,
                ...params.value,
                selectType: props.type == 1? 1 : props.selectType
            },
        })
        .then(res => {
            loading.value = false;
            tableData.value = res.data.data.records;
            page.value.total = res.data.data.total;
        });
}

function rowSave(form, done) {
    const data = {
        ...form,
        type: props.type // 使用props传入的type
    };
    axios
        .post(addUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(() => {
            done();
        });
}
function rowUpdate(row, index, done) {
    const data = {
        ...row,
    };
    axios
        .post(updateUrl, data)
        .then(res => {
            if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                onLoad();
                done();
            }
        })
        .catch(() => {
            done();
        });
}
function rowDel(form) {
    proxy
        .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
        })
        .then(() => {
            console.log(222);
            axios.post(delUrl + form.id).then(res => {
                proxy.$message({
                    type: 'success',
                    message: '删除成功',
                });
                onLoad();
            });
        })
        .catch(() => { });
}
function reset() {
    onLoad()
}
function searchChange(params, done) {
    onLoad();
    done();
}

// 显示开票信息
function showInvoiceInfo(row) {
    currentInvoiceInfo.value = {
        invoiceCompanyName: row.invoiceCompanyName,
        ratepayerIdentifyNumber: row.ratepayerIdentifyNumber
    };
    invoiceDialogVisible.value = true;
}

// 显示银行信息
function showBankInfo(row) {
    currentBankInfo.value = {
        accountName: row.accountName,
        bankAccount: row.bankAccount,
        bankName: row.bankName
    };
    bankDialogVisible.value = true;
}
</script>

<style lang="scss" scoped></style>