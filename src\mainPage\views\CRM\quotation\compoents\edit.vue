<template>
  <div class="wrap">
    <el-row :gutter="10">
      <el-col :span="5">
        <el-card
          :body-style="{ padding: 0 }"
          style="
            height: 100%;
            overflow-y: auto;
            font-size: 14px;
            padding: 5px 0 0 5px;
            box-sizing: border-box;
          "
        >
          <div style="font-size: 14px; font-weight: bolder">所有汇总:</div>
          <div style="display: flex; gap: 30px">
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 10px;
              "
            >
              <div style="color: #666">
                成本金额：
                <span style="color: #000">{{ allModuleTotal.costTotal.toFixed(2) }}</span>
              </div>
              <div style="color: #666">
                总金额：
                <span style="color: #000">{{ allModuleTotal.total?.toFixed(2) }}</span>
              </div>
              <div style="color: #666">
                客户报价：
                <span>{{
                  (
                    allModuleTotal.total.toFixed(2) - (form.discountsPrice?.toFixed(2) || 0)
                  )?.toFixed(2)
                }}</span>
              </div>
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 10px;
              "
            >
              <div style="color: #666">
                毛利润 ：<span>{{
                  (allModuleTotal.total - allModuleTotal.costTotal).toFixed(2)
                }}</span>
              </div>
              <!-- <div style="color: #666">
                折减：
                <span style="color: var(--el-color-danger)">
                  {{ form.discountsPrice?.toFixed(2) || `0.00` }}
                </span>
                <el-button  v-if="$route.query.type != 'detail'" @click="editDiscountsPrice" type="primary" icon="edit" text></el-button>
              </div> -->
              <div style="color: #666">
                毛利润率：
                <span>{{
                  (
                    (allModuleTotal.total -
                      (!!form.discountsPrice ? (form.discountsPrice * 1).toFixed(2) : 0) -
                      allModuleTotal.costTotal) /
                    (allModuleTotal.total -
                      (!!form.discountsPrice ? (form.discountsPrice * 1).toFixed(2) : 0))
                  ).toFixed(2)
                }}</span>
              </div>
            </div>
          </div>
        </el-card></el-col
      >
      <!-- <el-col :span="6">
          <el-card
            :body-style="{ padding: 0 }"
            style="
              height: 100%;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
              overflow-y: auto;
            "
          >
            <div v-if="form.currentModule > 0">
              <div style="font-size: 14px; font-weight: bolder">
                当前模块汇总:<el-text type="primary">{{
                  moduleDTOList.find((item, index) => index == form.currentModule).moduleName
                }}</el-text>
              </div>
              <div style="display: flex; gap: 10px">
                <div style="display: flex; flex-direction: column; justify-content: space-between">
                  <div style="color: #666">
                    设备金额：
                    <span style="color: #000">{{ currentModuleTotal.totalProductPrice }}</span>
                  </div>
                  <div style="color: #666">
                    人工金额：
                    <span style="color: #000">{{ currentModuleTotal.totalLaborCost }}</span>
                  </div>
                  <div style="color: #666">
                    其他金额： <span style="color: #000">{{ currentModuleTotal.totalQtPrice }}</span>
                  </div>
                  <div style="color: #666">
                    延保金额： <span style="color: #000">{{ currentModuleTotal.totalYbPrice }}</span>
                  </div>
                  <div style="color: #666">
                    报价金额：
                    <span style="color: #000">{{ currentModuleTotal.total }}</span>
                  </div>
                </div>
                <div style="display: flex; flex-direction: column; justify-content: space-between">
                  <div style="color: #666">
                    设备成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalCostPrice }}</span>
                  </div>
                  <div style="color: #666">
                    人工成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalRgcbPrice }}</span>
                  </div>
                  <div style="color: #666">
                    其他成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalQtcbPrice }}</span>
                  </div>
                  <div style="color: #666">
                    延保成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalYbcbPrice }}</span>
                  </div>
                  <div style="color: #666">
                    报价成本金额：
                    <span style="color: #000">{{ currentModuleTotal.costTotal }}</span>
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 10px;
                  "
                >
                  <div style="color: #666">
                    毛利润 ：<span>{{
                      (currentModuleTotal.total - currentModuleTotal.costTotal).toFixed(2)
                    }}</span>
                  </div>
  
                  <div style="color: #666">
                    毛利润率：
                    <span>{{
                      isNaN(
                        (
                          (currentModuleTotal.total - currentModuleTotal.costTotal) /
                          currentModuleTotal.total
                        ).toFixed(2)
                      )
                        ? '--'
                        : (
                            (currentModuleTotal.total - currentModuleTotal.costTotal) /
                            currentModuleTotal.total
                          ).toFixed(2)
                    }}</span>
                  </div>
                </div>
              </div>
            </div>
          </el-card></el-col
        > -->
      <el-col :span="8">
        <el-card
          :body-style="{ padding: 0 }"
          style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box; font-size: 14px"
        >
          <div style="font-size: 14px; font-weight: bolder">
            产品参考信息: <el-text type="primary">{{ referInfo.productName }}</el-text>
          </div>
          <div style="display: flex; gap: 10px">
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 10px;
              "
            >
              <div style="color: #666">
                最低销售价： <span style="color: #000">{{ referInfo.minSealPrice }}</span>
              </div>
              <div style="color: #666">
                最近设备销售价： <span style="color: #000">{{ referInfo.preSealPrice }}</span>
              </div>

              <div style="color: #666">
                参考销售价： <span style="color: #000">{{ referInfo.referSealPrice }}</span>
              </div>
              <!-- <div style="color: #666">
                最近人工销售价： <span style="color: #000">{{ `--` }}</span>
              </div>
              <div style="color: #666">
                最近其他销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->
            </div>
            <div
              style="
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                gap: 10px;
              "
            >
              <!-- <div style="color: #666">
                最近延保销售价： <span style="color: #000">{{ `--` }}</span>
              </div> -->
              <!-- <div style="color: #666">
                最低销售价： <span style="color: #000">{{ referInfo.minSealPrice }}</span>
              </div>
              <div style="color: #666">
                参考销售价： <span style="color: #000">{{ referInfo.referSealPrice }}</span>
              </div> -->
            </div>
          </div>
        </el-card></el-col
      >
      <el-col :span="7">
        <el-card
          :body-style="{ padding: 0 }"
          style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
        >
          <div
            style="
              font-size: 14px;
              font-weight: bolder;
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            快捷配置:
            <el-switch
              v-model="form.isLock"
              size="small"
              v-if="$route.query.type != 'detail'"
              active-text="锁定"
              inactive-text="解锁"
            />
            <div style="display: flex; align-items: center">
              <el-checkbox :label="isExpand ? '折叠行' : '展开行'" v-model="isExpand"></el-checkbox>
              <el-button type="primary" text size="small" @click="colmunDrawer = !colmunDrawer"
                >显示设置</el-button
              >
            </div>
          </div>
          <el-row>
            <el-form>
              <el-form-item style="margin: 0" label="设备利润比:">
                <el-col :span="12">
                  <el-popconfirm
                    hide-icon
                    @confirm="setPrice(form.productTax, 'sealPrice')"
                    title="修改将会重新计算所有产品设备单价，是否确认?"
                  >
                    <template #reference>
                      <el-input
                        size="small"
                        :disabled="form.isLock || $route.query.type == 'detail'"
                        v-model="form.productTax"
                        placeholder="请输入"
                      ></el-input>
                    </template>
                  </el-popconfirm>
                </el-col>
              </el-form-item>
              <el-form-item style="margin: 0" label="人工利润比:">
                <el-col :span="12">
                  <el-popconfirm
                    hide-icon
                    @confirm="setPrice(form.laborTax, 'laborPrice')"
                    title="修改将会重新计算所有产品人工单价，是否确认?"
                  >
                    <template #reference>
                      <el-input
                        size="small"
                        :disabled="form.isLock || $route.query.type == 'detail'"
                        v-model="form.laborTax"
                        placeholder="请输入"
                      ></el-input>
                    </template>
                  </el-popconfirm>
                </el-col>
              </el-form-item>
            </el-form>
          </el-row> </el-card
      ></el-col>
      <el-col :span="4">
        <el-card
          :body-style="{ padding: 0 }"
          style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
        >
          <div
            style="
              font-size: 14px;
              font-weight: bolder;
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div style="display: flex; align-items: center">
              导出报价单是否体现税金:
              <el-tooltip effect="dark" content="勾选后，导出的报价单中会体现税金" placement="top">
                <el-icon><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
            <el-switch
              style="margin-right: 10px"
              v-model="form.isHasTax"
              :active-value="1"
              :disabled="$route.query.type == 'detail'"
              inactive-value="0"
              size="small"
            />
          </div>
          <div style="display: flex; align-items: center; font-size: 14px">
            <div style="display: flex; align-items: center">
              设备税率
              <el-tooltip effect="dark" content="请填写小数，例如0.13，0.09" placement="top">
                <el-icon><QuestionFilled /></el-icon> </el-tooltip
              >：
            </div>
            <el-input
              size="small"
              style="width: 60%"
              :disabled="form.isHasTax == 0 || $route.query.type == 'detail'"
              v-model="form.productRate"
              append="%"
              placeholder="请输入"
            >
            </el-input>
          </div>
          <!-- <div style="display: flex; align-items: center; font-size: 14px">
            人工税率：
            <el-input
              size="small"
              style="width: 60%"
              :disabled="!form.isHasTax || $route.query.type == 'detail'"
              v-model="form.labourRate"
              placeholder="请输入"
            ></el-input>
          </div>
          <div style="display: flex; align-items: center; font-size: 14px">
            延保税率：
            <el-input
              size="small"
              style="width: 60%"
              :disabled="!form.isHasTax || $route.query.type == 'detail'"
              v-model="form.warrantyRate"
              placeholder="请输入"
            ></el-input>
          </div>
          <div style="display: flex; align-items: center; font-size: 14px">
            其他税率：
            <el-input
              size="small"
              style="width: 60%"
              :disabled="!form.isHasTax || $route.query.type == 'detail'"
              v-model="form.otherRate"
              placeholder="请输入"
            ></el-input>
          </div> -->
        </el-card>
      </el-col>
    </el-row>
    <!-- <el-row style="height: 70px">
        <el-col :span="24">
          <el-card :body-style="{ padding: '10px' }">
            <el-radio-group
              @change="setTableData"
              style="margin-right: 10px"
              v-model="form.currentModule"
              size="large"
              ref="sort-buttons"
            >
              <el-radio-button v-for="(item, index) in moduleDTOList" :key="item.id" :label="index">{{
                item.moduleName
              }}</el-radio-button>
              <el-button
                type=""
                size="large"
                v-if="$route.query.type != 'detail'"
                @click="addModule"
                style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="plus"
              ></el-button>
            </el-radio-group>
          </el-card>
        </el-col>
      </el-row> -->
    <el-row :gutter="10" style="height: calc(100% - 60px)">
      <!-- <el-col :span="form.currentModule == 0 ? 0 : 3">
          <el-card style="height: 100%">
            <div ref="category_box">
              <el-tag effect='plain'
                plain
                size="large"
                @click="handleScroll(index)"
                style="width: 100%; margin-bottom: 5px"
                v-for="(item, index) in categoryList"
                >{{ item }}</el-tag
              >
            </div>
            <el-button type="" v-if="$route.query.type != 'detail'" style="width: 100%" @click="addCategory" icon="plus"></el-button>
          </el-card>
        </el-col> -->
      <el-col :span="24" style="height: 100%">
        <el-card
          ref="tableCard"
          class="myCard"
          :body-style="{ padding: '5px' }"
          style="height: 100%; overflow: scroll"
        >
          <!-- <el-button type="primary" text @click="currentIndex = null" v-if="currentIndex"
            >退出编辑模式</el-button
          > -->
          <productSelectDrop
            v-if="$route.query.type != 'detail'"
            @select="handleProductSelectConfirm"
            style="margin-left: 5px"
          ></productSelectDrop>
          <table
            style="
              text-align: center;
              width: 100%;
              min-width: 100%;
              position: relative;
              margin-top: 5px;
            "
            border="1"
          >
            <colgroup>
              <col width="70px" />
              <col width="200px" />
              <col width="200px" />
              <col width="200px" />
              <col width="120px" />
              <col width="80px" />
              <col width="80px" />
              <col
                v-for="item in columnHideData?.filter(item => item.isShow)"
                :key="item.value"
                :width="`${item.width}px`"
              />
            </colgroup>
            <thead style="position: sticky; top: -1px; z-index: 100">
              <tr style="font-weight: bolder; color: black; background-color: #fff">
                <td>序号</td>
                <td>产品名称</td>
                <td>规格型号</td>
                <td>产品描述</td>
                <td class="center">品牌</td>
                <td class="center">单位</td>
                <td class="center">数量</td>
                <td class="center" v-for="item in columnHideData?.filter(item => item.isShow)">
                  {{ item.value }}
                </td>
              </tr>
            </thead>

            <tbody ref="tableBody">
              <tr
                class="cell_hover"
                @click="setReferPrice(i)"
                v-for="(i, ins) in moduleDTOList[0].detailDTOList"
                :key="i.uuid || i.id"
              >
                <td class="index_product">
                  <div style="display: flex;justify-content: space-around;align-items: center;">
                     <span class="index_product_span">{{ customIndex(ins, index) }}</span>
                  <span class="index_product_span source">{{
                    ['库', '模', '询', '空'][i.source]
                  }}</span>
                  <el-icon
                    @click="deleteProduct(i)"
                    class="delete_product"
                    type="danger"
                    v-if="$route.query.type != 'detail'"
                   title="删除"
                  >
                  <delete></delete>
                </el-icon>
                  <el-icon
                    v-if="$route.query.type != 'detail'"
                    style="cursor: move"
                    class="sort delete_product"
                    title="排序"
                  >
                    <sort></sort>
                  </el-icon>
                  <el-icon
                    style="cursor: pointer"
                    @click.capture="copyProduct(i)"
                    title="复制产品"
                    v-if="$route.query.type != 'detail'"
                    class="sort delete_product"
                  >
                    <copyDocument></copyDocument>
                  </el-icon>
                  <el-icon
                    style="cursor: pointer"
                    @click.capture="addInquiry(i)"
                    title="添加至询价单"
                    v-if="$route.query.type != 'detail'"
                    class="sort delete_product"
                  >
                    <document></document>>
                  </el-icon>
                  </div>
                </td>
                <!-- 名称 -->
                <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.customProductName"
                    type="textarea"
                    placeholder=""
                  ></el-input>
                  <div
                    style="white-space: wrap; overflow: auto"
                    @focus="i.height = 100"
                    :style="{
                      height: i.height + 'px',
                      lineHeight: i.height + 'px',
                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    spellcheck="false"
                    @blur="
                      e => {
                        i.height = 30;
                        i.customProductName = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.customProductName }}
                  </div>
                </td>
                <!-- 型号 -->
                <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.customProductSpecification"
                    type="textarea"
                    placeholder=""
                  ></el-input>
                  <div
                    style="white-space: wrap; overflow: auto"
                    @focus="i.height = 100"
                    @click="i.height = 100"
                    spellcheck="false"
                    :style="{
                      height: i.height + 'px',

                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    @blur="
                      e => {
                        i.height = 30;
                        i.customProductSpecification = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.customProductSpecification }}
                  </div>
                </td>
                <!-- 描述 -->
                <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.customProductDescription"
                    type="textarea"
                    placeholder=""
                  ></el-input>
                  <div
                    style="white-space: nowrap; overflow: auto"
                    @focus="i.height = 100"
                    @click="i.height = 100"
                    spellcheck="false"
                    :style="{
                      height: i.height + 'px',
                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    @blur="
                      e => {
                        i.height = 30;
                        i.customProductDescription = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.customProductDescription }}
                  </div>
                </td>
                <!-- 品牌 -->
                <td class="center">
                  <div
                    style="white-space: nowrap; overflow: auto"
                    :style="{
                      height: i.height + 'px',
                      lineHeight: i.height + 'px',
                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    spellcheck="false"
                    @blur="
                      e => {
                        i.height = 30;
                        i.productBrand = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                  >
                    {{ i.productBrand }}
                  </div>
                </td>
                <!-- 单位 -->
                <td class="center">
                  <div
                    style="white-space: nowrap; overflow: auto"
                    spellcheck="false"
                    :style="{
                      height: i.height + 'px',
                      lineHeight: i.height + 'px',
                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    @blur="
                      e => {
                        i.height = 30;
                        i.customUnit = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                  >
                    {{ i.customUnit }}
                  </div>
                </td>
                <!-- 数量 -->
                <td
                  class="center"
                  :class="{ active: currentIndex === item, error: i.number <= 0 }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-tooltip
                    content="数量不能小于等于0"
                    :disabled="!(i.number <= 0)"
                    placement="left"
                    effect="light"
                  >
                    <div
                      @blur="
                        e => {
                          i.number = e.target.innerText;
                        }
                      "
                      spellcheck="false"
                      :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    >
                      {{ i.number }}
                    </div>
                  </el-tooltip>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 设备单价 -->
                <td
                  class="center"
                  v-if="isTrue(props.repairId ? '维修单价' : '设备单价')"
                  :class="{
                    active: currentIndex === item,
                    error: i.sealPrice == '' || i.sealPrice * 1 < i.minSealPrice * 1,
                  }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-tooltip
                    :content="
                      i.sealPrice == ''
                        ? '单价不能为空'
                        : i.sealPrice * 1 < i.minSealPrice * 1
                        ? '单价不能小于最低销售单价'
                        : ''
                    "
                    :disabled="!(i.sealPrice == '' || i.sealPrice * 1 < i.minSealPrice * 1)"
                    placement="right"
                    effect="light"
                  >
                    <div
                      @blur="
                        e => {
                          i.sealPrice = e.target.innerText;
                        }
                      "
                      spellcheck="false"
                      :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    >
                      {{ i.sealPrice }}
                    </div>
                  </el-tooltip>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 设备金额 -->
                <td class="center" v-if="isTrue(props.repairId ? '维修金额' : '设备金额')">
                  {{ (i.sealPrice * i.number).toFixed(2) }}
                </td>
                <!-- 人工单价 -->
                <td
                  class="center"
                  v-if="isTrue('人工单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.laborCost"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.laborCost = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.laborCost }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 人工金额 -->
                <td class="center" v-if="isTrue('人工金额')">
                  {{ (i.laborCost * i.number).toFixed(2) }}
                </td>
                <!-- 延保单价 -->
                <td
                  class="center"
                  v-if="isTrue('延保单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.ybhsdj"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.ybhsdj = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.ybhsdj }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 延保金额 -->
                <td class="center" v-if="isTrue('延保金额')">
                  {{ (i.ybhsdj * i.number).toFixed(2) }}
                </td>
                <!-- 其他单价 -->
                <td
                  class="center"
                  v-if="isTrue('其他单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <!-- <el-input
                    @blur="handleBlur($event, item)"
                    v-if="currentIndex == item"
                    v-model="i.qthsdj"
                    style="width: 100%"
                  ></el-input> -->
                  <div
                    @blur="
                      e => {
                        i.qthsdj = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                  >
                    {{ i.qthsdj }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 其他金额 -->
                <td class="center" v-if="isTrue('其他金额')">
                  {{ (i.qthsdj * i.number).toFixed(2) }}
                </td>

                <!-- 设备成本单价 -->
                <td
                  class="center"
                  v-if="isTrue(props.repairId ? '维修成本单价' : '设备成本单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.costPrice"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.costPrice = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.costPrice }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 设备成本金额 -->
                <td class="center" v-if="isTrue(props.repairId ? '维修成本金额' : '设备成本金额')">
                  {{ (i.costPrice * i.number).toFixed(2) }}
                </td>
                <!-- 人工成本单价 -->
                <td
                  class="center"
                  v-if="isTrue('人工成本单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.rgcbdj"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.rgcbdj = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.rgcbdj }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 人工成本金额 -->
                <td class="center" v-if="isTrue('人工成本金额')">
                  {{ (i.rgcbdj * i.number).toFixed(2) }}
                </td>
                <!-- 延保成本单价 -->
                <td
                  class="center"
                  v-if="isTrue('延保成本单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.ybcbdj"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.ybcbdj = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.ybcbdj }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 延保成本金额 -->
                <td class="center" v-if="isTrue('延保成本金额')">
                  {{ (i.ybcbdj * i.number).toFixed(2) }}
                </td>
                <!-- 其他成本单价 -->
                <td
                  class="center"
                  v-if="isTrue('其他成本单价')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                  style="position: relative"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.qtcbdj"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.qtcbdj = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.qtcbdj }}
                  </div>
                  <!-- 一 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      top: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 二 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      right: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <!-- 四 -->
                  <div
                    style="
                      height: 10px;
                      border-left: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                  <div
                    style="
                      width: 10px;
                      border-top: 1px solid red;
                      position: absolute;
                      bottom: 0;
                      left: 0px;
                      z-index: 10;
                    "
                  ></div>
                </td>
                <!-- 其他成本金额 -->
                <td class="center" v-if="isTrue('其他成本金额')">
                  {{ (i.qtcbdj * i.number).toFixed(2) }}
                </td>
                <!-- 备注 -->
                <td
                  v-if="isTrue('备注')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.remark"
                    type="textarea"
                    placeholder=""
                  ></el-input>
                  <div
                    style="white-space: wrap; overflow: auto"
                    @focus="i.height = 100"
                    :style="{
                      height: i.height + 'px',
                      'white-space': i.height == 30 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 30 || !i.height ? 'hidden' : 'auto',
                    }"
                    @blur="
                      e => {
                        i.height = 30;
                        i.remark = e.target.innerText;
                      }
                    "
                    spellcheck="false"
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.remark }}
                  </div>
                </td>
                <!-- 专项成本 -->
                <td
                  class="center"
                  v-if="isTrue('专项成本')"
                  :class="{ active: currentIndex === item }"
                  @click="handleRowClick(item)"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="false"
                    v-model="i.specialCostPrice"
                    style="width: 100%"
                  ></el-input>
                  <div
                    @blur="
                      e => {
                        i.specialCostPrice = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    v-else
                  >
                    {{ i.specialCostPrice }}
                  </div>
                </td>
                <!-- 专项供应商 -->
                <td class="center" v-if="isTrue('专项供应商')">
                  <WfSupplierSelect
                    v-model="i.specialSupplierId"
                    placeholder="请选择专项供应商"
                    style="width: 100%"
                  ></WfSupplierSelect>
                </td>
              </tr>
              <tr>
                <td colspan="24">
                  <el-button
                    type="primary"
                    icon="plus"
                    title="从产品库选择产品"
                    v-if="$route.query.type != 'detail'"
                    @click="$refs['product-select'].visible = true"
                    plain
                    size="default"
                    style="margin: 2px 0; margin-left: 3px"
                    >库</el-button
                  >
                  <el-button
                    type="primary"
                    icon="Plus"
                    title="添加一个空产品"
                    v-if="$route.query.type != 'detail'"
                    @click="addEmptyProduct()"
                    plain
                    size="default"
                    >空</el-button
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </el-card></el-col
      >
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
    >
    </wf-product-select>
    <el-drawer title="列显隐" size="20%" v-model="colmunDrawer">
      <el-table border :data="columnHideData">
        <el-table-column label="列名称" prop="value"></el-table-column>
        <el-table-column label="隐藏/显示">
          <template #default="scope">
            <el-switch v-model="scope.row.isShow" />
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import productSelectDrop from '../../quotation/compoents/productSelectDrop.vue';
const { proxy } = getCurrentInstance();
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
let currentIndex = ref(null);

const route = useRoute;
let form = ref({
  currentModule: 0,
  currentclassify: null,
});
let moduleDTOList = ref([{ moduleName: '默认子项', id: null, detailDTOList: [] }]); // 所有数据的操作都会存在这里
const props = defineProps({
  data: {
    type: Object,
    default: () => ({}),
  },
  repairId: String,
});
watchEffect(() => {
  if (props.data) {
    console.log(props.data);
    form.value = props.data;
    moduleDTOList.value[0].detailDTOList = form.value.detailList.map(item => {
      return {
        ...item,
        height: 100,
      };
    });
  }
  if (props.data.columnHideData) {
    columnHideData.value = props.data.columnHideData;
  }
});

// const isDetail = route.query.type == 'detail'
//   const categoryList = computed(() => {
//     let arr = [];

//     if (form.value.currentModule == 0 || !form.value.currentModule) return [];
//     moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
//       if (arr.includes(item)) return;
//       arr.push(item);
//     });
//     return arr;
//   });

// 当前模块汇总
//   const currentModuleTotal = computed(() => {
//     if (form.value.currentModule == 0 || !form.value.currentModule) return {};
//     const list = moduleDTOList.value[form.value.currentModule].detailDTOList.filter(
//       item => item.detailType == 0
//     );
//     const {
//       totalProductPrice,
//       totalLaborCost,
//       totalYbPrice,
//       totalQtPrice,
//       totalRgcbPrice,
//       totalCostPrice,
//       totalQtcbPrice,
//       totalYbcbPrice,
//     } = {
//       totalProductPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.sealPrice;
//         }, 0)
//         .toFixed(2),
//       totalRgcbPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.rgcbdj;
//         }, 0)
//         .toFixed(2),
//       totalCostPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
//         }, 0)
//         .toFixed(2),
//       totalLaborCost: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.laborCost;
//         }, 0)
//         .toFixed(2),
//       totalYbPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.ybhsdj;
//         }, 0)
//         .toFixed(2),
//       totalQtPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.qthsdj;
//         }, 0)
//         .toFixed(2),
//       totalYbcbPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.ybcbdj;
//         }, 0)
//         .toFixed(2),
//       totalQtcbPrice: list
//         .reduce((pre, cur) => {
//           return pre + cur.number * cur.qtcbdj;
//         }, 0)
//         .toFixed(2),
//     };
//     return {
//       // 设备金额
//       totalProductPrice,
//       // 人工金额
//       totalLaborCost,
//       // 延保金额
//       totalYbPrice,
//       // 其他金额
//       totalQtPrice,
//       // 人工成本
//       totalRgcbPrice,
//       // 设备成本
//       totalCostPrice,
//       // 延保成本
//       totalYbcbPrice,
//       // 其他成本
//       totalQtcbPrice,
//       // 报价合计
//       total: totalProductPrice * 1 + totalLaborCost * 1 + totalYbPrice * 1 + totalQtPrice * 1,
//       //成本合计
//       costTotal: totalRgcbPrice * 1 + totalCostPrice * 1 + totalYbcbPrice * 1 + totalQtcbPrice * 1,
//     };
//   });
// 所有模块汇总
const allModuleTotal = computed(() => {
  const list = moduleDTOList.value[0].detailDTOList;

  const {
    totalProductPrice,
    totalLaborCost,
    totalYbPrice,
    totalQtPrice,
    totalRgcbPrice,
    totalCostPrice,
    totalQtcbPrice,
    totalYbcbPrice,
  } = {
    totalProductPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0)
      .toFixed(2),
    totalRgcbPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.rgcbdj;
      }, 0)
      .toFixed(2),
    totalCostPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
      }, 0)
      .toFixed(2),
    totalLaborCost: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.laborCost;
      }, 0)
      .toFixed(2),
    totalYbPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.ybhsdj;
      }, 0)
      .toFixed(2),
    totalQtPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.qthsdj;
      }, 0)
      .toFixed(2),
    totalYbcbPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.ybcbdj;
      }, 0)
      .toFixed(2),
    totalQtcbPrice: list
      .reduce((pre, cur) => {
        return pre + cur.number * cur.qtcbdj;
      }, 0)
      .toFixed(2),
  };
  return {
    // 报价合计
    total: totalProductPrice * 1 + totalLaborCost * 1 + totalYbPrice * 1 + totalQtPrice * 1,
    //成本合计
    costTotal: totalRgcbPrice * 1 + totalCostPrice * 1 + totalYbcbPrice * 1 + totalQtcbPrice * 1,
  };
});
function handleBlur(value, item) {
  console.log(value, item);
  //   currentIndex.value = null;
}
let tableData = ref([]); //表格临时数据

function setTableData() {
  if (form.value.currentModule == 0) {
    tableData.value = [];
    return;
  }
  setCategotyAndProductDrag();
  const currentData = moduleDTOList.value[form.value.currentModule];
  tableData.value = currentData.detailDTOList;
}

function addModule() {
  proxy.$refs.dialogForm.show({
    title: '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '模块名称',
          span: 24,
          prop: 'moduleName',
          rules: [
            {
              required: true,
              message: '请输入模块名称',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      moduleDTOList.value.push({
        moduleName: res.data.moduleName,

        remark: res.remark,
        detailDTOList: [],
      });
      res.close();
    },
  });
}
// 添加分类
function addCategory() {
  const currentData = moduleDTOList.value[form.value.currentModule];
  proxy.$refs.dialogForm.show({
    title: '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          rules: [
            {
              required: true,
              message: '请输入分类名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      currentData.detailDTOList.push({
        classify: res.data.classify,
        detailType: 1,
      });
      setTableData();
      res.close();
    },
  });
}
async function handleProductSelectConfirm(ids) {
  const res = await axios.get('/api/vt-admin/product/detailByIds', {
    params: {
      idList: ids,
    },
  });

  const data = res.data.data
    .map(item => {
      return {
        customProductName: item.productName,
        customProductSpecification: item.productSpecification,
        customProductDescription: item.description,
        customUnit: item.unitName,
        productId: item.id,
        classify: form.value.currentclassify,
        ...item,
        id: null,
        detailType: 0,
        number: 1,
        source: 0,
        rgcbdj: '',
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        uuid: randomLenNum(10),
      };
    })
    .reverse();

  moduleDTOList.value[0].detailDTOList.push(...data);
  setTableData();
}

// 列显示隐藏
let colmunDrawer = ref(false);
let columnHideData = ref([
  // {
  //   value :'产品名称',
  //   isShow:true,
  // },
  // {
  //   value :'品牌',
  //   isShow:true,
  // },
  // {
  //   value :'规格型号',
  //   isShow:true,
  // },
  // {
  //   value :'详细描述',
  //   isShow:true,
  // },
  // {
  //   value :'产品图片',
  //   isShow:true,
  // },
  // {
  //   value :'单位',
  //   isShow:true,
  // },
  // {
  //   value :'数量',
  //   isShow:true,
  // },
  {
    value: props.repairId ? '维修单价' : '设备单价',
    isShow: true,
    width: 120,
  },

  {
    value: props.repairId ? '维修金额' : '设备金额',
    isShow: true,
    width: 120,
  },

  {
    value: '人工单价',
    isShow: false,
    width: 120,
  },

  {
    value: '人工金额',
    isShow: false,
    width: 120,
  },

  {
    value: '其他单价',
    isShow: false,
    width: 120,
  },

  {
    value: '其他金额',
    isShow: false,
    width: 120,
  },

  {
    value: '延保单价',
    isShow: false,
    width: 120,
  },

  {
    value: '延保金额',
    isShow: false,
    width: 120,
  },

  {
    value: props.repairId ? '维修成本单价' : '设备成本单价',
    isShow: true,
    width: 100,
  },
  {
    value: props.repairId ? '维修成本金额' : '设备成本金额',
    isShow: true,
    width: 100,
  },
  {
    value: '人工成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '人工成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '其他成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '其他成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '备注',
    isShow: true,
    width: 330,
  },

  {
    value: '专项成本',
    isShow: false,
    width: 100,
  },
  {
    value: '专项供应商',
    isShow: false,
    width: 100,
  },
]);
const tableWidth = computed(() => {
  const value = columnHideData.value
    .filter(item => item.isShow)
    .reduce((pre, cur) => {
      return pre + cur.width;
    }, 0);
  return `${value}px`;
});

function isTrue(value) {
  return columnHideData.value.find(item => item.value == value)?.isShow;
}

// 设置参考信息
let referInfo = ref({});
function setReferPrice(item) {
  referInfo.value = {
    productName: item.customProductName,
    preSealPrice: item.preSealPrice,
    minSealPrice: item.minSealPrice,
    referSealPrice: item.referSealPrice,
  };
}

// 根据利润比计算价格
function setPrice(value, type) {
  moduleDTOList.value.forEach(cur => {
    if (!cur.detailDTOList) return;

    cur.detailDTOList.forEach(cur => {
      if (cur.detailType == 0) {
        cur[type == 'sealPrice' ? 'sealPrice' : 'laborCost'] = (
          (cur[type == 'sealPrice' ? 'costPrice' : 'rgcbdj'] * 1) /
          (1 - value)
        ).toFixed(2);
      }
    });
  });
  form.value.isLock = true;
}
onMounted(() => {
  setSort();
});
// 拖拽排序
const setSort = () => {
  const el = proxy.$refs['tableBody'];
  console.log(proxy.$refs.tableBody);
  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log(evt);

      const targetRow = moduleDTOList.value[0].detailDTOList.splice(evt.oldIndex, 1);
      moduleDTOList.value[0].detailDTOList.splice(evt.newIndex, 0, targetRow[0]);
      // setRandomKey()
      // setSort()
    },
  });
};
let randomKey = ref(null);
function setRandomKey() {
  randomKey.value = randomLenNum(10);
}
function setCategotyAndProductDrag(params) {
  // 设置分类拖拽
  const el = proxy.$refs.category_box;

  new Sortable(el, {
    // handle: '.move1',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log(evt);
    },
  });
}

// 自定义序号
function customIndex(index, parentIndex) {
  let i = 0;
  if (parentIndex == 0) {
    i = index + 1;
  } else {
    let sum = 0;
    for (let i = 0; i < parentIndex; i++) {
      sum += tableData.value[i].productList.filter(item => item.detailType == 0).length;
    }
    // 返回序号
    i = sum + index + 1;
  }

  return i;
}

function deleteProduct(i) {
  if (i.id) {
    const index = moduleDTOList.value[0].detailDTOList.findIndex(item => item.id == i.id);
    moduleDTOList.value[0].detailDTOList.splice(index, 1);
  } else {
    const index = moduleDTOList.value[0].detailDTOList.findIndex(item => item.uuid == i.uuid);
    moduleDTOList.value[0].detailDTOList.splice(index, 1);
  }
  setTableData();
}
function copyProduct(i) {
  moduleDTOList.value[0].detailDTOList.push({ ...i, uuid: randomLenNum(10), id: null });
}
function getData() {
  console.log(moduleDTOList.value);
  return {
    ...form.value,
    moduleDTOList: moduleDTOList.value[0],
    columnHideData: columnHideData.value,
  };
}
function setData({ id, key, value, data }) {
  if (data) {
    moduleDTOList.value[0].detailDTOList.push({
      ...data,
      productId: null,
    });
  } else {
    const item = moduleDTOList.value[0].detailDTOList.find(item => item.id == id);
    if (item) {
      item[key] = value;
    }
  }
}
function handleRowClick(item) {
  if (proxy.$route.query.type == 'detail') return;
  currentIndex.value = true;
}
function addEmptyProduct(item) {
  const data = {
    detailType: 0,
    classify: form.value.currentclassify,
    id: null,
    detailType: 0,
    source: 3,
    sealPrice: '',
    rgcbdj: '',
    ybcbdj: '',
    qtcbdj: '',
    ybhsdj: '',
    qthsdj: '',
    sealPrice: '',
    laborCost: '',
    remark: '',
    uuid: randomLenNum(10),
  };
  moduleDTOList.value[0].detailDTOList.push(data);
  setTableData();
}

function editDiscountsPrice() {
  proxy.$refs.dialogForm.show({
    title: '报价折减',
    width: '30%',
    option: {
      column: [
        {
          label: '折减金额',
          prop: 'discountsPrice',
          type: 'number',
          span: 24,
          value: form.value.discountsPrice || 0,
          rules: [
            {
              required: true,
              message: '请输入折减金额',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      form.value.discountsPrice = res.data.discountsPrice;
      res.close();
    },
  });
}
let isExpand = ref(true);
watch(
  () => isExpand.value,
  val => {
    moduleDTOList.value[0].detailDTOList.forEach(item => {
      item.height = val ? 100 : 30;
    });
  }
);
const emits = defineEmits(['addInquiry']);

function addInquiry(row) {
  emits('addInquiry', row);
}
defineExpose({
  getData,
  setData,
});
</script>

<style lang="scss" scoped>
.wrap {
  height: calc(100% - 20px);
  width: calc(100%);
  //   background: #fff;
  box-sizing: border-box;
  padding: 15px;
  margin-bottom: 0;
  border-radius: 5px;
  .barbox {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .box {
    height: calc(100% - 60px);
    width: 100%;
  }
}
table {
  width: 100%;
  border-collapse: collapse;
  //   margin: 25px 0;
  //   height: 100%;
  font-size: 0.9em;
  min-width: 400px;
  color: #111;
  border-radius: 5px 5px 0 0;
  border-color: #ccc;
  table-layout: fixed;
  // overflow: hidden;
  // box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}
th,
td {
  //   padding: 5px;
  text-align: left;
  height: 25px;
  white-space: nowrap;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #dddddd;
  // border-bottom: 1px solid #dddddd;
}
.active {
  white-space: wrap;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
  border-bottom: 1px solid #dddddd;
}
thead {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
  // border-bottom: 1px solid #dddddd;
  td {
    // word-break: unset;
    white-space: unset;
  }
}
th {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
}
tr {
  // background-color: #f3f3f3;
  height: 25px;
}
.category {
  background-color: var(--el-color-warning-light-8);
}
.cell_hover:hover {
  background-color: var(--el-color-info-light-8);
}

.index_product {
  &:hover .delete_product {
    display: inline-block;
  }
  &:hover .index_product_span {
    display: none;
  }
  .delete_product {
    display: none;
  }
  .source {
    display: inline-block;
    height: 20px;
    width: 20px;
    text-align: center;
    line-height: 20px;
    border-radius: 15px;
    border: 1px solid var(--el-color-success);
    color: var(--el-color-success);
  }
}
.center {
  text-align: center;
}
.error {
  background-color: var(--el-color-danger-light-8);
}
</style>
