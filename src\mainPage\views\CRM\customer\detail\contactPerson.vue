<template>
  <div>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
     
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      :table-loading="loading"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #labels="{ row }">
        <div v-if="!row.$labels">-</div>
        <div v-else>
          <el-tag effect='plain' v-for="item in row.$labels.split('|')">{{ item }}</el-tag>
        </div>
      </template>
    </avue-crud>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
let route = useRoute();
const props = defineProps(['type', 'id']);
let option = ref({
  labelWidth: 120,
  addBtn : props.type == 0 || props.type == 2,
  editBtn : props.type == 0 || props.type == 2,
  delBtn : props.type == 0 || props.type == 2,
  border:true,
 
  column: [
    {
      label: '姓名',
      prop: 'name',
    },
    {
      label: '性别',
      prop: 'sex',
      type: 'select',
      dicData: [
        {
          value: 1,
          label: '男',
        },
        {
          value: 2,
          label: '女',
        },
      ],
    },
    {
      label: '部门',
      prop: 'dept',
      type:'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=dept',
    },
    {
      label: '职务',
      prop: 'post',
      type:'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=position',
    },
    {
      label: '手机',
      prop: 'phone',
      type:'number'
    },
    {
      label: '在职状态',
      prop: 'jobStatus',
      type: 'switch',
      value: 1,
      dicData: [
        {
          label: '离职',
          value: 0,
        },
        {
          label: '在职',
          value: 1,
        },
      ],
    },
    {
      label: '固定电话',
      prop: 'fixedTelephone',
    },
    {
      type: 'select',
      label: '联系人标签',
      multiple: true,
      span: 12,
      dataType: 'string',
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
      },
      width: 300,
      prop: 'labels',
      dicUrl: '/blade-system/dict-biz/dictionary?code=contact_person_tag',
      remote: false,
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let loading = ref(false);
watchEffect(() => {
  if (props.id) {
   
    onLoad();
  }
},{
  flush: 'post'
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/customerContact/page', {
      params: {
        customerId: props.id,
        size,
        current,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
function rowSave(form, done, loading) {
 
  const data = {
    ...form,
    customerId: props.id,
  };
  axios
    .post('/api/vt-admin/customerContact/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    // labels:row.labels.join(','),
  };
  axios
    .post('/api/vt-admin/customerContact/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
   
      axios.post('/api/vt-admin/customerContact/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
