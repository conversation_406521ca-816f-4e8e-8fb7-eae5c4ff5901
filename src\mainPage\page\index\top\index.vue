<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="setting.collapse && !isHorizontal"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <div class="top-bar__title">
      <top-menu ref="topMenu" v-if="setting.menu"></top-menu>
      <top-search class="top-bar__item" v-if="setting.search"></top-search>
    </div>
    <div class="top-bar__right">
      <div class="top-bar__item">
        <top-ai></top-ai>
      </div>
      <div class="top-bar__item">
        <topWx_mini v-if="$store.getters.permission['show_portal_app']" type="1"></topWx_mini>
      </div>
      <div class="top-bar__item">
        <!-- v-if="$store.getters.permission['show_portal_app']"  -->
        <topWx_mini type="0"></topWx_mini>
      </div>
      <div v-if="setting.lock" class="top-bar__item">
        <top-lock></top-lock>
      </div>
      <div v-if="setting.theme" class="top-bar__item">
        <top-theme></top-theme>
      </div>
      <!-- <div class="top-bar__item">
        <top-lang></top-lang>
      </div> -->
      <div class="top-bar__item">
        <top-notice></top-notice>
      </div>
      <div class="top-bar__item" v-if="setting.fullscren">
        <top-full></top-full>
      </div>
      <div class="top-bar__item" v-if="setting.debug">
        <top-logs></top-logs>
      </div>
      <div class="top-user">
        <img class="top-bar__img" :src="userInfo.avatar" />
        <el-dropdown>
          <span class="el-dropdown-link">
            {{ userInfo.userName }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <router-link to="/">{{ $t('navbar.dashboard') }}</router-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <router-link to="/info/index">{{ $t('navbar.userinfo') }}</router-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <router-link
                  to="/info/companyIndex"
                  v-if="
                    userInfo.role_name.indexOf('admin') > -1 ||
                    userInfo.role_name.indexOf('manager') > -1
                  "
                >
                  系统设置
                </router-link>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  $store.getters.userInfo.tenant_id == '806174' &&
                  (userInfo.role_name.indexOf('admin') > -1 ||
                    userInfo.role_name.indexOf('manager') > -1)
                "
              >
                <span @click="handleTopSupport()"> 平台支持 </span>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  userInfo.role_name.indexOf('admin') > -1 ||
                  userInfo.role_name.indexOf('manager') > -1
                "
                @click="swithUser"
                >{{ '切换用户' }}
              </el-dropdown-item>
              <el-dropdown-item @click="logout">{{ $t('navbar.logOut') }} </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <top-setting></top-setting> -->
      </div>
    </div>
    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :check-type="'radio'"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
    <!-- 智能体助手弹窗 -->
    <el-drawer
      v-model="aiAssistantVisible"
      title="智能体助手"
      direction="rtl"
      size="60%"
      :with-header="true"
    >
      <iframe
        src="http://localhost/chatbot/VzJzY0VgndHZ5Gl0?auth_token=2323232323"
        style="width: 100%; height: 100%; min-height: 700px"
        frameborder="0"
        allow="microphone"
      >
      </iframe>
    </el-drawer>
  </div>
  <!-- 悬浮智能体助手按钮 -->
  <!-- <div class="floating-ai-assistant">
    <el-tooltip content="智能体助手" placement="left" effect="light">
      <div class="floating-ai-btn" @click="toggleAiAssistant">
        <el-icon class="ai-icon"><Service /></el-icon>
      </div>
    </el-tooltip>
  </div> -->
</template>
<script>
import { mapGetters } from 'vuex';
import topLock from './top-lock.vue';
import topMenu from './top-menu.vue';
import topSearch from './top-search.vue';
import topTheme from './top-theme.vue';
import topLogs from './top-logs.vue';
import topLang from './top-lang.vue';
import topFull from './top-full.vue';
import topWx_mini from './top-wx_mini.vue';
import topAi from './top-ai.vue';
import topSetting from '../setting.vue';
import topNotice from './top-notice.vue';
import { ElNotification } from 'element-plus';
import nfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import { getUser } from '@/api/system/user';
import { getList } from '@/api/system/param';
import { Service, Promotion } from '@element-plus/icons-vue';

export default {
  name: 'top',
  components: {
    topLock,
    topAi,
    topMenu,
    topSearch,
    topTheme,
    topLogs,
    topLang,
    topFull,
    topSetting,
    topNotice,
    nfUserSelect,
    topWx_mini,
    Service,
    Promotion,
  },

  data() {
    return {
      show_portal_app: '',
      aiAssistantVisible: false,
      currentMessage: '',
      messages: [],
      isSending: false,
    };
  },
  filters: {},
  created() {
    getList(1, 10, {
      paramKey: 'show_portal_app',
    }).then(res => {
      this.show_portal_app = res.data.data.records[0].paramValue;
    });
  },
  computed: {
    ...mapGetters([
      'setting',
      'userInfo',
      'tagWel',
      'tagList',
      'isCollapse',
      'tag',
      'logsLen',
      'logsFlag',
      'isHorizontal',
    ]),
  },
  methods: {
    setCollapse() {
      this.$store.commit('SET_COLLAPSE');
    },
    logout() {
      this.$confirm(this.$t('logoutTip'), this.$t('tip'), {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
        type: 'warning',
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push({ path: '/login' });
          setTimeout(() => {
            ElNotification.closeAll();
          }, 0);
        });
      });
    },
    handleUserSelectConfirm(user) {
      getUser(user).then(res => {
        const loading = this.$loading({
          lock: true,
          text: '登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('SwitchUser', res.data.data)
          .then(res => {
            this.$router.push({ path: `/redirect/home` });
            this.$store.commit('DEL_TAG', { fullPath: '/redirect/home' });
            this.$store.dispatch('GetMenu', '').then(data => {
              if (data.length !== 0) {
                this.$router.$avueRouter.formatRoutes(data, true);

                setTimeout(() => {
                  loading.close();
                  this.$store.dispatch('getMessageList', true);
                }, 1000);
              }
            });
          })
          .catch(err => {
            loading.close();
          });
      });
    },
    swithUser() {
      this.$refs['user-select'].visible = true;
    },
    toggleAiAssistant() {
      this.aiAssistantVisible = !this.aiAssistantVisible;
    },
    sendMessage() {
      if (!this.currentMessage.trim()) return;

      const userMessage = {
        content: this.currentMessage,
        type: 'user',
        time: new Date().toLocaleTimeString(),
      };

      this.messages.push(userMessage);
      this.isSending = true;

      // 模拟AI回复
      setTimeout(() => {
        const aiMessage = {
          content: `我理解您的问题是："${this.currentMessage}"。这是一个模拟回复，在实际应用中这里会连接到真实的AI服务。`,
          type: 'ai',
          time: new Date().toLocaleTimeString(),
        };

        this.messages.push(aiMessage);
        this.currentMessage = '';
        this.isSending = false;

        // 滚动到底部
        this.$nextTick(() => {
          if (this.$refs.messagesContainer) {
            this.$refs.messagesContainer.scrollTop = this.$refs.messagesContainer.scrollHeight;
          }
        });
      }, 1000);
    },
    handleTopSupport() {
      window.open('/plateFormSupport.html', '_blank');
    },
  },
};
</script>
<style lang="scss" scoped>
.floating-ai-assistant {
  position: fixed;
  right: 30px;
  bottom: 30%;
  z-index: 9999;

  .floating-ai-btn {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);

    &:hover {
      transform: scale(1.1);
      box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
    }

    &:active {
      transform: scale(0.95);
    }

    .ai-icon {
      color: white;
      font-size: 28px;
    }
  }
}

.ai-assistant-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.ai-welcome {
  text-align: center;
  margin-bottom: 30px;

  .ai-welcome-icon {
    color: #667eea;
    margin-bottom: 15px;
  }

  h3 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
}

.ai-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;

  .ai-messages {
    flex: 1;
    overflow-y: auto;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    background: #fafafa;

    .ai-empty-message {
      text-align: center;
      color: #999;
      padding: 40px 0;
    }

    .ai-message {
      margin-bottom: 15px;
      padding: 10px 15px;
      border-radius: 8px;
      max-width: 80%;

      &.user {
        background: #667eea;
        color: white;
        margin-left: auto;
        text-align: right;
      }

      &.ai {
        background: white;
        color: #333;
        border: 1px solid #e4e7ed;
      }

      .message-content {
        margin-bottom: 5px;
        line-height: 1.4;
      }

      .message-time {
        font-size: 12px;
        opacity: 0.7;
      }
    }
  }

  .ai-input-area {
    .el-input {
      .el-input__wrapper {
        border-radius: 20px;
      }
    }
  }
}
</style>
