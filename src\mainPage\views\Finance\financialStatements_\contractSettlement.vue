<template>
  <basic-container>
    <avue-crud :option="option" :data="tableData" v-model:page="page" v-model:search="params" @on-load="onLoad"
      @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud" @keyup.enter="onLoad"
      @row-del="rowDel" @search-reset="reset" @search-change="searchChange" @refresh-change="onLoad"
      @current-change="onLoad" @size-change="onLoad" v-model="form">
      <template #menu="{ row }">
        <el-button type="primary" icon="document" @click="settlement(row)" text>结算</el-button>
        <!-- <el-button type="primary" icon="document"  text>结算记录</el-button> -->
      </template>
        <el-link type="primary" @click="toDetail(row)">
          <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small"
            >合作</el-tag
          >
          <el-popover
            ref="popover1"
            placement="top-start"
            :disabled="!row.remark"
            width="200"
            trigger="hover"
            :content="row.remark"
          >
            <template #reference>
              <el-tag type="danger" v-if="row.contractStatus == 2" effect="dark" size="small"
                >中止</el-tag
              >
            </template>
          </el-popover>
          <el-tag
            v-if="row.isPreOrder == 1"
            type="danger"
            size="small"
            effect="plain"
            title="预订单"
            >预</el-tag
          >
          {{ row.contractCode }}
          <i
            title="该订单正在申请撤销"
            v-if="row.isApplyCancel == 1"
            class="element-icons el-icon-chehui1"
            style="color: var(--el-color-warning); font-size: 20px"
          ></i>
        </el-link>
      <template #contractType1="{ row }">
        <div style="display: flex;align-items: center;justify-content: center;">
          <img title="伙伴挂靠" v-if="row.contractType == 5 && row.cooperationType == 1"
            src="../../../assets/imgIcon/伙伴挂靠.png" style="height: 20px;width: 20px;" alt="">
          <img title="挂靠伙伴" v-if="row.contractType == 5 && row.cooperationType == 0"
            src="../../../assets/imgIcon/挂靠伙伴.png" style="height: 20px;width: 20px;" alt="">

          <!-- 如果是合作合同且有合作伙伴ID，显示可点击的链接 -->
          <el-popover
            v-if="row.contractType == 5 && row.cooperationCompanyId"
            placement="right"
            :width="500"
            trigger="click"
            @before-enter="loadPartnerDetailForRow(row.cooperationCompanyId)"
          >
            <template #reference>
              <el-link type="primary" style="text-overflow: ellipsis;white-space: nowrap;">
                {{[
                  {
                    value: 0,
                    label: '订单合同',
                  },
                  {
                    value: 1,
                    label: '项目合同',
                  },
                  {
                    value: 2,
                    label: '运维合同',
                  },
                  {
                    value: 3,
                    label: '实施合同',
                  },
                  {
                    value: 4,
                    label: '订单待确认合同',
                  },
                  {
                    value: 5,
                    label: '合作合同',
                  },
                ].find(item => item.value == row.contractType)?.label}}
              </el-link>
            </template>
            <template #default>
              <div v-loading="partnerLoading">
                <h4 style="margin: 0 0 16px 0; padding-bottom: 8px; border-bottom: 1px solid #eee;">
                  合作伙伴详情
                </h4>
                <el-descriptions :column="1" border size="small">
                  <el-descriptions-item label="伙伴名称">
                    {{ partnerDetail.partnerName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系人">
                    {{ partnerDetail.contactPerson || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="联系电话">
                    {{ partnerDetail.contactPhone || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item v-if="partnerDetail.type == 0" label="业务员">
                    {{ partnerDetail.businessPersonName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开票伙伴">
                    {{ partnerDetail.invoiceCompanyName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="纳税人识别号">
                    {{ partnerDetail.ratepayerIdentifyNumber || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开户行名称">
                    {{ partnerDetail.bankName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="账号名称">
                    {{ partnerDetail.accountName || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="开户行账号">
                    {{ partnerDetail.bankAccount || '-' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="备注">
                    {{ partnerDetail.remark || '-' }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </template>
          </el-popover>

          <!-- 如果不是合作合同或没有合作伙伴ID，显示普通文本 -->
          <span v-else style="text-overflow: ellipsis;white-space: nowrap;">
            {{[
              {
                value: 0,
                label: '订单合同',
              },
              {
                value: 1,
                label: '项目合同',
              },
              {
                value: 2,
                label: '运维合同',
              },
              {
                value: 3,
                label: '实施合同',
              },
              {
                value: 4,
                label: '订单待确认合同',
              },
              {
                value: 5,
                label: '合作合同',
              },
            ].find(item => item.value == row.contractType)?.label}}
          </span>
        </div>
      </template>
      <template #contractName="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
        
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="合同结算" v-model="drawer" size="1300" :destroy-on-close="true" :show-close="true"
      :wrapperClosable="true">
      <div class="settlement-container">
        <!-- 合同基本信息 -->
        <div class="section">
          <h3 class="section-title">合同基本信息</h3>
          <el-descriptions border :column="2" size="default">
            <el-descriptions-item label="合同编号">{{ contractData.contractCode }}</el-descriptions-item>
            <el-descriptions-item label="合同名称">{{ contractData.contractName }}</el-descriptions-item>
            <el-descriptions-item span="2" label="合作伙伴">
              <div style="display: flex;align-items: center;">
                <el-popover
                  placement="right"
                  :width="500"
                  trigger="click"
                  v-if="contractData.cooperationCompanyId"
                >
                  <template #reference>
                    <el-link type="primary" style="margin-right: 8px;">
                      {{ contractData.cooperationCompanyName }}
                    </el-link>
                  </template>
                  <template #default>
                    <div v-loading="partnerLoading">
                      <h4 style="margin: 0 0 16px 0; padding-bottom: 8px; border-bottom: 1px solid #eee;">
                        合作伙伴详情
                      </h4>
                      <el-descriptions :column="1" border size="small">
                        <el-descriptions-item label="伙伴名称">
                          {{ partnerDetail.partnerName || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="联系人">
                          {{ partnerDetail.contactPerson || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="联系电话">
                          {{ partnerDetail.contactPhone || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item v-if="partnerDetail.type == 0" label="业务员">
                          {{ partnerDetail.businessPersonName || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="开票伙伴">
                          {{ partnerDetail.invoiceCompanyName || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="纳税人识别号">
                          {{ partnerDetail.ratepayerIdentifyNumber || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="开户行名称">
                          {{ partnerDetail.bankName || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="账号名称">
                          {{ partnerDetail.accountName || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="开户行账号">
                          {{ partnerDetail.bankAccount || '-' }}
                        </el-descriptions-item>
                        <el-descriptions-item label="备注">
                          {{ partnerDetail.remark || '-' }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </template>
                </el-popover>
                <span v-else>{{ contractData.cooperationCompanyName }}</span>
                <img title="伙伴挂靠"
                  v-if="contractData.contractType == 5 && contractData.cooperationType == 1"
                  src="../../../assets/imgIcon/伙伴挂靠.png" style="height: 20px;width: 20px;" alt="">
                <img title="挂靠伙伴" v-if="contractData.contractType == 5 && contractData.cooperationType == 0"
                  src="../../../assets/imgIcon/挂靠伙伴.png" style="height: 20px;width: 20px;" alt="">
              </div>
            </el-descriptions-item>
            <el-descriptions-item label="客户名称">{{ contractData.customerName }}</el-descriptions-item>
            <el-descriptions-item label="合同总额">{{ contractData.contractTotalPrice }}</el-descriptions-item>
            <el-descriptions-item label="业务员">{{ contractData.businessUserName }}</el-descriptions-item>
            <el-descriptions-item label="管理费点数">{{ parseFloat(contractData.managementFeePoints)
              }}%</el-descriptions-item>
            <el-descriptions-item label="管理费">{{ (contractData.contractTotalPrice * contractData.managementFeePoints /
              100).toFixed(2) }}</el-descriptions-item>
            <el-descriptions-item label="签订日期">{{ contractData.signDate }}</el-descriptions-item>
            <el-descriptions-item label="合同附件" span="2"></el-descriptions-item>
            <el-descriptions-item label="备注" span="2">{{ contractData.remark }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 合作伙伴信息 -->
        <!-- <div class="section">
            <h3 class="section-title">合作伙伴信息</h3>
            <el-descriptions border :column="2" size="default">
              <el-descriptions-item label="合作伙伴名称">{{ partnerData.partnerName }}</el-descriptions-item>
              <el-descriptions-item label="联系人">{{ partnerData.contactPerson }}</el-descriptions-item>
              <el-descriptions-item label="联系电话">{{ partnerData.contactPhone }}</el-descriptions-item>
              <el-descriptions-item label="联系邮箱">{{ partnerData.contactEmail }}</el-descriptions-item>
              <el-descriptions-item label="公司地址" :span="2">{{ partnerData.address }}</el-descriptions-item>
              <el-descriptions-item label="开户银行">{{ partnerData.bankName }}</el-descriptions-item>
              <el-descriptions-item label="银行账号">{{ partnerData.bankAccount }}</el-descriptions-item>
            </el-descriptions>
          </div> -->

        <!-- 计划收款列表 -->
        <div class="section">
          <div class="section-header-with-stats">
            <h3 class="section-title">收款列表</h3>
            <div class="stats-cards">
              <div class="stat-card stat-card-success">
                <el-icon class="stat-icon"><Money /></el-icon>
                <div class="stat-label">已收款</div>
                <div class="stat-value">¥{{ (collectionStats.actualTotal * 1).toFixed(2) }}</div>
              </div>
            </div>
          </div>
          <plannedCollection :tableData="contractData.planCollectionVOList || []"
            @data-changed="handleDataChanged" />
        </div>

        <!-- 开票列表 -->
        <div class="section">
          <div class="section-header-with-stats">
            <h3 class="section-title">开票列表</h3>
            <div class="stats-cards">
              <div class="stat-card stat-card-success">
                <el-icon class="stat-icon"><Document /></el-icon>
                <div class="stat-label">已开票</div>
                <div class="stat-value">¥{{ (invoiceStats.invoicedTotal * 1).toFixed(2) }}</div>
              </div>
              <div class="stat-card stat-card-info">
                <el-icon class="stat-icon"><Coin /></el-icon>
                <div class="stat-label">税额</div>
                <div class="stat-value">¥{{ (invoiceStats.taxPrice * 1).toFixed(2) }}</div>
              </div>
            </div>
          </div>
          <invoice :tableData="contractData.sealContractInvoiceVOList || []"
            @data-changed="handleDataChanged" />
        </div>

        <!-- 付款列表 -->
        <div class="section">
          <div class="section-header">
            <div class="section-header-with-stats">
              <h3 class="section-title">付款列表</h3>
              <div class="stats-cards">
                <div class="stat-card stat-card-success">
                  <el-icon class="stat-icon"><Wallet /></el-icon>
                  <div class="stat-label">已付款金额</div>
                  <div class="stat-value">¥{{ (paymentStats.paidTotal * 1).toFixed(2) }}</div>
                </div>
              </div>
            </div>
            <el-button type="primary" icon="Plus" @click="handleAddPayment" title="新增付款"></el-button>
          </div>
          <purchaseContract :cooperationType="currentCooperationType" ref="purchaseContractRef" :tableData="contractData.purchaseContractVOList"
            :sealContractId="currentContractId"
            @data-changed="handleDataChanged" />
        </div>

        <!-- 收票列表 -->
        <div class="section">
          <div class="section-header">
            <div class="section-header-with-stats">
              <h3 class="section-title">收票列表</h3>
              <div class="stats-cards">
                <div class="stat-card stat-card-success">
                  <el-icon class="stat-icon"><Tickets /></el-icon>
                  <div class="stat-label">总收票</div>
                  <div class="stat-value">¥{{ (ticketStats.receiveTicketTotal * 1).toFixed(2) }}</div>
                </div>
                <div class="stat-card stat-card-info">
                  <el-icon class="stat-icon"><Coin /></el-icon>
                  <div class="stat-label">税额</div>
                  <div class="stat-value">¥{{ (ticketStats.taxPrice * 1).toFixed(2) }}</div>
                </div>
              </div>
            </div>
            <el-button type="primary" icon="Plus" @click="handleAddTicket" title="新增收票"></el-button>
          </div>
          <purchaseTicket ref="purchaseTicketRef" :cooperationType="currentCooperationType" :tableData="contractData.purchaseContractInvoiceVOList"
            :sealContractId="currentContractId"
            @data-changed="handleDataChanged" />
        </div>
 <div class="section" v-if="currentCooperationType == 1">
          <div class="section-header">
            <div class="section-header-with-stats">
              <h3 class="section-title">销售单列表</h3>
              <div class="stats-cards">
                <div class="stat-card stat-card-success">
                  <el-icon class="stat-icon"><ShoppingCart /></el-icon>
                  <div class="stat-label">销售总额</div>
                  <div class="stat-value">¥{{ (saleStats.totalPrice * 1).toFixed(2) }}</div>
                </div>
              </div>
            </div>
            <el-button type="primary" icon="Plus" @click="handleAddTicket" title="新增收票"></el-button>
          </div>
          <saleOrder ref="purchaseTicketRef" :cooperationType="currentCooperationType" :tableData="contractData.projectApplyPurchaseVOS"
            :sealContractId="currentContractId"
            @data-changed="handleDataChanged" />
        </div>
        <!-- 资金预支列表 -->
        <div class="section">
          <div class="section-header">
            <div class="section-header-with-stats">
              <h3 class="section-title">资金预支列表</h3>
              <div class="stats-cards">
                <div class="stat-card stat-card-success">
                  <el-icon class="stat-icon"><CreditCard /></el-icon>
                  <div class="stat-label">预支总额</div>
                  <div class="stat-value">¥{{ (fundStats.prePrice * 1).toFixed(2) }}</div>
                </div>
              </div>
            </div>
            <el-button type="primary" icon="Plus" @click="handleAddFund" title="新增资金预支"></el-button>
          </div>
          <fundTransactions ref="fundTransactionsRef" :cooperationType="currentCooperationType"
            :tableData="contractData.prePaymentVOList" :sealContractId="currentContractId"
            @data-changed="handleDataChanged" />
        </div>
        <!-- 结算列表 -->
        <div class="section">
          <div class="section-header">
            <div class="section-header-with-stats">
              <h3 class="section-title">结算列表</h3>
              <div class="stats-cards">
                <div class="stat-card stat-card-success">
                  <el-icon class="stat-icon"><Checked /></el-icon>
                  <div class="stat-label">结算总额</div>
                  <div class="stat-value">¥{{ (settlementStats.settlementTotal * 1).toFixed(2) }}</div>
                </div>
              </div>
            </div>
            <el-button type="primary" icon="Plus" @click="handleAddSettlement" title="新增结算"></el-button>
          </div>
          <settlementList ref="settlementListRef" :cooperationType="currentCooperationType"
            :tableData="contractData.sealContractSettlementVOS" :sealContractId="currentContractId"
            :form="contractData" @data-changed="handleDataChanged" />
        </div>
      </div>
    </el-drawer>

  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Money, Document, Coin, Wallet, Tickets, ShoppingCart, CreditCard, Checked } from '@element-plus/icons-vue';
import plannedCollection from './components/plannedCollection.vue';
import invoice from './components/invoice.vue';
import purchaseContract from './components/purchaseContract.vue';
import purchaseTicket from './components/purchaseTicket.vue';
import fundTransactions from './components/fundTransactions.vue';
import settlementList from './components/settlement.vue';
import saleOrder from './components/saleOrder.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  border: true,
  column: [
    {
      label: '合同编号',
      prop: 'contractCode',
      overHidden: true,
      // search: true,
      searchSpan: 4,
      width: 160,
      display: false, // 设置 display 为 false
    },
    {
      label: '合同名称',
      prop: 'contractName',

      overHidden: true,
      search: true,
      slot: true,
      display: false, // 设置 display 为 false
    },
    {
      label: '合同类型',
      width: 130,
      prop: 'contractType1',
      display: false,

      formatter: row => {
        const data = [
          {
            value: 0,
            label: '订单合同',
          },
          {
            value: 1,
            label: '项目合同',
          },
          {
            value: 2,
            label: '运维合同',
          },
          {
            value: 3,
            label: '实施合同',
          },
          {
            value: 4,
            label: '订单待确认合同',
          },
          {
            value: 5,
            label: '合作合同',
          },
        ];
        return data.find(item => item.value == row.contractType)?.label;
      },
    },
    {
      label:'合作伙伴',
      prop: 'cooperationCompanyName',
      width: 150,
      display: false, // 设置 display 为 false
    },
    {
      label: '客户名称',
      prop: 'customerName',
      //width: 150,
      search: true,
      component: 'wf-customer-drop',


      display: false, // 设置 display 为 false
    },
    {
      label: '合同总额',
      prop: 'contractTotalPrice',
      // search: true,
      // hide: true,
      searchSpan: 6,
      searchLabelWidth: 120,
      searchSlot: true,

      width: 100,
      display: false, // 设置 display 为 false
    },
    {
      label: '管理费点数',
      prop: 'managementFeePoints',
      width: 100,
      display: false, // 设置 display 为 false
      formatter: (row, value, column, cell) => {
        return parseFloat(value) + '%';
      },
    },
    // 管理费金额
    {
      label: '管理费金额',
      prop: 'managementFeeAmount',
      width: 100,
      display: false, // 设置 display 为 false
      formatter: (row, value, column, cell) => {
        // 合同 金额 * 点数 / 100
        return '¥' + (row.contractTotalPrice * (row.managementFeePoints / 100)).toFixed(2);
      },
    },
    {
      label:'已结算金额',
      prop: 'hasSettlementPrice',
      width: 100,
      display: false, // 设置 display 为 false
    },

    {
      label: '业务员',
      prop: 'businessName',
      search: true,
      component: 'wf-user-drop',
      width: 130,
      formatter: (row, value, column, cell) => {
        return row.businessUserName;
      },
      // hide: true,
      display: false, // 设置 display 为 false
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      display: false, // 设置 display 为 false
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const router = useRouter();
const tableUrl = '/api/vt-admin/sealContract/page';
let params = ref({});
let tableData = ref([]);
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        contractType: 5
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      tableData.value = [{}];
      loading.value = false;
    });
}
function reset() {
  onLoad();
}
function searchChange(_params, done) {
  onLoad();
  done();
}


let drawer = ref(false);
let currentContractId = ref(''); // 当前选中的合同ID
let currentCooperationType = ref(0);

// 合作伙伴详情数据
let partnerDetail = ref({});
let partnerLoading = ref(false);

// 统计数据
let collectionStats = ref({ planTotal: 0, actualTotal: 0 });
let invoiceStats = ref({ invoiceTotal: 0, invoicedTotal: 0 });
let paymentStats = ref({ paymentTotal: 0, paidTotal: 0 });
let ticketStats = ref({ receiveTicketTotal: 0 });
let fundStats = ref({ fundTotal: 0 });
let settlementStats = ref({ settlementTotal: 0 });
let saleStats = ref({ totalPrice: 0 });

// 合同基本信息
let contractData = ref({
  contractCode: 'HT2024001',
  contractName: '智能化系统集成项目合同',
  customerName: '某某科技有限公司',
  contractTotalPrice: '¥1,500,000.00',
  businessName: '张三',
  signDate: '2024-01-15',
  status: '执行中',
  remark: '该合同为智能化系统集成项目，包含硬件采购、软件开发及系统集成服务'
});

// 合作伙伴信息
let partnerData = ref({
  partnerName: '某某科技有限公司',
  contactPerson: '李经理',
  contactPhone: '***********',
  contactEmail: '<EMAIL>',
  address: '北京市朝阳区某某大厦10层',
  bankName: '中国工商银行北京分行',
  bankAccount: '6222 0202 0000 1234 567'
});



function settlement(row) {
  currentContractId.value = row.id;
  currentCooperationType.value = row.cooperationType;
  getContractInfo()
  drawer.value = true;
}
function getContractInfo() {
  axios
    .get('/api/vt-admin/sealContract/detailForSettlement', {
      params: {
        id: currentContractId.value,
      },
    })
    .then(res => {
      if (res.data.code == 200) {
        contractData.value = {
          ...res.data.data,
          projectApplyPurchaseVOS: res.data.data.projectApplyPurchaseVOS || [],
          prePaymentVOList: res.data.data.prePaymentVOList || [],
          sealContractSettlementVOS: res.data.data.sealContractSettlementVOS || [],
          sealContractInvoiceVOS: res.data.data.sealContractInvoiceVOS || [],
          sealContractPaymentVOS: res.data.data.sealContractPaymentVOS || [],
          
        };
        // 如果有合作伙伴ID，加载合作伙伴详情
        if (contractData.value.cooperationCompanyId) {
          loadPartnerDetail(contractData.value.cooperationCompanyId);
        }
        // 统计数据计算
        calculateStats();
      }
    })
    .catch(() => {
      contractData.value = {};
    });
}
function calculateStats() {
  // 计算收款统计
  const planCollectionData = contractData.value.planCollectionVOList || [];
  const planTotal = planCollectionData.reduce((sum, item) => sum + (parseFloat(item.planCollectionPrice) || 0), 0);
  const actualTotal = planCollectionData.reduce((sum, item) => sum + (parseFloat(item.actualCollection) || 0), 0);
  collectionStats.value = {
    planTotal: planTotal.toFixed(2),
    actualTotal: actualTotal.toFixed(2)
  };

  // 计算开票统计
  const invoiceData = contractData.value.sealContractInvoiceVOList || [];
  const invoiceTotal = invoiceData.reduce((sum, item) => sum + (parseFloat(item.invoicePrice) || 0), 0);
  const invoicedTotal = invoiceData.reduce((sum, item) => {
    if (item.invoiceStatus === '已开票' || item.invoiceStatus === 1) {
      return sum + (parseFloat(item.invoicePrice) || 0);
    }
    return sum;
  }, 0);
  const invoiceTaxPrice = invoiceData.reduce((sum, item) => {
    if (item.invoiceStatus === '已开票' || item.invoiceStatus === 1) {
      return sum + (parseFloat(item.taxPrice) || 0);
    }
    return sum;
  }, 0);
  invoiceStats.value = {
    invoiceTotal: invoiceTotal.toFixed(2),
    invoicedTotal: invoicedTotal.toFixed(2),
    taxPrice: invoiceTaxPrice.toFixed(2)
  };

  // 计算付款统计
  const purchaseData = contractData.value.purchaseContractVOList || [];
  const contractTotalPrice = purchaseData.reduce((sum, item) => sum + (parseFloat(item.contractTotalPrice) || 0), 0);
  const hasPaymentPrice = purchaseData.reduce((sum, item) => sum + (parseFloat(item.paymentPrice) || 0), 0);
  paymentStats.value = {
    paymentTotal: contractTotalPrice,
    paidTotal: hasPaymentPrice
  };

  // 计算收票统计
  const ticketData = contractData.value.purchaseContractInvoiceVOList || [];
  const ticketTotalPrice = ticketData.reduce((sum, item) => sum + (parseFloat(item.invoicePrice) || 0), 0);
  const ticketTaxPrice = ticketData.reduce((sum, item) => sum + (parseFloat(item.taxPrice) || 0), 0);
  ticketStats.value = {
    receiveTicketTotal: ticketTotalPrice,
    taxPrice: ticketTaxPrice
  };

  // 计算销售单统计
  const saleData = contractData.value.projectApplyPurchaseVOS || [];
  const saleTotalPrice = saleData.reduce((sum, item) => sum + (parseFloat(item.totalPrice) || 0), 0);
  saleStats.value = {
    totalPrice: saleTotalPrice
  };

  // 计算资金预支统计
  const fundData = contractData.value.prePaymentVOList || [];
  const prePrice = fundData.reduce((total, item) => {
    if (item.payStatus == 1) {
      total += item.amount * 1;
    }
    return total;
  }, 0);
  fundStats.value = {
    prePrice: prePrice
  };

  // 计算结算统计
  const settlementData = contractData.value.sealContractSettlementVOS || [];
  const settlementTotal = settlementData.reduce((sum, item) => {
    if (item.settlementStatus == 4) {
      sum += item.applySettlementPrice * 1;
    }
    return sum;
  }, 0);
  settlementStats.value = {
    settlementTotal: settlementTotal.toFixed(2)
  };
}
// 加载合作伙伴详情
function loadPartnerDetail(partnerId) {
  if (!partnerId) return;

  partnerLoading.value = true;
  axios
    .get('/api/vt-admin/partner/detail', {
      params: {
        id: partnerId,
      },
    })
    .then(res => {
      if (res.data.code === 200 && res.data.data) {
        partnerDetail.value = res.data.data;
      }
    })
    .catch(() => {
      partnerDetail.value = {};
    })
    .finally(() => {
      partnerLoading.value = false;
    });
}

// 为表格行加载合作伙伴详情（用于合同类型列的 popover）
function loadPartnerDetailForRow(partnerId) {
  loadPartnerDetail(partnerId);
}

// 处理子组件数据变更事件
function handleDataChanged() {
  getContractInfo();
}

// 子组件引用
const purchaseContractRef = ref(null);
const purchaseTicketRef = ref(null);
const fundTransactionsRef = ref(null);
const settlementListRef = ref(null);

// 新增付款
function handleAddPayment() {
  if (purchaseContractRef.value) {
    purchaseContractRef.value.handllAdd();
  }
}

// 新增收票
function handleAddTicket() {
  if (purchaseTicketRef.value) {
    purchaseTicketRef.value.toReceiveTicket();
  }
}

// 新增资金预支
function handleAddFund() {
  if (fundTransactionsRef.value && fundTransactionsRef.value.$refs.crud) {
    fundTransactionsRef.value.$refs.crud.rowAdd();
  }
}

// 新增结算
function handleAddSettlement() {
  if (settlementListRef.value && settlementListRef.value.$refs.crud) {
    settlementListRef.value.$refs.crud.rowAdd();
  }
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
      name: row.contractName,
      selectType: 0,
    },
  });
}
</script>

<style lang="scss" scoped>
.settlement-container {
  padding: 0;
}

.section {
  //   margin-bottom: 30px;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header-with-stats {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
  padding-bottom: 10px;
  border-bottom: 2px solid #409eff;
  display: inline-block;
  white-space: nowrap;
  //flex: 1;
}

// 统计卡片样式
.stats-cards {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  align-items: center;
}

.stat-card {
  position: relative;
  min-width: 90px;
  min-height: 90px;
  padding: 5px;
  border-radius: 8px;
  background: #fff;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;

  &:hover {
    transform: translateY(-4px);
    // box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  }

  .stat-icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.9;
  }

  .stat-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
    font-weight: 500;
  }

  .stat-value {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    text-align: center;
  }

  // 成功样式（绿色）
  &.stat-card-success {
    border-color: #67c23a;
    // background: linear-gradient(135deg, #f0f9ff 0%, #e8f5e9 100%);

    .stat-icon {
      color: #67c23a;
    }

    .stat-value {
      color: #67c23a;
    }
  }

  // 信息样式（蓝色）
  &.stat-card-info {
    border-color: #909399;
    // background: linear-gradient(135deg, #f5f7fa 0%, #e4e7ed 100%);

    .stat-icon {
      color: #909399;
    }

    .stat-value {
      color: #606266;
    }
  }
}

:deep(.el-drawer__body) {
  padding: 20px;
  overflow-y: auto;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-table) {
  font-size: 14px;
  margin-bottom: 20px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  background-color: #f5f7fa;
}

:deep(.el-table .el-table__footer-wrapper) {
  font-weight: 600;
  color: #409eff;
  background-color: #f5f7fa;
}

:deep(.el-table__row--summary) {
  background-color: #f5f7fa !important;
}
</style>
