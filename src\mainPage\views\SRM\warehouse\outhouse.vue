<template>
  <basic-container shadow="never">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="onLoad"
      @row-del="rowDel"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #outStorageCode="{ row }">
        <el-link type="primary" @click="todetail(row)">{{ row.outStorageCode }}</el-link>
      </template>
      <template #menu-left>
        <el-button icon="plus" @click="handleAdd" type="primary">新增</el-button>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="todetail(row)">详情</el-button>
        <el-button
          type="primary"
          text
          icon="delete"
          v-if="
            row.outStorageType == 2"
          @click="$refs.crud.rowDel(row)"
          >删除</el-button
        >

        <el-button
          type="primary"
          text
          icon="Finished"
         
          @click="confirm(row)"
          >到货</el-button
        >
        <el-button type="primary" text icon="edit" v-if="row.outStatus == 0" @click="editInfo(row)"
          >完善物流</el-button
        >
      </template>
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toOrderDetail(row)">{{ row.orderNo }}</el-link>
      </template>
      <template #productNames="{ row }">
        <el-tag
          effect="plain"
          style="margin-right: 5px"
          type="primary"
          v-for="(item, index) in row.productNames?.split(',')"
          :key="index"
          >{{ item }}</el-tag
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps(['orderId'])
let route = useRoute();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 250,
  border: true,
  column: [
    {
      label: '出库单号',
      prop: 'outStorageCode',
      width: 250,
      overHidden: true,
    },
    {
      label: '出库产品',
      prop: 'productNames',
      overHidden: true,
      width: 300,
      align: 'left',
      search: !route.query.id,
    },
    {
      label: '关联客户',
      prop: 'customerName',
      component: 'wf-customer-drop',
      search: !route.query.id,
      overHidden: true,
      width: 300,
    },
    {
      label: '关联订单',
      prop: 'orderNo',
      overHidden: true,
    },
    {
      label: '出库数量',
      prop: 'number',
      width: 90,
    },
    {
      label: '出库类型',
      prop: 'outStorageType',
      type: 'radio',

      dicData: [
        {
          value: 0,
          label: '订单出库',
        },
        {
          value: 1,
          label: '借测出库',
        },
        {
          value: 3,
          label: '采购退货',
        },
        {
          value: 2,
          label: '其他',
        },
      ],
      rules: [{ required: true, message: '请选择出库类型' }],
    },
    {
      label: '出库时间',
      prop: 'outDate',
      type: 'date',
      component: 'wf-daterange-search',
      search:!props.orderId,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      searchSpan: 5,
      width: 140,
     
      formatter: row => {
        return row.outDate || row.createTime;
      },
    },
    {
      label: '序列号',
      prop: 'serialNumber',
        search:!props.orderId,
      hide: true,
    },
    {
      label: '出库人',
      prop: 'createName',
      width: 100,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin//purchaseOutStorage/returnBack';
const updateUrl = '';
const tableUrl = '/api/vt-admin/purchaseOutStorage/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
watch(() => props.orderId, () => {
  onLoad()
})
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        orderId: route.query.id,
        size,
        current,
        ...params.value,
        startDate: params.value.outDate && params.value.outDate[0],
        endDate: params.value.outDate && params.value.outDate[1],
        outDate:null,
        createTime: null, 
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd(params) {
  router.push({
    path: '/SRM/warehouse/compoents/addOuthouse',
    query: {
      orderId: route.query.id || null,
    },
  });
}
function todetail(row) {
  router.push({
    path: '/SRM/warehouse/compoents/outhouseDetail',
    query: {
      id: row.id,
    },
  });
}
function confirm(row) {
  proxy.$refs.dialogForm.show({
    title: '到货',
    option: {
      column: [
        {
          label: '到货时间',
          prop: 'arriveDate',
          width: 170,
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        arriveDate: res.data.arriveDate,
      };
      axios
        .post('/api/vt-admin/purchaseOutStorage/arrive', data)
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          }
        })
        .catch(err => {
          done();
        });
    },
  });
}
function editInfo(row) {
  proxy.$refs.dialogForm.show({
    title: '完善物流',
    option: {
      column: [
        {
          label: '快递单号',
          prop: 'expressNumber',
          value: row.expressNumber,
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
            },
          ],
        },
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict-biz/dictionary?code=courierCompany',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          value: row.courierCompany,
          rules: [
            {
              required: true,
              message: '请选择快递公司',
            },
          ],
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },

          prop: 'courierCompany',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data
      };
      axios
        .post('/api/vt-admin/purchaseOutStorage/updateLogistics', data)
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          }
        })
        .catch(err => {
          done();
        });
    },
  });
}
function toOrderDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.orderId,
      activeName,
    },
  });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios
        .post(delUrl, {
          id: form.id,
        })
        .then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
