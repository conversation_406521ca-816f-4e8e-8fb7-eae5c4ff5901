<template>
  <basic-container style="height: 100%">
    <div ref="mainRef">
      <el-row style="height: 100%" :gutter="20" ref="rowRef">
        <el-col :span="5" style="height: 100%">
          <avue-tree :option="treeOption" @save="treeSave" ref="tree" v-model="form" @update="treeUpdate" @del="treeDel"
            :data="treeData" @node-click="nodeClick">
            <template #default="{ node, data }">
              <span :style="{
                color: data.id != '' ? '' : 'red',

              }">{{ data.categoryName }}</span>
            </template>
          </avue-tree>
        </el-col>
        <el-col v-loading="loading" :span="19">
          <!-- <el-row :gutter="10">
            <el-col :span="24">
              <div style="display: flex; justify-content: space-between">
                <el-button type="primary" icon="plus" @click="addOrChance">新增</el-button>
                <div style="display: flex; gap: 30px">
                  <el-input
                    placeholder="请输入机会名称"
                    style="width: 200px"
                    v-model="params.title"
                  ></el-input>
                
                  <div style="display: flex">
                    <el-button type="primary" @click="handleSearch" icon="search">搜索</el-button>
                    <el-button icon="delete" @click="reset">清空</el-button>
                  </div>
                </div>
              </div>
            </el-col>
          </el-row>
          <el-row
            ref="scrollRef"
            :gutter="10"
            style="height: 100%; overflow-y: auto; padding-bottom: 20px"
            @scroll="handleScroll"
          >
            <el-col :span="8" v-for="item in tableData" style="margin-bottom: 20px">
              <el-card shadow="hover" body-style="padding: 0px">
                <template #header>
                  <div
                    class="card-header"
                    style="
                      font-weight: bolder;
                      display: flex;
                      justify-content: space-between;
                      align-items: center;
                    "
                  >
                    <span>{{ item.title }}</span>
                    <el-button
                      type="danger"
                      plain
                      v-if="
                        $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                        $store.getters.userInfo.role_name.indexOf('	manager') > -1
                      "
                      size="small"
                      @click="handleDelete(item)"
                      circle
                      icon="delete"
                    ></el-button>
                  </div>
                </template>
<div style="padding: 20px">
  <el-text line-clamp="2">
    <div style="
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 3;
                        overflow: hidden;
                        min-height: 40px;
                        text-overflow: ellipsis;
                      ">
      {{ item.content }}
    </div>
  </el-text>
  <div>
    <File :fileList="item.attachList"></File>
  </div>
</div>
<div class="footer" style="
                    border-top: 1px solid var(--el-card-border-color);
                    height: 40px;
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                  ">
  <div style="display: flex; justify-content: flex-end">
    <el-button type="primary" style="margin-right: 0" @click="addOrChance(item)" icon="edit" v-if="
                        $store.getters.userInfo.user_id == item.createUser ||
                        $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                        $store.getters.userInfo.role_name.indexOf('	manager') > -1
                      " text>编辑</el-button>
    <el-button type="primary" @click="addDemand(row)" style="margin-left: 0" icon="Service" text>建立需求</el-button>
  </div>
</div>
</el-card>
</el-col>
<el-col :span="24" v-if="tableData.length === 0">
  <div style="display: flex; justify-content: center; height: 100%; align-items: center">
    <el-empty description="暂无数据"></el-empty>
  </div>
</el-col>
<el-col :span="24" v-if="tableData.length == page.total && page.total !== 0 && page.total > 10">
  <div style="display: flex; justify-content: center; height: 100%; align-items: center">
    没有更多数据了
  </div>
</el-col>
</el-row> -->
          <avue-crud :option="tableOption" :data="tableData" v-model:page="page" v-model:search="params"
            @on-load="onLoad" @row-update="rowUpdate" @row-save="rowSave" :table-loading="loading" ref="crud"
            @keyup.enter="onLoad" @row-del="rowDel" @search-reset="reset" :before-open="beforeOpen"
            @search-change="searchChange" @refresh-change="onLoad" @current-change="onLoad"
            @selection-change="handleSectionChange" @size-change="onLoad" v-model="tableForm">
            <template #menu-left="{ size }">
              <el-button type="primary" plain :size="size" @click="addDemandAll">建立需求采集</el-button>
              <!-- <el-button type="primary" plain :size="size" @click="aiHelp">AI帮助</el-button> -->
            </template>
            <template #content="{ row }">
              <div style="white-space: pre-wrap; text-align: left">
                {{ row.content }}
              </div>
            </template>
            <template #requiredmentProperty="{ row }">
              <el-link @click="manageCollection(row)" type="primary" >{{ row.title }}的需求采集表</el-link>
              
            </template>
            <template #menu="{ row, size }">

              <div style="display: flex; align-items: center">
                <el-button type="primary" text icon="edit" @click="$refs.crud.rowEdit(row)" :size="size" v-if="
                  row.createUser == $store.getters.userInfo.user_id ||
                  $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                  $store.getters.userInfo.role_name.indexOf('manager') > -1
                ">编辑</el-button>
                <el-button type="primary" text icon="delete" :size="size" @click="$refs.crud.rowDel(row)" v-if="
                  row.createUser == $store.getters.userInfo.user_id ||
                  $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                  $store.getters.userInfo.role_name.indexOf('manager') > -1
                ">删除</el-button>
                <el-dropdown v-if="
                  row.createUser == $store.getters.userInfo.user_id ||
                  $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                  $store.getters.userInfo.role_name.indexOf('manager') > -1
                " :size="size">
                  <el-button type="primary" text icon="more">更多<el-icon
                      class="is-icon"><arrow-down /></el-icon></el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="addDemand(row)">建立需求采集表</el-dropdown-item>
                      <el-dropdown-item @click="manageCollection(row)">管理需求采集表</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <template #categoryId="{ row, size }">
              <div style="display: flex; gap: 5px; flex-wrap: wrap; align-items: center; position: relative;">

                <el-popover :ref="`popover-${row.id}`" placement="right" :width="300" trigger="click"
                  popper-class="category-tree-popover">
                  <template #reference>
                    <div style="display: flex; gap: 5px; flex-wrap: wrap; align-items: center; position: relative;">
                      <el-tag v-for="item in row.$categoryId ? row.$categoryId.split('|') : ['点击设置分类']" :size="size"
                        style="margin-bottom: 5px; cursor: pointer;" :type="item == '点击设置分类' ? 'danger' : 'success'"
                        plain>{{ item }}</el-tag>
                    </div>
                  </template>
                  <div class="tree-container">
                    <el-tree :data="treeData.filter(item => item.id != '')" node-key="id"
                      :props="{ label: 'categoryName', children: 'children' }" show-checkbox
                      :default-checked-keys="row.categoryId ? row.categoryId.split(',') : []" :check-strictly="true"
                      default-expand-all @check-change="handleTreeCheckChange" ref="categoryTreeRef" />
                  </div>
                  <div style="margin-top: 10px; text-align: right;">
                    <el-button size="small" @click="$refs[`popover-${row.id}`]?.hide()">取消</el-button>
                    <el-button size="small" type="primary" @click="saveCategoryChange(row)">确认</el-button>
                  </div>
                </el-popover>
              </div>
            </template>
            <template #title="{ row, size }">
              <el-link type="primary" :size="size" @click="handleView(row)">{{
                row.title
              }}</el-link>
            </template>

          </avue-crud>
        </el-col>
      </el-row>
    </div>
    <!-- <mySplit v-model="splitValue" style="height: 100%;"> 
      <template v-slot:left>
       
      </template>
      <template v-slot:right>
        <div style="display: flex;height: 100%;">
          <div style="display: inline-block;width:20px"></div>
          <iframe style="width: 100%; height: 100%; " frameborder="0" allow="microphone" ref="iframeRef">
          </iframe>
        </div>
      </template>
    </mySplit> -->

    <dialogForm ref="dialogForm"></dialogForm>

    <!-- 新增需求 -->
    <el-drawer @close="option.emptyBtn = true" title="需求采集配置" v-model="drawer" direction="rtl" size="80%">
      <el-steps finish-status="success" style="margin-bottom: 10px" simple :active="form.step" align-center>
        <el-step title="需求信息" />
        <el-step title="采集配置" />
      </el-steps>
      <el-divider v-if="form.step == 0" />
      <avue-form ref="addFormRef" v-if="form.step == 0" :option="isAddAll ? multipleOption : option"
        v-model="form"></avue-form>
      <demandSet v-if="form.step == 1 && !isAddAll" ref="demandSetRef" :data="form"></demandSet>
      <demandSetAll v-if="form.step == 1 && isAddAll" ref="demandSetRef" :list="form.demandList"></demandSetAll>
      <template #footer>
        <div style="flex: auto">
          <el-button @click="drawer = false">取 消</el-button>
          <el-button @click="nextStep" v-if="form.step == 0">下一步</el-button>
          <el-button @click="form.step = 0" v-if="form.step == 1">上一步</el-button>
          <el-button type="primary" @click="confirmClick">确 认</el-button>
        </div>
      </template>
    </el-drawer>
    <el-dialog title="需求调查表" v-model="dialogVisible" append-to-body class="avue-dialog avue-dialog--top">
      <avue-form ref="dialogForm" :option="editOption" @submit="editSubmit" v-model="editForm">
        <template v-for="item in propIds" :key="item.id" #[item.id]>
          <el-checkbox-group v-if="item.type == 1" v-model="editForm[item.id]">
            <!-- works when <2.6.0, deprecated act as value when >=3.0.0 -->
            <el-checkbox v-for="item in item.requirementPropertyValuesVOS" :label="item.value" :key="item.value" />
          </el-checkbox-group>
          <avue-radio v-else v-model="editForm[item.id]" :dic="item.requirementPropertyValuesVOS.map(item => {
            return {
              label: item.value,
              value: item.value,
            };
          })
            "></avue-radio>
          <el-input :disabled="editOption.detail" v-if="JSON.stringify(editForm[item.id]).indexOf('其他') > -1"
            v-model="editForm[item.id + 'input']" type="textarea" placeholder="请输入内容"></el-input>
        </template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer" style="text-align: center">
        <!-- <el-button @click="dialogVisible = false">取 消</el-button> -->
        <el-button @click="$refs.dialogForm.submit()" v-if="!editOption.detail" type="primary">完成提交</el-button>
      </div>
    </el-dialog>
    <el-drawer title="关联需求列表" size="60%" v-model="detailDialogVisible">
      <demandList :discoverPcId="currentRow.id"></demandList>
    </el-drawer>

    <!-- 采集表管理抽屉 -->
    <el-drawer title="需求采集表管理" size="80%" v-model="collectionDrawerVisible">
      <el-form>
        <el-form-item>
          <el-button icon="plus" @click="addCollectionParams" type="primary">新增采集内容</el-button>
          <!-- <el-button icon="copyDocument" plain @click="copyCollectionList()" type="primary">复制到其他分类</el-button> -->
        </el-form-item>
        <el-table ref="collectionTableRef" class="avue-crud" :row-key="row => row.id" :data="collectionTableData" border
          @selection-change="handleCollectionSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" width="70" align="center">
            <template #default="scope">
              <div style="display: flex; align-items: center; justify-content: center">
                <span>{{ scope.$index + 1 }}</span>
                <el-icon class="sort" style="">
                  <Sort />
                </el-icon>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="#" width="50" type="expand">
            <template #default="scope">
              <div style="margin-left: 100px; display: flex">
                <draggable v-model="scope.row.valuesVOList" :animation="100"
                  @sort="a => { onCollectionMoveCallback(a, scope.row); }">
                  <transition-group>
                    <el-tag effect="plain" style="margin-right: 5px; margin-bottom: 5px"
                      v-for="element in scope.row.valuesVOList" :key="element.id" closable
                      @close="handleCollectionTagClose(element)">
                      {{ element.value }}
                    </el-tag>
                  </transition-group>
                </draggable>

                <el-input class="input-new-tag" v-if="scope.row.inputVisible" style="width: 50%"
                  v-model="scope.row.inputValue" :ref="'saveCollectionTagInput' + scope.$index" size="small"
                  @keyup.enter="addCollectionTag(scope.row)" @blur="addCollectionTag(scope.row)">
                </el-input>

                <el-button v-else-if="scope.row.type != 2" class="button-new-tag" size="small"
                  @click="showCollectionInput(scope.row, scope.$index)">+ 添加可选项</el-button>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="采集内容" prop="collectionContent" width="500" />
          <el-table-column label="备注" prop="remark" width=""> </el-table-column>
          <el-table-column label="创建人" prop="createName" width="120"> </el-table-column>
          <el-table-column label="编辑人" prop="updateName" width="120"> </el-table-column>
          <el-table-column label="回答方式" prop="type" width="150">
            <template #default="scope">
              <el-tag effect="plain" type="success" v-if="scope.row.type == 0">单选</el-tag>
              <el-tag effect="plain" type="success" v-else-if="scope.row.type == 1">多选</el-tag>
              <el-tag effect="plain" type="success" v-else>文本输入</el-tag>
              <el-tag effect="plain" type="danger" style="margin-left: 5px" v-if="scope.row.isRequired == 1">必填</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="操作" align="center" width="200">
            <template #default="scope">
              <el-button text icon="edit" size="small" type="primary"
                @click="editCollectionParams(scope.row)">编辑</el-button>
              <el-button text icon="delete" size="small" type="danger"
                @click="delCollectionParams(scope.row)">删除</el-button>
              <!-- <el-button
                text
                icon="CopyDocument"
                size="small"
                type="primary"
                @click="handleCollectionCopy(scope.row)"
                >复制</el-button
              > -->
            </template>
          </el-table-column>
        </el-table>
      </el-form>
    </el-drawer>

    <aiChat></aiChat>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import mySplit from '@/components/my-split/my-split.vue';
import { ref, getCurrentInstance, onMounted, reactive, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getToken } from '@/utils/auth';
import Sortable from 'sortablejs';
import { ElMessage, uploadListEmits } from 'element-plus';
import { ArrowDown, Sort } from '@element-plus/icons-vue';
import { VueDraggableNext as draggable } from 'vue-draggable-next';
import demandSet from '@/views/CRM/demand/components/demandSet.vue';
import demandSetAll from './demandSetAll.vue';
import demandList from './demandList.vue';
import aiChat from './aiChat.vue';
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: true,
  menu: true,
  filter: true,
  addBtn: true,
  props: {
    labelText: '标题',
    label: 'categoryName',
    value: 'id',
    children: 'children',
  },
  formOption: {
    column: {
      categoryName: {
        label: '分类名称',
        span: 12,
        rules: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
          },
        ],
      },
      sort: {
        label: '排序',
        span: 12,
      },
    },
  },
});

let params = ref({});
let tableForm = ref({
  categoryId: ''
});

let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let mainRef = ref(null);
onMounted(() => {
  getTreeData();
});
let loading = ref(false);
function treeSave(node, data, done, loading) {
  console.log(node, data);
  const value = {
    ...data,
    parentId: node.data?.id,
  };
  axios
    .post('/api/vt-admin/discoverCategory/save', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
        if (node.data?.id) {
          hasChildren.value = true;
        }
      }
    })
    .catch(err => {
      done();
      parentId.value = 0;
    });
}
function treeUpdate(node, data, done, loading) {
  const value = {
    ...data,
  };
  axios
    .post('/api/vt-admin/discoverCategory/update', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        // onLoad();
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function treeDel(data, done) {
  console.log(data);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/discoverCategory/remove?ids=' + data.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
        done(form);
      });
    })
    .catch(() => { });
}
let router = useRouter();
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/discoverCategory/tree', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
      treeData.value.unshift({
        id: '',
        categoryName: '待审查',
        leaf: false,
      });
      setShowCategory();
    });
}
function setShowCategory() {
  nextTick(() => {
    // 递归函数，用于找到最后一层节点的第一个id，找到后就停止查找
    function findLastLevelIds(nodes) {
      for (let node of nodes) {
        if (node.children && node.children.length > 0) {
          // 如果有子节点，递归查找子节点的最后一层id
          const result = findLastLevelIds(node.children);
          if (result.length > 0) {
            return result;
          }
        } else {
          // 如果没有子节点，将当前节点的id添加到结果数组中并返回
          return [node.id];
        }
      }
      return [];
    }

    const lastLevelIds = findLastLevelIds(treeData.value);
    if (lastLevelIds.length > 0) {
      proxy.$refs.tree.setCurrentKey(categoryId.value || lastLevelIds[lastLevelIds.length - 1]);
      if (!categoryId.value) {
        categoryId.value = lastLevelIds[lastLevelIds.length - 1];
        hasChildren.value = false;
      }
      onLoad(); // 调用 getTableData 函数，传入 categoryId 作为参数，用于获取表格数据，同时设置表格数据的 sor
    } else {
      proxy.$refs.tree.setCurrentKey(categoryId.value);
    }
  });
}

let categoryId = ref('');
let hasChildren = ref(true);
let parentId = ref('');
function nodeClick(val, accountName) {
  categoryId.value = val.id;
  parentId.value = val.parentId;
  hasChildren.value = val.children?.length > 0;
  console.log(val);
  page.value.currentPage = 1;
  onLoad();
}

// 表格处理
const addUrl = '/api/vt-admin/pc-discover/save';
const delUrl = '/api/vt-admin/pc-discover/remove?ids=';
const updateUrl = '/api/vt-admin/pc-discover/update';
const tableUrl = '/api/vt-admin/pc-discover/page';
let tableOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  border: true,
  editBtn: false,
  delBtn: false,
  reserveSelection: true, // 开启分页保留选择功能
  selection: true,
  searchMenuSpan: 4,
  calcHeight: 150,
  menuWidth: 200,
  size: 'small',
  column: [
    {
      label: '机会名称',
      prop: 'title',
      span: 12,
      search: true,
      width: 150,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入机会名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '关联分类',
      prop: 'categoryId',
      span: 12,
      width: 150,
      overHidden: true,
      type: 'tree',
      dicUrl: '/api/vt-admin/discoverCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
        children: 'children',
      },
      parent: false,
      dateType: 'String',
      multiple: true,
      search: false,
      rules: [
        {
          required: true,
          message: '请选择关联分类',
        },
      ],
    },
    {
      label: '机会画像',
      prop: 'content',
      type: 'textarea',
      span: 24,
      minWidth: 250,
      rules: [
        {
          required: true,
          message: '请输入机会画像',
        },
      ],
    },
    {
      label: '需求采集表',
      prop: 'requiredmentProperty',
      type: 'upload',
      dataType: 'object',
      width: 150,
      span: 24,
      slot: true,
    },
    {
      label: '创建人',
      prop: 'createName',
      display: false,
      width: 80,

      overHidden: true,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      valueFormat: 'YYYY-MM-DD',
      format: 'YYYY-MM-DD HH:mm',
      display: false,
      span: 12,
      width: 115,
      align: 'center',
    },
  ],
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        categoryId: categoryId.value,
        isNoCategory: categoryId.value == '' ? 1 : null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}

function rowSave(form, done, loading) {
  const data = {
    ...form,
    files: form.files && form.files.map(item => item.value).join(','),

  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => { });
}
function beforeOpen(done, type) {
  tableForm.value.files = tableForm.value.attachList
    ? tableForm.value.attachList.map(item => {
      return {
        value: item.id,
        label: item.originalName,
      };
    })
    : [];

  done();
}
let selectList = ref([]);
function handleSectionChange(list) {
  console.log(list);

  selectList.value = list;
}

// 分类编辑相关
let editingRowId = ref(null);
let categoryTreeRef = ref(null);

function showCategoryTree(row) {
  editingRowId.value = row.id;
  nextTick(() => {
    const popover = proxy.$refs[`popover-${row.id}`];
    if (popover && popover[0]) {
      popover[0].show?.();
    }
  });
}

function handleTreeCheckChange(data, checked, indeterminate) {
  // 如果选中的是非叶子节点，则取消选中
  if (checked && data.children && data.children.length > 0) {
    categoryTreeRef.value.setChecked(data.id, false);
    proxy.$message.warning('只能选择叶子节点');
  }
}

function saveCategoryChange(row) {
  if (!categoryTreeRef.value) return;

  const checkedKeys = categoryTreeRef.value.getCheckedKeys();
  const categoryIds = checkedKeys.join(',');

  // 调用更新接口
  axios
    .post('/api/vt-admin/pc-discover/updateCategoryId?ids=' + row.id + '&categoryId=' + categoryIds)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success('分类修改成功');
        proxy.$refs[`popover-${row.id}`].hide();
        onLoad();
      }
    })
    .catch(err => {
      proxy.$message.error('分类修改失败');
    });
}
let detailDialogVisible = ref(false);
function handleView(row) {
  currentRow.value = row;
  detailDialogVisible.value = true;
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
// 新增编辑 查看需求

let form = ref({ step: 0 });

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  submitBtn: false,
  emptyBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 6,
  menuWidth: 150,
  border: true,
  column: {
    clueName: {
      label: '需求名称',
      type: 'input',
      search: true,
      rules: [
        {
          required: true,
          message: '请输入需求名称',
        },
      ],
    },
    customerId: {
      label: '客户名称',
      component: 'wf-customer-select',
      hide: true,
      isHasAdd: true,
      formatter: row => {
        return row.customerName;
      },
      rules: [
        {
          required: true,
          message: '请选择客户名称',
        },
      ],
    },
    customerName: {
      label: '关联客户',
      display: false,
      search: true,
      component: 'wf-customer-drop',
    },
    remark: {
      label: '需求描述',
      type: 'textarea',
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入需求描述',
        },
      ],
    },
    // clueRequirementProperty: {
    //   label: '需求需求单',
    //   type: 'textarea',
    //   span: 24,
    //   width: 100,
    //   display: false,
    // },
    requirementStatus: {
      label: '需求状态',
      width: 120,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '未填写',
        },
        {
          value: 1,
          label: '已填写',
        },
      ],
      display: false,
    },
    createTime: {
      label: '创建时间',
      type: 'date',

      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      display: false,
      search: true,
      span: 12,
      width: 140,
      display: false,
    },
  },
});
let multipleOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  submitBtn: false,
  emptyBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 6,
  menuWidth: 150,
  border: true,

  group: [
    {
      label: '客户信息',
      column: [
        {
          prop: 'customerId',
          label: '客户名称',
          component: 'wf-customer-select',
          hide: true,
          isHasAdd: true,
          rules: [
            {
              required: true,
              message: '请选择客户名称',
            },
          ],
        },
      ],
    },
    {
      label: '需求信息',
      column: [
        {
          type: 'dynamic',
          labelWidth: 0,
          prop: 'demandList',
          label: '需求列表',
          emptyBtn: false,

          span: 24,
          index: false,
          children: {
            type: 'form',
            index: false,
            addBtn: false,
            delBtn: false,
            emptyBtn: false,
            column: [
              {
                prop: 'clueName',
                label: '需求名称',
                span: 20,
                component: 'el-input',
                rules: [
                  {
                    required: true,
                    message: '请输入需求名称',
                  },
                ],
              },
              {
                prop: 'remark',
                label: '需求描述',
                span: 20,
                type: 'textarea',
                component: 'el-input',
                rules: [],
              },
            ],
          },
        },
      ],
    },
  ],
});
let drawer = ref(false);
let currentRow = ref({});
let detailForm = ref({});
function set(row) {
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
        keys: [...new Set(res.data.data.propertyVOList.map(item => item.categoryId))],
      };

      form.value = res.data.data;
      currentRow.value = row;
      form.value.step = 1;
      drawer.value = true;
      option.value.emptyBtn = false;
    });
}

function confirmClick() {
  const data = proxy.$refs.demandSetRef.getData();


  if (!isAddAll.value) {
    axios
      .post('/api/vt-admin/clue/save', {
        ...form.value,
        discoverPcId: currentRow.value.id,
        propertyDTOList: data.map(item => {
          return {
            requirementPropertyId: item.id,
          };
        }),
      })
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);

          drawer.value = false;
          router.push({ path: '/CRM/demand/myDemand' });
        }
      })
      .catch(err => {
        done();
      });
  } else {
    axios
      .post('/api/vt-admin/clue/saveBatch', {
        discoverVOList: data.map(item => {
          return {
            ...item,
            customerId: form.value.customerId,
          };
        }),
        customerId: form.value.customerId,
      })
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          drawer.value = false;
          router.push({ path: '/CRM/demand/myDemand' });
        }
      })
      .catch(err => {
        done();
      });
  }

}
let url = ref(null);

async function addDemand(row) {
  currentClueId.value = row.id;
 const res = await getCollectionTableData()
  currentRow.value = row;
  isAddAll.value = false;
  form.value = {
    step: 0,
    clueName: row.title,
    propertyVOList: res.map(item => {
      return {
        ...item,
        requirementPropertyId:item.id,
        id:null
      }
    }),
  };
  drawer.value = true;
  option.value.emptyBtn = false;
  proxy.$refs.addFormRef.clearValidate();
  proxy.$refs.addFormRef.resetForm();
}
let isAddAll = ref(false);
async function addDemandAll() {


  // 假设存在一个函数用于检查是否有选择项，这里先简单用 selectList 长度判断
  if (selectList.value.length === 0) {
    ElMessage.warning('请先选择数据');
    return;
  }
  isAddAll.value = false;   // 暂时不需要添加多个采集需求
  proxy.$refs.addFormRef?.clearValidate();
  // proxy.$refs.addFormRef?.resetForm();
 const res = await Promise.all(selectList.value.map(item => getCollectionTableData(item.id)))
  form.value = {
    step: 0,
     clueName: [...new Set(selectList.value.map(item => item.title))].join('、') + '的需求采集表',
    // demandList: selectList.value.map(item => {
    //   return {
    //     ...item,
    //     clueName: item.title,
    //   };
    // }),
    propertyVOList: res.flat().map(item => {
        return {
          ...item,
          requirementPropertyId:item.id,
          id:null
        }
    }),
    customerId: null
  };
  drawer.value = true;
  option.value.emptyBtn = false;
}
let dialogVisible = ref(false);
let editOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelPosition: 'top',
  labelSuffix: ' ',
  column: [],
});
let editForm = ref({});
let propIds = ref([]);
function edit(row, type) {
  dialogVisible.value = true;
  currentRow.value = row;
  editOption.value.detail = type;
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
      };

      res.data.data.propertyVOList.forEach(item => {
        editForm.value[item.id] =
          item.type == 1 ? (item.value ? item.value.split(',') : []) : item.value;
        editForm.value[item.id + 'input'] =
          item.type == 0 || item.type == 1 ? item.otherInput : null;
        if (item.type == 0 || item.type == 1) {
          if (
            item.requirementPropertyValuesVOS.some(
              item => item.value && item.value.indexOf('其他') > -1
            )
          ) {
            propIds.value.push({
              ...item,
            });
          }
        }
      });
      const allData = res.data.data.propertyVOList.reduce((pre, cur) => {
        const data = pre.find(iten => iten.id == cur.categoryId);
        if (data) {
          data.tableData.push({ ...cur });
        } else {
          pre.push({
            id: cur.categoryId,
            categoryName: cur.categoryName,
            tableData: [{ ...cur }],
          });
        }
        return pre;
      }, []);

      editOption.value.group = allData.map(item => {
        return {
          label: item.categoryName,
          prop: item.id,
          column: item.tableData.map((item, index) => {
            return {
              prop: item.id,
              label: `${index + 1}.${item.collectionContent}${item.type == 0 ? '(单选)' : item.type == 1 ? '(多选)' : ''
                }`,
              span: 24,
              rules: [
                {
                  required: item.isRequired == 1,
                  message: '请填写此项',
                },
              ],
              placeholder: '请输入内容',
              value: item.type == 1 ? item.value && item.value.split(',') : item.value,
              type: item.type == 0 ? 'radio' : item.type == 1 ? 'checkbox' : 'textarea',
              props: {
                value: 'value',
                label: 'value',
              },
              dicData: item.type == 1 || item.type == 0 ? item.requirementPropertyValuesVOS : [],
            };
          }),
        };
      });
    });
}
function editSubmit(form, done, loading) {
  const data = {
    ...currentRow.value,
    propertyDTOList: Object.keys(form)
      .map(item => {
        return {
          id: item,
          value: Array.isArray(form[item]) ? form[item].join() : form[item],
          otherInput: form[item + 'input'] || null,
        };
      })
      .filter(item => item.id.indexOf('input') == -1),
  };

  axios.post('/api/vt-admin/clue/complete', data).then(r => {
    proxy.$message.success(r.data.msg);
    onLoad();
    done();
    dialogVisible.value = false;
  });
}
function nextStep(params) {
  proxy.$refs.addFormRef.validate(valid => {
    if (valid) {
      form.value.step = 1;
    } else {
      proxy.$message.error('请填写完整信息');
    }
  });
}
let splitValue = ref(0.7);
let iframeRef = ref(null);
onMounted(() => {
  initBot();
})
async function initBot(params) {
  //   async function compressAndEncodeBase64(input) {
  //     const uint8Array = new TextEncoder().encode(input);
  //     const compressedStream = new Response(
  //       new Blob([uint8Array])
  //         .stream()
  //         .pipeThrough(new CompressionStream("gzip"))
  //     ).arrayBuffer();
  //     const compressedUint8Array = new Uint8Array(await compressedStream);
  //     return btoa(String.fromCharCode(...compressedUint8Array));
  //   }

  // const {user_id,access_token} = proxy.$store.getters.userInfo;

  //   // Usage:
  //   // const user_id = await compressAndEncodeBase64("333");
  //   // const conversation_id = await compressAndEncodeBase64("abaf6368-7757-48ba-b5b1-6ba2a62152b7");
  //   // const token = await compressAndEncodeBase64("DE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwicIiwicDE2NjA4MjA2ODUwIiwicDE2NjA4MjA2ODUwIiwic");





  //   iframeRef.value.src = `http://localhost/chat/5d1WTr2PRTQq1uQx?blade_auth=${access_token}&sys.user_id=${user_id}`;

}

// 采集表管理相关
let collectionDrawerVisible = ref(false);
let collectionTableData = ref([]);
let collectionTableRef = ref();
let collectionSelectList = ref([]);
let currentClueId = ref('');

// 打开采集表管理抽屉
function manageCollection(row) {
  currentRow.value = row;
  currentClueId.value = row.id;
  collectionDrawerVisible.value = true;
  getCollectionTableData();
}

// 获取采集表数据 - 根据线索ID获取关联的采集配置
function getCollectionTableData(id) {
  return new Promise((resolve, reject) => {
    axios
      .get('/api/vt-admin/requirementProperty/page', {
        params: {
          categoryId:id || currentClueId.value,
          current: 1,
          size: 20000,
        },
      })
      .then(res => {
        collectionTableData.value = res.data.data.records;
        nextTick(() => {
          setCollectionSort();
        });
        resolve(res.data.data.records);
      })
      .catch(() => {
        collectionTableData.value = [];
        reject([]);
      });
  })

}

// 显示输入框
function showCollectionInput(row, index) {
  row.inputVisible = true;
  nextTick(() => {
    setTimeout(() => {
      const refKey = 'saveCollectionTagInput' + index;
      if (proxy.$refs[refKey]) {
        proxy.$refs[refKey].focus();
      }
    }, 200);
  });
}

// 新增采集内容
function addCollectionParams() {
  proxy.$refs.dialogForm.show({
    title: '新增采集内容',
    option: {
      column: [
        {
          label: '采集内容',
          type: 'input',
          prop: 'collectionContent',
          showWordLimit: true,
          rules: [{ required: true, message: '请输入采集内容' }],
        },
        {
          label: '回答方式',
          type: 'radio',
          prop: 'type',
          rules: [{ required: true, message: '请选择回答方式' }],
          dicData: [
            { label: '单选', value: 0 },
            { label: '多选', value: 1 },
            { label: '文本输入', value: 2 },
          ],
        },
        {
          label: '是否必填',
          type: 'switch',
          prop: 'isRequired',
          dicData: [
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ],
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/requirementProperty/save', {
          categoryId: currentClueId.value,
          collectionContent: res.data.collectionContent,
          isRequired: res.data.isRequired,
          isUse: res.data.isUse,
          type: res.data.type,
          remark: res.data.remark,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getCollectionTableData();
        });
    },
  });
}

// 编辑采集内容
function editCollectionParams(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑采集内容',
    option: {
      column: [
        {
          label: '采集内容',
          type: 'input',
          value: row.collectionContent,
          prop: 'collectionContent',
          showWordLimit: true,
          rules: [{ required: true, message: '请输入采集内容' }],
        },
        {
          label: '回答方式',
          type: 'radio',
          prop: 'type',
          value: row.type,
          dicData: [
            { label: '单选', value: 0 },
            { label: '多选', value: 1 },
            { label: '文本输入', value: 2 },
          ],
        },
        {
          label: '是否必填',
          type: 'switch',
          prop: 'isRequired',
          dicData: [
            { label: '否', value: 0 },
            { label: '是', value: 1 },
          ],
        },
        {
          label: '备注',
          prop: 'remark',
          value: row.remark,
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/requirementProperty/update', {
          id: row.id,
          type: res.data.type,
          isUse: res.data.isUse,
          collectionContent: res.data.collectionContent,
          isRequired: res.data.isRequired,
          remark: res.data.remark,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getCollectionTableData();
        });
    },
  });
}

// 添加标签
function addCollectionTag(row) {
  if (!row.inputValue) {
    row.inputVisible = false;
    return;
  }
  axios
    .post('/api/vt-admin/requirementProperty/addValue', {
      propertyId: row.id,
      value: row.inputValue,
    })
    .then(res => {
      proxy.$message.success('操作成功');
      getCollectionTableData();
    });
}

// 删除标签
function handleCollectionTagClose(row) {
  proxy
    .$confirm('确定删除此标签吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/requirementProperty/deleteValue?id=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getCollectionTableData();
      });
    });
}

// 删除采集内容
function delCollectionParams(row) {
  proxy
    .$confirm('确定删除此参数吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/requirementProperty/remove?ids=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getCollectionTableData();
      });
    });
}

// 标签排序回调
function onCollectionMoveCallback(e, row) {
  const data = row.valuesVOList.map((item, index) => {
    return {
      ...item,
      sort: index,
    };
  });
  axios
    .post('/api/vt-admin/requirementProperty/updateSort', {
      id: row.id,
      valuesVOList: data,
    })
    .then(res => {
      proxy.$message.success('排序成功');
      getCollectionTableData();
    });
}

// 设置表格排序
const setCollectionSort = () => {
  if (!collectionTableRef.value) return;
  let el = collectionTableRef.value.$el.querySelector('.el-table__body-wrapper tbody');
  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const targetRow = collectionTableData.value.splice(evt.oldIndex, 1);
      collectionTableData.value.splice(evt.newIndex, 0, targetRow[0]);

      axios
        .post('/api/vt-admin/requirementProperty/requirementPropertySort', {
          categoryId: currentClueId.value,
          sortVOList: collectionTableData.value.map((item, index) => {
            return {
              id: item.id,
              sort: index,
            };
          }),
        })
        .then(res => {
          getCollectionTableData();
        });
    },
  });
};

// 复制单个采集内容
function handleCollectionCopy(row) {
  proxy.$refs.dialogForm.show({
    title: '复制采集内容',
    option: {
      column: [
        {
          label: '选择分类',
          type: 'tree',
          prop: 'categoryId',
          parent: false,
          rules: [
            {
              required: true,
              message: '请选择分类',
            },
          ],
          props: {
            label: 'categoryName',
            name: 'categoryName',
            value: 'id',
            children: 'children',
          },
          dicUrl: '/api/vt-admin/requirementCategory/tree',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/requirementProperty/copy', {
          ids: row.id,
          categoryId: res.data.categoryId,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getCollectionTableData();
        });
    },
  });
}

// 选择变化
function handleCollectionSelectionChange(list) {
  collectionSelectList.value = list;
}

// 批量复制
function copyCollectionList() {
  if (collectionSelectList.value.length == 0) return ElMessage.warning('请选择要复制的内容');
  proxy.$refs.dialogForm.show({
    title: '复制采集内容',
    option: {
      column: [
        {
          label: '选择分类',
          type: 'tree',
          prop: 'categoryId',
          parent: false,
          rules: [
            {
              required: true,
              message: '请选择分类',
            },
          ],
          props: {
            label: 'categoryName',
            name: 'categoryName',
            value: 'id',
            children: 'children',
          },
          dicUrl: '/api/vt-admin/requirementCategory/tree',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/requirementProperty/copy', {
          ids: collectionSelectList.value.map(item => item.id).join(','),
          categoryId: res.data.categoryId,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getCollectionTableData();
        });
    },
  });
}

async function aiHelp() {
  splitValue.value = splitValue.value == 1 ? 0.7 : 1;
  await nextTick();
  initBot();
}

</script>

<style lang="scss" scoped>
.tree-container {
  max-height: 400px;
  overflow-y: auto;
}
</style>
