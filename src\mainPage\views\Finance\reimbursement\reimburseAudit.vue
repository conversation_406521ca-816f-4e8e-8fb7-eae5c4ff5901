<template>
  <basic-container>
   
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      :before-open="beforeOpen"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">申请总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
      </template>
      <template #reimbursementFiles1-form="{ row }">
        <File :fileList="row.fileList"></File>
      </template>
      <template #reimbursementFiles-form="{ row }">
        <File :fileList="form.fileList"></File>
      </template>
      <template #menu="{ row }">
        <!-- <el-button
          type="primary"
          v-if="row.reimbursementStatus == 0"
          @click="$refs.crud.rowEdit(row)"
          icon="edit"
          text
          >编辑</el-button
        > -->
        <el-button type="primary" @click="$refs.crud.rowView(row)" icon="check" text>审核</el-button>
        <el-button type="primary" @click="downloadAll(row)" icon="download" text
          >打包下载</el-button
        >
      </template>
      <template #reimbursementFiles="{ row }">
        <File :fileList="row.fileList"></File>
      </template>
      <template #menu-form="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-if="type === 'edit'"
          @click="handleUpdate(0)"
          >修改</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'edit'"
          @click="handleUpdate(1)"
          >修改并提交</el-button
        >
        <el-button type="primary" icon="el-icon-check" v-if="type === 'add'" @click="handleAdd(0)"
          >保存</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'add'"
          @click="handleAdd(1)"
          >保存并提交</el-button
        >
        <div
          v-if="type == 'view'"
          style="display: flex; justify-content: flex-start; height: 200px; width: 500px"
        >
          <el-form ref="auditFormRef" :model="auditForm" style="width: 100%" label-width="80px">
            <el-form-item label="审核结果">
              <el-radio-group v-model="auditForm.reimbursementStatus">
                <el-radio :label="2">通过</el-radio>
                <el-radio :label="3">驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                style="width: 100%"
                v-model="auditForm.auditRemark"
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div style="width: 100%;text-align: left;">
          <el-button v-if="type == 'view'" type="primary" @click="handleConfirm">提交</el-button>
        </div>
      </template>
      <template #reimbursementStatus="{ row }">
        <div v-if="row.reimbursementStatus == 1">
          <el-tag effect="plain" type="info" v-if="row.auditType == 0">待主管审核</el-tag>
          <el-tag effect="plain" type="info" v-if="row.auditType == 1">待总经理审核</el-tag>
        </div>
        <div v-else>
          <el-tag
            effect="plain"
            :type="
              row.reimbursementStatus == 0
                ? 'info'
                : row.reimbursementStatus == 2 || row.reimbursementStatus == 4
                ? 'success'
                : 'danger'
            "
            >{{ row.$reimbursementStatus }}</el-tag
          >
        </div>
      </template>
      <template #reimbursementCode="{ row }">
        <el-link type="primary" @click="crud.rowView(row)">{{ row.reimbursementCode }}</el-link>
      </template>
       <template #sealContractName-form="{ row }">
        <el-popover ref="popover1" placement="bottom" :disabled="row.reimbursementTypeName != '商务费'" title="明细" width="1300"  trigger="click">
          <template #reference>
            <span type="primary" style="cursor: pointer" @click="viewDetail(row)">{{
              row.sealContractName
            }}</span>
          </template>
          <div style="height: 600px;overflow-y: auto;">
             <el-divider>利润明细</el-divider>
          <analysis :seal-contract-name="row.sealContractName" :seal-contract-id="row.sealContractId"></analysis>
          <el-divider>收款明细</el-divider>
          <collection :seal-contract-name="row.sealContractName" :seal-contract-id="row.sealContractId"></collection>
          </div>
        </el-popover>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <costSelect ref="costSelectRef" @handleConfirm="handleCostConfirm"></costSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date';
import costSelect from './components/costSelect.vue';
import { download } from '@/utils/download';
import { downloadByUrl } from '@/utils/download';
import analysis from './components/analysis.vue';
import collection from './components/collection.vue';
let costSelectRef = ref();
const { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  saveBtn: true,
  delBtn: false,
  addBtnText: '发起',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  dialogWidth: '70%',
  updateBtn: false,
  dialogType: 'drawer',
  dialogClickModal: true,
  saveBtn: false,
  border: true,
  column: {
    reimbursementCode: {
      label: '报销单号',
      search: true,
      display: false,
    },

    reimbursementDetailDTOList: {
      label: '关联费用',
      type: 'dynamic',
      span: 24,
      hide: true,
      children: {
        align: 'center',
        headerAlign: 'center',
        showSummary: true,
        sumColumnList: [{ label: '总计:', name: 'expensePrice', type: 'sum', decimals: 2 }],
        rowAdd: done => {
          costSelectRef.value.open();
        },
        rowDel: (row, done) => {
          form.value.reimbursementPrice -= row.expensePrice * 1;
          done();
        },
        column: [
          {
            label: '关联客户',
            prop: 'customerName',
            overHidden: true,
            cell: false,
          },
          {
            label: '关联合同',
            prop: 'sealContractName',
            overHidden: true,
            
          },
          {
            type: 'tree',
            label: '费用类型',
            dicUrl: '/blade-system/dict-biz/dictionary-tree?code=expenseType',
            cascader: [],
            span: 12,
            width: 110,
            // search: true,
            display: true,
            cell: false,
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            prop: 'expenseType',
            parent: false,
          },
          // {
          //   type: 'number',
          //   label: '票据张数',
          //   span: 12,
          //   display: true,
          //   hide: true,
          //   prop: 'a170080951774565537',
          // },
          {
            type: 'number',
            label: '费用金额',
            span: 12,

            width: 110,
            display: true,
            prop: 'expensePrice',
            cell: false,
          },

          {
            type: 'textarea',
            label: '用途',
            span: 24,
            display: true,
            prop: 'purpose',
            showWordLimit: true,
            cell: false,
          },

          {
            type: 'date',
            label: '日期',
            span: 12,
            display: true,
            cell: false,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            prop: 'expenseDate',
            disabled: false,
            readonly: false,
            width: 110,
            required: true,
            formatter: (row, column, cellValue) => {
              return row.expenseDate;
            },
            rules: [
              {
                required: true,
                message: '计划收款时间必须填写',
              },
            ],
          },
          {
            label: '附件',
            prop: 'reimbursementFiles1',
            type: 'upload',
            overHidden: true,
            dataType: 'object',
            loadText: '附件上传中，请稍等',
            span: 24,
            slot: true,
            overHidden: true,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'id',
              name: 'originalName',
              // home: 'https://www.w3school.com.cn',
            },
            action: '/blade-resource/attach/upload',
            uploadPreview: file => {
              console.log(file, form.value);
            },
          },
        ],
      },
    },
    // reimbursementFiles: {
    //   label: '附件',

    //   type: 'upload',
    //   overHidden: true,
    //   dataType: 'object',
    //   loadText: '附件上传中，请稍等',
    //   span: 24,
    //   slot: true,
    //   overHidden: true,
    //   // align: 'center',
    //   propsHttp: {
    //     res: 'data',
    //     url: 'id',
    //     name: 'originalName',
    //     // home: 'https://www.w3school.com.cn',
    //   },
    //   action: '/blade-resource/attach/upload',
    //   uploadPreview: file => {
    //     console.log(file, form.value);
    //   },
    // },
    reimbursementStatus: {
      label: '报销状态',
      display: false,
      width: 120,
      dicData: [
        {
          value: 0,
          label: '草稿',
        },
        {
          value: 1,
          label: '待审核',
        },
        {
          value: 2,
          label: '审核成功',
        },
        {
          value: 3,
          label: '审核失败',
        },
        {
          value: 4,
          label: '完成报销',
        },
      ],
    },
    reimbursementPrice: {
      label: '报销金额',
      span: 12,
      width: 120,
    },
    createTime: {
      label: '申请时间',
      type: 'date',
      label: '日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      width: 120,
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
    },
    remark: {
      label: '备注',
      span: 24,
      type: 'textarea',
    },
    createUserName: {
      label: '申请人',
      component: 'wf-user-drop',
      search: true,
      readonly: true,
      width: 120,
      span: 24,
      params: {
        roleKeys: '',
      },
      value: proxy.$store.getters.userInfo.real_name,
    },
    // createTime: {
    //   type: 'date',
    //   label: '申请日期',
    //   span: 12,
    //   searchSpan: 6,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   width: 110,
    //   display: false,

    // },
    dateRange: {
      type: 'date',
      label: '日期',
      span: 12,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',

      component: 'wf-daterange-search',

      width: 110,
      required: true,
      hide: true,
      display: false,
      search: true,
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/reimbursement/save';
const delUrl = '/api/vt-admin/reimbursement/remove?ids=';
const updateUrl = '/api/vt-admin/reimbursement/update';
const tableUrl = '/api/vt-admin/reimbursement/page';
let params = ref({});
let tableData = ref([]);
onActivated(() => {
  onLoad();
});
let route = useRoute();
let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 1,
        startTime: params.value.dateRange && params.value.dateRange[0],
        endTime: params.value.dateRange && params.value.dateRange[1],
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/reimbursement/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 1,
        startTime: params.value.dateRange && params.value.dateRange[0],
        endTime: params.value.dateRange && params.value.dateRange[1],
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    reimbursementFiles:
      form.reimbursementFiles && form.reimbursementFiles.map(item => item.value).join(','),
    createTime: null,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    reimbursementFiles:
      row.reimbursementFiles && row.reimbursementFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleCostConfirm(list) {
  const arr = list.map(item => {
    return {
      ...item,
      id: null,
      expenseId: item.id,
    };
  });

  form.value.reimbursementDetailDTOList.push(...arr);
  totalAmount();
}
function totalAmount() {
  form.value.reimbursementPrice = form.value.reimbursementDetailDTOList
    .reduce((pre, cur) => {
      pre = pre + cur.expensePrice * 1;
      return pre;
    }, 0)
    .tofixed(2);
}
function beforeOpen(done, type) {
  if (['view', 'edit'].includes(type)) {
    axios
      .get('/api/vt-admin/reimbursement/detail', {
        params: {
          id: form.value.id,
        },
      })
      .then(res => {
        form.value = res.data.data;
        form.value.reimbursementFiles =
          form.value.fileList &&
          form.value.fileList.map(item => {
            return {
              label: item.originalName,
              value: item.id,
            };
          });
        form.value.reimbursementDetailDTOList =
          form.value.detailVOList &&
          form.value.detailVOList.map(item => {
            return {
              ...item,
              id: null,
              expenseId: item.id,
            };
          });

        if (type == 'view') {
          auditForm.value = {
            id: form.value.id,
            reimbursementStatus: 2,
          };
        }
        done();
      });
  }else{
    done();
  }

}
let crud = ref();
function handleUpdate(value) {
  form.value.reimbursementStatus = value;
  crud.value.rowUpdate();
}
function handleAdd(value) {
  form.value.reimbursementStatus = value;
  crud.value.rowSave();
}
let auditForm = ref({
  reimbursementStatus: 2,
});
function audit(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',

    option: {
      column: [
        {
          label: '审核结果',
          span: 24,
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择审核结果',
            },
          ],
          prop: 'reimbursementStatus',
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
        },
        {
          label: '原因',
          prop: 'auditRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/reimbursement/audit', {
          id: row.id,
          ...res.data,
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          proxy.$store.dispatch('getMessageList');
          res.close();
          onLoad();
        });
    },
  });
}
function handleConfirm() {
  axios
    .post('/api/vt-admin/reimbursement/audit', {
      ...auditForm.value,
    })
    .then(r => {
      proxy.$message.success(r.data.msg);
      proxy.$store.dispatch('getMessageList');
      onLoad();
      proxy.$refs.crud.closeDialog();
    });
}
function downloadAll(row) {
  downloadByUrl('/api/vt-admin/reimbursement/downExpenseFiles', {
    id: row.id,
  });
}
</script>

<style lang="scss" scoped>
 :deep(.el-drawer .avue-form){
  padding-bottom: 250px!important;
 }
 :deep(.avue-dialog__footer){
  z-index: 4;
 }
</style>
